using System;
using System.Drawing;
using System.Windows.Forms;
using AccountingSystem.Services;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// النموذج الرئيسي للنظام
    /// </summary>
    public partial class MainForm : Form
    {
        private MenuStrip menuStrip;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;
        private ToolStripStatusLabel userLabel;
        private ToolStripStatusLabel dateLabel;
        private Panel mainPanel;

        public MainForm()
        {
            InitializeComponent();
            SetupForm();
            UpdateStatusBar();
        }

        private void InitializeComponent()
        {
            this.menuStrip = new MenuStrip();
            this.statusStrip = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel();
            this.userLabel = new ToolStripStatusLabel();
            this.dateLabel = new ToolStripStatusLabel();
            this.mainPanel = new Panel();

            this.SuspendLayout();

            // Form
            this.Text = "نظام المحاسبة الذكي";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Icon = SystemIcons.Application;

            // Menu Strip
            this.menuStrip.BackColor = Color.FromArgb(25, 118, 210);
            this.menuStrip.ForeColor = Color.White;
            this.menuStrip.Font = new Font("Tahoma", 10F);
            this.menuStrip.RightToLeft = RightToLeft.Yes;

            CreateMenuItems();

            // Main Panel
            this.mainPanel.Dock = DockStyle.Fill;
            this.mainPanel.BackColor = Color.White;
            this.mainPanel.Padding = new Padding(10);

            // Status Strip
            this.statusStrip.BackColor = Color.FromArgb(240, 240, 240);
            this.statusStrip.Font = new Font("Tahoma", 9F);
            this.statusStrip.RightToLeft = RightToLeft.Yes;

            // Status Labels
            this.statusLabel.Text = "جاهز";
            this.statusLabel.Spring = true;
            this.statusLabel.TextAlign = ContentAlignment.MiddleLeft;

            this.userLabel.Text = $"المستخدم: {AuthenticationService.CurrentUser?.FullName}";
            this.userLabel.BorderSides = ToolStripStatusLabelBorderSides.Left;

            this.dateLabel.Text = $"التاريخ: {DateTime.Now:yyyy/MM/dd}";
            this.dateLabel.BorderSides = ToolStripStatusLabelBorderSides.Left;

            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.statusLabel,
                this.userLabel,
                this.dateLabel
            });

            // Add controls to form
            this.MainMenuStrip = this.menuStrip;
            this.Controls.Add(this.mainPanel);
            this.Controls.Add(this.menuStrip);
            this.Controls.Add(this.statusStrip);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void CreateMenuItems()
        {
            // قائمة الملف
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("تسجيل خروج", null, Logout_Click));
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("خروج", null, Exit_Click));

            // قائمة الحسابات
            var accountsMenu = new ToolStripMenuItem("الحسابات");
            accountsMenu.DropDownItems.Add(new ToolStripMenuItem("شجرة الحسابات", null, AccountsTree_Click));
            accountsMenu.DropDownItems.Add(new ToolStripMenuItem("إضافة حساب جديد", null, AddAccount_Click));

            // قائمة القيود
            var journalMenu = new ToolStripMenuItem("القيود اليومية");
            journalMenu.DropDownItems.Add(new ToolStripMenuItem("عرض القيود", null, ViewJournalEntries_Click));
            journalMenu.DropDownItems.Add(new ToolStripMenuItem("إضافة قيد جديد", null, AddJournalEntry_Click));

            // قائمة السندات
            var vouchersMenu = new ToolStripMenuItem("السندات");
            vouchersMenu.DropDownItems.Add(new ToolStripMenuItem("سندات القبض", null, Receipts_Click));
            vouchersMenu.DropDownItems.Add(new ToolStripMenuItem("سندات الصرف", null, Payments_Click));

            // قائمة العملاء
            var customersMenu = new ToolStripMenuItem("العملاء");
            customersMenu.DropDownItems.Add(new ToolStripMenuItem("إدارة العملاء", null, Customers_Click));
            customersMenu.DropDownItems.Add(new ToolStripMenuItem("إضافة عميل جديد", null, AddCustomer_Click));

            // قائمة المنتجات
            var productsMenu = new ToolStripMenuItem("المنتجات");
            productsMenu.DropDownItems.Add(new ToolStripMenuItem("إدارة المنتجات", null, Products_Click));
            productsMenu.DropDownItems.Add(new ToolStripMenuItem("إضافة منتج جديد", null, AddProduct_Click));

            // قائمة الفواتير
            var invoicesMenu = new ToolStripMenuItem("الفواتير");
            invoicesMenu.DropDownItems.Add(new ToolStripMenuItem("فواتير المبيعات", null, SalesInvoices_Click));
            invoicesMenu.DropDownItems.Add(new ToolStripMenuItem("فواتير المشتريات", null, PurchaseInvoices_Click));
            invoicesMenu.DropDownItems.Add(new ToolStripMenuItem("إضافة فاتورة جديدة", null, AddInvoice_Click));

            // قائمة التقارير
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("ميزان المراجعة", null, TrialBalance_Click));
            reportsMenu.DropDownItems.Add(new ToolStripSeparator());
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("كشف حساب العميل", null, CustomerStatement_Click));
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("تقرير سندات القبض", null, ReceiptsReport_Click));
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("تقرير سندات الصرف", null, PaymentsReport_Click));
            reportsMenu.DropDownItems.Add(new ToolStripSeparator());
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("قائمة الدخل", null, IncomeStatement_Click));
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("الميزانية العمومية", null, BalanceSheet_Click));

            // قائمة الذكاء الاصطناعي
            var aiMenu = new ToolStripMenuItem("الذكاء الاصطناعي");
            aiMenu.DropDownItems.Add(new ToolStripMenuItem("تحليل البيانات المالية", null, AIAnalysis_Click));
            aiMenu.DropDownItems.Add(new ToolStripMenuItem("التوصيات الذكية", null, AIRecommendations_Click));

            // قائمة الإعدادات
            var settingsMenu = new ToolStripMenuItem("الإعدادات");
            settingsMenu.DropDownItems.Add(new ToolStripMenuItem("إدارة المستخدمين", null, Users_Click));
            settingsMenu.DropDownItems.Add(new ToolStripMenuItem("إعدادات النظام", null, SystemSettings_Click));
            settingsMenu.DropDownItems.Add(new ToolStripSeparator());
            settingsMenu.DropDownItems.Add(new ToolStripMenuItem("نسخ احتياطي", null, Backup_Click));
            settingsMenu.DropDownItems.Add(new ToolStripMenuItem("استعادة نسخة احتياطية", null, Restore_Click));

            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("حول البرنامج", null, About_Click));

            // إضافة القوائم إلى شريط القوائم
            this.menuStrip.Items.AddRange(new ToolStripItem[] {
                fileMenu,
                accountsMenu,
                journalMenu,
                vouchersMenu,
                customersMenu,
                productsMenu,
                invoicesMenu,
                reportsMenu,
                aiMenu,
                settingsMenu,
                helpMenu
            });
        }

        private void SetupForm()
        {
            // إعداد النموذج
            this.FormClosing += MainForm_FormClosing;

            // إعداد مؤقت لتحديث شريط الحالة
            var timer = new Timer();
            timer.Interval = 1000; // كل ثانية
            timer.Tick += Timer_Tick;
            timer.Start();
        }

        private void UpdateStatusBar()
        {
            if (AuthenticationService.CurrentUser != null)
            {
                userLabel.Text = $"المستخدم: {AuthenticationService.CurrentUser.FullName}";
            }
            dateLabel.Text = $"التاريخ: {DateTime.Now:yyyy/MM/dd HH:mm:ss}";
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            dateLabel.Text = $"التاريخ: {DateTime.Now:yyyy/MM/dd HH:mm:ss}";
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد الإغلاق",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
            else
            {
                AuthenticationService.Logout();
            }
        }

        // Event Handlers للقوائم
        private void Logout_Click(object sender, EventArgs e)
        {
            AuthenticationService.Logout();
            this.Hide();
            var loginForm = new LoginForm();
            loginForm.Show();
        }

        private void Exit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void AccountsTree_Click(object sender, EventArgs e)
        {
            try
            {
                var accountsForm = new AccountsTreeForm();
                accountsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح شجرة الحسابات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddAccount_Click(object sender, EventArgs e)
        {
            try
            {
                var addAccountForm = new AddEditAccountForm();
                addAccountForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج إضافة الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewJournalEntries_Click(object sender, EventArgs e)
        {
            try
            {
                var journalListForm = new JournalEntriesListForm();
                journalListForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح قائمة القيود اليومية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddJournalEntry_Click(object sender, EventArgs e)
        {
            try
            {
                var journalEntryForm = new JournalEntryForm();
                journalEntryForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج القيد اليومي: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Receipts_Click(object sender, EventArgs e)
        {
            try
            {
                var receiptsForm = new ReceiptsForm();
                receiptsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح سندات القبض: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Payments_Click(object sender, EventArgs e)
        {
            try
            {
                var paymentsForm = new PaymentsForm();
                paymentsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح سندات الصرف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Customers_Click(object sender, EventArgs e)
        {
            try
            {
                var customersForm = new CustomersForm();
                customersForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح إدارة العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddCustomer_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new AddEditCustomerForm();
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم إضافة العميل بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Products_Click(object sender, EventArgs e)
        {
            try
            {
                var productsForm = new ProductsForm();
                productsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح إدارة المنتجات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddProduct_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new AddEditProductForm();
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم إضافة المنتج بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SalesInvoices_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم تطوير فواتير المبيعات قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void PurchaseInvoices_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم تطوير فواتير المشتريات قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AddInvoice_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم تطوير إضافة الفواتير قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void TrialBalance_Click(object sender, EventArgs e)
        {
            try
            {
                var trialBalanceForm = new TrialBalanceForm();
                trialBalanceForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح ميزان المراجعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void IncomeStatement_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم تطوير قائمة الدخل قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BalanceSheet_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم تطوير الميزانية العمومية قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AIAnalysis_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم تطوير تحليل البيانات بالذكاء الاصطناعي قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AIRecommendations_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم تطوير التوصيات الذكية قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Users_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم تطوير إدارة المستخدمين قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void SystemSettings_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم تطوير إعدادات النظام قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void About_Click(object sender, EventArgs e)
        {
            MessageBox.Show("نظام المحاسبة الذكي\nالإصدار 1.0\n\nتم تطويره باستخدام C# و SQLite و Dapper\nمع ميزات الذكاء الاصطناعي",
                "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Backup_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "ملفات قاعدة البيانات (*.db)|*.db|جميع الملفات (*.*)|*.*";
                saveDialog.Title = "حفظ النسخة الاحتياطية";
                saveDialog.FileName = $"backup_{DateTime.Now:yyyyMMdd_HHmmss}.db";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    Utils.BackupHelper.CreateBackup(saveDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Restore_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog openDialog = new OpenFileDialog();
                openDialog.Filter = "ملفات قاعدة البيانات (*.db)|*.db|جميع الملفات (*.*)|*.*";
                openDialog.Title = "اختيار النسخة الاحتياطية للاستعادة";

                if (openDialog.ShowDialog() == DialogResult.OK)
                {
                    if (Utils.BackupHelper.RestoreBackup(openDialog.FileName))
                    {
                        MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح.\nسيتم إعادة تشغيل التطبيق.",
                            "نجحت الاستعادة", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        Application.Restart();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CustomerStatement_Click(object sender, EventArgs e)
        {
            try
            {
                var statementForm = new CustomerStatementForm();
                statementForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح كشف حساب العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ReceiptsReport_Click(object sender, EventArgs e)
        {
            try
            {
                var receiptsForm = new ReceiptsForm();
                receiptsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير سندات القبض: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PaymentsReport_Click(object sender, EventArgs e)
        {
            try
            {
                var paymentsForm = new PaymentsForm();
                paymentsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير سندات الصرف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
