@echo off
chcp 65001 > nul
echo ========================================
echo اختبار الميزات الجديدة
echo ========================================
echo.

echo هذا الاختبار سيتحقق من:
echo 1. وجود نماذج إدارة العملاء والمنتجات
echo 2. صحة ملفات النماذج الجديدة
echo 3. إمكانية بناء النظام مع الميزات الجديدة
echo.

set errors=0
set warnings=0

echo بدء الاختبار...
echo.

echo ========================================
echo 1. فحص نماذج العملاء
echo ========================================

set customer_files=Forms\CustomersForm.cs Forms\AddEditCustomerForm.cs

for %%f in (%customer_files%) do (
    if exist "%%f" (
        echo ✓ %%f موجود
        
        rem فحص محتوى الملف
        findstr /C:"class CustomersForm" "%%f" >nul 2>&1
        if %ERRORLEVEL% EQU 0 (
            echo   └─ يحتوي على class صحيح
        ) else (
            findstr /C:"class AddEditCustomerForm" "%%f" >nul 2>&1
            if %ERRORLEVEL% EQU 0 (
                echo   └─ يحتوي على class صحيح
            ) else (
                echo   └─ ⚠ قد يحتوي على أخطاء في البنية
                set /a warnings+=1
            )
        )
    ) else (
        echo ✗ %%f مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 2. فحص نماذج المنتجات
echo ========================================

set product_files=Forms\ProductsForm.cs Forms\AddEditProductForm.cs

for %%f in (%product_files%) do (
    if exist "%%f" (
        echo ✓ %%f موجود
        
        rem فحص محتوى الملف
        findstr /C:"class ProductsForm" "%%f" >nul 2>&1
        if %ERRORLEVEL% EQU 0 (
            echo   └─ يحتوي على class صحيح
        ) else (
            findstr /C:"class AddEditProductForm" "%%f" >nul 2>&1
            if %ERRORLEVEL% EQU 0 (
                echo   └─ يحتوي على class صحيح
            ) else (
                echo   └─ ⚠ قد يحتوي على أخطاء في البنية
                set /a warnings+=1
            )
        )
    ) else (
        echo ✗ %%f مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 3. فحص النماذج (Models)
echo ========================================

set model_files=Models\Customer.cs Models\Product.cs

for %%f in (%model_files%) do (
    if exist "%%f" (
        echo ✓ %%f موجود
        
        rem فحص خصائص النموذج
        findstr /C:"CurrentBalance" "%%f" >nul 2>&1
        if %ERRORLEVEL% EQU 0 (
            echo   └─ يحتوي على خاصية CurrentBalance
        ) else (
            findstr /C:"UnitPrice" "%%f" >nul 2>&1
            if %ERRORLEVEL% EQU 0 (
                echo   └─ يحتوي على خصائص المنتج
            ) else (
                echo   └─ ⚠ قد تكون الخصائص مفقودة
                set /a warnings+=1
            )
        )
    ) else (
        echo ✗ %%f مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 4. فحص SimpleDatabaseHelper
echo ========================================

if exist "Data\SimpleDatabaseHelper.cs" (
    echo ✓ SimpleDatabaseHelper.cs موجود
    
    rem فحص دوال العملاء والمنتجات
    findstr /C:"GetAllCustomers" "Data\SimpleDatabaseHelper.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo   └─ يحتوي على دوال إدارة العملاء
    ) else (
        echo   └─ ⚠ دوال العملاء قد تكون مفقودة
        set /a warnings+=1
    )
    
    findstr /C:"GetAllProducts" "Data\SimpleDatabaseHelper.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo   └─ يحتوي على دوال إدارة المنتجات
    ) else (
        echo   └─ ⚠ دوال المنتجات قد تكون مفقودة
        set /a warnings+=1
    )
) else (
    echo ✗ SimpleDatabaseHelper.cs مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo 5. اختبار بناء سريع
echo ========================================

where csc >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ مترجم C# متوفر
    
    echo محاولة بناء الميزات الجديدة...
    
    csc /target:library ^
        /reference:System.dll ^
        /reference:System.Core.dll ^
        /reference:System.Drawing.dll ^
        /reference:System.Windows.Forms.dll ^
        /out:TestNewFeatures.dll ^
        Models\Customer.cs ^
        Models\Product.cs ^
        Forms\CustomersForm.cs ^
        Forms\AddEditCustomerForm.cs ^
        Forms\ProductsForm.cs ^
        Forms\AddEditProductForm.cs >nul 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo ✓ نجح بناء الميزات الجديدة
        if exist "TestNewFeatures.dll" del "TestNewFeatures.dll" >nul 2>&1
    ) else (
        echo ✗ فشل بناء الميزات الجديدة
        set /a errors+=1
    )
) else (
    echo ⚠ مترجم C# غير متوفر (استخدم Developer Command Prompt)
)

echo.
echo ========================================
echo 6. فحص ملفات البناء المحدثة
echo ========================================

set build_files=build_simple.bat build_without_sqlite.bat build_complete.bat

for %%f in (%build_files%) do (
    if exist "%%f" (
        echo ✓ %%f موجود
        
        rem فحص إذا كان يتضمن الملفات الجديدة
        findstr /C:"CustomersForm" "%%f" >nul 2>&1
        if %ERRORLEVEL% EQU 0 (
            echo   └─ يتضمن نماذج العملاء
        ) else (
            echo   └─ ⚠ قد لا يتضمن الميزات الجديدة
            set /a warnings+=1
        )
    ) else (
        echo ⚠ %%f مفقود (اختياري)
    )
)

echo.
echo ========================================
echo النتائج النهائية
echo ========================================

echo إجمالي الأخطاء: %errors%
echo إجمالي التحذيرات: %warnings%

if %errors% EQU 0 (
    echo.
    echo 🎉 ممتاز! الميزات الجديدة جاهزة للاستخدام
    echo.
    echo الميزات المتوفرة:
    echo ✓ إدارة العملاء الكاملة
    echo ✓ إدارة المنتجات الكاملة
    echo ✓ نماذج إضافة وتعديل
    echo ✓ بحث وفلترة
    echo ✓ ألوان ذكية للعرض
    echo.
    echo خيارات التشغيل:
    echo 1. build_simple.bat - للاختبار السريع
    echo 2. build_complete.bat - للنسخة الكاملة
    echo 3. build_without_sqlite.bat - لتجنب مشاكل SQLite
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    
    if %warnings% GTR 0 (
        echo.
        echo ⚠ يوجد %warnings% تحذير، لكن النظام يجب أن يعمل بشكل طبيعي
    )
    
) else (
    echo.
    echo ⚠ يوجد %errors% خطأ يجب إصلاحه
    echo.
    echo الإجراءات المقترحة:
    echo 1. تأكد من وجود جميع ملفات الميزات الجديدة
    echo 2. راجع ملف README.md للمتطلبات
    echo 3. استخدم quick_setup.bat للإعداد التلقائي
    echo.
    echo يمكنك تجربة الإصدار المبسط:
    echo build_simple.bat
)

echo.
echo تاريخ الاختبار: %date% %time%

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
