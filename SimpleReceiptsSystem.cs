using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace SimpleReceiptsSystem
{
    // نماذج البيانات البسيطة
    public class Customer
    {
        public int Id { get; set; }
        public string CustomerCode { get; set; }
        public string CustomerName { get; set; }
        public decimal CurrentBalance { get; set; }
    }

    public class Receipt
    {
        public int Id { get; set; }
        public string ReceiptNumber { get; set; }
        public DateTime ReceiptDate { get; set; }
        public int CustomerId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string Status { get; set; }
    }

    // نموذج تسجيل الدخول البسيط
    public class LoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;

        public LoginForm()
        {
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "تسجيل الدخول";
            this.Size = new Size(350, 200);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;

            var lblUsername = new Label();
            lblUsername.Text = "اسم المستخدم:";
            lblUsername.Location = new Point(250, 30);
            lblUsername.Size = new Size(80, 20);

            txtUsername = new TextBox();
            txtUsername.Location = new Point(50, 30);
            txtUsername.Size = new Size(180, 20);
            txtUsername.Text = "admin";

            var lblPassword = new Label();
            lblPassword.Text = "كلمة المرور:";
            lblPassword.Location = new Point(250, 70);
            lblPassword.Size = new Size(80, 20);

            txtPassword = new TextBox();
            txtPassword.Location = new Point(50, 70);
            txtPassword.Size = new Size(180, 20);
            txtPassword.UseSystemPasswordChar = true;
            txtPassword.Text = "admin123";

            btnLogin = new Button();
            btnLogin.Text = "دخول";
            btnLogin.Location = new Point(125, 110);
            btnLogin.Size = new Size(80, 30);
            btnLogin.BackColor = Color.FromArgb(76, 175, 80);
            btnLogin.ForeColor = Color.White;
            btnLogin.FlatStyle = FlatStyle.Flat;
            btnLogin.Click += BtnLogin_Click;

            this.Controls.Add(lblUsername);
            this.Controls.Add(txtUsername);
            this.Controls.Add(lblPassword);
            this.Controls.Add(txtPassword);
            this.Controls.Add(btnLogin);

            this.AcceptButton = btnLogin;
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (txtUsername.Text == "admin" && txtPassword.Text == "admin123")
            {
                this.Hide();
                var mainForm = new MainForm();
                mainForm.ShowDialog();
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ");
            }
        }
    }

    // النموذج الرئيسي البسيط
    public class MainForm : Form
    {
        public MainForm()
        {
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "نظام السندات البسيط";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;

            var menuStrip = new MenuStrip();
            menuStrip.RightToLeft = RightToLeft.Yes;

            var receiptsMenu = new ToolStripMenuItem("السندات");
            receiptsMenu.DropDownItems.Add(new ToolStripMenuItem("سندات القبض", null, OpenReceipts));
            receiptsMenu.DropDownItems.Add(new ToolStripMenuItem("تقرير بسيط", null, ShowSimpleReport));

            var exitMenu = new ToolStripMenuItem("خروج");
            exitMenu.Click += (s, e) => this.Close();

            menuStrip.Items.Add(receiptsMenu);
            menuStrip.Items.Add(exitMenu);

            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);

            // إضافة تسمية ترحيبية
            var lblWelcome = new Label();
            lblWelcome.Text = "مرحباً بك في نظام السندات البسيط\n\nاستخدم القائمة أعلاه للتنقل";
            lblWelcome.Location = new Point(200, 150);
            lblWelcome.Size = new Size(400, 100);
            lblWelcome.TextAlign = ContentAlignment.MiddleCenter;
            lblWelcome.Font = new Font("Tahoma", 12F);
            this.Controls.Add(lblWelcome);
        }

        private void OpenReceipts(object sender, EventArgs e)
        {
            try
            {
                var receiptsForm = new ReceiptsForm();
                receiptsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ");
            }
        }

        private void ShowSimpleReport(object sender, EventArgs e)
        {
            MessageBox.Show("تقرير بسيط:\n\nإجمالي السندات: 3\nإجمالي المبلغ: 8,700 ريال", 
                "تقرير السندات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    // نموذج سندات القبض البسيط
    public class ReceiptsForm : Form
    {
        private DataGridView dgvReceipts;
        private List<Receipt> receipts;
        private List<Customer> customers;

        public ReceiptsForm()
        {
            InitializeForm();
            LoadSampleData();
            DisplayData();
        }

        private void InitializeForm()
        {
            this.Text = "سندات القبض";
            this.Size = new Size(800, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;

            dgvReceipts = new DataGridView();
            dgvReceipts.Location = new Point(20, 20);
            dgvReceipts.Size = new Size(750, 350);
            dgvReceipts.AllowUserToAddRows = false;
            dgvReceipts.ReadOnly = true;
            dgvReceipts.RightToLeft = RightToLeft.Yes;

            // إعداد الأعمدة
            dgvReceipts.Columns.Add("ReceiptNumber", "رقم السند");
            dgvReceipts.Columns.Add("ReceiptDate", "التاريخ");
            dgvReceipts.Columns.Add("CustomerName", "العميل");
            dgvReceipts.Columns.Add("Amount", "المبلغ");
            dgvReceipts.Columns.Add("PaymentMethod", "طريقة الدفع");
            dgvReceipts.Columns.Add("Status", "الحالة");

            dgvReceipts.Columns["ReceiptDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            dgvReceipts.Columns["Amount"].DefaultCellStyle.Format = "N2";

            var btnClose = new Button();
            btnClose.Text = "إغلاق";
            btnClose.Location = new Point(20, 390);
            btnClose.Size = new Size(100, 30);
            btnClose.Click += (s, e) => this.Close();

            var btnAdd = new Button();
            btnAdd.Text = "إضافة سند";
            btnAdd.Location = new Point(670, 390);
            btnAdd.Size = new Size(100, 30);
            btnAdd.BackColor = Color.FromArgb(76, 175, 80);
            btnAdd.ForeColor = Color.White;
            btnAdd.FlatStyle = FlatStyle.Flat;
            btnAdd.Click += BtnAdd_Click;

            this.Controls.Add(dgvReceipts);
            this.Controls.Add(btnClose);
            this.Controls.Add(btnAdd);
        }

        private void LoadSampleData()
        {
            customers = new List<Customer>
            {
                new Customer { Id = 1, CustomerCode = "C001", CustomerName = "أحمد محمد علي", CurrentBalance = 5000 },
                new Customer { Id = 2, CustomerCode = "C002", CustomerName = "فاطمة عبدالله", CurrentBalance = -2500 },
                new Customer { Id = 3, CustomerCode = "C003", CustomerName = "محمد سعد الدين", CurrentBalance = 0 }
            };

            receipts = new List<Receipt>
            {
                new Receipt { Id = 1, ReceiptNumber = "R001", ReceiptDate = DateTime.Now.AddDays(-5), CustomerId = 1, Amount = 5000, PaymentMethod = "نقدي", Status = "مؤكد" },
                new Receipt { Id = 2, ReceiptNumber = "R002", ReceiptDate = DateTime.Now.AddDays(-3), CustomerId = 2, Amount = 2500, PaymentMethod = "شيك", Status = "مؤكد" },
                new Receipt { Id = 3, ReceiptNumber = "R003", ReceiptDate = DateTime.Now.AddDays(-1), CustomerId = 3, Amount = 1200, PaymentMethod = "تحويل بنكي", Status = "مسودة" }
            };
        }

        private void DisplayData()
        {
            dgvReceipts.Rows.Clear();
            
            foreach (var receipt in receipts)
            {
                var customer = customers.FirstOrDefault(c => c.Id == receipt.CustomerId);
                var customerName = customer?.CustomerName ?? "غير محدد";

                var rowIndex = dgvReceipts.Rows.Add();
                var row = dgvReceipts.Rows[rowIndex];

                row.Cells["ReceiptNumber"].Value = receipt.ReceiptNumber;
                row.Cells["ReceiptDate"].Value = receipt.ReceiptDate;
                row.Cells["CustomerName"].Value = customerName;
                row.Cells["Amount"].Value = receipt.Amount;
                row.Cells["PaymentMethod"].Value = receipt.PaymentMethod;
                row.Cells["Status"].Value = receipt.Status;

                // تلوين حسب الحالة
                if (receipt.Status == "مؤكد")
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                else if (receipt.Status == "مسودة")
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new AddReceiptForm(customers);
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                var newReceipt = addForm.NewReceipt;
                newReceipt.Id = receipts.Count + 1;
                newReceipt.ReceiptNumber = $"R{newReceipt.Id:000}";
                receipts.Add(newReceipt);
                DisplayData();
            }
        }
    }

    // نموذج إضافة سند بسيط
    public class AddReceiptForm : Form
    {
        private ComboBox cmbCustomer;
        private TextBox txtAmount;
        private ComboBox cmbPaymentMethod;
        private List<Customer> customers;

        public Receipt NewReceipt { get; private set; }

        public AddReceiptForm(List<Customer> customersList)
        {
            customers = customersList;
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "إضافة سند قبض";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.RightToLeft = RightToLeft.Yes;

            var lblCustomer = new Label();
            lblCustomer.Text = "العميل:";
            lblCustomer.Location = new Point(320, 30);
            lblCustomer.Size = new Size(60, 20);

            cmbCustomer = new ComboBox();
            cmbCustomer.Location = new Point(50, 30);
            cmbCustomer.Size = new Size(250, 20);
            cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList;
            foreach (var customer in customers)
                cmbCustomer.Items.Add($"{customer.CustomerCode} - {customer.CustomerName}");

            var lblAmount = new Label();
            lblAmount.Text = "المبلغ:";
            lblAmount.Location = new Point(320, 70);
            lblAmount.Size = new Size(60, 20);

            txtAmount = new TextBox();
            txtAmount.Location = new Point(50, 70);
            txtAmount.Size = new Size(150, 20);

            var lblPaymentMethod = new Label();
            lblPaymentMethod.Text = "طريقة الدفع:";
            lblPaymentMethod.Location = new Point(320, 110);
            lblPaymentMethod.Size = new Size(60, 20);

            cmbPaymentMethod = new ComboBox();
            cmbPaymentMethod.Location = new Point(50, 110);
            cmbPaymentMethod.Size = new Size(150, 20);
            cmbPaymentMethod.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbPaymentMethod.Items.AddRange(new string[] { "نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان" });

            var btnSave = new Button();
            btnSave.Text = "حفظ";
            btnSave.Location = new Point(220, 180);
            btnSave.Size = new Size(80, 30);
            btnSave.BackColor = Color.FromArgb(76, 175, 80);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.Click += BtnSave_Click;

            var btnCancel = new Button();
            btnCancel.Text = "إلغاء";
            btnCancel.Location = new Point(120, 180);
            btnCancel.Size = new Size(80, 30);
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            this.Controls.Add(lblCustomer);
            this.Controls.Add(cmbCustomer);
            this.Controls.Add(lblAmount);
            this.Controls.Add(txtAmount);
            this.Controls.Add(lblPaymentMethod);
            this.Controls.Add(cmbPaymentMethod);
            this.Controls.Add(btnSave);
            this.Controls.Add(btnCancel);
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (cmbCustomer.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار العميل");
                return;
            }

            if (!decimal.TryParse(txtAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح");
                return;
            }

            if (cmbPaymentMethod.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار طريقة الدفع");
                return;
            }

            NewReceipt = new Receipt
            {
                ReceiptDate = DateTime.Now,
                CustomerId = customers[cmbCustomer.SelectedIndex].Id,
                Amount = amount,
                PaymentMethod = cmbPaymentMethod.SelectedItem.ToString(),
                Status = "مسودة"
            };

            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }

    // نقطة دخول التطبيق
    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new LoginForm());
        }
    }
}
