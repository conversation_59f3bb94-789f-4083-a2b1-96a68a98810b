using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Dapper;
using AccountingSystem.Data;
using AccountingSystem.Models;
using AccountingSystem.Services;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج القيود اليومية
    /// </summary>
    public partial class JournalEntryForm : Form
    {
        private Panel panelHeader;
        private Label lblEntryNumber;
        private Label lblEntryDate;
        private Label lblDescription;
        private Label lblReference;
        private TextBox txtEntryNumber;
        private DateTimePicker dtpEntryDate;
        private TextBox txtDescription;
        private TextBox txtReference;
        
        private DataGridView dgvDetails;
        private Panel panelSummary;
        private Label lblTotalDebit;
        private Label lblTotalCredit;
        private Label lblDifference;
        private TextBox txtTotalDebit;
        private TextBox txtTotalCredit;
        private TextBox txtDifference;
        
        private Panel panelButtons;
        private Button btnSave;
        private Button btnPost;
        private Button btnCancel;
        private Button btnAddLine;
        private Button btnDeleteLine;
        
        private JournalEntry _journalEntry;
        private bool _isEditMode;
        private List<Account> _accounts;
        
        public JournalEntryForm(JournalEntry journalEntry = null)
        {
            _journalEntry = journalEntry ?? new JournalEntry();
            _isEditMode = journalEntry != null;
            
            InitializeComponent();
            LoadAccounts();
            SetupDataGridView();
            
            if (_isEditMode)
            {
                LoadJournalEntryData();
                this.Text = "تعديل قيد يومي";
            }
            else
            {
                this.Text = "إضافة قيد يومي جديد";
                GenerateEntryNumber();
                dtpEntryDate.Value = DateTime.Now;
            }
            
            CalculateTotals();
        }
        
        private void InitializeComponent()
        {
            this.panelHeader = new Panel();
            this.lblEntryNumber = new Label();
            this.lblEntryDate = new Label();
            this.lblDescription = new Label();
            this.lblReference = new Label();
            this.txtEntryNumber = new TextBox();
            this.dtpEntryDate = new DateTimePicker();
            this.txtDescription = new TextBox();
            this.txtReference = new TextBox();
            
            this.dgvDetails = new DataGridView();
            
            this.panelSummary = new Panel();
            this.lblTotalDebit = new Label();
            this.lblTotalCredit = new Label();
            this.lblDifference = new Label();
            this.txtTotalDebit = new TextBox();
            this.txtTotalCredit = new TextBox();
            this.txtDifference = new TextBox();
            
            this.panelButtons = new Panel();
            this.btnSave = new Button();
            this.btnPost = new Button();
            this.btnCancel = new Button();
            this.btnAddLine = new Button();
            this.btnDeleteLine = new Button();
            
            this.SuspendLayout();
            
            // Form
            this.Text = "القيود اليومية";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Header Panel
            this.panelHeader.Location = new Point(12, 12);
            this.panelHeader.Size = new Size(976, 120);
            this.panelHeader.BorderStyle = BorderStyle.FixedSingle;
            
            // Header Controls
            this.lblEntryNumber.Text = "رقم القيد:";
            this.lblEntryNumber.Location = new Point(850, 20);
            this.lblEntryNumber.Size = new Size(80, 23);
            this.lblEntryNumber.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtEntryNumber.Location = new Point(650, 20);
            this.txtEntryNumber.Size = new Size(180, 23);
            this.txtEntryNumber.ReadOnly = true;
            
            this.lblEntryDate.Text = "التاريخ:";
            this.lblEntryDate.Location = new Point(550, 20);
            this.lblEntryDate.Size = new Size(80, 23);
            this.lblEntryDate.TextAlign = ContentAlignment.MiddleRight;
            
            this.dtpEntryDate.Location = new Point(350, 20);
            this.dtpEntryDate.Size = new Size(180, 23);
            this.dtpEntryDate.Format = DateTimePickerFormat.Short;
            
            this.lblDescription.Text = "الوصف:";
            this.lblDescription.Location = new Point(850, 60);
            this.lblDescription.Size = new Size(80, 23);
            this.lblDescription.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtDescription.Location = new Point(450, 60);
            this.txtDescription.Size = new Size(380, 23);
            
            this.lblReference.Text = "المرجع:";
            this.lblReference.Location = new Point(350, 60);
            this.lblReference.Size = new Size(80, 23);
            this.lblReference.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtReference.Location = new Point(150, 60);
            this.txtReference.Size = new Size(180, 23);
            
            // Add controls to header panel
            this.panelHeader.Controls.AddRange(new Control[] {
                this.lblEntryNumber, this.txtEntryNumber,
                this.lblEntryDate, this.dtpEntryDate,
                this.lblDescription, this.txtDescription,
                this.lblReference, this.txtReference
            });
            
            // DataGridView
            this.dgvDetails.Location = new Point(12, 150);
            this.dgvDetails.Size = new Size(976, 350);
            this.dgvDetails.AllowUserToAddRows = true;
            this.dgvDetails.AllowUserToDeleteRows = true;
            this.dgvDetails.RightToLeft = RightToLeft.Yes;
            this.dgvDetails.Font = new Font("Tahoma", 10F);
            this.dgvDetails.CellValueChanged += DgvDetails_CellValueChanged;
            this.dgvDetails.UserDeletingRow += DgvDetails_UserDeletingRow;
            
            // Summary Panel
            this.panelSummary.Location = new Point(12, 520);
            this.panelSummary.Size = new Size(976, 60);
            this.panelSummary.BorderStyle = BorderStyle.FixedSingle;
            
            this.lblTotalDebit.Text = "إجمالي المدين:";
            this.lblTotalDebit.Location = new Point(850, 20);
            this.lblTotalDebit.Size = new Size(100, 23);
            this.lblTotalDebit.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtTotalDebit.Location = new Point(720, 20);
            this.txtTotalDebit.Size = new Size(120, 23);
            this.txtTotalDebit.ReadOnly = true;
            this.txtTotalDebit.BackColor = Color.LightGray;
            this.txtTotalDebit.TextAlign = HorizontalAlignment.Right;
            
            this.lblTotalCredit.Text = "إجمالي الدائن:";
            this.lblTotalCredit.Location = new Point(580, 20);
            this.lblTotalCredit.Size = new Size(100, 23);
            this.lblTotalCredit.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtTotalCredit.Location = new Point(450, 20);
            this.txtTotalCredit.Size = new Size(120, 23);
            this.txtTotalCredit.ReadOnly = true;
            this.txtTotalCredit.BackColor = Color.LightGray;
            this.txtTotalCredit.TextAlign = HorizontalAlignment.Right;
            
            this.lblDifference.Text = "الفرق:";
            this.lblDifference.Location = new Point(380, 20);
            this.lblDifference.Size = new Size(60, 23);
            this.lblDifference.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtDifference.Location = new Point(250, 20);
            this.txtDifference.Size = new Size(120, 23);
            this.txtDifference.ReadOnly = true;
            this.txtDifference.BackColor = Color.LightYellow;
            this.txtDifference.TextAlign = HorizontalAlignment.Right;
            
            // Add controls to summary panel
            this.panelSummary.Controls.AddRange(new Control[] {
                this.lblTotalDebit, this.txtTotalDebit,
                this.lblTotalCredit, this.txtTotalCredit,
                this.lblDifference, this.txtDifference
            });
            
            // Buttons Panel
            this.panelButtons.Location = new Point(12, 600);
            this.panelButtons.Size = new Size(976, 50);
            
            this.btnSave.Text = "حفظ";
            this.btnSave.Location = new Point(850, 10);
            this.btnSave.Size = new Size(100, 35);
            this.btnSave.BackColor = Color.FromArgb(25, 118, 210);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Click += BtnSave_Click;
            
            this.btnPost.Text = "ترحيل";
            this.btnPost.Location = new Point(730, 10);
            this.btnPost.Size = new Size(100, 35);
            this.btnPost.BackColor = Color.FromArgb(76, 175, 80);
            this.btnPost.ForeColor = Color.White;
            this.btnPost.FlatStyle = FlatStyle.Flat;
            this.btnPost.Click += BtnPost_Click;
            
            this.btnAddLine.Text = "إضافة سطر";
            this.btnAddLine.Location = new Point(610, 10);
            this.btnAddLine.Size = new Size(100, 35);
            this.btnAddLine.BackColor = Color.FromArgb(255, 152, 0);
            this.btnAddLine.ForeColor = Color.White;
            this.btnAddLine.FlatStyle = FlatStyle.Flat;
            this.btnAddLine.Click += BtnAddLine_Click;
            
            this.btnDeleteLine.Text = "حذف سطر";
            this.btnDeleteLine.Location = new Point(490, 10);
            this.btnDeleteLine.Size = new Size(100, 35);
            this.btnDeleteLine.BackColor = Color.FromArgb(244, 67, 54);
            this.btnDeleteLine.ForeColor = Color.White;
            this.btnDeleteLine.FlatStyle = FlatStyle.Flat;
            this.btnDeleteLine.Click += BtnDeleteLine_Click;
            
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.Location = new Point(20, 10);
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.BackColor = Color.Gray;
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Click += BtnCancel_Click;
            
            // Add controls to buttons panel
            this.panelButtons.Controls.AddRange(new Control[] {
                this.btnSave, this.btnPost, this.btnAddLine, this.btnDeleteLine, this.btnCancel
            });
            
            // Add controls to form
            this.Controls.Add(this.panelHeader);
            this.Controls.Add(this.dgvDetails);
            this.Controls.Add(this.panelSummary);
            this.Controls.Add(this.panelButtons);
            
            this.ResumeLayout(false);
        }
        
        private void LoadAccounts()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    _accounts = connection.Query<Account>(@"
                        SELECT * FROM Accounts 
                        WHERE IsActive = 1 AND IsParent = 0 
                        ORDER BY AccountCode").ToList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الحسابات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                _accounts = new List<Account>();
            }
        }
        
        private void SetupDataGridView()
        {
            dgvDetails.Columns.Clear();
            
            // Account Column (ComboBox)
            var accountColumn = new DataGridViewComboBoxColumn();
            accountColumn.Name = "AccountId";
            accountColumn.HeaderText = "الحساب";
            accountColumn.Width = 300;
            accountColumn.DataSource = _accounts;
            accountColumn.DisplayMember = "AccountName";
            accountColumn.ValueMember = "Id";
            dgvDetails.Columns.Add(accountColumn);
            
            // Description Column
            var descriptionColumn = new DataGridViewTextBoxColumn();
            descriptionColumn.Name = "Description";
            descriptionColumn.HeaderText = "البيان";
            descriptionColumn.Width = 200;
            dgvDetails.Columns.Add(descriptionColumn);
            
            // Debit Amount Column
            var debitColumn = new DataGridViewTextBoxColumn();
            debitColumn.Name = "DebitAmount";
            debitColumn.HeaderText = "مدين";
            debitColumn.Width = 120;
            debitColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            debitColumn.DefaultCellStyle.Format = "N2";
            dgvDetails.Columns.Add(debitColumn);
            
            // Credit Amount Column
            var creditColumn = new DataGridViewTextBoxColumn();
            creditColumn.Name = "CreditAmount";
            creditColumn.HeaderText = "دائن";
            creditColumn.Width = 120;
            creditColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            creditColumn.DefaultCellStyle.Format = "N2";
            dgvDetails.Columns.Add(creditColumn);
            
            // Reference Column
            var referenceColumn = new DataGridViewTextBoxColumn();
            referenceColumn.Name = "Reference";
            referenceColumn.HeaderText = "المرجع";
            referenceColumn.Width = 150;
            dgvDetails.Columns.Add(referenceColumn);
        }
        
        private void GenerateEntryNumber()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    var lastEntry = connection.QueryFirstOrDefault<string>(@"
                        SELECT EntryNumber 
                        FROM JournalEntries 
                        ORDER BY Id DESC 
                        LIMIT 1");
                    
                    int nextNumber = 1;
                    if (!string.IsNullOrEmpty(lastEntry))
                    {
                        // استخراج الرقم من رقم القيد
                        var parts = lastEntry.Split('-');
                        if (parts.Length > 1 && int.TryParse(parts[1], out int number))
                        {
                            nextNumber = number + 1;
                        }
                    }
                    
                    txtEntryNumber.Text = $"JE-{nextNumber:D6}";
                }
            }
            catch (Exception)
            {
                txtEntryNumber.Text = "JE-000001";
            }
        }
        
        private void LoadJournalEntryData()
        {
            if (_journalEntry != null)
            {
                txtEntryNumber.Text = _journalEntry.EntryNumber;
                dtpEntryDate.Value = _journalEntry.EntryDate;
                txtDescription.Text = _journalEntry.Description;
                txtReference.Text = _journalEntry.Reference;
                
                // تحميل تفاصيل القيد
                if (_journalEntry.Details != null)
                {
                    foreach (var detail in _journalEntry.Details)
                    {
                        var rowIndex = dgvDetails.Rows.Add();
                        var row = dgvDetails.Rows[rowIndex];
                        
                        row.Cells["AccountId"].Value = detail.AccountId;
                        row.Cells["Description"].Value = detail.Description;
                        row.Cells["DebitAmount"].Value = detail.DebitAmount > 0 ? detail.DebitAmount : (object)DBNull.Value;
                        row.Cells["CreditAmount"].Value = detail.CreditAmount > 0 ? detail.CreditAmount : (object)DBNull.Value;
                        row.Cells["Reference"].Value = detail.Reference;
                    }
                }
            }
        }
        
        private void DgvDetails_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                CalculateTotals();
            }
        }
        
        private void DgvDetails_UserDeletingRow(object sender, DataGridViewRowCancelEventArgs e)
        {
            // السماح بحذف الصف وإعادة حساب الإجماليات
            this.BeginInvoke(new Action(CalculateTotals));
        }
        
        private void CalculateTotals()
        {
            decimal totalDebit = 0;
            decimal totalCredit = 0;
            
            foreach (DataGridViewRow row in dgvDetails.Rows)
            {
                if (!row.IsNewRow)
                {
                    if (row.Cells["DebitAmount"].Value != null && 
                        decimal.TryParse(row.Cells["DebitAmount"].Value.ToString(), out decimal debit))
                    {
                        totalDebit += debit;
                    }
                    
                    if (row.Cells["CreditAmount"].Value != null && 
                        decimal.TryParse(row.Cells["CreditAmount"].Value.ToString(), out decimal credit))
                    {
                        totalCredit += credit;
                    }
                }
            }
            
            txtTotalDebit.Text = totalDebit.ToString("N2");
            txtTotalCredit.Text = totalCredit.ToString("N2");
            
            decimal difference = totalDebit - totalCredit;
            txtDifference.Text = difference.ToString("N2");
            
            // تلوين الفرق
            if (difference == 0)
            {
                txtDifference.BackColor = Color.LightGreen;
            }
            else
            {
                txtDifference.BackColor = Color.LightCoral;
            }
        }
        
        private void BtnAddLine_Click(object sender, EventArgs e)
        {
            dgvDetails.Rows.Add();
        }
        
        private void BtnDeleteLine_Click(object sender, EventArgs e)
        {
            if (dgvDetails.SelectedRows.Count > 0)
            {
                foreach (DataGridViewRow row in dgvDetails.SelectedRows)
                {
                    if (!row.IsNewRow)
                    {
                        dgvDetails.Rows.Remove(row);
                    }
                }
                CalculateTotals();
            }
        }
        
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateEntry())
            {
                SaveJournalEntry(false);
            }
        }
        
        private void BtnPost_Click(object sender, EventArgs e)
        {
            if (ValidateEntry())
            {
                var result = MessageBox.Show("هل أنت متأكد من ترحيل القيد؟\nلن يمكن تعديله بعد الترحيل.", 
                    "تأكيد الترحيل", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    SaveJournalEntry(true);
                }
            }
        }
        
        private bool ValidateEntry()
        {
            if (string.IsNullOrWhiteSpace(txtDescription.Text))
            {
                MessageBox.Show("يرجى إدخال وصف القيد", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDescription.Focus();
                return false;
            }
            
            if (dgvDetails.Rows.Count <= 1) // صف واحد فقط (الصف الجديد)
            {
                MessageBox.Show("يجب إدخال تفاصيل القيد", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
            
            decimal totalDebit = 0;
            decimal totalCredit = 0;
            
            foreach (DataGridViewRow row in dgvDetails.Rows)
            {
                if (!row.IsNewRow)
                {
                    if (row.Cells["AccountId"].Value == null)
                    {
                        MessageBox.Show("يرجى اختيار الحساب في جميع الأسطر", "تنبيه", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }
                    
                    decimal debit = 0;
                    decimal credit = 0;
                    
                    if (row.Cells["DebitAmount"].Value != null)
                        decimal.TryParse(row.Cells["DebitAmount"].Value.ToString(), out debit);
                    
                    if (row.Cells["CreditAmount"].Value != null)
                        decimal.TryParse(row.Cells["CreditAmount"].Value.ToString(), out credit);
                    
                    if (debit == 0 && credit == 0)
                    {
                        MessageBox.Show("يجب إدخال مبلغ في المدين أو الدائن", "تنبيه", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }
                    
                    if (debit > 0 && credit > 0)
                    {
                        MessageBox.Show("لا يمكن إدخال مبلغ في المدين والدائن معاً في نفس السطر", "تنبيه", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }
                    
                    totalDebit += debit;
                    totalCredit += credit;
                }
            }
            
            if (Math.Abs(totalDebit - totalCredit) > 0.01m)
            {
                MessageBox.Show("القيد غير متوازن. يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
            
            return true;
        }
        
        private void SaveJournalEntry(bool isPosted)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            int journalEntryId;
                            
                            if (_isEditMode)
                            {
                                // تحديث القيد الرئيسي
                                connection.Execute(@"
                                    UPDATE JournalEntries SET 
                                        EntryDate = @EntryDate,
                                        Description = @Description,
                                        Reference = @Reference,
                                        TotalDebit = @TotalDebit,
                                        TotalCredit = @TotalCredit,
                                        Status = @Status,
                                        IsPosted = @IsPosted,
                                        PostedDate = @PostedDate,
                                        PostedBy = @PostedBy,
                                        ModifiedDate = @ModifiedDate,
                                        ModifiedBy = @ModifiedBy
                                    WHERE Id = @Id",
                                    new
                                    {
                                        Id = _journalEntry.Id,
                                        EntryDate = dtpEntryDate.Value.ToString("yyyy-MM-dd"),
                                        Description = txtDescription.Text.Trim(),
                                        Reference = txtReference.Text.Trim(),
                                        TotalDebit = decimal.Parse(txtTotalDebit.Text),
                                        TotalCredit = decimal.Parse(txtTotalCredit.Text),
                                        Status = isPosted ? "مؤكد" : "مسودة",
                                        IsPosted = isPosted ? 1 : 0,
                                        PostedDate = isPosted ? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") : null,
                                        PostedBy = isPosted ? AuthenticationService.CurrentUser?.Username : null,
                                        ModifiedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                        ModifiedBy = AuthenticationService.CurrentUser?.Username
                                    }, transaction);
                                
                                // حذف التفاصيل القديمة
                                connection.Execute("DELETE FROM JournalEntryDetails WHERE JournalEntryId = @JournalEntryId", 
                                    new { JournalEntryId = _journalEntry.Id }, transaction);
                                
                                journalEntryId = _journalEntry.Id;
                            }
                            else
                            {
                                // إضافة قيد جديد
                                journalEntryId = connection.QuerySingle<int>(@"
                                    INSERT INTO JournalEntries (
                                        EntryNumber, EntryDate, Description, Reference,
                                        TotalDebit, TotalCredit, Status, IsPosted,
                                        PostedDate, PostedBy, CreatedDate, CreatedBy
                                    ) VALUES (
                                        @EntryNumber, @EntryDate, @Description, @Reference,
                                        @TotalDebit, @TotalCredit, @Status, @IsPosted,
                                        @PostedDate, @PostedBy, @CreatedDate, @CreatedBy
                                    );
                                    SELECT last_insert_rowid();",
                                    new
                                    {
                                        EntryNumber = txtEntryNumber.Text,
                                        EntryDate = dtpEntryDate.Value.ToString("yyyy-MM-dd"),
                                        Description = txtDescription.Text.Trim(),
                                        Reference = txtReference.Text.Trim(),
                                        TotalDebit = decimal.Parse(txtTotalDebit.Text),
                                        TotalCredit = decimal.Parse(txtTotalCredit.Text),
                                        Status = isPosted ? "مؤكد" : "مسودة",
                                        IsPosted = isPosted ? 1 : 0,
                                        PostedDate = isPosted ? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") : null,
                                        PostedBy = isPosted ? AuthenticationService.CurrentUser?.Username : null,
                                        CreatedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                        CreatedBy = AuthenticationService.CurrentUser?.Username
                                    }, transaction);
                            }
                            
                            // إضافة التفاصيل الجديدة
                            int lineNumber = 1;
                            foreach (DataGridViewRow row in dgvDetails.Rows)
                            {
                                if (!row.IsNewRow && row.Cells["AccountId"].Value != null)
                                {
                                    decimal debit = 0;
                                    decimal credit = 0;
                                    
                                    if (row.Cells["DebitAmount"].Value != null)
                                        decimal.TryParse(row.Cells["DebitAmount"].Value.ToString(), out debit);
                                    
                                    if (row.Cells["CreditAmount"].Value != null)
                                        decimal.TryParse(row.Cells["CreditAmount"].Value.ToString(), out credit);
                                    
                                    connection.Execute(@"
                                        INSERT INTO JournalEntryDetails (
                                            JournalEntryId, AccountId, DebitAmount, CreditAmount,
                                            Description, Reference, LineNumber
                                        ) VALUES (
                                            @JournalEntryId, @AccountId, @DebitAmount, @CreditAmount,
                                            @Description, @Reference, @LineNumber
                                        )",
                                        new
                                        {
                                            JournalEntryId = journalEntryId,
                                            AccountId = row.Cells["AccountId"].Value,
                                            DebitAmount = debit,
                                            CreditAmount = credit,
                                            Description = row.Cells["Description"].Value?.ToString() ?? "",
                                            Reference = row.Cells["Reference"].Value?.ToString() ?? "",
                                            LineNumber = lineNumber++
                                        }, transaction);
                                }
                            }
                            
                            transaction.Commit();
                            
                            string message = isPosted ? "تم حفظ وترحيل القيد بنجاح" : "تم حفظ القيد بنجاح";
                            MessageBox.Show(message, "نجح الحفظ", 
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            
                            this.DialogResult = DialogResult.OK;
                            this.Close();
                        }
                        catch (Exception)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ القيد: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
