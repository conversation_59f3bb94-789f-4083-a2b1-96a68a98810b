@echo off
chcp 65001 > nul
echo ========================================
echo بناء نظام السندات - ملف واحد
echo ========================================
echo.

echo هذا البناء يستخدم ملف واحد يحتوي على جميع النماذج
echo لتجنب مشاكل المراجع والـ namespace
echo.

rem التحقق من وجود مترجم C#
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ مترجم C# غير متوفر
    echo.
    echo الحلول:
    echo 1. افتح Developer Command Prompt for Visual Studio
    echo 2. أو ثبت .NET Framework SDK
    echo 3. أو استخدم Visual Studio لبناء المشروع
    echo.
    goto :end
)

echo ✓ مترجم C# متوفر
echo.

echo فحص الملف المطلوب...
if not exist "ReceiptsSystemAll.cs" (
    echo ✗ ملف ReceiptsSystemAll.cs مفقود
    echo يرجى التأكد من وجود الملف
    goto :end
)

echo ✓ ملف ReceiptsSystemAll.cs موجود
echo.

echo بناء النظام...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /out:ReceiptsSystemSingle.exe ^
    ReceiptsSystemAll.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء النظام بنجاح!
    echo.
    echo تم إنشاء: ReceiptsSystemSingle.exe
    echo.
    
    echo إنشاء ملفات الإعداد...
    
    rem إنشاء ملف App.config
    echo ^<?xml version="1.0" encoding="utf-8"?^> > ReceiptsSystemSingle.exe.config
    echo ^<configuration^> >> ReceiptsSystemSingle.exe.config
    echo   ^<startup^> >> ReceiptsSystemSingle.exe.config
    echo     ^<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/^> >> ReceiptsSystemSingle.exe.config
    echo   ^</startup^> >> ReceiptsSystemSingle.exe.config
    echo ^</configuration^> >> ReceiptsSystemSingle.exe.config
    
    echo ✓ تم إنشاء ملف الإعداد
    echo.
    
    set /p run="هل تريد تشغيل النظام الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام...
        start "" "ReceiptsSystemSingle.exe"
        echo.
        echo ✓ تم تشغيل النظام بنجاح!
    )
    
    echo.
    echo ========================================
    echo معلومات النظام
    echo ========================================
    echo.
    echo ملف التشغيل: ReceiptsSystemSingle.exe
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo الميزات المتوفرة:
    echo ✓ تسجيل دخول آمن
    echo ✓ إدارة سندات القبض
    echo ✓ إدارة سندات الصرف
    echo ✓ كشف حساب العميل التفصيلي
    echo ✓ بيانات تجريبية غنية
    echo ✓ واجهة عربية كاملة
    echo ✓ تلوين البيانات حسب الحالة
    echo.
    echo القوائم المتوفرة:
    echo • ملف ^> تسجيل خروج / خروج
    echo • السندات ^> سندات القبض
    echo • السندات ^> سندات الصرف
    echo • التقارير ^> كشف حساب العميل
    echo • مساعدة ^> حول البرنامج
    echo.
    echo البيانات التجريبية:
    echo • 3 عملاء بأرصدة مختلفة
    echo • 3 سندات قبض بحالات مختلفة
    echo • 3 سندات صرف بأنواع مختلفة
    echo • 3 فواتير بحالات دفع مختلفة
    echo • 3 مدفوعات بطرق دفع مختلفة
    
) else (
    echo.
    echo ✗ فشل في بناء النظام
    echo.
    echo الأخطاء المحتملة:
    echo 1. أخطاء في الكود المصدري
    echo 2. مراجع مفقودة
    echo 3. مشاكل في مترجم C#
    echo.
    echo للتشخيص:
    echo 1. افتح ReceiptsSystemAll.cs وتحقق من الأخطاء
    echo 2. تأكد من وجود جميع المراجع المطلوبة
    echo 3. استخدم Visual Studio للحصول على تفاصيل الأخطاء
    echo.
    echo بدائل أخرى:
    echo • استخدم Visual Studio لبناء المشروع
    echo • جرب build_step_by_step.bat
    echo • راجع ملف README_COMPLETE_SYSTEM.md
)

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
