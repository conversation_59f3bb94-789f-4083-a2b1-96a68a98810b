using System;
using System.Windows.Forms;
using AccountingSystem.Data;
using AccountingSystem.Forms;

namespace AccountingSystem
{
    internal static class Program
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                // تهيئة قاعدة البيانات
                DatabaseHelper.InitializeDatabase();

                // إنشاء نسخة احتياطية تلقائية
                Utils.BackupHelper.CreateAutoBackup();

                // تشغيل نموذج تسجيل الدخول
                Application.Run(new LoginForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
