using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Models;

namespace IntegratedInvoiceSystem.Services
{
    public static class DataService
    {
        // المجموعات الرئيسية للبيانات
        public static List<Customer> Customers { get; set; }
        public static List<Product> Products { get; set; }
        public static List<Invoice> Invoices { get; set; }
        public static List<Receipt> Receipts { get; set; }
        public static List<User> Users { get; set; }
        public static List<Operation> Operations { get; set; }
        public static List<Notification> Notifications { get; set; }
        public static Company CompanyInfo { get; set; }
        public static SystemSettings Settings { get; set; }

        static DataService()
        {
            InitializeData();
        }

        private static void InitializeData()
        {
            LoadSampleData();
            Operations = new List<Operation>();
            Notifications = new List<Notification>();
            Settings = new SystemSettings();
        }

        private static void LoadSampleData()
        {
            // معلومات الشركة
            CompanyInfo = new Company();

            // المستخدمين
            Users = new List<User>
            {
                new User
                {
                    Id = 1, Username = "admin", Password = "admin123", FullName = "مدير النظام",
                    Email = "<EMAIL>", Role = "مدير", IsActive = true,
                    LastLogin = DateTime.Now, CreatedDate = DateTime.Now.AddMonths(-6),
                    CreatedBy = "System",
                    Permissions = new List<string>
                    {
                        "إدارة الفواتير", "إدارة العملاء", "إدارة المنتجات",
                        "التقارير", "الإعدادات", "إدارة المستخدمين"
                    }
                },
                new User
                {
                    Id = 2, Username = "user", Password = "user123", FullName = "موظف المبيعات",
                    Email = "<EMAIL>", Role = "موظف", IsActive = true,
                    LastLogin = DateTime.Now.AddDays(-1), CreatedDate = DateTime.Now.AddMonths(-3),
                    CreatedBy = "admin",
                    Permissions = new List<string> { "إدارة الفواتير", "إدارة العملاء" }
                },
                new User
                {
                    Id = 3, Username = "accountant", Password = "acc123", FullName = "أحمد المحاسب",
                    Email = "<EMAIL>", Role = "محاسب", IsActive = true,
                    LastLogin = DateTime.Now.AddHours(-5), CreatedDate = DateTime.Now.AddMonths(-2),
                    CreatedBy = "admin",
                    Permissions = new List<string> { "إدارة الفواتير", "التقارير" }
                }
            };

            // العملاء
            Customers = new List<Customer>
            {
                new Customer
                {
                    Id = 1, CustomerCode = "C001", CustomerName = "أحمد محمد علي",
                    Phone = "**********", Email = "<EMAIL>",
                    Address = "الرياض، حي النخيل، شارع الملك فهد", CurrentBalance = 15000,
                    IsActive = true, CreatedDate = DateTime.Now.AddDays(-30),
                    TaxNumber = "***************", ContactPerson = "أحمد محمد"
                },
                new Customer
                {
                    Id = 2, CustomerCode = "C002", CustomerName = "شركة الأعمال المتقدمة",
                    Phone = "0507654321", Email = "<EMAIL>",
                    Address = "جدة، حي الصفا، طريق الملك عبدالعزيز", CurrentBalance = -2500,
                    IsActive = true, CreatedDate = DateTime.Now.AddDays(-25),
                    TaxNumber = "234567890123456", ContactPerson = "فاطمة عبدالله"
                },
                new Customer
                {
                    Id = 3, CustomerCode = "C003", CustomerName = "مؤسسة التقنية الحديثة",
                    Phone = "0551122334", Email = "<EMAIL>",
                    Address = "الدمام، حي الفيصلية، شارع الأمير محمد", CurrentBalance = 8000,
                    IsActive = true, CreatedDate = DateTime.Now.AddDays(-20),
                    TaxNumber = "345678901234567", ContactPerson = "محمد سعد الدين"
                },
                new Customer
                {
                    Id = 4, CustomerCode = "C004", CustomerName = "مكتب الاستشارات الإدارية",
                    Phone = "0554433221", Email = "<EMAIL>",
                    Address = "مكة، حي العزيزية، طريق الحرم", CurrentBalance = 0,
                    IsActive = true, CreatedDate = DateTime.Now.AddDays(-15),
                    TaxNumber = "456789012345678", ContactPerson = "نورا أحمد"
                },
                new Customer
                {
                    Id = 5, CustomerCode = "C005", CustomerName = "شركة المقاولات الكبرى",
                    Phone = "0556677889", Email = "<EMAIL>",
                    Address = "المدينة، حي قباء، شارع النور", CurrentBalance = 25000,
                    IsActive = true, CreatedDate = DateTime.Now.AddDays(-10),
                    TaxNumber = "567890123456789", ContactPerson = "خالد العتيبي"
                },
                new Customer
                {
                    Id = 6, CustomerCode = "C006", CustomerName = "معهد التدريب المهني",
                    Phone = "0559988776", Email = "<EMAIL>",
                    Address = "الطائف، حي الشفا، طريق الهدا", CurrentBalance = 12000,
                    IsActive = true, CreatedDate = DateTime.Now.AddDays(-5),
                    TaxNumber = "678901234567890", ContactPerson = "سارة الأحمد"
                },
                new Customer
                {
                    Id = 7, CustomerCode = "C007", CustomerName = "مجموعة الخدمات اللوجستية",
                    Phone = "0552233445", Email = "<EMAIL>",
                    Address = "الخبر، حي الراكة، شارع الملك خالد", CurrentBalance = 5500,
                    IsActive = true, CreatedDate = DateTime.Now.AddDays(-3),
                    TaxNumber = "789012345678901", ContactPerson = "عبدالله الزهراني"
                },
                new Customer
                {
                    Id = 8, CustomerCode = "C008", CustomerName = "شركة التسويق الرقمي",
                    Phone = "0558899001", Email = "<EMAIL>",
                    Address = "بريدة، حي الصالحية، طريق الملك عبدالله", CurrentBalance = -1200,
                    IsActive = true, CreatedDate = DateTime.Now.AddDays(-1),
                    TaxNumber = "890123456789012", ContactPerson = "ريم السعيد"
                }
            };

            // المنتجات
            Products = new List<Product>
            {
                new Product
                {
                    Id = 1, ProductCode = "P001", ProductName = "جهاز كمبيوتر محمول HP",
                    Category = "إلكترونيات", UnitPrice = 3500, CostPrice = 2800,
                    StockQuantity = 25, Unit = "جهاز", IsActive = true,
                    Description = "جهاز كمبيوتر محمول HP بمعالج Intel Core i7",
                    Barcode = "1234567890123"
                },
                new Product
                {
                    Id = 2, ProductCode = "P002", ProductName = "طابعة ليزر Canon",
                    Category = "إلكترونيات", UnitPrice = 800, CostPrice = 600,
                    StockQuantity = 15, Unit = "جهاز", IsActive = true,
                    Description = "طابعة ليزر ملونة عالية الجودة",
                    Barcode = "2345678901234"
                },
                new Product
                {
                    Id = 3, ProductCode = "P003", ProductName = "خدمة استشارية تقنية",
                    Category = "خدمات", UnitPrice = 500, CostPrice = 200,
                    StockQuantity = 999, Unit = "ساعة", IsActive = true,
                    Description = "استشارات تقنية متخصصة في تطوير الأنظمة",
                    Barcode = "3456789012345"
                },
                new Product
                {
                    Id = 4, ProductCode = "P004", ProductName = "مواد خام بلاستيكية",
                    Category = "مواد", UnitPrice = 150, CostPrice = 100,
                    StockQuantity = 200, Unit = "كيلو", IsActive = true,
                    Description = "مواد خام بلاستيكية عالية الجودة",
                    Barcode = "4567890123456"
                },
                new Product
                {
                    Id = 5, ProductCode = "P005", ProductName = "أثاث مكتبي متكامل",
                    Category = "مكتبية", UnitPrice = 2500, CostPrice = 1800,
                    StockQuantity = 50, Unit = "طقم", IsActive = true,
                    Description = "طقم أثاث مكتبي متكامل يشمل مكتب وكرسي",
                    Barcode = "5678901234567"
                },
                new Product
                {
                    Id = 6, ProductCode = "P006", ProductName = "رخصة برمجيات Microsoft",
                    Category = "تقنية", UnitPrice = 1200, CostPrice = 800,
                    StockQuantity = 100, Unit = "رخصة", IsActive = true,
                    Description = "رخصة برمجيات Microsoft Office Professional",
                    Barcode = "6789012345678"
                },
                new Product
                {
                    Id = 7, ProductCode = "P007", ProductName = "خدمة صيانة شاملة",
                    Category = "خدمات", UnitPrice = 300, CostPrice = 150,
                    StockQuantity = 999, Unit = "زيارة", IsActive = true,
                    Description = "خدمة صيانة شاملة للأجهزة والمعدات",
                    Barcode = "7890123456789"
                },
                new Product
                {
                    Id = 8, ProductCode = "P008", ProductName = "شاشة عرض LED",
                    Category = "إلكترونيات", UnitPrice = 1800, CostPrice = 1200,
                    StockQuantity = 30, Unit = "جهاز", IsActive = true,
                    Description = "شاشة عرض LED عالية الدقة 32 بوصة",
                    Barcode = "8901234567890"
                },
                new Product
                {
                    Id = 9, ProductCode = "P009", ProductName = "دورة تدريبية متخصصة",
                    Category = "تدريب", UnitPrice = 2000, CostPrice = 800,
                    StockQuantity = 999, Unit = "دورة", IsActive = true,
                    Description = "دورة تدريبية متخصصة في إدارة المشاريع",
                    Barcode = "9012345678901"
                },
                new Product
                {
                    Id = 10, ProductCode = "P010", ProductName = "نظام أمان متكامل",
                    Category = "أمان", UnitPrice = 5000, CostPrice = 3500,
                    StockQuantity = 20, Unit = "نظام", IsActive = true,
                    Description = "نظام أمان متكامل مع كاميرات وأجهزة إنذار",
                    Barcode = "0123456789012"
                }
            };

            LoadInvoicesData();
            LoadReceiptsData();
        }

        private static void LoadInvoicesData()
        {
            Invoices = new List<Invoice>
            {
                new Invoice
                {
                    Id = 1, InvoiceNumber = "INV-2024-001", InvoiceDate = DateTime.Now.AddDays(-45),
                    DueDate = DateTime.Now.AddDays(-15), CustomerId = 1, SubTotal = 10000, TaxAmount = 1500,
                    TotalAmount = 11500, PaidAmount = 5000, Status = "مدفوعة جزئياً",
                    Notes = "فاتورة مبيعات أجهزة كمبيوتر للمكتب الرئيسي",
                    CreatedDate = DateTime.Now.AddDays(-45), CreatedBy = "admin",
                    PaymentTerms = "30 يوم", DeliveryAddress = "الرياض، حي النخيل",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 1, ProductId = 1, ProductName = "جهاز كمبيوتر محمول HP", Quantity = 2, UnitPrice = 3500, DiscountPercent = 5, Notes = "خصم كمية" },
                        new InvoiceItem { Id = 2, ProductId = 2, ProductName = "طابعة ليزر Canon", Quantity = 4, UnitPrice = 800, DiscountPercent = 0, Notes = "" }
                    }
                },
                new Invoice
                {
                    Id = 2, InvoiceNumber = "INV-2024-002", InvoiceDate = DateTime.Now.AddDays(-30),
                    DueDate = DateTime.Now.AddDays(-10), CustomerId = 2, SubTotal = 5000, TaxAmount = 750,
                    TotalAmount = 5750, PaidAmount = 5750, Status = "مدفوعة",
                    Notes = "فاتورة خدمات استشارية لتطوير النظام",
                    CreatedDate = DateTime.Now.AddDays(-30), CreatedBy = "admin",
                    PaymentTerms = "فوري", DeliveryAddress = "جدة، حي الصفا",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 3, ProductId = 3, ProductName = "خدمة استشارية تقنية", Quantity = 10, UnitPrice = 500, DiscountPercent = 0, Notes = "استشارات تطوير" }
                    }
                },
                new Invoice
                {
                    Id = 3, InvoiceNumber = "INV-2024-003", InvoiceDate = DateTime.Now.AddDays(-25),
                    DueDate = DateTime.Now.AddDays(-5), CustomerId = 3, SubTotal = 8000, TaxAmount = 1200,
                    TotalAmount = 9200, PaidAmount = 0, Status = "متأخرة",
                    Notes = "فاتورة مواد خام للإنتاج",
                    CreatedDate = DateTime.Now.AddDays(-25), CreatedBy = "admin",
                    PaymentTerms = "15 يوم", DeliveryAddress = "الدمام، حي الفيصلية",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 4, ProductId = 4, ProductName = "مواد خام بلاستيكية", Quantity = 50, UnitPrice = 150, DiscountPercent = 6.25m, Notes = "خصم كمية كبيرة" }
                    }
                },
                new Invoice
                {
                    Id = 4, InvoiceNumber = "INV-2024-004", InvoiceDate = DateTime.Now.AddDays(-20),
                    DueDate = DateTime.Now.AddDays(10), CustomerId = 4, SubTotal = 12000, TaxAmount = 1800,
                    TotalAmount = 13800, PaidAmount = 0, Status = "مؤكدة",
                    Notes = "فاتورة أثاث مكتبي للمكتب الجديد",
                    CreatedDate = DateTime.Now.AddDays(-20), CreatedBy = "admin",
                    PaymentTerms = "45 يوم", DeliveryAddress = "مكة، حي العزيزية",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 5, ProductId = 5, ProductName = "أثاث مكتبي متكامل", Quantity = 5, UnitPrice = 2500, DiscountPercent = 4, Notes = "خصم عميل مميز" }
                    }
                },
                new Invoice
                {
                    Id = 5, InvoiceNumber = "INV-2024-005", InvoiceDate = DateTime.Now.AddDays(-15),
                    DueDate = DateTime.Now.AddDays(15), CustomerId = 5, SubTotal = 20000, TaxAmount = 3000,
                    TotalAmount = 23000, PaidAmount = 10000, Status = "مدفوعة جزئياً",
                    Notes = "فاتورة مشروع تطوير متكامل",
                    CreatedDate = DateTime.Now.AddDays(-15), CreatedBy = "admin",
                    PaymentTerms = "60 يوم", DeliveryAddress = "المدينة، حي قباء",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 6, ProductId = 6, ProductName = "رخصة برمجيات Microsoft", Quantity = 15, UnitPrice = 1200, DiscountPercent = 10, Notes = "خصم حجم" },
                        new InvoiceItem { Id = 7, ProductId = 3, ProductName = "خدمة استشارية تقنية", Quantity = 10, UnitPrice = 500, DiscountPercent = 0, Notes = "استشارات تنفيذ" }
                    }
                }
            };
        }

        private static void LoadReceiptsData()
        {
            Receipts = new List<Receipt>
            {
                new Receipt
                {
                    Id = 1, ReceiptNumber = "R001", ReceiptDate = DateTime.Now.AddDays(-28),
                    CustomerId = 1, InvoiceId = 1, Amount = 5000, PaymentMethod = "نقدي",
                    ReferenceNumber = "CASH001", Status = "مؤكد",
                    CreatedDate = DateTime.Now.AddDays(-28), CreatedBy = "admin",
                    Notes = "دفعة أولى", BankName = "", ClearanceDate = DateTime.Now.AddDays(-28)
                },
                new Receipt
                {
                    Id = 2, ReceiptNumber = "R002", ReceiptDate = DateTime.Now.AddDays(-18),
                    CustomerId = 2, InvoiceId = 2, Amount = 5750, PaymentMethod = "تحويل بنكي",
                    ReferenceNumber = "TRF002", Status = "مؤكد",
                    CreatedDate = DateTime.Now.AddDays(-18), CreatedBy = "admin",
                    Notes = "دفع كامل", BankName = "البنك الأهلي", ClearanceDate = DateTime.Now.AddDays(-17)
                },
                new Receipt
                {
                    Id = 3, ReceiptNumber = "R003", ReceiptDate = DateTime.Now.AddDays(-10),
                    CustomerId = 5, InvoiceId = 5, Amount = 10000, PaymentMethod = "شيك",
                    ReferenceNumber = "CHK003", Status = "مؤكد",
                    CreatedDate = DateTime.Now.AddDays(-10), CreatedBy = "admin",
                    Notes = "دفعة أولى", BankName = "بنك الراجحي", ClearanceDate = DateTime.Now.AddDays(-8)
                }
            };
        }

        // دوال إدارة البيانات
        public static void AddInvoice(Invoice invoice)
        {
            invoice.Id = Invoices.Count > 0 ? Invoices.Max(i => i.Id) + 1 : 1;
            invoice.InvoiceNumber = $"INV-2024-{invoice.Id:000}";
            invoice.CreatedDate = DateTime.Now;
            invoice.CreatedBy = AuthService.CurrentUser?.Username ?? "system";

            // تحديث معرفات الأصناف
            int itemId = 1;
            if (Invoices.Any())
            {
                itemId = Invoices.SelectMany(i => i.Items).Max(item => item.Id) + 1;
            }

            foreach (var item in invoice.Items)
            {
                item.Id = itemId++;
                item.InvoiceId = invoice.Id;
            }

            Invoices.Add(invoice);
            UpdateCustomerBalance(invoice.CustomerId);
            LogOperation("Create", "Invoice", invoice.Id, $"تم إنشاء فاتورة جديدة: {invoice.InvoiceNumber}");
        }

        public static void UpdateInvoice(Invoice invoice)
        {
            var existingInvoice = Invoices.FirstOrDefault(i => i.Id == invoice.Id);
            if (existingInvoice != null)
            {
                var index = Invoices.IndexOf(existingInvoice);
                Invoices[index] = invoice;
                UpdateCustomerBalance(invoice.CustomerId);
                LogOperation("Update", "Invoice", invoice.Id, $"تم تحديث الفاتورة: {invoice.InvoiceNumber}");
            }
        }

        public static void DeleteInvoice(int invoiceId)
        {
            var invoice = Invoices.FirstOrDefault(i => i.Id == invoiceId);
            if (invoice != null)
            {
                var customerId = invoice.CustomerId;
                Invoices.Remove(invoice);
                UpdateCustomerBalance(customerId);
                LogOperation("Delete", "Invoice", invoiceId, $"تم حذف الفاتورة: {invoice.InvoiceNumber}");
            }
        }

        public static void AddCustomer(Customer customer)
        {
            customer.Id = Customers.Count > 0 ? Customers.Max(c => c.Id) + 1 : 1;
            customer.CustomerCode = $"C{customer.Id:000}";
            customer.CreatedDate = DateTime.Now;
            Customers.Add(customer);
            LogOperation("Create", "Customer", customer.Id, $"تم إضافة عميل جديد: {customer.CustomerName}");
        }

        public static void AddProduct(Product product)
        {
            product.Id = Products.Count > 0 ? Products.Max(p => p.Id) + 1 : 1;
            product.ProductCode = $"P{product.Id:000}";
            Products.Add(product);
            LogOperation("Create", "Product", product.Id, $"تم إضافة منتج جديد: {product.ProductName}");
        }

        private static void UpdateCustomerBalance(int customerId)
        {
            var customer = Customers.FirstOrDefault(c => c.Id == customerId);
            if (customer != null)
            {
                var totalInvoices = Invoices.Where(i => i.CustomerId == customerId).Sum(i => i.TotalAmount);
                var totalPayments = Receipts.Where(r => r.CustomerId == customerId && r.Status == "مؤكد").Sum(r => r.Amount);
                customer.CurrentBalance = totalInvoices - totalPayments;
            }
        }

        private static void LogOperation(string type, string entityType, int entityId, string description)
        {
            var operation = new Operation
            {
                Id = Operations.Count + 1,
                Type = type,
                EntityType = entityType,
                EntityId = entityId,
                Description = description,
                Timestamp = DateTime.Now,
                UserId = AuthService.CurrentUser?.Username ?? "system",
                UserName = AuthService.CurrentUser?.FullName ?? "النظام",
                IsSuccessful = true
            };
            Operations.Add(operation);
        }

        // دوال التقارير والإحصائيات
        public static DashboardStats GetDashboardStats()
        {
            return new DashboardStats
            {
                TotalInvoices = Invoices.Count,
                TotalSales = Invoices.Sum(i => i.TotalAmount),
                PaidInvoices = Invoices.Count(i => i.Status == "مدفوعة"),
                PaidAmount = Invoices.Where(i => i.Status == "مدفوعة").Sum(i => i.TotalAmount),
                OverdueInvoices = Invoices.Count(i => i.IsOverdue),
                OverdueAmount = Invoices.Where(i => i.IsOverdue).Sum(i => i.RemainingAmount),
                TotalCustomers = Customers.Count(c => c.IsActive),
                TotalBalance = Customers.Sum(c => c.CurrentBalance),
                NewCustomersThisMonth = Customers.Count(c => c.CreatedDate >= DateTime.Now.AddDays(-30)),
                AverageInvoiceValue = Invoices.Any() ? Invoices.Average(i => i.TotalAmount) : 0,
                CollectionRate = Invoices.Sum(i => i.TotalAmount) > 0 ? (Invoices.Sum(i => i.PaidAmount) / Invoices.Sum(i => i.TotalAmount)) * 100 : 0,
                LowStockProducts = Products.Count(p => p.StockQuantity < Settings.LowStockWarning),
                TodayPayments = Receipts.Where(r => r.ReceiptDate.Date == DateTime.Today && r.Status == "مؤكد").Sum(r => r.Amount),
                DueToday = Invoices.Count(i => i.DueDate.Date == DateTime.Today && i.Status != "مدفوعة")
            };
        }

        // حفظ واستعادة البيانات
        public static void SaveData()
        {
            try
            {
                var dataFolder = "Data";
                if (!Directory.Exists(dataFolder))
                    Directory.CreateDirectory(dataFolder);

                File.WriteAllText(Path.Combine(dataFolder, "LastSave.txt"), DateTime.Now.ToString());
                LogOperation("System", "Backup", 0, "تم حفظ البيانات بنجاح");
            }
            catch (Exception ex)
            {
                LogOperation("System", "Backup", 0, $"خطأ في حفظ البيانات: {ex.Message}");
                throw;
            }
        }

        public static void LoadData()
        {
            try
            {
                var dataFolder = "Data";
                if (Directory.Exists(dataFolder))
                {
                    var lastSaveFile = Path.Combine(dataFolder, "LastSave.txt");
                    if (File.Exists(lastSaveFile))
                    {
                        var lastSave = File.ReadAllText(lastSaveFile);
                        LogOperation("System", "Restore", 0, $"تم تحميل البيانات من: {lastSave}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogOperation("System", "Restore", 0, $"خطأ في تحميل البيانات: {ex.Message}");
            }
        }
    }
}
