@echo off
chcp 65001 > nul
echo ========================================
echo اختبار النماذج المصححة
echo ========================================
echo.

echo هذا الاختبار سيتحقق من:
echo 1. صحة خصائص نموذج العميل (CurrentBalance)
echo 2. صحة خصائص نموذج المنتج (SalePrice, PurchasePrice, CurrentStock)
echo 3. توافق النماذج مع الواجهات
echo 4. إمكانية البناء بدون أخطاء
echo.

set errors=0

echo بدء الاختبار...
echo.

echo ========================================
echo 1. فحص نموذج العميل
echo ========================================

if exist "Models\Customer.cs" (
    echo ✓ ملف Customer.cs موجود
    
    findstr /C:"CurrentBalance" "Models\Customer.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على خاصية CurrentBalance
    ) else (
        echo ✗ خاصية CurrentBalance مفقودة
        set /a errors+=1
    )
) else (
    echo ✗ ملف Customer.cs مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo 2. فحص نموذج المنتج
echo ========================================

if exist "Models\Product.cs" (
    echo ✓ ملف Product.cs موجود
    
    findstr /C:"SalePrice" "Models\Product.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على خاصية SalePrice
    ) else (
        echo ✗ خاصية SalePrice مفقودة
        set /a errors+=1
    )
    
    findstr /C:"PurchasePrice" "Models\Product.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على خاصية PurchasePrice
    ) else (
        echo ✗ خاصية PurchasePrice مفقودة
        set /a errors+=1
    )
    
    findstr /C:"CurrentStock" "Models\Product.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على خاصية CurrentStock
    ) else (
        echo ✗ خاصية CurrentStock مفقودة
        set /a errors+=1
    )
) else (
    echo ✗ ملف Product.cs مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo 3. فحص نماذج العملاء
echo ========================================

if exist "Forms\CustomersForm.cs" (
    echo ✓ ملف CustomersForm.cs موجود
    
    findstr /C:"CurrentBalance" "Forms\CustomersForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يستخدم CurrentBalance بدلاً من Balance
    ) else (
        echo ✗ لا يزال يستخدم Balance القديم
        set /a errors+=1
    )
) else (
    echo ✗ ملف CustomersForm.cs مفقود
    set /a errors+=1
)

if exist "Forms\AddEditCustomerForm.cs" (
    echo ✓ ملف AddEditCustomerForm.cs موجود
    
    findstr /C:"CurrentBalance" "Forms\AddEditCustomerForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يستخدم CurrentBalance بدلاً من Balance
    ) else (
        echo ✗ لا يزال يستخدم Balance القديم
        set /a errors+=1
    )
) else (
    echo ✗ ملف AddEditCustomerForm.cs مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo 4. فحص نماذج المنتجات
echo ========================================

if exist "Forms\ProductsForm.cs" (
    echo ✓ ملف ProductsForm.cs موجود
    
    findstr /C:"SalePrice" "Forms\ProductsForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يستخدم SalePrice بدلاً من UnitPrice
    ) else (
        echo ✗ لا يزال يستخدم UnitPrice القديم
        set /a errors+=1
    )
    
    findstr /C:"CurrentStock" "Forms\ProductsForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يستخدم CurrentStock بدلاً من StockQuantity
    ) else (
        echo ✗ لا يزال يستخدم StockQuantity القديم
        set /a errors+=1
    )
) else (
    echo ✗ ملف ProductsForm.cs مفقود
    set /a errors+=1
)

if exist "Forms\AddEditProductForm.cs" (
    echo ✓ ملف AddEditProductForm.cs موجود
    
    findstr /C:"txtSalePrice" "Forms\AddEditProductForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يستخدم txtSalePrice بدلاً من txtUnitPrice
    ) else (
        echo ✗ لا يزال يستخدم txtUnitPrice القديم
        set /a errors+=1
    )
) else (
    echo ✗ ملف AddEditProductForm.cs مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo 5. اختبار بناء سريع
echo ========================================

where csc >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ مترجم C# متوفر
    
    echo محاولة بناء النماذج المصححة...
    
    csc /target:library ^
        /reference:System.dll ^
        /reference:System.Core.dll ^
        /reference:System.Drawing.dll ^
        /reference:System.Windows.Forms.dll ^
        /out:TestFixedModels.dll ^
        Models\Customer.cs ^
        Models\Product.cs >nul 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo ✓ نجح بناء النماذج المصححة
        if exist "TestFixedModels.dll" del "TestFixedModels.dll" >nul 2>&1
    ) else (
        echo ✗ فشل بناء النماذج المصححة
        set /a errors+=1
    )
) else (
    echo ⚠ مترجم C# غير متوفر (استخدم Developer Command Prompt)
)

echo.
echo ========================================
echo النتائج النهائية
echo ========================================

echo إجمالي الأخطاء: %errors%

if %errors% EQU 0 (
    echo.
    echo 🎉 ممتاز! تم إصلاح جميع مشاكل النماذج
    echo.
    echo التحديثات المطبقة:
    echo ✓ استبدال Balance بـ CurrentBalance في نماذج العملاء
    echo ✓ استبدال UnitPrice بـ SalePrice في نماذج المنتجات
    echo ✓ استبدال CostPrice بـ PurchasePrice في نماذج المنتجات
    echo ✓ استبدال StockQuantity بـ CurrentStock في نماذج المنتجات
    echo ✓ تحديث SimpleDatabaseHelper لدعم الخصائص الجديدة
    echo.
    echo النظام الآن جاهز للتشغيل بدون أخطاء!
    echo.
    echo خيارات التشغيل:
    echo 1. build_simple.bat - للاختبار السريع
    echo 2. build_complete.bat - للنسخة الكاملة
    echo 3. build_without_sqlite.bat - لتجنب مشاكل SQLite
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    
) else (
    echo.
    echo ⚠ يوجد %errors% خطأ يجب إصلاحه
    echo.
    echo الإجراءات المقترحة:
    echo 1. تأكد من وجود جميع ملفات النماذج
    echo 2. راجع خصائص النماذج في ملفات Models
    echo 3. تأكد من تطابق أسماء الخصائص في النماذج والواجهات
    echo.
    echo يمكنك مراجعة الملفات التالية:
    echo - Models\Customer.cs
    echo - Models\Product.cs
    echo - Forms\CustomersForm.cs
    echo - Forms\ProductsForm.cs
)

echo.
echo تاريخ الاختبار: %date% %time%

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
