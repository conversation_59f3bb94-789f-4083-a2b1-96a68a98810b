@echo off
chcp 65001 > nul
echo ========================================
echo بناء نظام السندات فقط
echo ========================================
echo.

echo هذا البناء سيركز على نظام السندات والتقارير فقط
echo لتجنب مشاكل المراجع المعقدة
echo.

echo بناء النظام...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Configuration.dll ^
    /out:ReceiptsSystem.exe ^
    Models\Account.cs ^
    Models\JournalEntry.cs ^
    Models\Customer.cs ^
    Models\Product.cs ^
    Models\Receipt.cs ^
    Models\Payment.cs ^
    Models\Invoice.cs ^
    Models\User.cs ^
    Services\SimpleAuthenticationService.cs ^
    Data\SimpleDatabaseHelper.cs ^
    Utils\SecurityHelper.cs ^
    Utils\ConfigHelper.cs ^
    Forms\LoginForm.cs ^
    Forms\AddEditReceiptForm.cs ^
    Forms\ReceiptsReportForm.cs ^
    Forms\AddEditPaymentForm.cs ^
    Forms\PaymentsReportForm.cs ^
    Forms\CustomerPaymentForm.cs ^
    Forms\ReceiptsForm.cs ^
    Forms\PaymentsForm.cs ^
    Forms\CustomerStatementForm.cs ^
    Forms\MainFormSimple.cs ^
    Properties\Resources.Designer.cs ^
    Properties\Settings.Designer.cs ^
    ProgramSimple.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء نظام السندات بنجاح!
    echo.
    echo تم إنشاء: ReceiptsSystem.exe
    echo.
    
    echo نسخ ملفات الإعداد...
    if exist "App.config" copy "App.config" "ReceiptsSystem.exe.config" >nul
    
    set /p run="هل تريد تشغيل النظام الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام...
        start "" "ReceiptsSystem.exe"
        echo.
        echo تم تشغيل النظام بنجاح!
    )
    
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo الميزات المتوفرة:
    echo • إدارة سندات القبض
    echo • إدارة سندات الصرف
    echo • كشف حساب العميل
    echo • تقارير شاملة
    
) else (
    echo.
    echo ✗ فشل في بناء النظام
    echo.
    echo جرب البناء التدريجي:
    echo build_step_by_step.bat
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
