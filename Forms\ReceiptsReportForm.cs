using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AccountingSystem.Models;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج تقرير سندات القبض
    /// </summary>
    public partial class ReceiptsReportForm : Form
    {
        private DataGridView dgvReport;
        private Button btnPrint;
        private Button btnExport;
        private Button btnClose;
        private Label lblTitle;
        private Label lblTotalReceipts;
        private Label lblTotalAmount;
        private Label lblByPaymentMethod;
        private TextBox txtTotalReceipts;
        private TextBox txtTotalAmount;
        private DataGridView dgvPaymentMethods;
        
        private List<Receipt> receipts;
        private List<Customer> customers;
        
        public ReceiptsReportForm(List<Receipt> receiptsList, List<Customer> customersList)
        {
            receipts = receiptsList;
            customers = customersList;
            InitializeComponent();
            LoadReport();
        }
        
        private void InitializeComponent()
        {
            this.dgvReport = new DataGridView();
            this.btnPrint = new Button();
            this.btnExport = new Button();
            this.btnClose = new Button();
            this.lblTitle = new Label();
            this.lblTotalReceipts = new Label();
            this.lblTotalAmount = new Label();
            this.lblByPaymentMethod = new Label();
            this.txtTotalReceipts = new TextBox();
            this.txtTotalAmount = new TextBox();
            this.dgvPaymentMethods = new DataGridView();
            
            this.SuspendLayout();
            
            // Form
            this.Text = "تقرير سندات القبض";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Title
            this.lblTitle.Text = "تقرير سندات القبض";
            this.lblTitle.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(25, 118, 210);
            this.lblTitle.Location = new Point(350, 20);
            this.lblTitle.Size = new Size(300, 35);
            this.lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            
            // Summary Controls
            this.lblTotalReceipts.Text = "إجمالي عدد السندات:";
            this.lblTotalReceipts.Location = new Point(800, 70);
            this.lblTotalReceipts.Size = new Size(120, 23);
            this.lblTotalReceipts.TextAlign = ContentAlignment.MiddleRight;
            this.lblTotalReceipts.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            this.txtTotalReceipts.Location = new Point(650, 70);
            this.txtTotalReceipts.Size = new Size(120, 23);
            this.txtTotalReceipts.ReadOnly = true;
            this.txtTotalReceipts.BackColor = Color.LightGray;
            this.txtTotalReceipts.TextAlign = HorizontalAlignment.Right;
            this.txtTotalReceipts.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            this.lblTotalAmount.Text = "إجمالي المبلغ:";
            this.lblTotalAmount.Location = new Point(500, 70);
            this.lblTotalAmount.Size = new Size(100, 23);
            this.lblTotalAmount.TextAlign = ContentAlignment.MiddleRight;
            this.lblTotalAmount.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            this.txtTotalAmount.Location = new Point(350, 70);
            this.txtTotalAmount.Size = new Size(140, 23);
            this.txtTotalAmount.ReadOnly = true;
            this.txtTotalAmount.BackColor = Color.LightGray;
            this.txtTotalAmount.TextAlign = HorizontalAlignment.Right;
            this.txtTotalAmount.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            // Main Report DataGridView
            this.dgvReport.Location = new Point(20, 110);
            this.dgvReport.Size = new Size(960, 350);
            this.dgvReport.AllowUserToAddRows = false;
            this.dgvReport.AllowUserToDeleteRows = false;
            this.dgvReport.ReadOnly = true;
            this.dgvReport.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvReport.RightToLeft = RightToLeft.Yes;
            this.dgvReport.Font = new Font("Tahoma", 10F);
            
            SetupMainReportGrid();
            
            // Payment Methods Summary
            this.lblByPaymentMethod.Text = "ملخص حسب طريقة الدفع:";
            this.lblByPaymentMethod.Location = new Point(800, 480);
            this.lblByPaymentMethod.Size = new Size(150, 23);
            this.lblByPaymentMethod.TextAlign = ContentAlignment.MiddleRight;
            this.lblByPaymentMethod.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            this.dgvPaymentMethods.Location = new Point(500, 510);
            this.dgvPaymentMethods.Size = new Size(480, 120);
            this.dgvPaymentMethods.AllowUserToAddRows = false;
            this.dgvPaymentMethods.AllowUserToDeleteRows = false;
            this.dgvPaymentMethods.ReadOnly = true;
            this.dgvPaymentMethods.RightToLeft = RightToLeft.Yes;
            this.dgvPaymentMethods.Font = new Font("Tahoma", 10F);
            
            SetupPaymentMethodsGrid();
            
            // Action Buttons
            this.btnPrint.Text = "طباعة";
            this.btnPrint.Location = new Point(350, 580);
            this.btnPrint.Size = new Size(100, 35);
            this.btnPrint.BackColor = Color.FromArgb(103, 58, 183);
            this.btnPrint.ForeColor = Color.White;
            this.btnPrint.FlatStyle = FlatStyle.Flat;
            this.btnPrint.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            this.btnPrint.Click += BtnPrint_Click;
            
            this.btnExport.Text = "تصدير";
            this.btnExport.Location = new Point(220, 580);
            this.btnExport.Size = new Size(100, 35);
            this.btnExport.BackColor = Color.FromArgb(76, 175, 80);
            this.btnExport.ForeColor = Color.White;
            this.btnExport.FlatStyle = FlatStyle.Flat;
            this.btnExport.Font = new Font("Tahoma", 10F);
            this.btnExport.Click += BtnExport_Click;
            
            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(90, 580);
            this.btnClose.Size = new Size(100, 35);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Font = new Font("Tahoma", 10F);
            this.btnClose.Click += BtnClose_Click;
            
            // Add controls to form
            this.Controls.Add(this.lblTitle);
            this.Controls.Add(this.lblTotalReceipts);
            this.Controls.Add(this.txtTotalReceipts);
            this.Controls.Add(this.lblTotalAmount);
            this.Controls.Add(this.txtTotalAmount);
            this.Controls.Add(this.dgvReport);
            this.Controls.Add(this.lblByPaymentMethod);
            this.Controls.Add(this.dgvPaymentMethods);
            this.Controls.Add(this.btnPrint);
            this.Controls.Add(this.btnExport);
            this.Controls.Add(this.btnClose);
            
            this.ResumeLayout(false);
        }
        
        private void SetupMainReportGrid()
        {
            dgvReport.Columns.Clear();
            
            dgvReport.Columns.Add("ReceiptNumber", "رقم السند");
            dgvReport.Columns["ReceiptNumber"].Width = 100;
            
            dgvReport.Columns.Add("ReceiptDate", "التاريخ");
            dgvReport.Columns["ReceiptDate"].Width = 100;
            dgvReport.Columns["ReceiptDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            
            dgvReport.Columns.Add("CustomerName", "العميل");
            dgvReport.Columns["CustomerName"].Width = 200;
            
            dgvReport.Columns.Add("Amount", "المبلغ");
            dgvReport.Columns["Amount"].Width = 120;
            dgvReport.Columns["Amount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvReport.Columns["Amount"].DefaultCellStyle.Format = "N2";
            
            dgvReport.Columns.Add("PaymentMethod", "طريقة الدفع");
            dgvReport.Columns["PaymentMethod"].Width = 120;
            
            dgvReport.Columns.Add("ReferenceNumber", "رقم المرجع");
            dgvReport.Columns["ReferenceNumber"].Width = 120;
            
            dgvReport.Columns.Add("Status", "الحالة");
            dgvReport.Columns["Status"].Width = 80;
            
            dgvReport.Columns.Add("Notes", "ملاحظات");
            dgvReport.Columns["Notes"].Width = 200;
        }
        
        private void SetupPaymentMethodsGrid()
        {
            dgvPaymentMethods.Columns.Clear();
            
            dgvPaymentMethods.Columns.Add("PaymentMethod", "طريقة الدفع");
            dgvPaymentMethods.Columns["PaymentMethod"].Width = 150;
            
            dgvPaymentMethods.Columns.Add("Count", "عدد السندات");
            dgvPaymentMethods.Columns["Count"].Width = 100;
            dgvPaymentMethods.Columns["Count"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            
            dgvPaymentMethods.Columns.Add("TotalAmount", "إجمالي المبلغ");
            dgvPaymentMethods.Columns["TotalAmount"].Width = 150;
            dgvPaymentMethods.Columns["TotalAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvPaymentMethods.Columns["TotalAmount"].DefaultCellStyle.Format = "N2";
            
            dgvPaymentMethods.Columns.Add("Percentage", "النسبة %");
            dgvPaymentMethods.Columns["Percentage"].Width = 80;
            dgvPaymentMethods.Columns["Percentage"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvPaymentMethods.Columns["Percentage"].DefaultCellStyle.Format = "N1";
        }
        
        private void LoadReport()
        {
            try
            {
                LoadMainReport();
                LoadPaymentMethodsSummary();
                LoadSummaryData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void LoadMainReport()
        {
            dgvReport.Rows.Clear();
            
            foreach (var receipt in receipts.OrderByDescending(r => r.ReceiptDate))
            {
                var customer = customers.FirstOrDefault(c => c.Id == receipt.CustomerId);
                var customerName = customer?.CustomerName ?? "غير محدد";
                
                var rowIndex = dgvReport.Rows.Add();
                var row = dgvReport.Rows[rowIndex];
                
                row.Cells["ReceiptNumber"].Value = receipt.ReceiptNumber;
                row.Cells["ReceiptDate"].Value = receipt.ReceiptDate;
                row.Cells["CustomerName"].Value = customerName;
                row.Cells["Amount"].Value = receipt.Amount;
                row.Cells["PaymentMethod"].Value = receipt.PaymentMethod;
                row.Cells["ReferenceNumber"].Value = receipt.ReferenceNumber;
                row.Cells["Status"].Value = receipt.Status;
                row.Cells["Notes"].Value = receipt.Notes;
                
                // تلوين الصفوف حسب الحالة
                if (receipt.Status == "مؤكد")
                {
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else if (receipt.Status == "مسودة")
                {
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                }
                else if (receipt.Status == "ملغي")
                {
                    row.DefaultCellStyle.BackColor = Color.LightCoral;
                }
            }
        }
        
        private void LoadPaymentMethodsSummary()
        {
            dgvPaymentMethods.Rows.Clear();
            
            var paymentMethodGroups = receipts
                .Where(r => r.Status == "مؤكد")
                .GroupBy(r => r.PaymentMethod)
                .Select(g => new
                {
                    PaymentMethod = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(r => r.Amount)
                })
                .OrderByDescending(g => g.TotalAmount);
            
            decimal grandTotal = receipts.Where(r => r.Status == "مؤكد").Sum(r => r.Amount);
            
            foreach (var group in paymentMethodGroups)
            {
                var rowIndex = dgvPaymentMethods.Rows.Add();
                var row = dgvPaymentMethods.Rows[rowIndex];
                
                row.Cells["PaymentMethod"].Value = group.PaymentMethod;
                row.Cells["Count"].Value = group.Count;
                row.Cells["TotalAmount"].Value = group.TotalAmount;
                row.Cells["Percentage"].Value = grandTotal > 0 ? (group.TotalAmount / grandTotal) * 100 : 0;
            }
        }
        
        private void LoadSummaryData()
        {
            var confirmedReceipts = receipts.Where(r => r.Status == "مؤكد").ToList();
            
            txtTotalReceipts.Text = confirmedReceipts.Count.ToString();
            txtTotalAmount.Text = confirmedReceipts.Sum(r => r.Amount).ToString("N2");
        }
        
        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                PrintReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void PrintReport()
        {
            var reportLines = new List<string>();
            
            // عنوان التقرير
            reportLines.Add("تقرير سندات القبض");
            reportLines.Add("==================");
            reportLines.Add($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
            reportLines.Add($"إجمالي عدد السندات: {txtTotalReceipts.Text}");
            reportLines.Add($"إجمالي المبلغ: {txtTotalAmount.Text} ريال");
            reportLines.Add("");
            
            // تفاصيل السندات
            reportLines.Add("تفاصيل السندات:");
            reportLines.Add("رقم السند    التاريخ      العميل                    المبلغ        طريقة الدفع    الحالة");
            reportLines.Add("================================================================================");
            
            foreach (var receipt in receipts.OrderByDescending(r => r.ReceiptDate))
            {
                var customer = customers.FirstOrDefault(c => c.Id == receipt.CustomerId);
                var customerName = customer?.CustomerName ?? "غير محدد";
                
                string line = $"{receipt.ReceiptNumber.PadRight(12)} {receipt.ReceiptDate:yyyy/MM/dd} " +
                             $"{customerName.PadRight(25)} {receipt.Amount.ToString("N2").PadLeft(12)} " +
                             $"{receipt.PaymentMethod.PadRight(15)} {receipt.Status}";
                reportLines.Add(line);
            }
            
            reportLines.Add("================================================================================");
            
            // ملخص طرق الدفع
            reportLines.Add("");
            reportLines.Add("ملخص حسب طريقة الدفع:");
            reportLines.Add("طريقة الدفع        عدد السندات    إجمالي المبلغ    النسبة %");
            reportLines.Add("================================================================");
            
            var paymentMethodGroups = receipts
                .Where(r => r.Status == "مؤكد")
                .GroupBy(r => r.PaymentMethod)
                .Select(g => new
                {
                    PaymentMethod = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(r => r.Amount)
                })
                .OrderByDescending(g => g.TotalAmount);
            
            decimal grandTotal = receipts.Where(r => r.Status == "مؤكد").Sum(r => r.Amount);
            
            foreach (var group in paymentMethodGroups)
            {
                decimal percentage = grandTotal > 0 ? (group.TotalAmount / grandTotal) * 100 : 0;
                string line = $"{group.PaymentMethod.PadRight(18)} {group.Count.ToString().PadLeft(12)} " +
                             $"{group.TotalAmount.ToString("N2").PadLeft(15)} {percentage.ToString("N1").PadLeft(10)}";
                reportLines.Add(line);
            }
            
            // حفظ التقرير مؤقتاً وطباعته
            string tempFile = System.IO.Path.GetTempFileName() + ".txt";
            System.IO.File.WriteAllLines(tempFile, reportLines, System.Text.Encoding.UTF8);
            
            try
            {
                System.Diagnostics.Process.Start("notepad.exe", "/p " + tempFile);
            }
            catch
            {
                MessageBox.Show($"تم حفظ التقرير في: {tempFile}", "تقرير سندات القبض", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        
        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "ملفات CSV (*.csv)|*.csv|ملفات نصية (*.txt)|*.txt";
                saveDialog.Title = "تصدير تقرير سندات القبض";
                saveDialog.FileName = $"تقرير_سندات_القبض_{DateTime.Now:yyyyMMdd}.csv";
                
                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToCSV(saveDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void ExportToCSV(string filePath)
        {
            var lines = new List<string>();
            
            // رأس الملف
            lines.Add("رقم السند,التاريخ,العميل,المبلغ,طريقة الدفع,رقم المرجع,الحالة,ملاحظات");
            
            // البيانات
            foreach (var receipt in receipts.OrderByDescending(r => r.ReceiptDate))
            {
                var customer = customers.FirstOrDefault(c => c.Id == receipt.CustomerId);
                var customerName = customer?.CustomerName ?? "غير محدد";
                
                lines.Add($"{receipt.ReceiptNumber},{receipt.ReceiptDate:yyyy/MM/dd},{customerName}," +
                         $"{receipt.Amount:N2},{receipt.PaymentMethod},{receipt.ReferenceNumber}," +
                         $"{receipt.Status},{receipt.Notes}");
            }
            
            System.IO.File.WriteAllLines(filePath, lines, System.Text.Encoding.UTF8);
            
            MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{filePath}", "نجح التصدير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
