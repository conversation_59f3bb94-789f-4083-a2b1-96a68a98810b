# دليل استكشاف الأخطاء - نظام المحاسبة الذكي

## مشكلة Mixed Mode Assembly

### الخطأ:
```
System.IO.FileLoadException: 'Mixed mode assembly is built against version 'v2.0.50727' of the runtime and cannot be loaded in the 4.0 runtime without additional configuration information.'
```

### السبب:
هذا الخطأ يحدث عندما تحاول تشغيل مكتبة SQLite مبنية لـ .NET Framework 2.0 في بيئة .NET Framework 4.0 أو أحدث.

### الحلول المتاحة:

#### الحل 1: تحديث App.config (مطبق)
تم تحديث ملف `App.config` لدعم Mixed Mode Assemblies:
```xml
<startup useLegacyV2RuntimeActivationPolicy="true">
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
</startup>
```

#### الحل 2: استخدام الإصدار المبسط (موصى به)
```bash
build_simple.bat
```
- يعمل بدون مكتبات خارجية
- لا يحتاج SQLite
- مناسب للاختبار السريع

#### الحل 3: استخدام البناء بدون SQLite
```bash
build_without_sqlite.bat
```
- يستخدم ملفات نصية بدلاً من SQLite
- يتجنب مشكلة Mixed Mode
- يحتفظ بجميع الميزات الأساسية

#### الحل 4: تحديث مكتبة SQLite
1. تحميل إصدار حديث من System.Data.SQLite
2. استبدال الملف القديم
3. إعادة بناء المشروع

## مشاكل أخرى شائعة

### مشكلة: "ConfigurationManager does not exist"

#### الحل:
1. تأكد من إضافة مرجع `System.Configuration`
2. أو استخدم الإصدار المبسط:
```bash
build_simple.bat
```

### مشكلة: "لم يتم العثور على مترجم C#"

#### الحل:
1. **استخدام Developer Command Prompt:**
   - ابحث عن "Developer Command Prompt for Visual Studio"
   - تشغيل كـ Administrator
   - انتقل لمجلد المشروع
   - تشغيل ملف البناء

2. **إضافة مسار مترجم C# إلى PATH:**
   ```
   C:\Windows\Microsoft.NET\Framework64\v4.0.30319\
   ```

3. **تثبيت .NET Framework SDK**

### مشكلة: "ملفات مكتبات مفقودة"

#### الحل:
1. **للإصدار المبسط:**
```bash
build_simple.bat  # لا يحتاج مكتبات خارجية
```

2. **للإصدار الكامل:**
   - تحميل المكتبات المطلوبة:
     - System.Data.SQLite.dll
     - Dapper.dll
     - Newtonsoft.Json.dll
   - وضعها في مجلد المشروع

### مشكلة: "أخطاء في البناء"

#### الحل:
1. **فحص النظام:**
```bash
final_check.bat
```

2. **اختبار الملفات:**
```bash
test_system.bat
```

3. **استخدام الإعداد السريع:**
```bash
quick_setup.bat
```

## خيارات البناء المختلفة

### 1. الإصدار المبسط (الأسرع)
```bash
build_simple.bat
```
**المميزات:**
- ✅ لا يحتاج مكتبات خارجية
- ✅ يعمل فوراً
- ✅ مناسب للاختبار
- ❌ ميزات محدودة

### 2. الإصدار بدون SQLite
```bash
build_without_sqlite.bat
```
**المميزات:**
- ✅ يتجنب مشكلة Mixed Mode
- ✅ جميع الميزات الأساسية
- ✅ يستخدم ملفات نصية
- ❌ أداء أبطأ من SQLite

### 3. الإصدار الكامل
```bash
build_and_run.bat
```
**المميزات:**
- ✅ جميع الميزات
- ✅ أداء عالي
- ✅ قاعدة بيانات SQLite
- ❌ يحتاج مكتبات خارجية

## خطوات استكشاف الأخطاء

### الخطوة 1: تحديد نوع المشكلة
1. تشغيل `final_check.bat` لفحص النظام
2. قراءة رسالة الخطأ بعناية
3. تحديد ما إذا كانت المشكلة في:
   - المكتبات
   - الكود
   - الإعدادات
   - البيئة

### الخطوة 2: تجربة الحلول البديلة
1. **للمشاكل السريعة:** `build_simple.bat`
2. **لمشاكل SQLite:** `build_without_sqlite.bat`
3. **للمشاكل العامة:** `quick_setup.bat`

### الخطوة 3: فحص البيئة
1. التأكد من وجود .NET Framework 4.8
2. التأكد من وجود مترجم C#
3. التأكد من الصلاحيات

### الخطوة 4: فحص الملفات
1. التأكد من وجود جميع ملفات المشروع
2. التأكد من سلامة ملفات الإعداد
3. التأكد من وجود المكتبات المطلوبة

## نصائح للوقاية من المشاكل

### 1. استخدام البيئة المناسبة
- استخدم Developer Command Prompt
- تأكد من تثبيت .NET Framework SDK
- تشغيل كـ Administrator عند الحاجة

### 2. النسخ الاحتياطي
- احتفظ بنسخة من المشروع الأصلي
- اعمل نسخة احتياطية قبل التعديلات الكبيرة

### 3. اختبار تدريجي
- ابدأ بالإصدار المبسط
- انتقل للإصدار الكامل تدريجياً
- اختبر كل ميزة على حدة

### 4. قراءة التوثيق
- راجع `README.md` للمعلومات التقنية
- راجع `دليل_المستخدم.md` للاستخدام
- راجع `البدء_السريع.md` للبدء

## الحصول على المساعدة

### معلومات مطلوبة عند الإبلاغ عن مشكلة:
1. **رسالة الخطأ الكاملة**
2. **خطوات إعادة إنتاج المشكلة**
3. **نوع الإصدار المستخدم**
4. **نظام التشغيل والإصدار**
5. **إصدار .NET Framework**

### ملفات مفيدة للتشخيص:
- نتائج `final_check.bat`
- نتائج `test_system.bat`
- محتوى مجلد المشروع
- ملفات الإعداد

---

**نصيحة:** في معظم الحالات، استخدام `build_simple.bat` يحل المشاكل الأساسية ويسمح لك بتجربة النظام فوراً.
