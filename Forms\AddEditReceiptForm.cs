using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AccountingSystem.Models;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج إضافة/تعديل سند القبض
    /// </summary>
    public partial class AddEditReceiptForm : Form
    {
        private DateTimePicker dtpReceiptDate;
        private ComboBox cmbCustomer;
        private TextBox txtAmount;
        private ComboBox cmbPaymentMethod;
        private TextBox txtReferenceNumber;
        private TextBox txtNotes;
        private ComboBox cmbStatus;
        private Button btnSave;
        private Button btnCancel;
        
        private Label lblReceiptDate;
        private Label lblCustomer;
        private Label lblAmount;
        private Label lblPaymentMethod;
        private Label lblReferenceNumber;
        private Label lblNotes;
        private Label lblStatus;
        
        public Receipt Receipt { get; private set; }
        private List<Customer> customers;
        private bool isEditMode;
        
        public AddEditReceiptForm(List<Customer> customersList, Receipt receipt = null)
        {
            customers = customersList;
            InitializeComponent();
            
            if (receipt != null)
            {
                isEditMode = true;
                Receipt = receipt;
                LoadReceiptData();
                this.Text = "تعديل سند القبض";
            }
            else
            {
                isEditMode = false;
                Receipt = new Receipt();
                this.Text = "إضافة سند قبض جديد";
            }
        }
        
        private void InitializeComponent()
        {
            this.dtpReceiptDate = new DateTimePicker();
            this.cmbCustomer = new ComboBox();
            this.txtAmount = new TextBox();
            this.cmbPaymentMethod = new ComboBox();
            this.txtReferenceNumber = new TextBox();
            this.txtNotes = new TextBox();
            this.cmbStatus = new ComboBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            
            this.lblReceiptDate = new Label();
            this.lblCustomer = new Label();
            this.lblAmount = new Label();
            this.lblPaymentMethod = new Label();
            this.lblReferenceNumber = new Label();
            this.lblNotes = new Label();
            this.lblStatus = new Label();
            
            this.SuspendLayout();
            
            // Form
            this.Size = new Size(500, 450);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Receipt Date
            this.lblReceiptDate.Text = "تاريخ السند:";
            this.lblReceiptDate.Location = new Point(400, 30);
            this.lblReceiptDate.Size = new Size(80, 23);
            this.lblReceiptDate.TextAlign = ContentAlignment.MiddleRight;
            
            this.dtpReceiptDate.Location = new Point(50, 30);
            this.dtpReceiptDate.Size = new Size(330, 23);
            this.dtpReceiptDate.Format = DateTimePickerFormat.Short;
            this.dtpReceiptDate.Value = DateTime.Now;
            
            // Customer
            this.lblCustomer.Text = "العميل:";
            this.lblCustomer.Location = new Point(400, 70);
            this.lblCustomer.Size = new Size(80, 23);
            this.lblCustomer.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbCustomer.Location = new Point(50, 70);
            this.cmbCustomer.Size = new Size(330, 23);
            this.cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList;
            LoadCustomers();
            
            // Amount
            this.lblAmount.Text = "المبلغ:";
            this.lblAmount.Location = new Point(400, 110);
            this.lblAmount.Size = new Size(80, 23);
            this.lblAmount.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtAmount.Location = new Point(50, 110);
            this.txtAmount.Size = new Size(200, 23);
            this.txtAmount.TextAlign = HorizontalAlignment.Right;
            this.txtAmount.Font = new Font("Tahoma", 10F);
            
            // Payment Method
            this.lblPaymentMethod.Text = "طريقة الدفع:";
            this.lblPaymentMethod.Location = new Point(400, 150);
            this.lblPaymentMethod.Size = new Size(80, 23);
            this.lblPaymentMethod.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbPaymentMethod.Location = new Point(50, 150);
            this.cmbPaymentMethod.Size = new Size(200, 23);
            this.cmbPaymentMethod.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbPaymentMethod.Items.AddRange(new string[] { "نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان", "بطاقة مدين" });
            this.cmbPaymentMethod.SelectedIndex = 0;
            
            // Reference Number
            this.lblReferenceNumber.Text = "رقم المرجع:";
            this.lblReferenceNumber.Location = new Point(400, 190);
            this.lblReferenceNumber.Size = new Size(80, 23);
            this.lblReferenceNumber.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtReferenceNumber.Location = new Point(50, 190);
            this.txtReferenceNumber.Size = new Size(200, 23);
            this.txtReferenceNumber.Font = new Font("Tahoma", 10F);
            
            // Notes
            this.lblNotes.Text = "ملاحظات:";
            this.lblNotes.Location = new Point(400, 230);
            this.lblNotes.Size = new Size(80, 23);
            this.lblNotes.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtNotes.Location = new Point(50, 230);
            this.txtNotes.Size = new Size(330, 60);
            this.txtNotes.Multiline = true;
            this.txtNotes.ScrollBars = ScrollBars.Vertical;
            this.txtNotes.Font = new Font("Tahoma", 10F);
            
            // Status
            this.lblStatus.Text = "الحالة:";
            this.lblStatus.Location = new Point(400, 310);
            this.lblStatus.Size = new Size(80, 23);
            this.lblStatus.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbStatus.Location = new Point(50, 310);
            this.cmbStatus.Size = new Size(150, 23);
            this.cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbStatus.Items.AddRange(new string[] { "مسودة", "مؤكد", "ملغي" });
            this.cmbStatus.SelectedIndex = 0;
            
            // Save Button
            this.btnSave.Text = "حفظ";
            this.btnSave.Location = new Point(280, 360);
            this.btnSave.Size = new Size(100, 35);
            this.btnSave.BackColor = Color.FromArgb(76, 175, 80);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            this.btnSave.Click += BtnSave_Click;
            
            // Cancel Button
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.Location = new Point(150, 360);
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.BackColor = Color.FromArgb(244, 67, 54);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Tahoma", 10F);
            this.btnCancel.Click += BtnCancel_Click;
            
            // Add controls to form
            this.Controls.Add(this.lblReceiptDate);
            this.Controls.Add(this.dtpReceiptDate);
            this.Controls.Add(this.lblCustomer);
            this.Controls.Add(this.cmbCustomer);
            this.Controls.Add(this.lblAmount);
            this.Controls.Add(this.txtAmount);
            this.Controls.Add(this.lblPaymentMethod);
            this.Controls.Add(this.cmbPaymentMethod);
            this.Controls.Add(this.lblReferenceNumber);
            this.Controls.Add(this.txtReferenceNumber);
            this.Controls.Add(this.lblNotes);
            this.Controls.Add(this.txtNotes);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.cmbStatus);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnCancel);
            
            this.ResumeLayout(false);
            
            // Set default button
            this.AcceptButton = this.btnSave;
            this.CancelButton = this.btnCancel;
        }
        
        private void LoadCustomers()
        {
            cmbCustomer.Items.Clear();
            foreach (var customer in customers)
            {
                cmbCustomer.Items.Add($"{customer.CustomerCode} - {customer.CustomerName}");
            }
            
            if (cmbCustomer.Items.Count > 0)
                cmbCustomer.SelectedIndex = 0;
        }
        
        private void LoadReceiptData()
        {
            if (Receipt != null)
            {
                dtpReceiptDate.Value = Receipt.ReceiptDate;
                
                // تحديد العميل
                var customerIndex = customers.FindIndex(c => c.Id == Receipt.CustomerId);
                if (customerIndex >= 0)
                    cmbCustomer.SelectedIndex = customerIndex;
                
                txtAmount.Text = Receipt.Amount.ToString("F2");
                
                // تحديد طريقة الدفع
                var paymentMethodIndex = cmbPaymentMethod.Items.IndexOf(Receipt.PaymentMethod);
                if (paymentMethodIndex >= 0)
                    cmbPaymentMethod.SelectedIndex = paymentMethodIndex;
                
                txtReferenceNumber.Text = Receipt.ReferenceNumber;
                txtNotes.Text = Receipt.Notes;
                
                // تحديد الحالة
                var statusIndex = cmbStatus.Items.IndexOf(Receipt.Status);
                if (statusIndex >= 0)
                    cmbStatus.SelectedIndex = statusIndex;
            }
        }
        
        private bool ValidateInput()
        {
            if (cmbCustomer.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار العميل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCustomer.Focus();
                return false;
            }
            
            if (!decimal.TryParse(txtAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAmount.Focus();
                return false;
            }
            
            if (cmbPaymentMethod.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار طريقة الدفع", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbPaymentMethod.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(txtReferenceNumber.Text))
            {
                MessageBox.Show("يرجى إدخال رقم المرجع", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtReferenceNumber.Focus();
                return false;
            }
            
            return true;
        }
        
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;
                
                Receipt.ReceiptDate = dtpReceiptDate.Value;
                Receipt.CustomerId = customers[cmbCustomer.SelectedIndex].Id;
                Receipt.Amount = decimal.Parse(txtAmount.Text);
                Receipt.PaymentMethod = cmbPaymentMethod.SelectedItem.ToString();
                Receipt.ReferenceNumber = txtReferenceNumber.Text.Trim();
                Receipt.Notes = txtNotes.Text.Trim();
                Receipt.Status = cmbStatus.SelectedItem.ToString();
                
                if (!isEditMode)
                {
                    Receipt.CreatedDate = DateTime.Now;
                    Receipt.CreatedBy = Services.AuthenticationService.CurrentUser?.Username ?? "System";
                }
                else
                {
                    Receipt.ModifiedDate = DateTime.Now;
                    Receipt.ModifiedBy = Services.AuthenticationService.CurrentUser?.Username ?? "System";
                }
                
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السند: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
