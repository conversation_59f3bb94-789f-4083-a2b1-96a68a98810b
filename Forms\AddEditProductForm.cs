using System;
using System.Drawing;
using System.Windows.Forms;
using AccountingSystem.Models;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج إضافة/تعديل المنتج
    /// </summary>
    public partial class AddEditProductForm : Form
    {
        private TextBox txtProductName;
        private ComboBox cmbCategory;
        private TextBox txtSalePrice;
        private TextBox txtPurchasePrice;
        private TextBox txtCurrentStock;
        private ComboBox cmbUnit;
        private TextBox txtDescription;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;

        private Label lblProductName;
        private Label lblCategory;
        private Label lblSalePrice;
        private Label lblPurchasePrice;
        private Label lblCurrentStock;
        private Label lblUnit;
        private Label lblDescription;

        public Product Product { get; private set; }
        private bool isEditMode;

        public AddEditProductForm(Product product = null)
        {
            InitializeComponent();

            if (product != null)
            {
                isEditMode = true;
                Product = product;
                LoadProductData();
                this.Text = "تعديل المنتج";
            }
            else
            {
                isEditMode = false;
                Product = new Product();
                this.Text = "إضافة منتج جديد";
            }
        }

        private void InitializeComponent()
        {
            this.txtProductName = new TextBox();
            this.cmbCategory = new ComboBox();
            this.txtSalePrice = new TextBox();
            this.txtPurchasePrice = new TextBox();
            this.txtCurrentStock = new TextBox();
            this.cmbUnit = new ComboBox();
            this.txtDescription = new TextBox();
            this.chkIsActive = new CheckBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();

            this.lblProductName = new Label();
            this.lblCategory = new Label();
            this.lblSalePrice = new Label();
            this.lblPurchasePrice = new Label();
            this.lblCurrentStock = new Label();
            this.lblUnit = new Label();
            this.lblDescription = new Label();

            this.SuspendLayout();

            // Form
            this.Size = new Size(500, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Product Name Label
            this.lblProductName.Text = "اسم المنتج:";
            this.lblProductName.Location = new Point(400, 30);
            this.lblProductName.Size = new Size(80, 23);
            this.lblProductName.TextAlign = ContentAlignment.MiddleRight;

            // Product Name TextBox
            this.txtProductName.Location = new Point(50, 30);
            this.txtProductName.Size = new Size(330, 23);
            this.txtProductName.Font = new Font("Tahoma", 10F);

            // Category Label
            this.lblCategory.Text = "الفئة:";
            this.lblCategory.Location = new Point(400, 70);
            this.lblCategory.Size = new Size(80, 23);
            this.lblCategory.TextAlign = ContentAlignment.MiddleRight;

            // Category ComboBox
            this.cmbCategory.Location = new Point(50, 70);
            this.cmbCategory.Size = new Size(330, 23);
            this.cmbCategory.Font = new Font("Tahoma", 10F);
            this.cmbCategory.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbCategory.Items.AddRange(new string[] { "إلكترونيات", "ملابس", "أغذية", "أدوات منزلية", "كتب", "أخرى" });

            // Sale Price Label
            this.lblSalePrice.Text = "سعر البيع:";
            this.lblSalePrice.Location = new Point(400, 110);
            this.lblSalePrice.Size = new Size(80, 23);
            this.lblSalePrice.TextAlign = ContentAlignment.MiddleRight;

            // Sale Price TextBox
            this.txtSalePrice.Location = new Point(50, 110);
            this.txtSalePrice.Size = new Size(150, 23);
            this.txtSalePrice.Font = new Font("Tahoma", 10F);
            this.txtSalePrice.TextAlign = HorizontalAlignment.Right;

            // Purchase Price Label
            this.lblPurchasePrice.Text = "سعر الشراء:";
            this.lblPurchasePrice.Location = new Point(400, 150);
            this.lblPurchasePrice.Size = new Size(80, 23);
            this.lblPurchasePrice.TextAlign = ContentAlignment.MiddleRight;

            // Purchase Price TextBox
            this.txtPurchasePrice.Location = new Point(50, 150);
            this.txtPurchasePrice.Size = new Size(150, 23);
            this.txtPurchasePrice.Font = new Font("Tahoma", 10F);
            this.txtPurchasePrice.TextAlign = HorizontalAlignment.Right;

            // Current Stock Label
            this.lblCurrentStock.Text = "المخزون الحالي:";
            this.lblCurrentStock.Location = new Point(400, 190);
            this.lblCurrentStock.Size = new Size(80, 23);
            this.lblCurrentStock.TextAlign = ContentAlignment.MiddleRight;

            // Current Stock TextBox
            this.txtCurrentStock.Location = new Point(50, 190);
            this.txtCurrentStock.Size = new Size(100, 23);
            this.txtCurrentStock.Font = new Font("Tahoma", 10F);
            this.txtCurrentStock.TextAlign = HorizontalAlignment.Right;

            // Unit Label
            this.lblUnit.Text = "الوحدة:";
            this.lblUnit.Location = new Point(280, 190);
            this.lblUnit.Size = new Size(50, 23);
            this.lblUnit.TextAlign = ContentAlignment.MiddleRight;

            // Unit ComboBox
            this.cmbUnit.Location = new Point(170, 190);
            this.cmbUnit.Size = new Size(100, 23);
            this.cmbUnit.Font = new Font("Tahoma", 10F);
            this.cmbUnit.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbUnit.Items.AddRange(new string[] { "قطعة", "كيس", "زجاجة", "علبة", "كيلو", "متر", "لتر", "صندوق" });

            // Description Label
            this.lblDescription.Text = "الوصف:";
            this.lblDescription.Location = new Point(400, 230);
            this.lblDescription.Size = new Size(80, 23);
            this.lblDescription.TextAlign = ContentAlignment.MiddleRight;

            // Description TextBox
            this.txtDescription.Location = new Point(50, 230);
            this.txtDescription.Size = new Size(330, 80);
            this.txtDescription.Font = new Font("Tahoma", 10F);
            this.txtDescription.Multiline = true;
            this.txtDescription.ScrollBars = ScrollBars.Vertical;

            // IsActive CheckBox
            this.chkIsActive.Text = "نشط";
            this.chkIsActive.Location = new Point(300, 330);
            this.chkIsActive.Size = new Size(80, 23);
            this.chkIsActive.Checked = true;
            this.chkIsActive.Font = new Font("Tahoma", 10F);

            // Save Button
            this.btnSave.Text = "حفظ";
            this.btnSave.Location = new Point(280, 400);
            this.btnSave.Size = new Size(100, 35);
            this.btnSave.BackColor = Color.FromArgb(76, 175, 80);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            this.btnSave.Click += BtnSave_Click;

            // Cancel Button
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.Location = new Point(150, 400);
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.BackColor = Color.FromArgb(244, 67, 54);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Tahoma", 10F);
            this.btnCancel.Click += BtnCancel_Click;

            // Add controls to form
            this.Controls.Add(this.lblProductName);
            this.Controls.Add(this.txtProductName);
            this.Controls.Add(this.lblCategory);
            this.Controls.Add(this.cmbCategory);
            this.Controls.Add(this.lblSalePrice);
            this.Controls.Add(this.txtSalePrice);
            this.Controls.Add(this.lblPurchasePrice);
            this.Controls.Add(this.txtPurchasePrice);
            this.Controls.Add(this.lblCurrentStock);
            this.Controls.Add(this.txtCurrentStock);
            this.Controls.Add(this.lblUnit);
            this.Controls.Add(this.cmbUnit);
            this.Controls.Add(this.lblDescription);
            this.Controls.Add(this.txtDescription);
            this.Controls.Add(this.chkIsActive);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnCancel);

            this.ResumeLayout(false);

            // Set default button
            this.AcceptButton = this.btnSave;
            this.CancelButton = this.btnCancel;
        }

        private void LoadProductData()
        {
            if (Product != null)
            {
                txtProductName.Text = Product.ProductName;
                cmbCategory.Text = Product.Category;
                txtSalePrice.Text = Product.SalePrice.ToString("F2");
                txtPurchasePrice.Text = Product.PurchasePrice.ToString("F2");
                txtCurrentStock.Text = Product.CurrentStock.ToString();
                cmbUnit.Text = Product.Unit;
                txtDescription.Text = Product.Description;
                chkIsActive.Checked = Product.IsActive;
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtProductName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المنتج", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtProductName.Focus();
                return false;
            }

            if (cmbCategory.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار فئة المنتج", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCategory.Focus();
                return false;
            }

            if (!decimal.TryParse(txtSalePrice.Text, out decimal salePrice) || salePrice < 0)
            {
                MessageBox.Show("يرجى إدخال سعر بيع صحيح", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSalePrice.Focus();
                return false;
            }

            if (!decimal.TryParse(txtPurchasePrice.Text, out decimal purchasePrice) || purchasePrice < 0)
            {
                MessageBox.Show("يرجى إدخال سعر شراء صحيح", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPurchasePrice.Focus();
                return false;
            }

            if (!decimal.TryParse(txtCurrentStock.Text, out decimal currentStock) || currentStock < 0)
            {
                MessageBox.Show("يرجى إدخال مخزون صحيح", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCurrentStock.Focus();
                return false;
            }

            if (cmbUnit.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار وحدة المنتج", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbUnit.Focus();
                return false;
            }

            return true;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                Product.ProductName = txtProductName.Text.Trim();
                Product.Category = cmbCategory.SelectedItem.ToString();
                Product.SalePrice = decimal.Parse(txtSalePrice.Text);
                Product.PurchasePrice = decimal.Parse(txtPurchasePrice.Text);
                Product.CurrentStock = decimal.Parse(txtCurrentStock.Text);
                Product.Unit = cmbUnit.SelectedItem.ToString();
                Product.Description = txtDescription.Text.Trim();
                Product.IsActive = chkIsActive.Checked;

                if (!isEditMode)
                {
                    Product.CreatedDate = DateTime.Now;
                    Product.CreatedBy = Services.AuthenticationService.CurrentUser?.Username ?? "System";
                }
                else
                {
                    Product.ModifiedDate = DateTime.Now;
                    Product.ModifiedBy = Services.AuthenticationService.CurrentUser?.Username ?? "System";
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
