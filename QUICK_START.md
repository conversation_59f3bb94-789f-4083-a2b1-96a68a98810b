# دليل البدء السريع - نظام السندات

## 🚀 **البدء السريع (3 خطوات)**

### **الخطوة 1: اختب<PERSON>ر الملف**
```bash
test_single_file.bat
```

### **الخطوة 2: بناء النظام**
```bash
build_single_file.bat
```

### **الخطوة 3: تشغيل النظام**
- شغل `ReceiptsSystemSingle.exe`
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

---

## 🔧 **حل مشكلة "ReceiptsForm could not be found"**

### **السبب:**
المشكلة كانت في المراجع المتداخلة بين الملفات المنفصلة.

### **الحل:**
تم إنشاء ملف واحد `ReceiptsSystemAll.cs` يحتوي على:
- ✅ جميع نماذج البيانات
- ✅ جميع النماذج والواجهات  
- ✅ خدمة المصادقة
- ✅ نقطة دخول التطبيق

### **المزايا:**
- 🚫 لا توجد مشاكل مراجع
- 🚫 لا توجد مشاكل namespace
- ✅ بناء سريع وبسيط
- ✅ ملف واحد سهل الإدارة

---

## 📋 **الميزات المتوفرة**

### **🔐 تسجيل الدخول:**
- نموذج آمن لتسجيل الدخول
- بيانات افتراضية: admin/admin123

### **📊 النموذج الرئيسي:**
- قوائم منظمة باللغة العربية
- شريط حالة يعرض المستخدم والتاريخ
- واجهة RTL كاملة

### **📋 سندات القبض:**
- عرض جميع سندات القبض
- بيانات تجريبية (3 سندات)
- تلوين حسب الحالة (مؤكد/مسودة)
- أزرار إضافة وإغلاق

### **💰 سندات الصرف:**
- عرض جميع سندات الصرف
- أنواع مختلفة (رواتب، مصاريف، إيجارات)
- بيانات تجريبية (3 سندات)
- تلوين حسب الحالة

### **📊 كشف حساب العميل:**
- اختيار العميل من قائمة
- تبويب منفصل للفواتير والمدفوعات
- ملخص العميل والرصيد
- تلوين حسب حالة الدفع والاستحقاق

---

## 📊 **البيانات التجريبية**

### **العملاء (3 عملاء):**
1. **أحمد محمد علي (C001)** - رصيد: 5,000 ر.س
2. **فاطمة عبدالله (C002)** - رصيد: -2,500 ر.س  
3. **محمد سعد الدين (C003)** - رصيد: 0 ر.س

### **سندات القبض (3 سندات):**
1. **R001** - أحمد محمد علي - 5,000 ر.س - نقدي - مؤكد
2. **R002** - فاطمة عبدالله - 2,500 ر.س - شيك - مؤكد
3. **R003** - محمد سعد الدين - 1,200 ر.س - تحويل بنكي - مسودة

### **سندات الصرف (3 سندات):**
1. **P001** - رواتب - أحمد محمد - 8,000 ر.س - مؤكد
2. **P002** - مصاريف تشغيلية - شركة الكهرباء - 1,500 ر.س - مؤكد
3. **P003** - إيجارات - مالك العقار - 12,000 ر.س - مسودة

### **الفواتير (3 فواتير):**
1. **INV001** - أحمد محمد علي - 8,000 ر.س (مدفوع: 3,000) - جزئي
2. **INV002** - أحمد محمد علي - 5,500 ر.س (مدفوع: 5,500) - مدفوع
3. **INV003** - فاطمة عبدالله - 3,200 ر.س (مدفوع: 700) - جزئي

---

## 🎯 **كيفية الاستخدام**

### **1. تسجيل الدخول:**
- شغل البرنامج
- أدخل: admin / admin123
- اضغط "دخول"

### **2. سندات القبض:**
- من القائمة: السندات > سندات القبض
- ستظهر قائمة بجميع السندات
- يمكن الضغط على "إضافة سند" (قيد التطوير)

### **3. سندات الصرف:**
- من القائمة: السندات > سندات الصرف
- ستظهر قائمة بجميع سندات الصرف
- يمكن الضغط على "إضافة سند" (قيد التطوير)

### **4. كشف حساب العميل:**
- من القائمة: التقارير > كشف حساب العميل
- اختر العميل من القائمة
- اضغط "إنشاء الكشف"
- راجع الفواتير في تبويب "الفواتير"
- راجع المدفوعات في تبويب "المدفوعات"

---

## 🔧 **استكشاف الأخطاء**

### **إذا فشل البناء:**
```bash
# تحقق من الملف أولاً
test_single_file.bat

# إذا كان الملف سليم، جرب:
build_single_file.bat
```

### **إذا لم يعمل مترجم C#:**
1. افتح **Developer Command Prompt for Visual Studio**
2. أو ثبت **.NET Framework SDK**
3. أو استخدم **Visual Studio** مباشرة

### **إذا ظهرت أخطاء في التشغيل:**
- تأكد من وجود ملف `.config`
- تأكد من إصدار .NET Framework
- جرب تشغيل البرنامج كمدير

---

## 📁 **الملفات المهمة**

### **الملفات الأساسية:**
- `ReceiptsSystemAll.cs` - الكود المصدري الكامل
- `build_single_file.bat` - ملف البناء
- `test_single_file.bat` - ملف الاختبار

### **الملفات المنتجة:**
- `ReceiptsSystemSingle.exe` - البرنامج النهائي
- `ReceiptsSystemSingle.exe.config` - ملف الإعداد

### **ملفات التوثيق:**
- `QUICK_START.md` - هذا الدليل
- `README_COMPLETE_SYSTEM.md` - الدليل الشامل

---

## 🎉 **النجاح!**

إذا اتبعت الخطوات أعلاه، ستحصل على:
- ✅ نظام سندات كامل وعملي
- ✅ واجهة عربية جميلة
- ✅ بيانات تجريبية غنية
- ✅ تلوين تلقائي للبيانات
- ✅ نظام تسجيل دخول آمن

**استمتع بالنظام!** 🚀
