using System;
using System.Drawing;
using System.Windows.Forms;
using AccountingSystem.Models;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج إضافة/تعديل سند الصرف
    /// </summary>
    public partial class AddEditPaymentForm : Form
    {
        private DateTimePicker dtpPaymentDate;
        private ComboBox cmbPaymentType;
        private TextBox txtBeneficiary;
        private TextBox txtAmount;
        private ComboBox cmbPaymentMethod;
        private TextBox txtReferenceNumber;
        private TextBox txtDescription;
        private ComboBox cmbStatus;
        private Button btnSave;
        private Button btnCancel;
        
        private Label lblPaymentDate;
        private Label lblPaymentType;
        private Label lblBeneficiary;
        private Label lblAmount;
        private Label lblPaymentMethod;
        private Label lblReferenceNumber;
        private Label lblDescription;
        private Label lblStatus;
        
        public Payment Payment { get; private set; }
        private bool isEditMode;
        
        public AddEditPaymentForm(Payment payment = null)
        {
            InitializeComponent();
            
            if (payment != null)
            {
                isEditMode = true;
                Payment = payment;
                LoadPaymentData();
                this.Text = "تعديل سند الصرف";
            }
            else
            {
                isEditMode = false;
                Payment = new Payment();
                this.Text = "إضافة سند صرف جديد";
            }
        }
        
        private void InitializeComponent()
        {
            this.dtpPaymentDate = new DateTimePicker();
            this.cmbPaymentType = new ComboBox();
            this.txtBeneficiary = new TextBox();
            this.txtAmount = new TextBox();
            this.cmbPaymentMethod = new ComboBox();
            this.txtReferenceNumber = new TextBox();
            this.txtDescription = new TextBox();
            this.cmbStatus = new ComboBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            
            this.lblPaymentDate = new Label();
            this.lblPaymentType = new Label();
            this.lblBeneficiary = new Label();
            this.lblAmount = new Label();
            this.lblPaymentMethod = new Label();
            this.lblReferenceNumber = new Label();
            this.lblDescription = new Label();
            this.lblStatus = new Label();
            
            this.SuspendLayout();
            
            // Form
            this.Size = new Size(500, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Payment Date
            this.lblPaymentDate.Text = "تاريخ الصرف:";
            this.lblPaymentDate.Location = new Point(400, 30);
            this.lblPaymentDate.Size = new Size(80, 23);
            this.lblPaymentDate.TextAlign = ContentAlignment.MiddleRight;
            
            this.dtpPaymentDate.Location = new Point(50, 30);
            this.dtpPaymentDate.Size = new Size(330, 23);
            this.dtpPaymentDate.Format = DateTimePickerFormat.Short;
            this.dtpPaymentDate.Value = DateTime.Now;
            
            // Payment Type
            this.lblPaymentType.Text = "نوع الصرف:";
            this.lblPaymentType.Location = new Point(400, 70);
            this.lblPaymentType.Size = new Size(80, 23);
            this.lblPaymentType.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbPaymentType.Location = new Point(50, 70);
            this.cmbPaymentType.Size = new Size(330, 23);
            this.cmbPaymentType.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbPaymentType.Items.AddRange(new string[] { "مصاريف تشغيلية", "رواتب", "إيجارات", "مشتريات", "أخرى" });
            this.cmbPaymentType.SelectedIndex = 0;
            
            // Beneficiary
            this.lblBeneficiary.Text = "المستفيد:";
            this.lblBeneficiary.Location = new Point(400, 110);
            this.lblBeneficiary.Size = new Size(80, 23);
            this.lblBeneficiary.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtBeneficiary.Location = new Point(50, 110);
            this.txtBeneficiary.Size = new Size(330, 23);
            this.txtBeneficiary.Font = new Font("Tahoma", 10F);
            
            // Amount
            this.lblAmount.Text = "المبلغ:";
            this.lblAmount.Location = new Point(400, 150);
            this.lblAmount.Size = new Size(80, 23);
            this.lblAmount.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtAmount.Location = new Point(50, 150);
            this.txtAmount.Size = new Size(200, 23);
            this.txtAmount.TextAlign = HorizontalAlignment.Right;
            this.txtAmount.Font = new Font("Tahoma", 10F);
            
            // Payment Method
            this.lblPaymentMethod.Text = "طريقة الدفع:";
            this.lblPaymentMethod.Location = new Point(400, 190);
            this.lblPaymentMethod.Size = new Size(80, 23);
            this.lblPaymentMethod.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbPaymentMethod.Location = new Point(50, 190);
            this.cmbPaymentMethod.Size = new Size(200, 23);
            this.cmbPaymentMethod.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbPaymentMethod.Items.AddRange(new string[] { "نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان", "بطاقة مدين" });
            this.cmbPaymentMethod.SelectedIndex = 0;
            
            // Reference Number
            this.lblReferenceNumber.Text = "رقم المرجع:";
            this.lblReferenceNumber.Location = new Point(400, 230);
            this.lblReferenceNumber.Size = new Size(80, 23);
            this.lblReferenceNumber.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtReferenceNumber.Location = new Point(50, 230);
            this.txtReferenceNumber.Size = new Size(200, 23);
            this.txtReferenceNumber.Font = new Font("Tahoma", 10F);
            
            // Description
            this.lblDescription.Text = "الوصف:";
            this.lblDescription.Location = new Point(400, 270);
            this.lblDescription.Size = new Size(80, 23);
            this.lblDescription.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtDescription.Location = new Point(50, 270);
            this.txtDescription.Size = new Size(330, 80);
            this.txtDescription.Multiline = true;
            this.txtDescription.ScrollBars = ScrollBars.Vertical;
            this.txtDescription.Font = new Font("Tahoma", 10F);
            
            // Status
            this.lblStatus.Text = "الحالة:";
            this.lblStatus.Location = new Point(400, 370);
            this.lblStatus.Size = new Size(80, 23);
            this.lblStatus.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbStatus.Location = new Point(50, 370);
            this.cmbStatus.Size = new Size(150, 23);
            this.cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbStatus.Items.AddRange(new string[] { "مسودة", "مؤكد", "ملغي" });
            this.cmbStatus.SelectedIndex = 0;
            
            // Save Button
            this.btnSave.Text = "حفظ";
            this.btnSave.Location = new Point(280, 420);
            this.btnSave.Size = new Size(100, 35);
            this.btnSave.BackColor = Color.FromArgb(76, 175, 80);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            this.btnSave.Click += BtnSave_Click;
            
            // Cancel Button
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.Location = new Point(150, 420);
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.BackColor = Color.FromArgb(244, 67, 54);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Tahoma", 10F);
            this.btnCancel.Click += BtnCancel_Click;
            
            // Add controls to form
            this.Controls.Add(this.lblPaymentDate);
            this.Controls.Add(this.dtpPaymentDate);
            this.Controls.Add(this.lblPaymentType);
            this.Controls.Add(this.cmbPaymentType);
            this.Controls.Add(this.lblBeneficiary);
            this.Controls.Add(this.txtBeneficiary);
            this.Controls.Add(this.lblAmount);
            this.Controls.Add(this.txtAmount);
            this.Controls.Add(this.lblPaymentMethod);
            this.Controls.Add(this.cmbPaymentMethod);
            this.Controls.Add(this.lblReferenceNumber);
            this.Controls.Add(this.txtReferenceNumber);
            this.Controls.Add(this.lblDescription);
            this.Controls.Add(this.txtDescription);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.cmbStatus);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnCancel);
            
            this.ResumeLayout(false);
            
            // Set default button
            this.AcceptButton = this.btnSave;
            this.CancelButton = this.btnCancel;
        }
        
        private void LoadPaymentData()
        {
            if (Payment != null)
            {
                dtpPaymentDate.Value = Payment.PaymentDate;
                
                // تحديد نوع الصرف
                var typeIndex = cmbPaymentType.Items.IndexOf(Payment.PaymentType);
                if (typeIndex >= 0)
                    cmbPaymentType.SelectedIndex = typeIndex;
                
                txtBeneficiary.Text = Payment.Beneficiary;
                txtAmount.Text = Payment.Amount.ToString("F2");
                
                // تحديد طريقة الدفع
                var methodIndex = cmbPaymentMethod.Items.IndexOf(Payment.PaymentMethod);
                if (methodIndex >= 0)
                    cmbPaymentMethod.SelectedIndex = methodIndex;
                
                txtReferenceNumber.Text = Payment.ReferenceNumber;
                txtDescription.Text = Payment.Description;
                
                // تحديد الحالة
                var statusIndex = cmbStatus.Items.IndexOf(Payment.Status);
                if (statusIndex >= 0)
                    cmbStatus.SelectedIndex = statusIndex;
            }
        }
        
        private bool ValidateInput()
        {
            if (cmbPaymentType.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار نوع الصرف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbPaymentType.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(txtBeneficiary.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستفيد", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtBeneficiary.Focus();
                return false;
            }
            
            if (!decimal.TryParse(txtAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAmount.Focus();
                return false;
            }
            
            if (cmbPaymentMethod.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار طريقة الدفع", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbPaymentMethod.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(txtReferenceNumber.Text))
            {
                MessageBox.Show("يرجى إدخال رقم المرجع", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtReferenceNumber.Focus();
                return false;
            }
            
            return true;
        }
        
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;
                
                Payment.PaymentDate = dtpPaymentDate.Value;
                Payment.PaymentType = cmbPaymentType.SelectedItem.ToString();
                Payment.Beneficiary = txtBeneficiary.Text.Trim();
                Payment.Amount = decimal.Parse(txtAmount.Text);
                Payment.PaymentMethod = cmbPaymentMethod.SelectedItem.ToString();
                Payment.ReferenceNumber = txtReferenceNumber.Text.Trim();
                Payment.Description = txtDescription.Text.Trim();
                Payment.Status = cmbStatus.SelectedItem.ToString();
                
                if (!isEditMode)
                {
                    Payment.CreatedDate = DateTime.Now;
                    Payment.CreatedBy = Services.AuthenticationService.CurrentUser?.Username ?? "System";
                }
                else
                {
                    Payment.ModifiedDate = DateTime.Now;
                    Payment.ModifiedBy = Services.AuthenticationService.CurrentUser?.Username ?? "System";
                }
                
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السند: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
