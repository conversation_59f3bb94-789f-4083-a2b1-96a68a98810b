using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;
using System.IO;

namespace IntegratedInvoiceSystem
{
    // نماذج البيانات الشاملة
    public class Customer
    {
        public int Id { get; set; }
        public string CustomerCode { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public decimal CurrentBalance { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public string TaxNumber { get; set; }
        public string ContactPerson { get; set; }
    }

    public class Product
    {
        public int Id { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string Category { get; set; }
        public decimal UnitPrice { get; set; }
        public int StockQuantity { get; set; }
        public string Unit { get; set; }
        public bool IsActive { get; set; }
        public decimal CostPrice { get; set; }
        public string Description { get; set; }
        public string Barcode { get; set; }
    }

    public class Invoice
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public int CustomerId { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public string PaymentTerms { get; set; }
        public string DeliveryAddress { get; set; }
        public List<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();

        public decimal RemainingAmount => TotalAmount - PaidAmount;
        public bool IsOverdue => DueDate < DateTime.Now && Status != "مدفوعة" && Status != "ملغية";
        public int DaysOverdue => IsOverdue ? (DateTime.Now - DueDate).Days : 0;
        public decimal ProfitAmount => Items.Sum(i => (i.UnitPrice - (DataService.Products.FirstOrDefault(p => p.Id == i.ProductId)?.CostPrice ?? 0)) * i.Quantity);
    }

    public class InvoiceItem
    {
        public int Id { get; set; }
        public int InvoiceId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountPercent { get; set; }
        public decimal TotalPrice => (Quantity * UnitPrice) * (1 - DiscountPercent / 100);
        public string Notes { get; set; }
    }

    public class Receipt
    {
        public int Id { get; set; }
        public string ReceiptNumber { get; set; }
        public DateTime ReceiptDate { get; set; }
        public int CustomerId { get; set; }
        public int? InvoiceId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string ReferenceNumber { get; set; }
        public string Notes { get; set; }
        public string Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public string BankName { get; set; }
        public DateTime? ClearanceDate { get; set; }
    }

    public class Company
    {
        public string Name { get; set; } = "شركة الفواتير المتكاملة";
        public string Address { get; set; } = "الرياض، المملكة العربية السعودية";
        public string Phone { get; set; } = "+966 11 123 4567";
        public string Email { get; set; } = "<EMAIL>";
        public string TaxNumber { get; set; } = "***************";
        public string CommercialRegister { get; set; } = "**********";
        public string Logo { get; set; } = "";
        public decimal TaxRate { get; set; } = 0.15m;
    }

    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string FullName { get; set; }
        public string Role { get; set; }
        public bool IsActive { get; set; }
        public DateTime LastLogin { get; set; }
        public List<string> Permissions { get; set; } = new List<string>();
    }

    // خدمة البيانات المتكاملة
    public static class DataService
    {
        public static List<Customer> Customers { get; set; }
        public static List<Product> Products { get; set; }
        public static List<Invoice> Invoices { get; set; }
        public static List<Receipt> Receipts { get; set; }
        public static List<User> Users { get; set; }
        public static Company CompanyInfo { get; set; }

        static DataService()
        {
            LoadSampleData();
        }

        private static void LoadSampleData()
        {
            // معلومات الشركة
            CompanyInfo = new Company();

            // المستخدمين
            Users = new List<User>
            {
                new User { Id = 1, Username = "admin", Password = "admin123", FullName = "مدير النظام", Role = "مدير", IsActive = true, LastLogin = DateTime.Now,
                    Permissions = new List<string> { "إدارة الفواتير", "إدارة العملاء", "إدارة المنتجات", "التقارير", "الإعدادات" } },
                new User { Id = 2, Username = "user", Password = "user123", FullName = "موظف المبيعات", Role = "موظف", IsActive = true, LastLogin = DateTime.Now.AddDays(-1),
                    Permissions = new List<string> { "إدارة الفواتير", "إدارة العملاء" } }
            };

            // العملاء المحسنين
            Customers = new List<Customer>
            {
                new Customer { Id = 1, CustomerCode = "C001", CustomerName = "أحمد محمد علي", Phone = "0501234567", Email = "<EMAIL>",
                    Address = "الرياض، حي النخيل، شارع الملك فهد", CurrentBalance = 15000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-30),
                    TaxNumber = "***************", ContactPerson = "أحمد محمد" },
                new Customer { Id = 2, CustomerCode = "C002", CustomerName = "شركة الأعمال المتقدمة", Phone = "0507654321", Email = "<EMAIL>",
                    Address = "جدة، حي الصفا، طريق الملك عبدالعزيز", CurrentBalance = -2500, IsActive = true, CreatedDate = DateTime.Now.AddDays(-25),
                    TaxNumber = "234567890123456", ContactPerson = "فاطمة عبدالله" },
                new Customer { Id = 3, CustomerCode = "C003", CustomerName = "مؤسسة التقنية الحديثة", Phone = "0551122334", Email = "<EMAIL>",
                    Address = "الدمام، حي الفيصلية، شارع الأمير محمد", CurrentBalance = 8000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-20),
                    TaxNumber = "345678901234567", ContactPerson = "محمد سعد الدين" },
                new Customer { Id = 4, CustomerCode = "C004", CustomerName = "مكتب الاستشارات الإدارية", Phone = "0554433221", Email = "<EMAIL>",
                    Address = "مكة، حي العزيزية، طريق الحرم", CurrentBalance = 0, IsActive = true, CreatedDate = DateTime.Now.AddDays(-15),
                    TaxNumber = "456789012345678", ContactPerson = "نورا أحمد" },
                new Customer { Id = 5, CustomerCode = "C005", CustomerName = "شركة المقاولات الكبرى", Phone = "0556677889", Email = "<EMAIL>",
                    Address = "المدينة، حي قباء، شارع النور", CurrentBalance = 25000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-10),
                    TaxNumber = "567890123456789", ContactPerson = "خالد العتيبي" },
                new Customer { Id = 6, CustomerCode = "C006", CustomerName = "معهد التدريب المهني", Phone = "0559988776", Email = "<EMAIL>",
                    Address = "الطائف، حي الشفا، طريق الهدا", CurrentBalance = 12000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-5),
                    TaxNumber = "678901234567890", ContactPerson = "سارة الأحمد" },
                new Customer { Id = 7, CustomerCode = "C007", CustomerName = "مجموعة الخدمات اللوجستية", Phone = "0552233445", Email = "<EMAIL>",
                    Address = "الخبر، حي الراكة، شارع الملك خالد", CurrentBalance = 5500, IsActive = true, CreatedDate = DateTime.Now.AddDays(-3),
                    TaxNumber = "789012345678901", ContactPerson = "عبدالله الزهراني" },
                new Customer { Id = 8, CustomerCode = "C008", CustomerName = "شركة التسويق الرقمي", Phone = "0558899001", Email = "<EMAIL>",
                    Address = "بريدة، حي الصالحية، طريق الملك عبدالله", CurrentBalance = -1200, IsActive = true, CreatedDate = DateTime.Now.AddDays(-1),
                    TaxNumber = "890123456789012", ContactPerson = "ريم السعيد" }
            };

            // المنتجات المحسنة
            Products = new List<Product>
            {
                new Product { Id = 1, ProductCode = "P001", ProductName = "جهاز كمبيوتر محمول HP", Category = "إلكترونيات",
                    UnitPrice = 3500, CostPrice = 2800, StockQuantity = 25, Unit = "جهاز", IsActive = true,
                    Description = "جهاز كمبيوتر محمول HP بمعالج Intel Core i7", Barcode = "1234567890123" },
                new Product { Id = 2, ProductCode = "P002", ProductName = "طابعة ليزر Canon", Category = "إلكترونيات",
                    UnitPrice = 800, CostPrice = 600, StockQuantity = 15, Unit = "جهاز", IsActive = true,
                    Description = "طابعة ليزر ملونة عالية الجودة", Barcode = "2345678901234" },
                new Product { Id = 3, ProductCode = "P003", ProductName = "خدمة استشارية تقنية", Category = "خدمات",
                    UnitPrice = 500, CostPrice = 200, StockQuantity = 999, Unit = "ساعة", IsActive = true,
                    Description = "استشارات تقنية متخصصة في تطوير الأنظمة", Barcode = "3456789012345" },
                new Product { Id = 4, ProductCode = "P004", ProductName = "مواد خام بلاستيكية", Category = "مواد",
                    UnitPrice = 150, CostPrice = 100, StockQuantity = 200, Unit = "كيلو", IsActive = true,
                    Description = "مواد خام بلاستيكية عالية الجودة", Barcode = "4567890123456" },
                new Product { Id = 5, ProductCode = "P005", ProductName = "أثاث مكتبي متكامل", Category = "مكتبية",
                    UnitPrice = 2500, CostPrice = 1800, StockQuantity = 50, Unit = "طقم", IsActive = true,
                    Description = "طقم أثاث مكتبي متكامل يشمل مكتب وكرسي", Barcode = "5678901234567" },
                new Product { Id = 6, ProductCode = "P006", ProductName = "رخصة برمجيات Microsoft", Category = "تقنية",
                    UnitPrice = 1200, CostPrice = 800, StockQuantity = 100, Unit = "رخصة", IsActive = true,
                    Description = "رخصة برمجيات Microsoft Office Professional", Barcode = "6789012345678" },
                new Product { Id = 7, ProductCode = "P007", ProductName = "خدمة صيانة شاملة", Category = "خدمات",
                    UnitPrice = 300, CostPrice = 150, StockQuantity = 999, Unit = "زيارة", IsActive = true,
                    Description = "خدمة صيانة شاملة للأجهزة والمعدات", Barcode = "7890123456789" },
                new Product { Id = 8, ProductCode = "P008", ProductName = "شاشة عرض LED", Category = "إلكترونيات",
                    UnitPrice = 1800, CostPrice = 1200, StockQuantity = 30, Unit = "جهاز", IsActive = true,
                    Description = "شاشة عرض LED عالية الدقة 32 بوصة", Barcode = "8901234567890" },
                new Product { Id = 9, ProductCode = "P009", ProductName = "دورة تدريبية متخصصة", Category = "تدريب",
                    UnitPrice = 2000, CostPrice = 800, StockQuantity = 999, Unit = "دورة", IsActive = true,
                    Description = "دورة تدريبية متخصصة في إدارة المشاريع", Barcode = "9012345678901" },
                new Product { Id = 10, ProductCode = "P010", ProductName = "نظام أمان متكامل", Category = "أمان",
                    UnitPrice = 5000, CostPrice = 3500, StockQuantity = 20, Unit = "نظام", IsActive = true,
                    Description = "نظام أمان متكامل مع كاميرات وأجهزة إنذار", Barcode = "0123456789012" }
            };

            // الفواتير المحسنة
            Invoices = new List<Invoice>
            {
                new Invoice
                {
                    Id = 1, InvoiceNumber = "INV-2024-001", InvoiceDate = DateTime.Now.AddDays(-45),
                    DueDate = DateTime.Now.AddDays(-15), CustomerId = 1, SubTotal = 10000, TaxAmount = 1500,
                    TotalAmount = 11500, PaidAmount = 5000, Status = "مدفوعة جزئياً",
                    Notes = "فاتورة مبيعات أجهزة كمبيوتر للمكتب الرئيسي", CreatedDate = DateTime.Now.AddDays(-45), CreatedBy = "admin",
                    PaymentTerms = "30 يوم", DeliveryAddress = "الرياض، حي النخيل",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 1, ProductId = 1, ProductName = "جهاز كمبيوتر محمول HP", Quantity = 2, UnitPrice = 3500, DiscountPercent = 5, Notes = "خصم كمية" },
                        new InvoiceItem { Id = 2, ProductId = 2, ProductName = "طابعة ليزر Canon", Quantity = 4, UnitPrice = 800, DiscountPercent = 0, Notes = "" }
                    }
                },
                new Invoice
                {
                    Id = 2, InvoiceNumber = "INV-2024-002", InvoiceDate = DateTime.Now.AddDays(-30),
                    DueDate = DateTime.Now.AddDays(-10), CustomerId = 2, SubTotal = 5000, TaxAmount = 750,
                    TotalAmount = 5750, PaidAmount = 5750, Status = "مدفوعة",
                    Notes = "فاتورة خدمات استشارية لتطوير النظام", CreatedDate = DateTime.Now.AddDays(-30), CreatedBy = "admin",
                    PaymentTerms = "فوري", DeliveryAddress = "جدة، حي الصفا",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 3, ProductId = 3, ProductName = "خدمة استشارية تقنية", Quantity = 10, UnitPrice = 500, DiscountPercent = 0, Notes = "استشارات تطوير" }
                    }
                },
                new Invoice
                {
                    Id = 3, InvoiceNumber = "INV-2024-003", InvoiceDate = DateTime.Now.AddDays(-25),
                    DueDate = DateTime.Now.AddDays(-5), CustomerId = 3, SubTotal = 8000, TaxAmount = 1200,
                    TotalAmount = 9200, PaidAmount = 0, Status = "متأخرة",
                    Notes = "فاتورة مواد خام للإنتاج", CreatedDate = DateTime.Now.AddDays(-25), CreatedBy = "admin",
                    PaymentTerms = "15 يوم", DeliveryAddress = "الدمام، حي الفيصلية",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 4, ProductId = 4, ProductName = "مواد خام بلاستيكية", Quantity = 50, UnitPrice = 150, DiscountPercent = 6.25m, Notes = "خصم كمية كبيرة" }
                    }
                },
                new Invoice
                {
                    Id = 4, InvoiceNumber = "INV-2024-004", InvoiceDate = DateTime.Now.AddDays(-20),
                    DueDate = DateTime.Now.AddDays(10), CustomerId = 4, SubTotal = 12000, TaxAmount = 1800,
                    TotalAmount = 13800, PaidAmount = 0, Status = "مؤكدة",
                    Notes = "فاتورة أثاث مكتبي للمكتب الجديد", CreatedDate = DateTime.Now.AddDays(-20), CreatedBy = "admin",
                    PaymentTerms = "45 يوم", DeliveryAddress = "مكة، حي العزيزية",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 5, ProductId = 5, ProductName = "أثاث مكتبي متكامل", Quantity = 5, UnitPrice = 2500, DiscountPercent = 4, Notes = "خصم عميل مميز" }
                    }
                },
                new Invoice
                {
                    Id = 5, InvoiceNumber = "INV-2024-005", InvoiceDate = DateTime.Now.AddDays(-15),
                    DueDate = DateTime.Now.AddDays(15), CustomerId = 5, SubTotal = 20000, TaxAmount = 3000,
                    TotalAmount = 23000, PaidAmount = 10000, Status = "مدفوعة جزئياً",
                    Notes = "فاتورة مشروع تطوير متكامل", CreatedDate = DateTime.Now.AddDays(-15), CreatedBy = "admin",
                    PaymentTerms = "60 يوم", DeliveryAddress = "المدينة، حي قباء",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 6, ProductId = 6, ProductName = "رخصة برمجيات Microsoft", Quantity = 15, UnitPrice = 1200, DiscountPercent = 10, Notes = "خصم حجم" },
                        new InvoiceItem { Id = 7, ProductId = 3, ProductName = "خدمة استشارية تقنية", Quantity = 10, UnitPrice = 500, DiscountPercent = 0, Notes = "استشارات تنفيذ" }
                    }
                },
                new Invoice
                {
                    Id = 6, InvoiceNumber = "INV-2024-006", InvoiceDate = DateTime.Now.AddDays(-10),
                    DueDate = DateTime.Now.AddDays(20), CustomerId = 1, SubTotal = 3000, TaxAmount = 450,
                    TotalAmount = 3450, PaidAmount = 0, Status = "مسودة",
                    Notes = "فاتورة خدمات صيانة دورية", CreatedDate = DateTime.Now.AddDays(-10), CreatedBy = "admin",
                    PaymentTerms = "30 يوم", DeliveryAddress = "الرياض، حي النخيل",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 8, ProductId = 7, ProductName = "خدمة صيانة شاملة", Quantity = 10, UnitPrice = 300, DiscountPercent = 0, Notes = "صيانة دورية" }
                    }
                },
                new Invoice
                {
                    Id = 7, InvoiceNumber = "INV-2024-007", InvoiceDate = DateTime.Now.AddDays(-60),
                    DueDate = DateTime.Now.AddDays(-30), CustomerId = 2, SubTotal = 7500, TaxAmount = 1125,
                    TotalAmount = 8625, PaidAmount = 0, Status = "متأخرة",
                    Notes = "فاتورة دورات تدريبية للموظفين", CreatedDate = DateTime.Now.AddDays(-60), CreatedBy = "admin",
                    PaymentTerms = "30 يوم", DeliveryAddress = "جدة، حي الصفا",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 9, ProductId = 9, ProductName = "دورة تدريبية متخصصة", Quantity = 4, UnitPrice = 2000, DiscountPercent = 6.67m, Notes = "خصم مجموعة" }
                    }
                },
                new Invoice
                {
                    Id = 8, InvoiceNumber = "INV-2024-008", InvoiceDate = DateTime.Now.AddDays(-5),
                    DueDate = DateTime.Now.AddDays(25), CustomerId = 6, SubTotal = 15600, TaxAmount = 2340,
                    TotalAmount = 17940, PaidAmount = 17940, Status = "مدفوعة",
                    Notes = "فاتورة معدات تدريبية متكاملة", CreatedDate = DateTime.Now.AddDays(-5), CreatedBy = "admin",
                    PaymentTerms = "فوري", DeliveryAddress = "الطائف، حي الشفا",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 10, ProductId = 1, ProductName = "جهاز كمبيوتر محمول HP", Quantity = 4, UnitPrice = 3500, DiscountPercent = 10, Notes = "خصم كمية" },
                        new InvoiceItem { Id = 11, ProductId = 8, ProductName = "شاشة عرض LED", Quantity = 2, UnitPrice = 1800, DiscountPercent = 0, Notes = "للقاعات التدريبية" }
                    }
                },
                new Invoice
                {
                    Id = 9, InvoiceNumber = "INV-2024-009", InvoiceDate = DateTime.Now.AddDays(-3),
                    DueDate = DateTime.Now.AddDays(27), CustomerId = 7, SubTotal = 5000, TaxAmount = 750,
                    TotalAmount = 5750, PaidAmount = 2000, Status = "مدفوعة جزئياً",
                    Notes = "فاتورة نظام أمان للمستودعات", CreatedDate = DateTime.Now.AddDays(-3), CreatedBy = "admin",
                    PaymentTerms = "30 يوم", DeliveryAddress = "الخبر، حي الراكة",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 12, ProductId = 10, ProductName = "نظام أمان متكامل", Quantity = 1, UnitPrice = 5000, DiscountPercent = 0, Notes = "تركيب وتشغيل" }
                    }
                },
                new Invoice
                {
                    Id = 10, InvoiceNumber = "INV-2024-010", InvoiceDate = DateTime.Now.AddDays(-1),
                    DueDate = DateTime.Now.AddDays(29), CustomerId = 8, SubTotal = 4000, TaxAmount = 600,
                    TotalAmount = 4600, PaidAmount = 0, Status = "مؤكدة",
                    Notes = "فاتورة خدمات تسويق رقمي", CreatedDate = DateTime.Now.AddDays(-1), CreatedBy = "admin",
                    PaymentTerms = "30 يوم", DeliveryAddress = "بريدة، حي الصالحية",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 13, ProductId = 3, ProductName = "خدمة استشارية تقنية", Quantity = 8, UnitPrice = 500, DiscountPercent = 0, Notes = "استشارات تسويق" }
                    }
                }
            };

            // السندات المحسنة
            Receipts = new List<Receipt>
            {
                new Receipt { Id = 1, ReceiptNumber = "R001", ReceiptDate = DateTime.Now.AddDays(-28), CustomerId = 1, InvoiceId = 1,
                    Amount = 5000, PaymentMethod = "نقدي", ReferenceNumber = "CASH001", Status = "مؤكد",
                    CreatedDate = DateTime.Now.AddDays(-28), CreatedBy = "admin", Notes = "دفعة أولى", BankName = "", ClearanceDate = DateTime.Now.AddDays(-28) },
                new Receipt { Id = 2, ReceiptNumber = "R002", ReceiptDate = DateTime.Now.AddDays(-18), CustomerId = 2, InvoiceId = 2,
                    Amount = 5750, PaymentMethod = "تحويل بنكي", ReferenceNumber = "TRF002", Status = "مؤكد",
                    CreatedDate = DateTime.Now.AddDays(-18), CreatedBy = "admin", Notes = "دفع كامل", BankName = "البنك الأهلي", ClearanceDate = DateTime.Now.AddDays(-17) },
                new Receipt { Id = 3, ReceiptNumber = "R003", ReceiptDate = DateTime.Now.AddDays(-10), CustomerId = 5, InvoiceId = 5,
                    Amount = 10000, PaymentMethod = "شيك", ReferenceNumber = "CHK003", Status = "مؤكد",
                    CreatedDate = DateTime.Now.AddDays(-10), CreatedBy = "admin", Notes = "دفعة أولى", BankName = "بنك الراجحي", ClearanceDate = DateTime.Now.AddDays(-8) },
                new Receipt { Id = 4, ReceiptNumber = "R004", ReceiptDate = DateTime.Now.AddDays(-3), CustomerId = 6, InvoiceId = 8,
                    Amount = 17940, PaymentMethod = "تحويل بنكي", ReferenceNumber = "TRF004", Status = "مؤكد",
                    CreatedDate = DateTime.Now.AddDays(-3), CreatedBy = "admin", Notes = "دفع كامل", BankName = "بنك سامبا", ClearanceDate = DateTime.Now.AddDays(-2) },
                new Receipt { Id = 5, ReceiptNumber = "R005", ReceiptDate = DateTime.Now.AddDays(-1), CustomerId = 1, InvoiceId = null,
                    Amount = 2000, PaymentMethod = "نقدي", ReferenceNumber = "CASH005", Status = "مسودة",
                    CreatedDate = DateTime.Now.AddDays(-1), CreatedBy = "admin", Notes = "دفعة على الحساب", BankName = "", ClearanceDate = null },
                new Receipt { Id = 6, ReceiptNumber = "R006", ReceiptDate = DateTime.Now.AddDays(-2), CustomerId = 7, InvoiceId = 9,
                    Amount = 2000, PaymentMethod = "بطاقة ائتمان", ReferenceNumber = "CARD006", Status = "مؤكد",
                    CreatedDate = DateTime.Now.AddDays(-2), CreatedBy = "admin", Notes = "دفعة جزئية", BankName = "بنك الإنماء", ClearanceDate = DateTime.Now.AddDays(-2) },
                new Receipt { Id = 7, ReceiptNumber = "R007", ReceiptDate = DateTime.Now.AddHours(-6), CustomerId = 3, InvoiceId = null,
                    Amount = 1000, PaymentMethod = "نقدي", ReferenceNumber = "CASH007", Status = "مؤكد",
                    CreatedDate = DateTime.Now.AddHours(-6), CreatedBy = "admin", Notes = "دفعة على الحساب", BankName = "", ClearanceDate = DateTime.Now.AddHours(-6) }
            };
        }

        // دوال إدارة البيانات
        public static void AddInvoice(Invoice invoice)
        {
            invoice.Id = Invoices.Count > 0 ? Invoices.Max(i => i.Id) + 1 : 1;
            invoice.InvoiceNumber = $"INV-2024-{invoice.Id:000}";
            invoice.CreatedDate = DateTime.Now;
            invoice.CreatedBy = AuthService.CurrentUser;

            // تحديث معرفات الأصناف
            int itemId = 1;
            if (Invoices.Any())
            {
                itemId = Invoices.SelectMany(i => i.Items).Max(item => item.Id) + 1;
            }

            foreach (var item in invoice.Items)
            {
                item.Id = itemId++;
                item.InvoiceId = invoice.Id;
            }

            Invoices.Add(invoice);
            UpdateCustomerBalance(invoice.CustomerId);
        }

        public static void UpdateInvoice(Invoice invoice)
        {
            var existingInvoice = Invoices.FirstOrDefault(i => i.Id == invoice.Id);
            if (existingInvoice != null)
            {
                var index = Invoices.IndexOf(existingInvoice);
                Invoices[index] = invoice;
                UpdateCustomerBalance(invoice.CustomerId);
            }
        }

        public static void DeleteInvoice(int invoiceId)
        {
            var invoice = Invoices.FirstOrDefault(i => i.Id == invoiceId);
            if (invoice != null)
            {
                var customerId = invoice.CustomerId;
                Invoices.Remove(invoice);
                UpdateCustomerBalance(customerId);
            }
        }

        public static void AddReceipt(Receipt receipt)
        {
            receipt.Id = Receipts.Count > 0 ? Receipts.Max(r => r.Id) + 1 : 1;
            receipt.ReceiptNumber = $"R{receipt.Id:000}";
            receipt.CreatedDate = DateTime.Now;
            receipt.CreatedBy = AuthService.CurrentUser;
            Receipts.Add(receipt);

            // تحديث المبلغ المدفوع في الفاتورة
            if (receipt.InvoiceId.HasValue)
            {
                var invoice = Invoices.FirstOrDefault(i => i.Id == receipt.InvoiceId.Value);
                if (invoice != null)
                {
                    invoice.PaidAmount += receipt.Amount;

                    // تحديث حالة الفاتورة
                    if (invoice.PaidAmount >= invoice.TotalAmount)
                    {
                        invoice.Status = "مدفوعة";
                    }
                    else if (invoice.PaidAmount > 0)
                    {
                        invoice.Status = "مدفوعة جزئياً";
                    }
                }
            }

            UpdateCustomerBalance(receipt.CustomerId);
        }

        public static void AddCustomer(Customer customer)
        {
            customer.Id = Customers.Count > 0 ? Customers.Max(c => c.Id) + 1 : 1;
            customer.CustomerCode = $"C{customer.Id:000}";
            customer.CreatedDate = DateTime.Now;
            Customers.Add(customer);
        }

        public static void AddProduct(Product product)
        {
            product.Id = Products.Count > 0 ? Products.Max(p => p.Id) + 1 : 1;
            product.ProductCode = $"P{product.Id:000}";
            Products.Add(product);
        }

        private static void UpdateCustomerBalance(int customerId)
        {
            var customer = Customers.FirstOrDefault(c => c.Id == customerId);
            if (customer != null)
            {
                var totalInvoices = Invoices.Where(i => i.CustomerId == customerId).Sum(i => i.TotalAmount);
                var totalPayments = Receipts.Where(r => r.CustomerId == customerId && r.Status == "مؤكد").Sum(r => r.Amount);
                customer.CurrentBalance = totalInvoices - totalPayments;
            }
        }

        // دوال التقارير والإحصائيات
        public static decimal GetTotalSales(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = Invoices.AsQueryable();
            if (fromDate.HasValue) query = query.Where(i => i.InvoiceDate >= fromDate.Value);
            if (toDate.HasValue) query = query.Where(i => i.InvoiceDate <= toDate.Value);
            return query.Sum(i => i.TotalAmount);
        }

        public static decimal GetTotalPayments(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = Receipts.Where(r => r.Status == "مؤكد").AsQueryable();
            if (fromDate.HasValue) query = query.Where(r => r.ReceiptDate >= fromDate.Value);
            if (toDate.HasValue) query = query.Where(r => r.ReceiptDate <= toDate.Value);
            return query.Sum(r => r.Amount);
        }

        public static decimal GetTotalProfit(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = Invoices.AsQueryable();
            if (fromDate.HasValue) query = query.Where(i => i.InvoiceDate >= fromDate.Value);
            if (toDate.HasValue) query = query.Where(i => i.InvoiceDate <= toDate.Value);
            return query.Sum(i => i.ProfitAmount);
        }

        public static List<Invoice> GetOverdueInvoices()
        {
            return Invoices.Where(i => i.IsOverdue).OrderBy(i => i.DueDate).ToList();
        }

        public static List<Customer> GetTopCustomers(int count = 10)
        {
            return Customers.OrderByDescending(c => c.CurrentBalance).Take(count).ToList();
        }

        // حفظ واستعادة البيانات
        public static void SaveData()
        {
            try
            {
                // يمكن إضافة حفظ البيانات في ملفات JSON أو قاعدة بيانات
                // هذا مثال بسيط للحفظ في ملفات نصية
                var dataFolder = "Data";
                if (!Directory.Exists(dataFolder))
                    Directory.CreateDirectory(dataFolder);

                // حفظ معلومات آخر حفظ
                File.WriteAllText(Path.Combine(dataFolder, "LastSave.txt"), DateTime.Now.ToString());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void LoadData()
        {
            try
            {
                var dataFolder = "Data";
                if (Directory.Exists(dataFolder))
                {
                    var lastSaveFile = Path.Combine(dataFolder, "LastSave.txt");
                    if (File.Exists(lastSaveFile))
                    {
                        var lastSave = File.ReadAllText(lastSaveFile);
                        // يمكن إضافة تحميل البيانات من الملفات
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    // خدمة المصادقة المحسنة
    public static class AuthService
    {
        public static User CurrentUser { get; private set; }
        public static string CurrentUserName => CurrentUser?.FullName ?? "غير محدد";

        public static bool Login(string username, string password)
        {
            var user = DataService.Users.FirstOrDefault(u => u.Username == username && u.Password == password && u.IsActive);
            if (user != null)
            {
                CurrentUser = user;
                user.LastLogin = DateTime.Now;
                return true;
            }
            return false;
        }

        public static void Logout()
        {
            CurrentUser = null;
        }

        public static bool HasPermission(string permission)
        {
            return CurrentUser?.Permissions.Contains(permission) ?? false;
        }

        public static bool IsAdmin()
        {
            return CurrentUser?.Role == "مدير";
        }
    }

    // نموذج تسجيل الدخول المتكامل
    public class LoginForm : Form
    {
        private Panel mainPanel;
        private Panel loginPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Label lblTitle;
        private ComboBox cmbLanguage;

        public LoginForm()
        {
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "تسجيل الدخول - النظام المتكامل للفواتير";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.RightToLeft = RightToLeft.Yes;

            // الخلفية الرئيسية مع تدرج
            mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Paint += MainPanel_Paint;

            // لوحة تسجيل الدخول
            loginPanel = new Panel();
            loginPanel.Size = new Size(500, 450);
            loginPanel.Location = new Point(250, 125);
            loginPanel.BackColor = Color.White;
            loginPanel.Paint += LoginPanel_Paint;

            // العنوان الرئيسي
            lblTitle = new Label();
            lblTitle.Text = "النظام المتكامل للفواتير";
            lblTitle.Font = new Font("Segoe UI", 32F, FontStyle.Bold);
            lblTitle.ForeColor = Color.FromArgb(41, 128, 185);
            lblTitle.Location = new Point(50, 50);
            lblTitle.Size = new Size(400, 70);
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;

            var lblSubtitle = new Label();
            lblSubtitle.Text = "نظام شامل ومتكامل لإدارة الفواتير والحسابات";
            lblSubtitle.Font = new Font("Segoe UI", 14F);
            lblSubtitle.ForeColor = Color.FromArgb(127, 140, 141);
            lblSubtitle.Location = new Point(50, 120);
            lblSubtitle.Size = new Size(400, 35);
            lblSubtitle.TextAlign = ContentAlignment.MiddleCenter;

            var lblVersion = new Label();
            lblVersion.Text = "الإصدار 3.0 - محسن ومتكامل";
            lblVersion.Font = new Font("Segoe UI", 11F);
            lblVersion.ForeColor = Color.FromArgb(155, 89, 182);
            lblVersion.Location = new Point(50, 155);
            lblVersion.Size = new Size(400, 25);
            lblVersion.TextAlign = ContentAlignment.MiddleCenter;

            // اسم المستخدم
            var lblUsername = new Label();
            lblUsername.Text = "اسم المستخدم";
            lblUsername.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblUsername.ForeColor = Color.FromArgb(52, 73, 94);
            lblUsername.Location = new Point(50, 200);
            lblUsername.Size = new Size(120, 30);

            txtUsername = new TextBox();
            txtUsername.Location = new Point(50, 230);
            txtUsername.Size = new Size(400, 35);
            txtUsername.Font = new Font("Segoe UI", 12F);
            txtUsername.Text = "admin";
            txtUsername.BorderStyle = BorderStyle.FixedSingle;

            // كلمة المرور
            var lblPassword = new Label();
            lblPassword.Text = "كلمة المرور";
            lblPassword.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblPassword.ForeColor = Color.FromArgb(52, 73, 94);
            lblPassword.Location = new Point(50, 280);
            lblPassword.Size = new Size(120, 30);

            txtPassword = new TextBox();
            txtPassword.Location = new Point(50, 310);
            txtPassword.Size = new Size(400, 35);
            txtPassword.Font = new Font("Segoe UI", 12F);
            txtPassword.UseSystemPasswordChar = true;
            txtPassword.Text = "admin123";
            txtPassword.BorderStyle = BorderStyle.FixedSingle;

            // زر الدخول
            btnLogin = new Button();
            btnLogin.Text = "دخول إلى النظام";
            btnLogin.Location = new Point(50, 370);
            btnLogin.Size = new Size(400, 50);
            btnLogin.BackColor = Color.FromArgb(41, 128, 185);
            btnLogin.ForeColor = Color.White;
            btnLogin.FlatStyle = FlatStyle.Flat;
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            btnLogin.Cursor = Cursors.Hand;
            btnLogin.Click += BtnLogin_Click;

            // معلومات المستخدمين
            var lblUsers = new Label();
            lblUsers.Text = "المستخدمين المتاحين:\nadmin/admin123 (مدير)\nuser/user123 (موظف)";
            lblUsers.Font = new Font("Segoe UI", 9F);
            lblUsers.ForeColor = Color.FromArgb(127, 140, 141);
            lblUsers.Location = new Point(50, 430);
            lblUsers.Size = new Size(200, 60);

            // إضافة التحكمات
            loginPanel.Controls.Add(lblTitle);
            loginPanel.Controls.Add(lblSubtitle);
            loginPanel.Controls.Add(lblVersion);
            loginPanel.Controls.Add(lblUsername);
            loginPanel.Controls.Add(txtUsername);
            loginPanel.Controls.Add(lblPassword);
            loginPanel.Controls.Add(txtPassword);
            loginPanel.Controls.Add(btnLogin);
            loginPanel.Controls.Add(lblUsers);

            mainPanel.Controls.Add(loginPanel);
            this.Controls.Add(mainPanel);

            this.AcceptButton = btnLogin;
        }

        private void MainPanel_Paint(object sender, PaintEventArgs e)
        {
            // تدرج الخلفية
            using (LinearGradientBrush brush = new LinearGradientBrush(
                mainPanel.ClientRectangle,
                Color.FromArgb(74, 144, 226),
                Color.FromArgb(41, 128, 185),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, mainPanel.ClientRectangle);
            }
        }

        private void LoginPanel_Paint(object sender, PaintEventArgs e)
        {
            // ظل للوحة
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, loginPanel.Width, loginPanel.Height), 25);
                loginPanel.Region = new Region(path);
            }
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (AuthService.Login(txtUsername.Text, txtPassword.Text))
            {
                this.Hide();
                var mainForm = new IntegratedMainForm();
                mainForm.ShowDialog();
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    // النموذج الرئيسي المتكامل
    public class IntegratedMainForm : Form
    {
        private Panel sidePanel;
        private Panel topPanel;
        private Panel contentPanel;
        private Label lblWelcome;
        private Label lblDateTime;
        private Label lblUserInfo;
        private Timer timeTimer;
        private string currentView = "dashboard";

        public IntegratedMainForm()
        {
            InitializeForm();
            SetupSidePanel();
            SetupTopPanel();
            ShowDashboard();
            StartTimer();
        }

        private void InitializeForm()
        {
            this.Text = "النظام المتكامل للفواتير - الواجهة الرئيسية";
            this.Size = new Size(1800, 1100);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(236, 240, 241);

            // الشريط الجانبي
            sidePanel = new Panel();
            sidePanel.Width = 350;
            sidePanel.Dock = DockStyle.Right;
            sidePanel.BackColor = Color.FromArgb(44, 62, 80);
            sidePanel.Paint += SidePanel_Paint;

            // الشريط العلوي
            topPanel = new Panel();
            topPanel.Height = 100;
            topPanel.Dock = DockStyle.Top;
            topPanel.BackColor = Color.White;
            topPanel.Paint += TopPanel_Paint;

            // منطقة المحتوى
            contentPanel = new Panel();
            contentPanel.Dock = DockStyle.Fill;
            contentPanel.BackColor = Color.FromArgb(236, 240, 241);
            contentPanel.Padding = new Padding(30);

            this.Controls.Add(contentPanel);
            this.Controls.Add(topPanel);
            this.Controls.Add(sidePanel);
        }

        private void SetupSidePanel()
        {
            // شعار النظام
            var logoPanel = new Panel();
            logoPanel.Height = 140;
            logoPanel.Dock = DockStyle.Top;
            logoPanel.BackColor = Color.FromArgb(52, 73, 94);

            var lblLogo = new Label();
            lblLogo.Text = "💼\nالنظام المتكامل\nللفواتير";
            lblLogo.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            lblLogo.ForeColor = Color.White;
            lblLogo.TextAlign = ContentAlignment.MiddleCenter;
            lblLogo.Dock = DockStyle.Fill;

            logoPanel.Controls.Add(lblLogo);
            sidePanel.Controls.Add(logoPanel);

            // قائمة التنقل المتكاملة
            var menuItems = new[]
            {
                new { Text = "🏠 الداشبورد الرئيسي", Action = new Action(() => ShowDashboard()), Key = "dashboard", Permission = "" },
                new { Text = "📊 إدارة الفواتير", Action = new Action(() => ShowInvoicesManagement()), Key = "invoices", Permission = "إدارة الفواتير" },
                new { Text = "➕ إضافة فاتورة جديدة", Action = new Action(() => ShowAddInvoice()), Key = "add_invoice", Permission = "إدارة الفواتير" },
                new { Text = "💰 السندات والمدفوعات", Action = new Action(() => ShowReceipts()), Key = "receipts", Permission = "إدارة الفواتير" },
                new { Text = "👥 إدارة العملاء", Action = new Action(() => ShowCustomers()), Key = "customers", Permission = "إدارة العملاء" },
                new { Text = "📦 إدارة المنتجات", Action = new Action(() => ShowProducts()), Key = "products", Permission = "إدارة المنتجات" },
                new { Text = "📈 التقارير والإحصائيات", Action = new Action(() => ShowReports()), Key = "reports", Permission = "التقارير" },
                new { Text = "🏢 معلومات الشركة", Action = new Action(() => ShowCompanyInfo()), Key = "company", Permission = "الإعدادات" },
                new { Text = "👤 إدارة المستخدمين", Action = new Action(() => ShowUsers()), Key = "users", Permission = "الإعدادات" },
                new { Text = "⚙️ الإعدادات العامة", Action = new Action(() => ShowSettings()), Key = "settings", Permission = "الإعدادات" },
                new { Text = "💾 حفظ البيانات", Action = new Action(() => SaveData()), Key = "save", Permission = "" },
                new { Text = "🚪 تسجيل خروج", Action = new Action(() => Logout()), Key = "logout", Permission = "" }
            };

            int yPos = 160;
            foreach (var item in menuItems)
            {
                // التحقق من الصلاحيات
                if (!string.IsNullOrEmpty(item.Permission) && !AuthService.HasPermission(item.Permission))
                    continue;

                var menuButton = CreateMenuButton(item.Text, item.Action, item.Key);
                menuButton.Location = new Point(15, yPos);
                sidePanel.Controls.Add(menuButton);
                yPos += 70;
            }
        }

        private Button CreateMenuButton(string text, Action action, string key)
        {
            var button = new Button();
            button.Text = text;
            button.Size = new Size(320, 60);
            button.BackColor = currentView == key ? Color.FromArgb(52, 152, 219) : Color.Transparent;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            button.TextAlign = ContentAlignment.MiddleLeft;
            button.Padding = new Padding(30, 0, 0, 0);
            button.Cursor = Cursors.Hand;
            button.Tag = key;
            button.Click += (s, e) =>
            {
                currentView = key;
                UpdateMenuButtons();
                action();
            };

            // تأثيرات التفاعل
            button.MouseEnter += (s, e) =>
            {
                if (currentView != key)
                    button.BackColor = Color.FromArgb(52, 152, 219);
            };
            button.MouseLeave += (s, e) =>
            {
                if (currentView != key)
                    button.BackColor = Color.Transparent;
            };

            return button;
        }

        private void UpdateMenuButtons()
        {
            foreach (Control control in sidePanel.Controls)
            {
                if (control is Button button && button.Tag != null)
                {
                    string key = button.Tag.ToString();
                    button.BackColor = currentView == key ? Color.FromArgb(52, 152, 219) : Color.Transparent;
                }
            }
        }

        private void SetupTopPanel()
        {
            // رسالة الترحيب
            lblWelcome = new Label();
            lblWelcome.Text = $"مرحباً، {AuthService.CurrentUserName} 👋";
            lblWelcome.Font = new Font("Segoe UI", 22F, FontStyle.Bold);
            lblWelcome.ForeColor = Color.FromArgb(44, 62, 80);
            lblWelcome.Location = new Point(40, 20);
            lblWelcome.Size = new Size(500, 50);

            // معلومات المستخدم
            lblUserInfo = new Label();
            lblUserInfo.Text = $"الدور: {AuthService.CurrentUser?.Role} | آخر دخول: {AuthService.CurrentUser?.LastLogin:yyyy/MM/dd HH:mm}";
            lblUserInfo.Font = new Font("Segoe UI", 12F);
            lblUserInfo.ForeColor = Color.FromArgb(127, 140, 141);
            lblUserInfo.Location = new Point(40, 70);
            lblUserInfo.Size = new Size(600, 25);

            // التاريخ والوقت
            lblDateTime = new Label();
            lblDateTime.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            lblDateTime.ForeColor = Color.FromArgb(52, 152, 219);
            lblDateTime.Location = new Point(topPanel.Width - 500, 20);
            lblDateTime.Size = new Size(450, 35);
            lblDateTime.TextAlign = ContentAlignment.MiddleRight;
            lblDateTime.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            // أزرار سريعة
            var btnNotifications = new Button();
            btnNotifications.Text = "🔔 الإشعارات";
            btnNotifications.Size = new Size(130, 45);
            btnNotifications.Location = new Point(topPanel.Width - 500, 55);
            btnNotifications.BackColor = Color.FromArgb(231, 76, 60);
            btnNotifications.ForeColor = Color.White;
            btnNotifications.FlatStyle = FlatStyle.Flat;
            btnNotifications.FlatAppearance.BorderSize = 0;
            btnNotifications.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnNotifications.Cursor = Cursors.Hand;
            btnNotifications.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnNotifications.Click += (s, e) => ShowNotifications();

            var btnQuickAdd = new Button();
            btnQuickAdd.Text = "⚡ إضافة سريعة";
            btnQuickAdd.Size = new Size(140, 45);
            btnQuickAdd.Location = new Point(topPanel.Width - 360, 55);
            btnQuickAdd.BackColor = Color.FromArgb(46, 204, 113);
            btnQuickAdd.ForeColor = Color.White;
            btnQuickAdd.FlatStyle = FlatStyle.Flat;
            btnQuickAdd.FlatAppearance.BorderSize = 0;
            btnQuickAdd.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnQuickAdd.Cursor = Cursors.Hand;
            btnQuickAdd.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnQuickAdd.Click += (s, e) => ShowQuickAdd();

            var btnBackup = new Button();
            btnBackup.Text = "💾 نسخ احتياطي";
            btnBackup.Size = new Size(140, 45);
            btnBackup.Location = new Point(topPanel.Width - 210, 55);
            btnBackup.BackColor = Color.FromArgb(155, 89, 182);
            btnBackup.ForeColor = Color.White;
            btnBackup.FlatStyle = FlatStyle.Flat;
            btnBackup.FlatAppearance.BorderSize = 0;
            btnBackup.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnBackup.Cursor = Cursors.Hand;
            btnBackup.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnBackup.Click += (s, e) => CreateBackup();

            topPanel.Controls.Add(lblWelcome);
            topPanel.Controls.Add(lblUserInfo);
            topPanel.Controls.Add(lblDateTime);
            topPanel.Controls.Add(btnNotifications);
            topPanel.Controls.Add(btnQuickAdd);
            topPanel.Controls.Add(btnBackup);
        }

        private void SidePanel_Paint(object sender, PaintEventArgs e)
        {
            // تدرج للشريط الجانبي
            using (LinearGradientBrush brush = new LinearGradientBrush(
                sidePanel.ClientRectangle,
                Color.FromArgb(44, 62, 80),
                Color.FromArgb(52, 73, 94),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, sidePanel.ClientRectangle);
            }
        }

        private void TopPanel_Paint(object sender, PaintEventArgs e)
        {
            // ظل للشريط العلوي
            using (Pen pen = new Pen(Color.FromArgb(189, 195, 199), 3))
            {
                e.Graphics.DrawLine(pen, 0, topPanel.Height - 3, topPanel.Width, topPanel.Height - 3);
            }
        }

        private void StartTimer()
        {
            timeTimer = new Timer();
            timeTimer.Interval = 1000;
            timeTimer.Tick += (s, e) =>
            {
                lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy\nHH:mm:ss",
                    new System.Globalization.CultureInfo("ar-SA"));
            };
            timeTimer.Start();

            // تحديث فوري
            lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy\nHH:mm:ss",
                new System.Globalization.CultureInfo("ar-SA"));
        }

        // عرض الداشبورد الرئيسي المتكامل
        private void ShowDashboard()
        {
            contentPanel.Controls.Clear();
            currentView = "dashboard";
            UpdateMenuButtons();

            // لوحة الإحصائيات المحسنة
            var statsContainer = new Panel();
            statsContainer.Height = 180;
            statsContainer.Dock = DockStyle.Top;
            statsContainer.BackColor = Color.Transparent;
            statsContainer.Padding = new Padding(0, 0, 0, 30);

            // إحصائيات شاملة
            var stats = new[]
            {
                new { Title = "إجمالي الفواتير", Value = DataService.Invoices.Count.ToString(), SubValue = $"{DataService.Invoices.Sum(i => i.TotalAmount):N0} ر.س", Color = Color.FromArgb(52, 152, 219), Icon = "📊" },
                new { Title = "الفواتير المدفوعة", Value = DataService.Invoices.Count(i => i.Status == "مدفوعة").ToString(), SubValue = $"{DataService.Invoices.Where(i => i.Status == "مدفوعة").Sum(i => i.TotalAmount):N0} ر.س", Color = Color.FromArgb(46, 204, 113), Icon = "✅" },
                new { Title = "الفواتير المتأخرة", Value = DataService.Invoices.Count(i => i.IsOverdue).ToString(), SubValue = $"{DataService.Invoices.Where(i => i.IsOverdue).Sum(i => i.RemainingAmount):N0} ر.س", Color = Color.FromArgb(231, 76, 60), Icon = "⚠️" },
                new { Title = "إجمالي العملاء", Value = DataService.Customers.Count(c => c.IsActive).ToString(), SubValue = $"{DataService.Customers.Sum(c => c.CurrentBalance):N0} ر.س رصيد", Color = Color.FromArgb(155, 89, 182), Icon = "👥" },
                new { Title = "إجمالي الأرباح", Value = $"{DataService.GetTotalProfit():N0} ر.س", SubValue = "هذا الشهر", Color = Color.FromArgb(255, 152, 0), Icon = "💰" }
            };

            int cardWidth = 350;
            int cardSpacing = 30;
            int startX = 30;

            for (int i = 0; i < stats.Length; i++)
            {
                var statCard = CreateStatCard(stats[i].Title, stats[i].Value, stats[i].SubValue, stats[i].Color, stats[i].Icon);
                int row = i / 3;
                int col = i % 3;
                statCard.Location = new Point(startX + (col * (cardWidth + cardSpacing)), 20 + (row * 90));
                statsContainer.Controls.Add(statCard);
            }

            contentPanel.Controls.Add(statsContainer);

            // منطقة الرسوم البيانية والجداول
            var chartsContainer = new Panel();
            chartsContainer.Dock = DockStyle.Fill;
            chartsContainer.BackColor = Color.Transparent;

            // الرسم البياني للمبيعات
            var salesChart = CreateSalesChart();
            salesChart.Location = new Point(30, 30);
            chartsContainer.Controls.Add(salesChart);

            // جدول الفواتير الأخيرة
            var recentInvoicesPanel = CreateRecentInvoicesPanel();
            recentInvoicesPanel.Location = new Point(800, 30);
            chartsContainer.Controls.Add(recentInvoicesPanel);

            // جدول العملاء الأعلى رصيداً
            var topCustomersPanel = CreateTopCustomersPanel();
            topCustomersPanel.Location = new Point(30, 420);
            chartsContainer.Controls.Add(topCustomersPanel);

            // إحصائيات سريعة إضافية
            var quickStatsPanel = CreateQuickStatsPanel();
            quickStatsPanel.Location = new Point(800, 420);
            chartsContainer.Controls.Add(quickStatsPanel);

            contentPanel.Controls.Add(chartsContainer);
        }

        private Panel CreateStatCard(string title, string value, string subValue, Color color, string icon)
        {
            var card = new Panel();
            card.Size = new Size(350, 140);
            card.BackColor = Color.White;
            card.Paint += (s, e) => DrawCardShadow(e, card, color);

            // الأيقونة
            var iconLabel = new Label();
            iconLabel.Text = icon;
            iconLabel.Font = new Font("Segoe UI Emoji", 32F);
            iconLabel.Location = new Point(270, 30);
            iconLabel.Size = new Size(70, 70);
            iconLabel.TextAlign = ContentAlignment.MiddleCenter;

            // العنوان
            var titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(30, 30);
            titleLabel.Size = new Size(220, 35);

            // القيمة الرئيسية
            var valueLabel = new Label();
            valueLabel.Text = value;
            valueLabel.Font = new Font("Segoe UI", 26F, FontStyle.Bold);
            valueLabel.ForeColor = color;
            valueLabel.Location = new Point(30, 65);
            valueLabel.Size = new Size(200, 45);

            // القيمة الفرعية
            var subValueLabel = new Label();
            subValueLabel.Text = subValue;
            subValueLabel.Font = new Font("Segoe UI", 12F);
            subValueLabel.ForeColor = Color.FromArgb(127, 140, 141);
            subValueLabel.Location = new Point(30, 115);
            subValueLabel.Size = new Size(220, 25);

            card.Controls.Add(iconLabel);
            card.Controls.Add(titleLabel);
            card.Controls.Add(valueLabel);
            card.Controls.Add(subValueLabel);

            return card;
        }

        private void DrawCardShadow(PaintEventArgs e, Panel card, Color accentColor)
        {
            // خلفية بيضاء مع حواف مدورة
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, card.Width, card.Height), 15);
                card.Region = new Region(path);

                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }

            // خط ملون في الأعلى
            using (SolidBrush brush = new SolidBrush(accentColor))
            {
                e.Graphics.FillRectangle(brush, 0, 0, card.Width, 6);
            }
        }

        private Panel CreateSalesChart()
        {
            var chartPanel = new Panel();
            chartPanel.Size = new Size(750, 360);
            chartPanel.BackColor = Color.White;
            chartPanel.Paint += (s, e) => DrawSalesChart(e, chartPanel);

            var titleLabel = new Label();
            titleLabel.Text = "📈 مبيعات آخر 7 أيام";
            titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(30, 25);
            titleLabel.Size = new Size(300, 40);

            chartPanel.Controls.Add(titleLabel);
            return chartPanel;
        }

        private void DrawSalesChart(PaintEventArgs e, Panel panel)
        {
            // خلفية بيضاء مع حواف مدورة
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, panel.Width, panel.Height), 15);
                panel.Region = new Region(path);

                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }

            // رسم بياني محسن للمبيعات
            var salesData = new[] { 22000, 28000, 25000, 32000, 38000, 35000, 45000 };
            var days = new[] { "السبت", "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة" };

            int chartX = 70;
            int chartY = 80;
            int chartWidth = 620;
            int chartHeight = 220;
            int maxValue = salesData.Max();

            // رسم الخطوط الإرشادية
            using (Pen gridPen = new Pen(Color.FromArgb(236, 240, 241), 2))
            {
                for (int i = 0; i <= 5; i++)
                {
                    int y = chartY + (chartHeight * i / 5);
                    e.Graphics.DrawLine(gridPen, chartX, y, chartX + chartWidth, y);
                }
            }

            // رسم البيانات
            using (Pen linePen = new Pen(Color.FromArgb(52, 152, 219), 5))
            using (SolidBrush pointBrush = new SolidBrush(Color.FromArgb(52, 152, 219)))
            using (SolidBrush areaBrush = new SolidBrush(Color.FromArgb(50, 52, 152, 219)))
            {
                var points = new Point[salesData.Length];
                var areaPoints = new Point[salesData.Length + 2];

                for (int i = 0; i < salesData.Length; i++)
                {
                    int x = chartX + (chartWidth * i / (salesData.Length - 1));
                    int y = chartY + chartHeight - (int)((double)salesData[i] / maxValue * chartHeight);
                    points[i] = new Point(x, y);
                    areaPoints[i] = new Point(x, y);

                    // رسم النقطة
                    e.Graphics.FillEllipse(pointBrush, x - 8, y - 8, 16, 16);

                    // رسم القيمة
                    using (Font font = new Font("Segoe UI", 11F, FontStyle.Bold))
                    using (SolidBrush textBrush = new SolidBrush(Color.FromArgb(52, 73, 94)))
                    {
                        string valueText = (salesData[i] / 1000).ToString() + "ك";
                        var textSize = e.Graphics.MeasureString(valueText, font);
                        e.Graphics.DrawString(valueText, font, textBrush, x - textSize.Width / 2, y - 35);
                    }

                    // رسم اسم اليوم
                    using (Font font = new Font("Segoe UI", 12F))
                    using (SolidBrush textBrush = new SolidBrush(Color.FromArgb(127, 140, 141)))
                    {
                        var textSize = e.Graphics.MeasureString(days[i], font);
                        e.Graphics.DrawString(days[i], font, textBrush, x - textSize.Width / 2, chartY + chartHeight + 20);
                    }
                }

                // رسم المنطقة تحت الخط
                areaPoints[salesData.Length] = new Point(chartX + chartWidth, chartY + chartHeight);
                areaPoints[salesData.Length + 1] = new Point(chartX, chartY + chartHeight);
                e.Graphics.FillPolygon(areaBrush, areaPoints);

                // رسم الخط
                if (points.Length > 1)
                {
                    e.Graphics.DrawLines(linePen, points);
                }
            }
        }

        private Panel CreateRecentInvoicesPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(750, 360);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "📋 الفواتير الأخيرة";
            titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(30, 25);
            titleLabel.Size = new Size(300, 40);

            var listView = new ListView();
            listView.Location = new Point(30, 75);
            listView.Size = new Size(690, 265);
            listView.View = View.Details;
            listView.FullRowSelect = true;
            listView.GridLines = true;
            listView.HeaderStyle = ColumnHeaderStyle.Nonclickable;
            listView.Font = new Font("Segoe UI", 11F);

            listView.Columns.Add("رقم الفاتورة", 140);
            listView.Columns.Add("العميل", 200);
            listView.Columns.Add("المبلغ", 130);
            listView.Columns.Add("الحالة", 130);
            listView.Columns.Add("التاريخ", 90);

            var recentInvoices = DataService.Invoices.OrderByDescending(i => i.InvoiceDate).Take(12);
            foreach (var invoice in recentInvoices)
            {
                var customer = DataService.Customers.FirstOrDefault(c => c.Id == invoice.CustomerId);
                var item = new ListViewItem(invoice.InvoiceNumber);
                item.SubItems.Add(customer?.CustomerName ?? "غير محدد");
                item.SubItems.Add(invoice.TotalAmount.ToString("N0"));
                item.SubItems.Add(invoice.Status);
                item.SubItems.Add(invoice.InvoiceDate.ToString("MM/dd"));

                // تلوين حسب الحالة
                if (invoice.Status == "مدفوعة")
                    item.BackColor = Color.FromArgb(212, 237, 218);
                else if (invoice.Status == "متأخرة")
                    item.BackColor = Color.FromArgb(248, 215, 218);
                else if (invoice.Status == "مدفوعة جزئياً")
                    item.BackColor = Color.FromArgb(255, 243, 205);

                listView.Items.Add(item);
            }

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(listView);
            return panel;
        }

        private Panel CreateTopCustomersPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(750, 300);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "🏆 أعلى العملاء رصيداً";
            titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(30, 25);
            titleLabel.Size = new Size(300, 40);

            var topCustomers = DataService.Customers.OrderByDescending(c => c.CurrentBalance).Take(7);
            int yPos = 80;
            int rank = 1;

            foreach (var customer in topCustomers)
            {
                var customerPanel = new Panel();
                customerPanel.Location = new Point(30, yPos);
                customerPanel.Size = new Size(690, 30);
                customerPanel.BackColor = Color.FromArgb(248, 249, 250);

                var rankLabel = new Label();
                rankLabel.Text = rank.ToString();
                rankLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
                rankLabel.ForeColor = Color.FromArgb(52, 152, 219);
                rankLabel.Location = new Point(15, 5);
                rankLabel.Size = new Size(40, 25);
                rankLabel.TextAlign = ContentAlignment.MiddleCenter;

                var nameLabel = new Label();
                nameLabel.Text = customer.CustomerName;
                nameLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                nameLabel.ForeColor = Color.FromArgb(52, 73, 94);
                nameLabel.Location = new Point(70, 5);
                nameLabel.Size = new Size(250, 25);

                var codeLabel = new Label();
                codeLabel.Text = customer.CustomerCode;
                codeLabel.Font = new Font("Segoe UI", 10F);
                codeLabel.ForeColor = Color.FromArgb(127, 140, 141);
                codeLabel.Location = new Point(330, 5);
                codeLabel.Size = new Size(100, 25);

                var phoneLabel = new Label();
                phoneLabel.Text = customer.Phone;
                phoneLabel.Font = new Font("Segoe UI", 10F);
                phoneLabel.ForeColor = Color.FromArgb(127, 140, 141);
                phoneLabel.Location = new Point(440, 5);
                phoneLabel.Size = new Size(120, 25);

                var balanceLabel = new Label();
                balanceLabel.Text = customer.CurrentBalance.ToString("N0") + " ر.س";
                balanceLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                balanceLabel.ForeColor = customer.CurrentBalance >= 0 ? Color.FromArgb(46, 204, 113) : Color.FromArgb(231, 76, 60);
                balanceLabel.Location = new Point(570, 5);
                balanceLabel.Size = new Size(120, 25);
                balanceLabel.TextAlign = ContentAlignment.MiddleRight;

                customerPanel.Controls.Add(rankLabel);
                customerPanel.Controls.Add(nameLabel);
                customerPanel.Controls.Add(codeLabel);
                customerPanel.Controls.Add(phoneLabel);
                customerPanel.Controls.Add(balanceLabel);

                panel.Controls.Add(customerPanel);
                yPos += 35;
                rank++;
            }

            panel.Controls.Add(titleLabel);
            return panel;
        }

        private Panel CreateQuickStatsPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(750, 300);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "⚡ إحصائيات سريعة";
            titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(30, 25);
            titleLabel.Size = new Size(300, 40);

            var quickStats = new[]
            {
                new { Label = "متوسط قيمة الفاتورة", Value = DataService.Invoices.Average(i => i.TotalAmount).ToString("N0") + " ر.س", Icon = "💰" },
                new { Label = "إجمالي المدفوعات اليوم", Value = DataService.Receipts.Where(r => r.ReceiptDate.Date == DateTime.Today && r.Status == "مؤكد").Sum(r => r.Amount).ToString("N0") + " ر.س", Icon = "📈" },
                new { Label = "عدد الفواتير المستحقة", Value = DataService.Invoices.Count(i => i.DueDate <= DateTime.Now && i.Status != "مدفوعة").ToString(), Icon = "⏰" },
                new { Label = "نسبة التحصيل", Value = ((DataService.Invoices.Sum(i => i.PaidAmount) / DataService.Invoices.Sum(i => i.TotalAmount)) * 100).ToString("F1") + "%", Icon = "📊" },
                new { Label = "عدد العملاء الجدد", Value = DataService.Customers.Count(c => c.CreatedDate >= DateTime.Now.AddDays(-30)).ToString(), Icon = "👤" }
            };

            int yPos = 80;
            foreach (var stat in quickStats)
            {
                var statPanel = new Panel();
                statPanel.Location = new Point(30, yPos);
                statPanel.Size = new Size(690, 40);
                statPanel.BackColor = Color.FromArgb(248, 249, 250);

                var iconLabel = new Label();
                iconLabel.Text = stat.Icon;
                iconLabel.Font = new Font("Segoe UI Emoji", 20F);
                iconLabel.Location = new Point(20, 8);
                iconLabel.Size = new Size(40, 30);

                var labelText = new Label();
                labelText.Text = stat.Label;
                labelText.Font = new Font("Segoe UI", 12F);
                labelText.ForeColor = Color.FromArgb(52, 73, 94);
                labelText.Location = new Point(80, 10);
                labelText.Size = new Size(400, 25);

                var valueText = new Label();
                valueText.Text = stat.Value;
                valueText.Font = new Font("Segoe UI", 13F, FontStyle.Bold);
                valueText.ForeColor = Color.FromArgb(52, 152, 219);
                valueText.Location = new Point(490, 8);
                valueText.Size = new Size(180, 30);
                valueText.TextAlign = ContentAlignment.MiddleRight;

                statPanel.Controls.Add(iconLabel);
                statPanel.Controls.Add(labelText);
                statPanel.Controls.Add(valueText);

                panel.Controls.Add(statPanel);
                yPos += 45;
            }

            panel.Controls.Add(titleLabel);
            return panel;
        }

        private void DrawPanelBackground(PaintEventArgs e, Panel panel)
        {
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, panel.Width, panel.Height), 15);
                panel.Region = new Region(path);

                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }
        }

        // دوال التنقل والعمليات
        private void ShowInvoicesManagement()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة الفواتير المتكاملة\n\nالميزات المتوفرة:\n• عرض جميع الفواتير مع فلترة متقدمة\n• إضافة وتعديل وحذف الفواتير\n• طباعة الفواتير\n• تسجيل المدفوعات\n• تتبع حالات الفواتير",
                "إدارة الفواتير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowAddInvoice()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إضافة فاتورة جديدة\n\nالميزات المتوفرة:\n• نموذج شامل لإدخال بيانات الفاتورة\n• إضافة أصناف متعددة\n• حساب تلقائي للضرائب والخصومات\n• اختيار العميل من قائمة\n• حفظ كمسودة أو تأكيد",
                "إضافة فاتورة جديدة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReceipts()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة السندات والمدفوعات\n\nالميزات المتوفرة:\n• عرض جميع السندات\n• إضافة سند قبض جديد\n• ربط السندات بالفواتير\n• طرق دفع متعددة\n• تقارير المدفوعات",
                "إدارة السندات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCustomers()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة العملاء\n\nالميزات المتوفرة:\n• عرض جميع العملاء\n• إضافة عميل جديد\n• تعديل بيانات العملاء\n• كشف حساب العميل\n• تقارير العملاء\n• إدارة الأرصدة",
                "إدارة العملاء", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowProducts()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة المنتجات\n\nالميزات المتوفرة:\n• عرض جميع المنتجات\n• إضافة منتج جديد\n• تعديل أسعار المنتجات\n• إدارة المخزون\n• فئات المنتجات\n• الباركود",
                "إدارة المنتجات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReports()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة التقارير والإحصائيات\n\nالتقارير المتوفرة:\n• تقرير المبيعات\n• تقرير المدفوعات\n• تقرير العملاء\n• تقرير الأرباح\n• تقرير المخزون\n• تقارير مخصصة",
                "التقارير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCompanyInfo()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة معلومات الشركة\n\nالإعدادات المتوفرة:\n• اسم الشركة وعنوانها\n• معلومات الاتصال\n• الرقم الضريبي\n• السجل التجاري\n• شعار الشركة",
                "معلومات الشركة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowUsers()
        {
            if (!AuthService.IsAdmin())
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة المستخدمين", "صلاحيات غير كافية",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("🎉 سيتم فتح شاشة إدارة المستخدمين\n\nالميزات المتوفرة:\n• عرض جميع المستخدمين\n• إضافة مستخدم جديد\n• تعديل الصلاحيات\n• تغيير كلمات المرور\n• تفعيل/إلغاء تفعيل المستخدمين",
                "إدارة المستخدمين", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowSettings()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة الإعدادات العامة\n\nالإعدادات المتوفرة:\n• إعدادات الضرائب\n• إعدادات الطباعة\n• إعدادات النسخ الاحتياطي\n• إعدادات التنبيهات\n• إعدادات العملة",
                "الإعدادات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowNotifications()
        {
            var overdueCount = DataService.Invoices.Count(i => i.IsOverdue);
            var dueToday = DataService.Invoices.Count(i => i.DueDate.Date == DateTime.Today && i.Status != "مدفوعة");
            var lowStock = DataService.Products.Count(p => p.StockQuantity < 10);

            MessageBox.Show($"🔔 الإشعارات:\n\n• {overdueCount} فاتورة متأخرة\n• {dueToday} فاتورة مستحقة اليوم\n• {lowStock} منتج بمخزون منخفض\n• آخر تحديث: {DateTime.Now:HH:mm}",
                "الإشعارات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowQuickAdd()
        {
            var quickMenu = new ContextMenuStrip();
            quickMenu.Items.Add("➕ فاتورة جديدة", null, (s, e) => ShowAddInvoice());
            quickMenu.Items.Add("👤 عميل جديد", null, (s, e) => MessageBox.Show("سيتم فتح نموذج إضافة عميل جديد", "إضافة عميل"));
            quickMenu.Items.Add("📦 منتج جديد", null, (s, e) => MessageBox.Show("سيتم فتح نموذج إضافة منتج جديد", "إضافة منتج"));
            quickMenu.Items.Add("💰 سند قبض", null, (s, e) => MessageBox.Show("سيتم فتح نموذج إضافة سند قبض", "إضافة سند"));

            quickMenu.Show(Cursor.Position);
        }

        private void CreateBackup()
        {
            try
            {
                DataService.SaveData();
                MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نسخ احتياطي",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveData()
        {
            try
            {
                DataService.SaveData();
                MessageBox.Show("تم حفظ البيانات بنجاح", "حفظ البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Logout()
        {
            var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                AuthService.Logout();
                this.Close();
            }
        }
    }

    // إضافة دالة مساعدة للحواف المدورة
    public static class GraphicsExtensions
    {
        public static void AddRoundedRectangle(this GraphicsPath path, Rectangle rect, int radius)
        {
            int diameter = radius * 2;
            Size size = new Size(diameter, diameter);
            Rectangle arc = new Rectangle(rect.Location, size);

            // الزاوية اليسرى العلوية
            path.AddArc(arc, 180, 90);

            // الزاوية اليمنى العلوية
            arc.X = rect.Right - diameter;
            path.AddArc(arc, 270, 90);

            // الزاوية اليمنى السفلى
            arc.Y = rect.Bottom - diameter;
            path.AddArc(arc, 0, 90);

            // الزاوية اليسرى السفلى
            arc.X = rect.Left;
            path.AddArc(arc, 90, 90);

            path.CloseFigure();
        }
    }

    // نقطة دخول التطبيق المتكامل
    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // تحميل البيانات عند بدء التطبيق
            DataService.LoadData();

            // تشغيل نموذج تسجيل الدخول
            Application.Run(new LoginForm());

            // حفظ البيانات عند إغلاق التطبيق
            DataService.SaveData();
        }
    }
}