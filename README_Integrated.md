# النظام المتكامل للفواتير - الإصدار المجزأ 3.0

## 🎯 نظرة عامة

النظام المتكامل للفواتير هو نظام شامل ومتطور لإدارة الفواتير والحسابات، تم تطويره بلغة C# مع واجهة Windows Forms. يتميز هذا الإصدار بتجزئة الكود إلى ملفات منفصلة لسهولة التطوير والصيانة.

## ✨ الميزات الرئيسية

### 🏠 الواجهة الرئيسية
- داشبورد تفاعلي مع إحصائيات شاملة
- شريط جانبي بـ 12 قسم رئيسي
- شريط علوي مع معلومات المستخدم والوقت
- أزرار سريعة للعمليات الشائعة

### 📊 إدارة الفواتير
- إنشاء وتعديل وحذف الفواتير
- تتبع حالات الفواتير (مؤكدة، مدفوعة، متأخرة، مدفوعة جزئياً)
- حساب تلقائي للضرائب والخصومات
- ربط الفواتير بالعملاء والمنتجات

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع أرصدة العملاء
- معلومات اتصال مفصلة
- كشف حساب العميل

### 📦 إدارة المنتجات
- كتالوج شامل للمنتجات
- إدارة المخزون
- فئات المنتجات
- أسعار التكلفة والبيع

### 💰 إدارة المدفوعات
- سندات القبض
- طرق دفع متعددة (نقدي، تحويل، شيك)
- ربط المدفوعات بالفواتير
- تتبع حالة المدفوعات

### 👤 إدارة المستخدمين
- نظام صلاحيات متقدم
- أدوار مختلفة (مدير، موظف)
- تسجيل العمليات
- أمان محسن

## 🏗️ هيكل المشروع

```
IntegratedInvoiceSystem/
├── Models/
│   └── DataModels.cs          # نماذج البيانات والكائنات
├── Services/
│   ├── DataService.cs         # خدمة إدارة البيانات
│   └── AuthService.cs         # خدمة المصادقة والصلاحيات
├── Forms/
│   ├── LoginForm.cs           # نموذج تسجيل الدخول
│   └── MainDashboardForm.cs   # النموذج الرئيسي
├── Controls/
│   └── DashboardControl.cs    # عنصر الداشبورد
├── Data/                      # ملفات البيانات
├── Backup/                    # النسخ الاحتياطية
├── Reports/                   # التقارير المُصدرة
├── IntegratedProgram.cs       # نقطة دخول التطبيق المتكامل
└── build_integrated_system.bat # ملف البناء
```

## 🚀 التشغيل والبناء

### متطلبات النظام
- Windows 7 أو أحدث
- .NET Framework 4.0 أو أحدث
- مترجم C# (Visual Studio أو .NET SDK)

### طريقة البناء

#### الطريقة الأولى: استخدام ملف البناء
```bash
build_integrated_system.bat
```

#### الطريقة الثانية: البناء اليدوي
```bash
csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /main:IntegratedInvoiceSystem.IntegratedProgram ^
    /out:IntegratedInvoiceSystem.exe ^
    Models\DataModels.cs ^
    Services\DataService.cs ^
    Services\AuthService.cs ^
    Forms\MainDashboardForm.cs ^
    Controls\DashboardControl.cs ^
    IntegratedProgram.cs
```

## 👤 معلومات تسجيل الدخول

### المدير
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **الصلاحيات:** جميع الصلاحيات

### الموظف
- **اسم المستخدم:** user
- **كلمة المرور:** user123
- **الصلاحيات:** الفواتير والعملاء فقط

## 📊 البيانات التجريبية

يحتوي النظام على بيانات تجريبية شاملة:
- 8 عملاء بمعلومات كاملة وأرصدة متنوعة
- 10 منتجات بفئات وأسعار مختلفة
- 5 فواتير بحالات وأصناف متعددة
- 3 سندات قبض مربوطة بالفواتير
- 2 مستخدم بصلاحيات مختلفة

## 🎨 التصميم والواجهة

- ألوان متناسقة ومريحة للعين
- خطوط Segoe UI الأنيقة
- حواف مدورة في جميع العناصر
- ظلال وتأثيرات ثلاثية الأبعاد
- تدرجات لونية جميلة
- أيقونات تعبيرية واضحة
- دعم كامل للغة العربية

## 🔧 الوظائف المتقدمة

- حساب تلقائي للضرائب والخصومات
- تتبع حالات الفواتير المختلفة
- ربط المدفوعات بالفواتير
- حساب الأرباح والخسائر
- فلترة وبحث متقدم
- تلوين تلقائي حسب الحالة
- نسخ احتياطية تلقائية

## 💡 مزايا التجزئة

- **سهولة التطوير:** كل مكون في ملف منفصل
- **العمل الجماعي:** إمكانية العمل على أجزاء مختلفة بشكل متوازي
- **تنظيم أفضل:** هيكل واضح ومنطقي للكود
- **سهولة الصيانة:** إصلاح الأخطاء وإضافة الميزات أسهل
- **إعادة الاستخدام:** إمكانية استخدام المكونات في مشاريع أخرى

## 📈 الإحصائيات والتقارير

### الداشبورد يعرض:
- إجمالي الفواتير والمبيعات
- الفواتير المدفوعة والمتأخرة
- إجمالي العملاء والأرصدة
- رسم بياني للمبيعات (7 أيام)
- جدول الفواتير الأخيرة
- أعلى العملاء رصيداً
- إحصائيات سريعة

## 🔒 الأمان والصلاحيات

- نظام مصادقة محسن
- صلاحيات متدرجة حسب الدور
- تسجيل جميع العمليات
- حماية البيانات الحساسة
- تشفير كلمات المرور

## 🛠️ التطوير والتخصيص

### إضافة ميزات جديدة:
1. أضف النماذج المطلوبة في `Models/DataModels.cs`
2. أضف الخدمات في مجلد `Services/`
3. أنشئ النماذج الجديدة في مجلد `Forms/`
4. أضف العناصر المخصصة في مجلد `Controls/`
5. حدث ملف البناء لتضمين الملفات الجديدة

### أفضل الممارسات:
- اتبع نمط التسمية المستخدم
- أضف تعليقات واضحة للكود
- اختبر الميزات الجديدة بعناية
- حافظ على التصميم المتناسق

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع الكود المصدري للتفاصيل
- تحقق من رسائل الخطأ في وقت التشغيل
- استخدم Visual Studio للتشخيص المتقدم

## 📝 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## 🎉 شكر خاص

شكراً لجميع المطورين والمساهمين في تطوير هذا النظام المتكامل.

---

**النظام المتكامل للفواتير - الإصدار 3.0 المجزأ**
*نظام شامل ومتطور لإدارة الفواتير والحسابات*

## 🎯 الأقسام المتوفرة في النظام:

### 🏠 الداشبورد الرئيسي
- إحصائيات شاملة وتفاعلية
- 5 بطاقات إحصائيات رئيسية
- رسم بياني للمبيعات
- جداول تفاعلية

### 📊 إدارة الفواتير
- عرض وإدارة كاملة للفواتير
- فلترة متقدمة
- تتبع الحالات

### ➕ إضافة فاتورة جديدة
- نموذج متطور لإنشاء الفواتير
- حساب تلقائي
- اختيار العملاء والمنتجات

### 💰 السندات والمدفوعات
- إدارة المدفوعات والتحصيلات
- طرق دفع متعددة
- ربط بالفواتير

### 👥 إدارة العملاء
- قاعدة بيانات العملاء الشاملة
- تتبع الأرصدة
- كشوف الحسابات

### 📦 إدارة المنتجات
- كتالوج المنتجات والمخزون
- فئات وأسعار
- إدارة الكميات

### 📈 التقارير والإحصائيات
- تحليلات مفصلة ومتقدمة
- تقارير متنوعة
- إحصائيات شاملة

### 🏢 معلومات الشركة
- بيانات الشركة والإعدادات
- معلومات الاتصال
- الرقم الضريبي

### 👤 إدارة المستخدمين
- إدارة المستخدمين والصلاحيات
- أدوار مختلفة
- أمان محسن

### ⚙️ الإعدادات العامة
- تخصيص النظام والتفضيلات
- إعدادات الضرائب
- إعدادات الطباعة

### 💾 حفظ البيانات
- نسخ احتياطية وحفظ
- استعادة البيانات
- حماية المعلومات

### 🚪 تسجيل خروج
- خروج آمن من النظام
- حفظ الجلسة
- تنظيف الذاكرة

## 🎉 هذا أشمل وأفضل نظام فواتير متكامل ومجزأ!
