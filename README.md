# نظام المحاسبة الذكي

نظام محاسبي شامل مطور باستخدام C# مع WinForms وقاعدة بيانات SQLite، مدعوم بميزات الذكاء الاصطناعي.

## المميزات الرئيسية

### 1. واجهة تسجيل الدخول الآمنة
- نظام مصادقة متقدم مع تشفير كلمات المرور
- إدارة جلسات المستخدمين
- صلاحيات متعددة المستويات

### 2. شجرة الحسابات
- هيكل حسابات مرن وقابل للتخصيص
- دعم الحسابات الرئيسية والفرعية
- تصنيف الحسابات (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)

### 3. القيود اليومية
- إدخال وإدارة القيود اليومية
- التحقق من توازن القيود تلقائياً
- ربط القيود بالمستندات المرجعية

### 4. سندات القبض والصرف
- إنشاء وإدارة سندات القبض والصرف
- دعم طرق دفع متعددة (نقدي، شيك، تحويل بنكي)
- ربط السندات بالحسابات المحاسبية

### 5. إدارة العملاء والموردين
- قاعدة بيانات شاملة للعملاء والموردين
- تتبع الأرصدة والمعاملات
- إدارة حدود الائتمان وشروط السداد

### 6. إدارة المنتجات والمخزون
- كتالوج شامل للمنتجات
- تتبع المخزون والكميات
- ربط المنتجات بالحسابات المحاسبية

### 7. نظام الفواتير
- فواتير المبيعات والمشتريات
- حساب الضرائب والخصومات تلقائياً
- تتبع حالات الفواتير (مسودة، مؤكدة، مدفوعة)
- دعم طرق سداد متعددة

### 8. ميزات الذكاء الاصطناعي
- **تحليل الأداء المالي**: تحليل شامل للإيرادات والمصروفات والنسب المالية
- **التوصيات الذكية**: توصيات مبنية على البيانات لتحسين الأداء المالي
- **كشف الأخطاء**: اكتشاف الأخطاء المحاسبية المحتملة تلقائياً
- **تحليل الاتجاهات**: تحليل اتجاهات المبيعات والنمو

### 9. التقارير والطباعة
- تقارير مالية شاملة
- طباعة الفواتير والسندات
- تصدير التقارير لملفات نصية
- معاينة قبل الطباعة

### 10. الأمان والحماية
- تشفير كلمات المرور باستخدام SHA256
- حماية من SQL Injection
- تسجيل العمليات والتدقيق
- نسخ احتياطية لقاعدة البيانات

## المتطلبات التقنية

- **نظام التشغيل**: Windows 7 أو أحدث
- **إطار العمل**: .NET Framework 4.8
- **قاعدة البيانات**: SQLite
- **أدوات الوصول للبيانات**: Dapper
- **واجهة المستخدم**: Windows Forms

## التثبيت والتشغيل

### المتطلبات المسبقة
1. تثبيت .NET Framework 4.8
2. Visual Studio 2019 أو أحدث (للتطوير)
3. مكتبات SQLite و Dapper و Newtonsoft.Json

### خطوات التثبيت السريع
1. تشغيل `quick_setup.bat` للإعداد السريع
2. تشغيل `install_requirements.bat` لتثبيت المتطلبات
3. تشغيل `build_and_run.bat` لبناء وتشغيل النظام
4. أو تشغيل `start_system.bat` إذا كان النظام مبني مسبقاً

### خطوات التثبيت المتقدم
1. استنساخ المشروع أو تحميل الملفات
2. فتح ملف `AccountingSystem.sln` في Visual Studio
3. استعادة حزم NuGet المطلوبة
4. بناء المشروع (Build)
5. تشغيل التطبيق

### أول تشغيل
- **اسم المستخدم الافتراضي**: admin
- **كلمة المرور الافتراضية**: admin123

⚠️ **مهم**: يُنصح بتغيير كلمة المرور فور تسجيل الدخول الأول

## هيكل المشروع

```
AccountingSystem/
├── Models/                 # نماذج البيانات
│   ├── User.cs
│   ├── Account.cs
│   ├── JournalEntry.cs
│   ├── Receipt.cs
│   ├── Payment.cs
│   ├── Product.cs
│   ├── Customer.cs
│   └── Invoice.cs
├── Data/                   # طبقة البيانات
│   └── DatabaseHelper.cs
├── Services/               # طبقة الخدمات
│   ├── AuthenticationService.cs
│   └── AIAnalysisService.cs
├── Forms/                  # واجهات المستخدم
│   ├── LoginForm.cs
│   └── MainForm.cs
├── Utils/                  # الأدوات المساعدة
│   ├── SecurityHelper.cs
│   └── ReportGenerator.cs
└── Properties/             # خصائص المشروع
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:
- **Users**: المستخدمون
- **Accounts**: شجرة الحسابات
- **JournalEntries**: القيود اليومية
- **JournalEntryDetails**: تفاصيل القيود اليومية
- **Receipts**: سندات القبض
- **Payments**: سندات الصرف
- **Products**: المنتجات
- **Customers**: العملاء والموردين
- **Invoices**: الفواتير
- **InvoiceDetails**: تفاصيل الفواتير

## الاستخدام

### تسجيل الدخول
1. تشغيل التطبيق
2. إدخال اسم المستخدم وكلمة المرور
3. النقر على "تسجيل الدخول"

### إدارة الحسابات
1. من القائمة الرئيسية، اختر "الحسابات"
2. اختر "شجرة الحسابات" لعرض الحسابات
3. اختر "إضافة حساب جديد" لإضافة حساب

### إدخال القيود اليومية
1. من القائمة الرئيسية، اختر "القيود اليومية"
2. اختر "إضافة قيد جديد"
3. أدخل تفاصيل القيد
4. تأكد من توازن المدين والدائن

### استخدام ميزات الذكاء الاصطناعي
1. من القائمة الرئيسية، اختر "الذكاء الاصطناعي"
2. اختر "تحليل البيانات المالية" للحصول على تحليل شامل
3. اختر "التوصيات الذكية" للحصول على توصيات مبنية على البيانات

## التطوير المستقبلي

### المرحلة الحالية (مكتملة)
- [x] تطوير واجهات إدارة الحسابات
- [x] تطوير واجهات القيود اليومية
- [x] تطوير تقرير ميزان المراجعة
- [x] تطوير خدمات الذكاء الاصطناعي الأساسية
- [x] تطوير نظام النسخ الاحتياطي
- [x] إنشاء دليل المستخدم

### المرحلة التالية
- [ ] تطوير واجهات إدارة العملاء
- [ ] تطوير واجهات إدارة المنتجات
- [ ] تطوير واجهات الفواتير
- [ ] تطوير سندات القبض والصرف
- [ ] تطوير التقارير المالية المتقدمة
- [ ] تحسين ميزات الذكاء الاصطناعي

### ميزات مستقبلية
- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] تكامل مع البنوك
- [ ] تحليلات ذكاء اصطناعي متقدمة
- [ ] دعم العملات المتعددة
- [ ] نظام الموافقات والتدفقات

## الدعم والمساهمة

للحصول على الدعم أو المساهمة في تطوير المشروع:
1. إنشاء Issue للإبلاغ عن الأخطاء
2. إرسال Pull Request للمساهمات
3. مراجعة الوثائق للمزيد من التفاصيل

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للمزيد من التفاصيل.

## إخلاء المسؤولية

هذا النظام مطور لأغراض تعليمية وتجريبية. يُنصح بإجراء اختبارات شاملة قبل الاستخدام في بيئة الإنتاج.
