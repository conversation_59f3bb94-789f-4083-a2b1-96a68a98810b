using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AccountingSystem.Models;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج إدارة سندات الصرف
    /// </summary>
    public partial class PaymentsForm : Form
    {
        private DataGridView dgvPayments;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnPrint;
        private Button btnReport;
        private Button btnClose;
        private TextBox txtSearch;
        private Label lblSearch;
        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private Label lblFromDate;
        private Label lblToDate;
        private ComboBox cmbPaymentType;
        private Label lblPaymentType;
        private Label lblTotalAmount;
        private TextBox txtTotalAmount;

        private List<Payment> payments;

        public PaymentsForm()
        {
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.dgvPayments = new DataGridView();
            this.btnAdd = new Button();
            this.btnEdit = new Button();
            this.btnDelete = new Button();
            this.btnPrint = new Button();
            this.btnReport = new Button();
            this.btnClose = new Button();
            this.txtSearch = new TextBox();
            this.lblSearch = new Label();
            this.dtpFromDate = new DateTimePicker();
            this.dtpToDate = new DateTimePicker();
            this.lblFromDate = new Label();
            this.lblToDate = new Label();
            this.cmbPaymentType = new ComboBox();
            this.lblPaymentType = new Label();
            this.lblTotalAmount = new Label();
            this.txtTotalAmount = new TextBox();

            this.SuspendLayout();

            // Form
            this.Text = "إدارة سندات الصرف";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Search Controls
            this.lblSearch.Text = "البحث:";
            this.lblSearch.Location = new Point(1100, 20);
            this.lblSearch.Size = new Size(60, 23);
            this.lblSearch.TextAlign = ContentAlignment.MiddleRight;

            this.txtSearch.Location = new Point(950, 20);
            this.txtSearch.Size = new Size(140, 23);
            this.txtSearch.TextChanged += TxtSearch_TextChanged;

            // Date Range Controls
            this.lblFromDate.Text = "من تاريخ:";
            this.lblFromDate.Location = new Point(880, 20);
            this.lblFromDate.Size = new Size(60, 23);
            this.lblFromDate.TextAlign = ContentAlignment.MiddleRight;

            this.dtpFromDate.Location = new Point(750, 20);
            this.dtpFromDate.Size = new Size(120, 23);
            this.dtpFromDate.Format = DateTimePickerFormat.Short;
            this.dtpFromDate.Value = DateTime.Now.AddMonths(-1);
            this.dtpFromDate.ValueChanged += DateFilter_Changed;

            this.lblToDate.Text = "إلى تاريخ:";
            this.lblToDate.Location = new Point(690, 20);
            this.lblToDate.Size = new Size(60, 23);
            this.lblToDate.TextAlign = ContentAlignment.MiddleRight;

            this.dtpToDate.Location = new Point(560, 20);
            this.dtpToDate.Size = new Size(120, 23);
            this.dtpToDate.Format = DateTimePickerFormat.Short;
            this.dtpToDate.Value = DateTime.Now;
            this.dtpToDate.ValueChanged += DateFilter_Changed;

            // Payment Type Filter
            this.lblPaymentType.Text = "نوع الصرف:";
            this.lblPaymentType.Location = new Point(500, 20);
            this.lblPaymentType.Size = new Size(60, 23);
            this.lblPaymentType.TextAlign = ContentAlignment.MiddleRight;

            this.cmbPaymentType.Location = new Point(350, 20);
            this.cmbPaymentType.Size = new Size(140, 23);
            this.cmbPaymentType.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbPaymentType.Items.AddRange(new string[] { "جميع الأنواع", "مصاريف تشغيلية", "رواتب", "إيجارات", "مشتريات", "أخرى" });
            this.cmbPaymentType.SelectedIndex = 0;
            this.cmbPaymentType.SelectedIndexChanged += CmbPaymentType_SelectedIndexChanged;

            // Action Buttons
            this.btnAdd.Text = "إضافة سند";
            this.btnAdd.Location = new Point(1050, 60);
            this.btnAdd.Size = new Size(100, 30);
            this.btnAdd.BackColor = Color.FromArgb(76, 175, 80);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.FlatStyle = FlatStyle.Flat;
            this.btnAdd.Click += BtnAdd_Click;

            this.btnEdit.Text = "تعديل";
            this.btnEdit.Location = new Point(930, 60);
            this.btnEdit.Size = new Size(100, 30);
            this.btnEdit.BackColor = Color.FromArgb(255, 152, 0);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.Click += BtnEdit_Click;

            this.btnDelete.Text = "حذف";
            this.btnDelete.Location = new Point(810, 60);
            this.btnDelete.Size = new Size(100, 30);
            this.btnDelete.BackColor = Color.FromArgb(244, 67, 54);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.Click += BtnDelete_Click;

            this.btnPrint.Text = "طباعة";
            this.btnPrint.Location = new Point(690, 60);
            this.btnPrint.Size = new Size(100, 30);
            this.btnPrint.BackColor = Color.FromArgb(103, 58, 183);
            this.btnPrint.ForeColor = Color.White;
            this.btnPrint.FlatStyle = FlatStyle.Flat;
            this.btnPrint.Click += BtnPrint_Click;

            this.btnReport.Text = "تقرير";
            this.btnReport.Location = new Point(570, 60);
            this.btnReport.Size = new Size(100, 30);
            this.btnReport.BackColor = Color.FromArgb(0, 150, 136);
            this.btnReport.ForeColor = Color.White;
            this.btnReport.FlatStyle = FlatStyle.Flat;
            this.btnReport.Click += BtnReport_Click;

            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(20, 60);
            this.btnClose.Size = new Size(100, 30);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Click += BtnClose_Click;

            // DataGridView
            this.dgvPayments.Location = new Point(20, 110);
            this.dgvPayments.Size = new Size(1160, 500);
            this.dgvPayments.AllowUserToAddRows = false;
            this.dgvPayments.AllowUserToDeleteRows = false;
            this.dgvPayments.ReadOnly = true;
            this.dgvPayments.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvPayments.RightToLeft = RightToLeft.Yes;
            this.dgvPayments.Font = new Font("Tahoma", 10F);
            this.dgvPayments.DoubleClick += DgvPayments_DoubleClick;

            SetupDataGridView();

            // Total Amount
            this.lblTotalAmount.Text = "إجمالي المبلغ:";
            this.lblTotalAmount.Location = new Point(1050, 630);
            this.lblTotalAmount.Size = new Size(100, 23);
            this.lblTotalAmount.TextAlign = ContentAlignment.MiddleRight;
            this.lblTotalAmount.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.txtTotalAmount.Location = new Point(900, 630);
            this.txtTotalAmount.Size = new Size(140, 23);
            this.txtTotalAmount.ReadOnly = true;
            this.txtTotalAmount.BackColor = Color.LightGray;
            this.txtTotalAmount.TextAlign = HorizontalAlignment.Right;
            this.txtTotalAmount.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            // Add controls to form
            this.Controls.Add(this.lblSearch);
            this.Controls.Add(this.txtSearch);
            this.Controls.Add(this.lblFromDate);
            this.Controls.Add(this.dtpFromDate);
            this.Controls.Add(this.lblToDate);
            this.Controls.Add(this.dtpToDate);
            this.Controls.Add(this.lblPaymentType);
            this.Controls.Add(this.cmbPaymentType);
            this.Controls.Add(this.btnAdd);
            this.Controls.Add(this.btnEdit);
            this.Controls.Add(this.btnDelete);
            this.Controls.Add(this.btnPrint);
            this.Controls.Add(this.btnReport);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.dgvPayments);
            this.Controls.Add(this.lblTotalAmount);
            this.Controls.Add(this.txtTotalAmount);

            this.ResumeLayout(false);
        }

        private void SetupDataGridView()
        {
            dgvPayments.Columns.Clear();

            dgvPayments.Columns.Add("Id", "المعرف");
            dgvPayments.Columns["Id"].Width = 60;
            dgvPayments.Columns["Id"].Visible = false;

            dgvPayments.Columns.Add("PaymentNumber", "رقم السند");
            dgvPayments.Columns["PaymentNumber"].Width = 100;

            dgvPayments.Columns.Add("PaymentDate", "التاريخ");
            dgvPayments.Columns["PaymentDate"].Width = 100;
            dgvPayments.Columns["PaymentDate"].DefaultCellStyle.Format = "yyyy/MM/dd";

            dgvPayments.Columns.Add("PaymentType", "نوع الصرف");
            dgvPayments.Columns["PaymentType"].Width = 120;

            dgvPayments.Columns.Add("Beneficiary", "المستفيد");
            dgvPayments.Columns["Beneficiary"].Width = 200;

            dgvPayments.Columns.Add("Amount", "المبلغ");
            dgvPayments.Columns["Amount"].Width = 120;
            dgvPayments.Columns["Amount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvPayments.Columns["Amount"].DefaultCellStyle.Format = "N2";

            dgvPayments.Columns.Add("PaymentMethod", "طريقة الدفع");
            dgvPayments.Columns["PaymentMethod"].Width = 120;

            dgvPayments.Columns.Add("ReferenceNumber", "رقم المرجع");
            dgvPayments.Columns["ReferenceNumber"].Width = 120;

            dgvPayments.Columns.Add("Description", "الوصف");
            dgvPayments.Columns["Description"].Width = 200;

            dgvPayments.Columns.Add("Status", "الحالة");
            dgvPayments.Columns["Status"].Width = 80;
        }

        private void LoadData()
        {
            try
            {
                LoadPayments();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadPayments()
        {
            // إنشاء بيانات تجريبية لسندات الصرف
            payments = new List<Payment>
            {
                new Payment { Id = 1, PaymentNumber = "P001", PaymentDate = DateTime.Now.AddDays(-15), PaymentType = "رواتب", Beneficiary = "أحمد محمد علي", Amount = 8000.00m, PaymentMethod = "تحويل بنكي", ReferenceNumber = "TRF001", Description = "راتب شهر أكتوبر", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-15), CreatedBy = "admin" },
                new Payment { Id = 2, PaymentNumber = "P002", PaymentDate = DateTime.Now.AddDays(-12), PaymentType = "مصاريف تشغيلية", Beneficiary = "شركة الكهرباء", Amount = 1500.00m, PaymentMethod = "شيك", ReferenceNumber = "CHK001", Description = "فاتورة كهرباء أكتوبر", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-12), CreatedBy = "admin" },
                new Payment { Id = 3, PaymentNumber = "P003", PaymentDate = DateTime.Now.AddDays(-10), PaymentType = "إيجارات", Beneficiary = "مالك العقار", Amount = 12000.00m, PaymentMethod = "نقدي", ReferenceNumber = "RENT001", Description = "إيجار المكتب لشهر أكتوبر", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-10), CreatedBy = "admin" },
                new Payment { Id = 4, PaymentNumber = "P004", PaymentDate = DateTime.Now.AddDays(-8), PaymentType = "مشتريات", Beneficiary = "مورد المكتبية", Amount = 2500.00m, PaymentMethod = "بطاقة ائتمان", ReferenceNumber = "CC001", Description = "أدوات مكتبية ومستلزمات", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-8), CreatedBy = "admin" },
                new Payment { Id = 5, PaymentNumber = "P005", PaymentDate = DateTime.Now.AddDays(-5), PaymentType = "مصاريف تشغيلية", Beneficiary = "شركة الاتصالات", Amount = 800.00m, PaymentMethod = "تحويل بنكي", ReferenceNumber = "TRF002", Description = "فاتورة هاتف وإنترنت", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-5), CreatedBy = "admin" },
                new Payment { Id = 6, PaymentNumber = "P006", PaymentDate = DateTime.Now.AddDays(-3), PaymentType = "أخرى", Beneficiary = "شركة الصيانة", Amount = 1200.00m, PaymentMethod = "نقدي", ReferenceNumber = "MAINT001", Description = "صيانة أجهزة الكمبيوتر", Status = "مسودة", CreatedDate = DateTime.Now.AddDays(-3), CreatedBy = "admin" },
                new Payment { Id = 7, PaymentNumber = "P007", PaymentDate = DateTime.Now.AddDays(-1), PaymentType = "رواتب", Beneficiary = "فاطمة عبدالله", Amount = 6500.00m, PaymentMethod = "تحويل بنكي", ReferenceNumber = "TRF003", Description = "راتب شهر أكتوبر", Status = "مسودة", CreatedDate = DateTime.Now.AddDays(-1), CreatedBy = "admin" }
            };

            RefreshDataGridView();
        }

        private void RefreshDataGridView()
        {
            dgvPayments.Rows.Clear();

            var filteredPayments = ApplyFilters();
            decimal totalAmount = 0;

            foreach (var payment in filteredPayments.OrderByDescending(p => p.PaymentDate))
            {
                var rowIndex = dgvPayments.Rows.Add();
                var row = dgvPayments.Rows[rowIndex];

                row.Cells["Id"].Value = payment.Id;
                row.Cells["PaymentNumber"].Value = payment.PaymentNumber;
                row.Cells["PaymentDate"].Value = payment.PaymentDate;
                row.Cells["PaymentType"].Value = payment.PaymentType;
                row.Cells["Beneficiary"].Value = payment.Beneficiary;
                row.Cells["Amount"].Value = payment.Amount;
                row.Cells["PaymentMethod"].Value = payment.PaymentMethod;
                row.Cells["ReferenceNumber"].Value = payment.ReferenceNumber;
                row.Cells["Description"].Value = payment.Description;
                row.Cells["Status"].Value = payment.Status;

                // تلوين الصفوف حسب الحالة
                if (payment.Status == "مؤكد")
                {
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else if (payment.Status == "مسودة")
                {
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                }
                else if (payment.Status == "ملغي")
                {
                    row.DefaultCellStyle.BackColor = Color.LightCoral;
                }

                totalAmount += payment.Amount;
            }

            txtTotalAmount.Text = totalAmount.ToString("N2");
        }

        private List<Payment> ApplyFilters()
        {
            var filtered = payments.AsEnumerable();

            // فلتر التاريخ
            filtered = filtered.Where(p => p.PaymentDate >= dtpFromDate.Value.Date &&
                                          p.PaymentDate <= dtpToDate.Value.Date);

            // فلتر نوع الصرف
            if (cmbPaymentType.SelectedIndex > 0)
            {
                string selectedType = cmbPaymentType.SelectedItem.ToString();
                filtered = filtered.Where(p => p.PaymentType == selectedType);
            }

            // فلتر البحث
            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                string searchText = txtSearch.Text.ToLower();
                filtered = filtered.Where(p =>
                    p.PaymentNumber.ToLower().Contains(searchText) ||
                    p.Beneficiary.ToLower().Contains(searchText) ||
                    p.ReferenceNumber.ToLower().Contains(searchText) ||
                    (p.Description != null && p.Description.ToLower().Contains(searchText))
                );
            }

            return filtered.ToList();
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            RefreshDataGridView();
        }

        private void DateFilter_Changed(object sender, EventArgs e)
        {
            RefreshDataGridView();
        }

        private void CmbPaymentType_SelectedIndexChanged(object sender, EventArgs e)
        {
            RefreshDataGridView();
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new AddEditPaymentForm();
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    var newPayment = addForm.Payment;
                    newPayment.Id = payments.Count > 0 ? payments.Max(p => p.Id) + 1 : 1;
                    newPayment.PaymentNumber = $"P{newPayment.Id:000}";
                    payments.Add(newPayment);
                    RefreshDataGridView();

                    MessageBox.Show("تم إضافة السند بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            EditSelectedPayment();
        }

        private void DgvPayments_DoubleClick(object sender, EventArgs e)
        {
            EditSelectedPayment();
        }

        private void EditSelectedPayment()
        {
            try
            {
                if (dgvPayments.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار سند للتعديل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int paymentId = (int)dgvPayments.SelectedRows[0].Cells["Id"].Value;
                var payment = payments.FirstOrDefault(p => p.Id == paymentId);

                if (payment != null)
                {
                    if (payment.Status == "مؤكد")
                    {
                        MessageBox.Show("لا يمكن تعديل سند مؤكد", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    var editForm = new AddEditPaymentForm(payment);
                    if (editForm.ShowDialog() == DialogResult.OK)
                    {
                        var updatedPayment = editForm.Payment;
                        var index = payments.FindIndex(p => p.Id == paymentId);
                        if (index >= 0)
                        {
                            payments[index] = updatedPayment;
                            RefreshDataGridView();

                            MessageBox.Show("تم تعديل السند بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvPayments.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار سند للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int paymentId = (int)dgvPayments.SelectedRows[0].Cells["Id"].Value;
                var payment = payments.FirstOrDefault(p => p.Id == paymentId);

                if (payment != null)
                {
                    if (payment.Status == "مؤكد")
                    {
                        MessageBox.Show("لا يمكن حذف سند مؤكد", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    var result = MessageBox.Show($"هل أنت متأكد من حذف السند '{payment.PaymentNumber}'؟",
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        payments.Remove(payment);
                        RefreshDataGridView();

                        MessageBox.Show("تم حذف السند بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvPayments.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار سند للطباعة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int paymentId = (int)dgvPayments.SelectedRows[0].Cells["Id"].Value;
                var payment = payments.FirstOrDefault(p => p.Id == paymentId);

                if (payment != null)
                {
                    PrintPayment(payment);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintPayment(Payment payment)
        {
            var reportLines = new List<string>();

            reportLines.Add("سند صرف");
            reportLines.Add("========");
            reportLines.Add($"رقم السند: {payment.PaymentNumber}");
            reportLines.Add($"التاريخ: {payment.PaymentDate:yyyy/MM/dd}");
            reportLines.Add($"نوع الصرف: {payment.PaymentType}");
            reportLines.Add($"المستفيد: {payment.Beneficiary}");
            reportLines.Add($"المبلغ: {payment.Amount:N2} ريال");
            reportLines.Add($"طريقة الدفع: {payment.PaymentMethod}");
            reportLines.Add($"رقم المرجع: {payment.ReferenceNumber}");
            reportLines.Add($"الوصف: {payment.Description}");
            reportLines.Add($"الحالة: {payment.Status}");
            reportLines.Add("");
            reportLines.Add($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");

            string tempFile = System.IO.Path.GetTempFileName() + ".txt";
            System.IO.File.WriteAllLines(tempFile, reportLines, System.Text.Encoding.UTF8);

            try
            {
                System.Diagnostics.Process.Start("notepad.exe", "/p " + tempFile);
            }
            catch
            {
                MessageBox.Show($"تم حفظ السند في: {tempFile}", "سند الصرف",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportForm = new PaymentsReportForm(ApplyFilters());
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
