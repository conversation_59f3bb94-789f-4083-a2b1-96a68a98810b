@echo off
chcp 65001 > nul
echo ========================================
echo بناء النظام خطوة بخطوة
echo ========================================
echo.

echo هذا البناء سيتم على مراحل لتجنب مشاكل المراجع
echo.

set errors=0

echo ========================================
echo المرحلة 1: بناء النماذج الأساسية
echo ========================================

echo بناء النماذج الأساسية...
csc /target:library ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /out:Models.dll ^
    Models\Account.cs ^
    Models\JournalEntry.cs ^
    Models\Customer.cs ^
    Models\Product.cs ^
    Models\Receipt.cs ^
    Models\Payment.cs ^
    Models\Invoice.cs ^
    Models\User.cs

if %ERRORLEVEL% NEQ 0 (
    echo ✗ فشل بناء النماذج الأساسية
    set /a errors+=1
    goto :end
) else (
    echo ✓ تم بناء النماذج الأساسية بنجاح
)

echo.
echo ========================================
echo المرحلة 2: بناء الخدمات والأدوات
echo ========================================

echo بناء الخدمات والأدوات...
csc /target:library ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Data.dll ^
    /reference:System.Configuration.dll ^
    /reference:Models.dll ^
    /out:Services.dll ^
    Services\SimpleAuthenticationService.cs ^
    Services\AccountService.cs ^
    Services\JournalService.cs ^
    Services\AIAnalysisService.cs ^
    Data\SimpleDatabaseHelper.cs ^
    Utils\SecurityHelper.cs ^
    Utils\ReportGenerator.cs ^
    Utils\BackupHelper.cs ^
    Utils\ConfigHelper.cs

if %ERRORLEVEL% NEQ 0 (
    echo ✗ فشل بناء الخدمات والأدوات
    set /a errors+=1
    goto :end
) else (
    echo ✓ تم بناء الخدمات والأدوات بنجاح
)

echo.
echo ========================================
echo المرحلة 3: بناء النماذج الأساسية للواجهات
echo ========================================

echo بناء النماذج الأساسية للواجهات...
csc /target:library ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:Models.dll ^
    /reference:Services.dll ^
    /out:BasicForms.dll ^
    Forms\LoginForm.cs ^
    Forms\AddEditAccountForm.cs ^
    Forms\AddEditCustomerForm.cs ^
    Forms\AddEditProductForm.cs ^
    Properties\Resources.Designer.cs ^
    Properties\Settings.Designer.cs

if %ERRORLEVEL% NEQ 0 (
    echo ✗ فشل بناء النماذج الأساسية للواجهات
    set /a errors+=1
    goto :end
) else (
    echo ✓ تم بناء النماذج الأساسية للواجهات بنجاح
)

echo.
echo ========================================
echo المرحلة 4: بناء نماذج السندات والتقارير
echo ========================================

echo بناء نماذج السندات والتقارير...
csc /target:library ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:Models.dll ^
    /reference:Services.dll ^
    /reference:BasicForms.dll ^
    /out:AdvancedForms.dll ^
    Forms\AddEditReceiptForm.cs ^
    Forms\AddEditPaymentForm.cs ^
    Forms\CustomerPaymentForm.cs ^
    Forms\ReceiptsReportForm.cs ^
    Forms\PaymentsReportForm.cs

if %ERRORLEVEL% NEQ 0 (
    echo ✗ فشل بناء نماذج السندات والتقارير
    set /a errors+=1
    goto :end
) else (
    echo ✓ تم بناء نماذج السندات والتقارير بنجاح
)

echo.
echo ========================================
echo المرحلة 5: بناء النماذج الرئيسية
echo ========================================

echo بناء النماذج الرئيسية...
csc /target:library ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:Models.dll ^
    /reference:Services.dll ^
    /reference:BasicForms.dll ^
    /reference:AdvancedForms.dll ^
    /out:MainForms.dll ^
    Forms\AccountsTreeForm.cs ^
    Forms\JournalEntryForm.cs ^
    Forms\JournalEntriesListForm.cs ^
    Forms\TrialBalanceForm.cs ^
    Forms\CustomersForm.cs ^
    Forms\ProductsForm.cs ^
    Forms\ReceiptsForm.cs ^
    Forms\PaymentsForm.cs ^
    Forms\CustomerStatementForm.cs

if %ERRORLEVEL% NEQ 0 (
    echo ✗ فشل بناء النماذج الرئيسية
    set /a errors+=1
    goto :end
) else (
    echo ✓ تم بناء النماذج الرئيسية بنجاح
)

echo.
echo ========================================
echo المرحلة 6: بناء التطبيق النهائي
echo ========================================

echo بناء التطبيق النهائي...
csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Configuration.dll ^
    /reference:Models.dll ^
    /reference:Services.dll ^
    /reference:BasicForms.dll ^
    /reference:AdvancedForms.dll ^
    /reference:MainForms.dll ^
    /out:AccountingSystemStepByStep.exe ^
    Forms\MainForm.cs ^
    Program.cs

if %ERRORLEVEL% NEQ 0 (
    echo ✗ فشل بناء التطبيق النهائي
    set /a errors+=1
    goto :end
) else (
    echo ✓ تم بناء التطبيق النهائي بنجاح
)

:end
echo.
echo ========================================
echo تنظيف الملفات المؤقتة
echo ========================================

del Models.dll >nul 2>&1
del Services.dll >nul 2>&1
del BasicForms.dll >nul 2>&1
del AdvancedForms.dll >nul 2>&1
del MainForms.dll >nul 2>&1

if %errors% EQU 0 (
    echo.
    echo ✓ تم بناء النظام بنجاح!
    echo.
    echo تم إنشاء: AccountingSystemStepByStep.exe
    echo.
    
    echo نسخ ملفات الإعداد...
    if exist "App.config" copy "App.config" "AccountingSystemStepByStep.exe.config" >nul
    
    set /p run="هل تريد تشغيل النظام الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام...
        start "" "AccountingSystemStepByStep.exe"
        echo.
        echo تم تشغيل النظام بنجاح!
    )
    
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    
) else (
    echo.
    echo ✗ فشل في بناء النظام
    echo عدد الأخطاء: %errors%
    echo.
    echo جرب البناء المبسط:
    echo build_simple.bat
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
