using System;
using System.Drawing;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Services;

namespace IntegratedInvoiceSystem.Forms
{
    /// <summary>
    /// نموذج معلومات الشركة
    /// </summary>
    public partial class CompanyInfoForm : Form
    {
        #region المتغيرات
        private TextBox txtCompanyName;
        private TextBox txtAddress;
        private TextBox txtPhone;
        private TextBox txtEmail;
        private TextBox txtTaxNumber;
        private TextBox txtCommercialRegister;
        private PictureBox picLogo;
        private Button btnSave;
        private Button btnCancel;
        private Button btnBrowseLogo;
        #endregion

        #region البناء والتهيئة
        public CompanyInfoForm()
        {
            InitializeComponent();
            LoadCompanyInfo();
        }

        private void InitializeComponent()
        {
            this.Text = "معلومات الشركة";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
            SetupLayout();
        }

        private void CreateControls()
        {
            // اسم الشركة
            var lblCompanyName = new Label();
            lblCompanyName.Text = "اسم الشركة:";
            lblCompanyName.Location = new Point(450, 30);
            lblCompanyName.Size = new Size(100, 23);

            txtCompanyName = new TextBox();
            txtCompanyName.Location = new Point(200, 30);
            txtCompanyName.Size = new Size(240, 23);

            // العنوان
            var lblAddress = new Label();
            lblAddress.Text = "العنوان:";
            lblAddress.Location = new Point(450, 70);
            lblAddress.Size = new Size(100, 23);

            txtAddress = new TextBox();
            txtAddress.Location = new Point(200, 70);
            txtAddress.Size = new Size(240, 60);
            txtAddress.Multiline = true;

            // الهاتف
            var lblPhone = new Label();
            lblPhone.Text = "الهاتف:";
            lblPhone.Location = new Point(450, 150);
            lblPhone.Size = new Size(100, 23);

            txtPhone = new TextBox();
            txtPhone.Location = new Point(200, 150);
            txtPhone.Size = new Size(240, 23);

            // البريد الإلكتروني
            var lblEmail = new Label();
            lblEmail.Text = "البريد الإلكتروني:";
            lblEmail.Location = new Point(450, 190);
            lblEmail.Size = new Size(100, 23);

            txtEmail = new TextBox();
            txtEmail.Location = new Point(200, 190);
            txtEmail.Size = new Size(240, 23);

            // الرقم الضريبي
            var lblTaxNumber = new Label();
            lblTaxNumber.Text = "الرقم الضريبي:";
            lblTaxNumber.Location = new Point(450, 230);
            lblTaxNumber.Size = new Size(100, 23);

            txtTaxNumber = new TextBox();
            txtTaxNumber.Location = new Point(200, 230);
            txtTaxNumber.Size = new Size(240, 23);

            // السجل التجاري
            var lblCommercialRegister = new Label();
            lblCommercialRegister.Text = "السجل التجاري:";
            lblCommercialRegister.Location = new Point(450, 270);
            lblCommercialRegister.Size = new Size(100, 23);

            txtCommercialRegister = new TextBox();
            txtCommercialRegister.Location = new Point(200, 270);
            txtCommercialRegister.Size = new Size(240, 23);

            // شعار الشركة
            var lblLogo = new Label();
            lblLogo.Text = "شعار الشركة:";
            lblLogo.Location = new Point(450, 310);
            lblLogo.Size = new Size(100, 23);

            picLogo = new PictureBox();
            picLogo.Location = new Point(200, 310);
            picLogo.Size = new Size(100, 100);
            picLogo.BorderStyle = BorderStyle.FixedSingle;
            picLogo.SizeMode = PictureBoxSizeMode.StretchImage;

            btnBrowseLogo = new Button();
            btnBrowseLogo.Text = "تصفح...";
            btnBrowseLogo.Location = new Point(320, 310);
            btnBrowseLogo.Size = new Size(80, 30);
            btnBrowseLogo.Click += BtnBrowseLogo_Click;

            // أزرار الحفظ والإلغاء
            btnSave = new Button();
            btnSave.Text = "حفظ";
            btnSave.Location = new Point(350, 430);
            btnSave.Size = new Size(100, 35);
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button();
            btnCancel.Text = "إلغاء";
            btnCancel.Location = new Point(240, 430);
            btnCancel.Size = new Size(100, 35);
            btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.Click += BtnCancel_Click;

            // إضافة العناصر للنموذج
            this.Controls.AddRange(new Control[] {
                lblCompanyName, txtCompanyName,
                lblAddress, txtAddress,
                lblPhone, txtPhone,
                lblEmail, txtEmail,
                lblTaxNumber, txtTaxNumber,
                lblCommercialRegister, txtCommercialRegister,
                lblLogo, picLogo, btnBrowseLogo,
                btnSave, btnCancel
            });
        }

        private void SetupLayout()
        {
            // تحسين التخطيط والألوان
            this.BackColor = Color.FromArgb(248, 249, 250);
            
            foreach (Control control in this.Controls)
            {
                if (control is Label)
                {
                    control.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    control.ForeColor = Color.FromArgb(52, 73, 94);
                }
                else if (control is TextBox)
                {
                    control.Font = new Font("Segoe UI", 10F);
                    ((TextBox)control).BorderStyle = BorderStyle.FixedSingle;
                }
            }
        }
        #endregion

        #region معالجات الأحداث
        private void BtnBrowseLogo_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.gif";
                openFileDialog.Title = "اختر شعار الشركة";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        picLogo.Image = Image.FromFile(openFileDialog.FileName);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تحميل الصورة: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveCompanyInfo();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
        #endregion

        #region الوظائف المساعدة
        private void LoadCompanyInfo()
        {
            try
            {
                // تحميل معلومات الشركة من الإعدادات
                txtCompanyName.Text = Properties.Settings.Default.CompanyName ?? "شركة المثال للتجارة";
                txtAddress.Text = Properties.Settings.Default.CompanyAddress ?? "الرياض، المملكة العربية السعودية";
                txtPhone.Text = Properties.Settings.Default.CompanyPhone ?? "+966 11 123 4567";
                txtEmail.Text = Properties.Settings.Default.CompanyEmail ?? "<EMAIL>";
                txtTaxNumber.Text = Properties.Settings.Default.TaxNumber ?? "*********";
                txtCommercialRegister.Text = Properties.Settings.Default.CommercialRegister ?? "1010123456";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معلومات الشركة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtCompanyName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الشركة", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCompanyName.Focus();
                return false;
            }

            return true;
        }

        private void SaveCompanyInfo()
        {
            try
            {
                // حفظ معلومات الشركة في الإعدادات
                Properties.Settings.Default.CompanyName = txtCompanyName.Text;
                Properties.Settings.Default.CompanyAddress = txtAddress.Text;
                Properties.Settings.Default.CompanyPhone = txtPhone.Text;
                Properties.Settings.Default.CompanyEmail = txtEmail.Text;
                Properties.Settings.Default.TaxNumber = txtTaxNumber.Text;
                Properties.Settings.Default.CommercialRegister = txtCommercialRegister.Text;
                Properties.Settings.Default.Save();

                MessageBox.Show("تم حفظ معلومات الشركة بنجاح", "نجح الحفظ",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ معلومات الشركة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion
    }
}
