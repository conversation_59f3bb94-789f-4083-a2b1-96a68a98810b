//------------------------------------------------------------------------------
// <auto-generated>
//     تم إنشاء هذا الرمز بواسطة أداة.
//     إصدار وقت التشغيل:4.0.30319.42000
//
//     التغييرات على هذا الملف قد تسبب سلوكًا غير صحيح وستفقد إذا
//     تم إعادة إنشاء الرمز.
// </auto-generated>
//------------------------------------------------------------------------------

namespace AccountingSystem.Properties {
    using System;
    
    
    /// <summary>
    ///   فئة مورد مكتوبة بقوة للبحث عن السلاسل المترجمة، إلخ.
    /// </summary>
    // تم إنشاء هذه الفئة تلقائيًا بواسطة فئة StronglyTypedResourceBuilder
    // عبر أداة مثل ResGen أو Visual Studio.
    // لإضافة أو إزالة عضو، قم بتحرير ملف .ResX الخاص بك ثم قم بتشغيل ResGen
    // مع مفتاح /str، أو أعد بناء مشروع VS الخاص بك.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   إرجاع مثيل ResourceManager المخزن مؤقتًا المستخدم بواسطة هذه الفئة.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("AccountingSystem.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   يتجاوز خاصية CurrentUICulture للخيط الحالي لجميع
        ///   عمليات البحث عن الموارد باستخدام فئة الموارد المكتوبة بقوة هذه.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
    }
}
