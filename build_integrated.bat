@echo off
chcp 65001 > nul
echo ========================================
echo بناء النظام المتكامل للفواتير - الإصدار المجزأ
echo ========================================
echo.

echo 🎯 النظام المدمج والشامل - مجزأ لسهولة التطوير
echo ✨ دمج جميع الميزات في نظام واحد مع تجزئة الملفات
echo 🏠 واجهة رئيسية محسنة مع داشبورد متكامل
echo.

rem التحقق من وجود مترجم C#
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ مترجم C# غير متوفر
    echo يرجى فتح Developer Command Prompt for Visual Studio
    goto :end
)

echo ✓ مترجم C# متوفر
echo.

echo فحص الملفات المطلوبة...

if not exist "Models\DataModels.cs" (
    echo ✗ ملف Models\DataModels.cs مفقود
    goto :end
)

if not exist "Services\DataService.cs" (
    echo ✗ ملف Services\DataService.cs مفقود
    goto :end
)

if not exist "Services\AuthService.cs" (
    echo ✗ ملف Services\AuthService.cs مفقود
    goto :end
)

if not exist "Forms\MainDashboardForm.cs" (
    echo ✗ ملف Forms\MainDashboardForm.cs مفقود
    goto :end
)

if not exist "Controls\DashboardControl.cs" (
    echo ✗ ملف Controls\DashboardControl.cs مفقود
    goto :end
)

if not exist "IntegratedProgram.cs" (
    echo ✗ ملف IntegratedProgram.cs مفقود
    goto :end
)

echo ✓ جميع الملفات موجودة
echo.

echo بناء النظام المتكامل...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /main:IntegratedInvoiceSystem.IntegratedProgram ^
    /out:IntegratedInvoiceSystem.exe ^
    Models\DataModels.cs ^
    Services\DataService.cs ^
    Services\AuthService.cs ^
    Forms\MainDashboardForm.cs ^
    Controls\DashboardControl.cs ^
    IntegratedProgram.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء النظام المتكامل بنجاح!
    echo.
    echo تم إنشاء: IntegratedInvoiceSystem.exe
    echo.
    
    echo إنشاء مجلدات البيانات...
    if not exist "Data" mkdir Data
    if not exist "Backup" mkdir Backup
    if not exist "Reports" mkdir Reports
    echo ✓ تم إنشاء مجلدات البيانات
    echo.
    
    set /p run="هل تريد تشغيل النظام المتكامل الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام المتكامل...
        start "" "IntegratedInvoiceSystem.exe"
        echo.
        echo ✓ تم تشغيل النظام المتكامل بنجاح!
    )
    
    echo.
    echo ========================================
    echo النظام المتكامل للفواتير - الإصدار 3.0
    echo ========================================
    echo.
    echo 👤 معلومات تسجيل الدخول:
    echo.
    echo المدير:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo الصلاحيات: جميع الصلاحيات
    echo.
    echo الموظف:
    echo اسم المستخدم: user
    echo كلمة المرور: user123
    echo الصلاحيات: الفواتير والعملاء فقط
    echo.
    echo 🎯 الميزات الجديدة في النظام المجزأ:
    echo ✅ تجزئة الكود إلى ملفات منفصلة لسهولة التطوير
    echo ✅ نماذج البيانات في ملف منفصل (Models\DataModels.cs)
    echo ✅ خدمات البيانات في ملف منفصل (Services\DataService.cs)
    echo ✅ خدمة المصادقة في ملف منفصل (Services\AuthService.cs)
    echo ✅ النموذج الرئيسي في ملف منفصل (Forms\MainDashboardForm.cs)
    echo ✅ عنصر الداشبورد في ملف منفصل (Controls\DashboardControl.cs)
    echo ✅ سهولة الصيانة والتطوير
    echo ✅ تنظيم أفضل للكود
    echo ✅ إمكانية العمل الجماعي على المشروع
    echo.
    echo 🏗️ هيكل المشروع:
    echo • Models\ - نماذج البيانات والكائنات
    echo • Services\ - خدمات البيانات والمصادقة
    echo • Forms\ - النماذج والواجهات
    echo • Controls\ - العناصر المخصصة
    echo • Data\ - ملفات البيانات
    echo • Backup\ - النسخ الاحتياطية
    echo • Reports\ - التقارير المُصدرة
    echo.
    echo 📊 الداشبورد المتكامل:
    echo • 5 بطاقات إحصائيات رئيسية
    echo • رسم بياني للمبيعات (7 أيام)
    echo • جدول الفواتير الأخيرة (12 فاتورة)
    echo • أعلى العملاء رصيداً (7 عملاء)
    echo • إحصائيات سريعة (5 إحصائيات)
    echo • تلوين تلقائي حسب الحالة
    echo • تحديث فوري للبيانات
    echo.
    echo 🎯 الأقسام المتوفرة:
    echo • 🏠 الداشبورد الرئيسي - إحصائيات شاملة وتفاعلية
    echo • 📊 إدارة الفواتير - عرض وإدارة كاملة للفواتير
    echo • ➕ إضافة فاتورة جديدة - نموذج متطور لإنشاء الفواتير
    echo • 💰 السندات والمدفوعات - إدارة المدفوعات والتحصيلات
    echo • 👥 إدارة العملاء - قاعدة بيانات العملاء الشاملة
    echo • 📦 إدارة المنتجات - كتالوج المنتجات والمخزون
    echo • 📈 التقارير والإحصائيات - تحليلات مفصلة ومتقدمة
    echo • 🏢 معلومات الشركة - بيانات الشركة والإعدادات
    echo • 👤 إدارة المستخدمين - إدارة المستخدمين والصلاحيات
    echo • ⚙️ الإعدادات العامة - تخصيص النظام والتفضيلات
    echo • 💾 حفظ البيانات - نسخ احتياطية وحفظ
    echo • 🚪 تسجيل خروج - خروج آمن من النظام
    echo.
    echo 📊 البيانات التجريبية الشاملة:
    echo • 8 عملاء بمعلومات كاملة وأرصدة متنوعة
    echo • 10 منتجات بفئات وأسعار مختلفة
    echo • 5 فواتير بحالات وأصناف متعددة
    echo • 3 سندات قبض مربوطة بالفواتير
    echo • 2 مستخدم بصلاحيات مختلفة
    echo • معلومات شركة كاملة
    echo • بيانات واقعية ومنطقية
    echo.
    echo 🎉 هذا أشمل وأفضل نظام فواتير متكامل ومجزأ!
    echo.
    echo 💡 مزايا التجزئة:
    echo • سهولة التطوير والصيانة
    echo • إمكانية العمل الجماعي
    echo • تنظيم أفضل للكود
    echo • سهولة إضافة ميزات جديدة
    echo • تقليل التعقيد
    echo • إعادة الاستخدام
    
) else (
    echo.
    echo ✗ فشل في بناء النظام المتكامل
    echo.
    echo تحقق من:
    echo 1. وجود جميع الملفات المطلوبة
    echo 2. عدم وجود أخطاء في الكود
    echo 3. استخدام Developer Command Prompt
    echo 4. صحة مراجع المكتبات
)

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
