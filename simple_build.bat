@echo off
chcp 65001 > nul
echo ========================================
echo بناء نظام المحاسبة الذكي (مبسط)
echo ========================================
echo.

echo هذا البناء المبسط لا يتطلب Visual Studio
echo سيتم استخدام مترجم C# المدمج في Windows
echo.

echo التحقق من وجود مترجم C#...
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo لم يتم العثور على مترجم C#
    echo يرجى تثبيت .NET Framework SDK أو Visual Studio
    echo أو استخدام Developer Command Prompt
    pause
    exit /b 1
)

echo ✓ تم العثور على مترجم C#

echo.
echo التحقق من وجود الملفات المطلوبة...

set missing_files=0

if not exist "Program.cs" (
    echo ✗ Program.cs مفقود
    set /a missing_files+=1
) else (
    echo ✓ Program.cs موجود
)

if not exist "App.config" (
    echo ✗ App.config مفقود
    set /a missing_files+=1
) else (
    echo ✓ App.config موجود
)

if %missing_files% GTR 0 (
    echo.
    echo خطأ: يوجد ملفات مفقودة ضرورية
    pause
    exit /b 1
)

echo.
echo إنشاء قائمة بملفات المصدر...

echo Program.cs > sources.txt
echo Properties\AssemblyInfo.cs >> sources.txt

for /r Models %%f in (*.cs) do echo %%f >> sources.txt
for /r Data %%f in (*.cs) do echo %%f >> sources.txt
for /r Services %%f in (*.cs) do echo %%f >> sources.txt
for /r Utils %%f in (*.cs) do echo %%f >> sources.txt
for /r Forms %%f in (*.cs) do echo %%f >> sources.txt

echo Properties\Resources.Designer.cs >> sources.txt
echo Properties\Settings.Designer.cs >> sources.txt

echo.
echo بناء المشروع...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:System.Configuration.dll ^
    /out:AccountingSystem.exe ^
    /filelist:sources.txt

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء المشروع بنجاح!
    echo.
    
    echo نسخ ملفات الإعداد...
    if exist "App.config" copy "App.config" "AccountingSystem.exe.config" >nul
    if exist "system_config.json" echo ✓ ملف الإعداد موجود
    
    echo.
    echo ========================================
    echo تم إكمال البناء بنجاح!
    echo ========================================
    echo.
    echo يمكنك الآن تشغيل النظام باستخدام:
    echo AccountingSystem.exe
    echo.
    echo أو تشغيل start_system.bat
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    
    del sources.txt >nul 2>&1
    
) else (
    echo.
    echo ✗ فشل في بناء المشروع
    echo.
    echo الأخطاء المحتملة:
    echo 1. ملفات مكتبات مفقودة (SQLite, Dapper, Newtonsoft.Json)
    echo 2. أخطاء في الكود
    echo 3. مراجع مفقودة
    echo.
    echo للحصول على بناء كامل، استخدم:
    echo build_and_run.bat (يتطلب Visual Studio أو MSBuild)
    
    del sources.txt >nul 2>&1
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
