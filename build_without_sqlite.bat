@echo off
chcp 65001 > nul
echo ========================================
echo بناء النظام بدون SQLite (حل مشكلة Mixed Mode)
echo ========================================
echo.

echo هذا البناء يتجنب مشكلة Mixed Mode Assembly
echo ويستخدم ملفات نصية بدلاً من SQLite
echo.

echo التحقق من وجود مترجم C#...
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo لم يتم العثور على مترجم C#
    echo.
    echo الحلول المقترحة:
    echo 1. تشغيل Developer Command Prompt for Visual Studio
    echo 2. تثبيت .NET Framework SDK
    echo 3. إضافة مسار مترجم C# إلى متغير PATH
    echo.
    pause
    exit /b 1
)

echo ✓ تم العثور على مترجم C#

echo.
echo التحقق من وجود الملفات المطلوبة...

set missing_files=0

rem فحص الملفات الأساسية
set required_files=Program.cs App.config

for %%f in (%required_files%) do (
    if not exist "%%f" (
        echo ✗ %%f مفقود
        set /a missing_files+=1
    ) else (
        echo ✓ %%f موجود
    )
)

rem فحص المجلدات
set required_folders=Models Data Services Utils Forms Properties

for %%d in (%required_folders%) do (
    if not exist "%%d" (
        echo ✗ مجلد %%d مفقود
        set /a missing_files+=1
    ) else (
        echo ✓ مجلد %%d موجود
    )
)

if %missing_files% GTR 0 (
    echo.
    echo خطأ: يوجد %missing_files% ملف/مجلد مفقود
    echo يرجى التأكد من وجود جميع ملفات المشروع
    pause
    exit /b 1
)

echo.
echo إنشاء قائمة بملفات المصدر (بدون SQLite)...

echo Program.cs > sources_no_sqlite.txt
echo Properties\AssemblyInfo.cs >> sources_no_sqlite.txt

rem إضافة النماذج (Models)
for %%f in (Models\*.cs) do echo %%f >> sources_no_sqlite.txt

rem إضافة الخدمات (بدون SQLite)
echo Services\SimpleAuthenticationService.cs >> sources_no_sqlite.txt
echo Services\AIAnalysisService.cs >> sources_no_sqlite.txt

rem إضافة البيانات (بدون SQLite)
echo Data\SimpleDatabaseHelper.cs >> sources_no_sqlite.txt

rem إضافة الأدوات
for %%f in (Utils\*.cs) do (
    rem تجاهل ConfigHelper إذا كان يسبب مشاكل
    if not "%%f"=="Utils\ConfigHelper.cs" (
        echo %%f >> sources_no_sqlite.txt
    )
)

rem إضافة النماذج
echo Forms\LoginForm.cs >> sources_no_sqlite.txt
echo Forms\MainForm.cs >> sources_no_sqlite.txt
echo Forms\AccountsTreeForm.cs >> sources_no_sqlite.txt
echo Forms\AddEditAccountForm.cs >> sources_no_sqlite.txt
echo Forms\JournalEntryForm.cs >> sources_no_sqlite.txt
echo Forms\JournalEntriesListForm.cs >> sources_no_sqlite.txt
echo Forms\TrialBalanceForm.cs >> sources_no_sqlite.txt
echo Forms\CustomersForm.cs >> sources_no_sqlite.txt
echo Forms\AddEditCustomerForm.cs >> sources_no_sqlite.txt
echo Forms\ProductsForm.cs >> sources_no_sqlite.txt
echo Forms\AddEditProductForm.cs >> sources_no_sqlite.txt

rem إضافة ملفات Properties
echo Properties\Resources.Designer.cs >> sources_no_sqlite.txt
echo Properties\Settings.Designer.cs >> sources_no_sqlite.txt

echo.
echo بناء المشروع بدون SQLite...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:System.Configuration.dll ^
    /out:AccountingSystemNoSQLite.exe ^
    /filelist:sources_no_sqlite.txt

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء المشروع بنجاح!
    echo.

    echo نسخ ملفات الإعداد...
    if exist "App.config" copy "App.config" "AccountingSystemNoSQLite.exe.config" >nul
    if exist "system_config.json" echo ✓ ملف الإعداد موجود

    echo.
    echo ========================================
    echo تم إكمال البناء بنجاح!
    echo ========================================
    echo.
    echo تم إنشاء: AccountingSystemNoSQLite.exe
    echo.

    set /p run="هل تريد تشغيل النظام الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام...
        start "" "AccountingSystemNoSQLite.exe"
        echo.
        echo تم تشغيل النظام بنجاح!
    )

    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo ملاحظات:
    echo • يستخدم ملفات نصية بدلاً من SQLite
    echo • يتجنب مشكلة Mixed Mode Assembly
    echo • جميع الميزات الأساسية متوفرة
    echo • البيانات تحفظ في مجلد SimpleData

    del sources_no_sqlite.txt >nul 2>&1

) else (
    echo.
    echo ✗ فشل في بناء المشروع
    echo.
    echo الأخطاء المحتملة:
    echo 1. أخطاء في الكود
    echo 2. مراجع مفقودة
    echo 3. مشاكل في مترجم C#
    echo.
    echo للحصول على بناء مبسط أكثر، جرب:
    echo build_simple.bat

    del sources_no_sqlite.txt >nul 2>&1
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
