using AccountingSystem.Services;
using IntegratedInvoiceSystem.Forms;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج تسجيل الدخول
    /// </summary>
    public partial class LoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnExit;
        private Label lblTitle;
        private Label lblUsername;
        private Label lblPassword;
        private PictureBox pictureBox;
        
        public LoginForm()
        {
            InitializeComponent();
            SetupForm();
        }
        
        private void InitializeComponent()
        {
            this.txtUsername = new TextBox();
            this.txtPassword = new TextBox();
            this.btnLogin = new Button();
            this.btnExit = new Button();
            this.lblTitle = new Label();
            this.lblUsername = new Label();
            this.lblPassword = new Label();
            this.pictureBox = new PictureBox();
            
            this.SuspendLayout();
            
            // Form
            this.Text = "تسجيل الدخول - نظام المحاسبة الذكي";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Title Label
            this.lblTitle.Text = "نظام المحاسبة الذكي";
            this.lblTitle.Font = new Font("Tahoma", 18F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(25, 118, 210);
            this.lblTitle.Location = new Point(50, 30);
            this.lblTitle.Size = new Size(350, 35);
            this.lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            
            // Picture Box
            this.pictureBox.Location = new Point(175, 70);
            this.pictureBox.Size = new Size(100, 80);
            this.pictureBox.BackColor = Color.FromArgb(25, 118, 210);
            this.pictureBox.BorderStyle = BorderStyle.FixedSingle;
            
            // Username Label
            this.lblUsername.Text = "اسم المستخدم:";
            this.lblUsername.Font = new Font("Tahoma", 10F);
            this.lblUsername.Location = new Point(320, 170);
            this.lblUsername.Size = new Size(100, 23);
            this.lblUsername.TextAlign = ContentAlignment.MiddleRight;
            
            // Username TextBox
            this.txtUsername.Font = new Font("Tahoma", 10F);
            this.txtUsername.Location = new Point(50, 170);
            this.txtUsername.Size = new Size(250, 23);
            this.txtUsername.Text = "admin"; // قيمة افتراضية للاختبار
            
            // Password Label
            this.lblPassword.Text = "كلمة المرور:";
            this.lblPassword.Font = new Font("Tahoma", 10F);
            this.lblPassword.Location = new Point(320, 210);
            this.lblPassword.Size = new Size(100, 23);
            this.lblPassword.TextAlign = ContentAlignment.MiddleRight;
            
            // Password TextBox
            this.txtPassword.Font = new Font("Tahoma", 10F);
            this.txtPassword.Location = new Point(50, 210);
            this.txtPassword.Size = new Size(250, 23);
            this.txtPassword.UseSystemPasswordChar = true;
            this.txtPassword.Text = "admin123"; // قيمة افتراضية للاختبار
            
            // Login Button
            this.btnLogin.Text = "تسجيل الدخول";
            this.btnLogin.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            this.btnLogin.Location = new Point(200, 260);
            this.btnLogin.Size = new Size(120, 35);
            this.btnLogin.BackColor = Color.FromArgb(25, 118, 210);
            this.btnLogin.ForeColor = Color.White;
            this.btnLogin.FlatStyle = FlatStyle.Flat;
            this.btnLogin.FlatAppearance.BorderSize = 0;
            this.btnLogin.Click += new EventHandler(this.btnLogin_Click);
            
            // Exit Button
            this.btnExit.Text = "خروج";
            this.btnExit.Font = new Font("Tahoma", 10F);
            this.btnExit.Location = new Point(50, 260);
            this.btnExit.Size = new Size(120, 35);
            this.btnExit.BackColor = Color.FromArgb(244, 67, 54);
            this.btnExit.ForeColor = Color.White;
            this.btnExit.FlatStyle = FlatStyle.Flat;
            this.btnExit.FlatAppearance.BorderSize = 0;
            this.btnExit.Click += new EventHandler(this.btnExit_Click);
            
            // Add controls to form
            this.Controls.Add(this.lblTitle);
            this.Controls.Add(this.pictureBox);
            this.Controls.Add(this.lblUsername);
            this.Controls.Add(this.txtUsername);
            this.Controls.Add(this.lblPassword);
            this.Controls.Add(this.txtPassword);
            this.Controls.Add(this.btnLogin);
            this.Controls.Add(this.btnExit);
            
            this.ResumeLayout(false);
        }
        
        private void SetupForm()
        {
            // تعيين Enter كزر افتراضي
            this.AcceptButton = this.btnLogin;
            
            // تعيين Escape كزر إلغاء
            this.CancelButton = this.btnExit;
            
            // التركيز على حقل اسم المستخدم
            this.txtUsername.Focus();
        }
        
        private void btnLogin_Click(object sender, EventArgs e)
        {
            try
            {
                string username = txtUsername.Text.Trim();
                string password = txtPassword.Text;
                
                if (string.IsNullOrEmpty(username))
                {
                    MessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }
                
                if (string.IsNullOrEmpty(password))
                {
                    MessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return;
                }
                
                // تعطيل الأزرار أثناء المعالجة
                btnLogin.Enabled = false;
                btnExit.Enabled = false;
                this.Cursor = Cursors.WaitCursor;
                
                // محاولة تسجيل الدخول
                if (AuthenticationService.Login(username, password))
                {
                    // إخفاء نموذج تسجيل الدخول
                    this.Hide();
                    
                    // فتح النموذج الرئيسي
                    var mainForm = new MainDashboardForm();
                    //var mainForm = new MainForm();
                    mainForm.FormClosed += (s, args) => this.Close();
                    mainForm.Show();
                }
                else
                {
                    MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    
                    // مسح كلمة المرور والتركيز عليها
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدخول: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // إعادة تفعيل الأزرار
                btnLogin.Enabled = true;
                btnExit.Enabled = true;
                this.Cursor = Cursors.Default;
            }
        }
        
        private void btnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }
        
        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnLogin_Click(this, EventArgs.Empty);
            }
            else if (e.KeyCode == Keys.Escape)
            {
                btnExit_Click(this, EventArgs.Empty);
            }
            
            base.OnKeyDown(e);
        }
    }
}
