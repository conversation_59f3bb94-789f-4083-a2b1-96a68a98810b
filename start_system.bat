@echo off
chcp 65001 > nul
echo ========================================
echo نظام المحاسبة الذكي
echo ========================================
echo.

echo التحقق من وجود الملفات المطلوبة...

if not exist "AccountingSystem.exe" (
    echo لم يتم العثور على ملف التطبيق الرئيسي
    echo يرجى تشغيل build_and_run.bat أولاً لبناء النظام
    echo.
    pause
    exit /b 1
)

echo تشغيل نظام المحاسبة الذكي...
echo.

start "" "AccountingSystem.exe"

if %ERRORLEVEL% EQU 0 (
    echo تم تشغيل النظام بنجاح!
    echo.
    echo معلومات تسجيل الدخول الافتراضية:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo يُنصح بتغيير كلمة المرور بعد تسجيل الدخول الأول
    echo.
) else (
    echo فشل في تشغيل النظام
    echo تأكد من وجود جميع الملفات المطلوبة
    echo.
)

echo اضغط أي مفتاح للإغلاق...
pause > nul
