using System;
using System.IO;
using AccountingSystem.Data;
using AccountingSystem.Services;
using AccountingSystem.Models;

namespace AccountingSystem.Tests
{
    /// <summary>
    /// اختبارات النظام الأساسية
    /// </summary>
    public static class SystemTests
    {
        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("بدء تشغيل اختبارات النظام...");
            Console.WriteLine("================================");
            
            try
            {
                // اختبار قاعدة البيانات
                TestDatabase();
                
                // اختبار المصادقة
                TestAuthentication();
                
                // اختبار الأمان
                TestSecurity();
                
                // اختبار الذكاء الاصطناعي
                TestAIAnalysis();
                
                Console.WriteLine("================================");
                Console.WriteLine("تم إكمال جميع الاختبارات بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"فشل في الاختبارات: {ex.Message}");
            }
        }
        
        /// <summary>
        /// اختبار قاعدة البيانات
        /// </summary>
        private static void TestDatabase()
        {
            Console.WriteLine("اختبار قاعدة البيانات...");
            
            try
            {
                // حذف قاعدة البيانات إذا كانت موجودة
                if (File.Exists("database.db"))
                {
                    File.Delete("database.db");
                }
                
                // تهيئة قاعدة البيانات
                DatabaseHelper.InitializeDatabase();
                
                // التحقق من وجود ملف قاعدة البيانات
                if (!File.Exists("database.db"))
                {
                    throw new Exception("فشل في إنشاء ملف قاعدة البيانات");
                }
                
                // التحقق من الاتصال
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    if (connection.State != System.Data.ConnectionState.Open)
                    {
                        throw new Exception("فشل في الاتصال بقاعدة البيانات");
                    }
                }
                
                Console.WriteLine("✓ اختبار قاعدة البيانات نجح");
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل اختبار قاعدة البيانات: {ex.Message}");
            }
        }
        
        /// <summary>
        /// اختبار المصادقة
        /// </summary>
        private static void TestAuthentication()
        {
            Console.WriteLine("اختبار المصادقة...");
            
            try
            {
                // اختبار تسجيل الدخول بالمستخدم الافتراضي
                bool loginResult = AuthenticationService.Login("admin", "admin123");
                if (!loginResult)
                {
                    throw new Exception("فشل في تسجيل الدخول بالمستخدم الافتراضي");
                }
                
                // التحقق من المستخدم الحالي
                if (AuthenticationService.CurrentUser == null)
                {
                    throw new Exception("المستخدم الحالي فارغ بعد تسجيل الدخول");
                }
                
                if (AuthenticationService.CurrentUser.Username != "admin")
                {
                    throw new Exception("اسم المستخدم الحالي غير صحيح");
                }
                
                // اختبار الصلاحيات
                if (!AuthenticationService.HasPermission("Admin"))
                {
                    throw new Exception("فشل في التحقق من صلاحيات المدير");
                }
                
                // اختبار تسجيل الخروج
                AuthenticationService.Logout();
                if (AuthenticationService.CurrentUser != null)
                {
                    throw new Exception("فشل في تسجيل الخروج");
                }
                
                // اختبار تسجيل الدخول بكلمة مرور خاطئة
                bool wrongPasswordResult = AuthenticationService.Login("admin", "wrongpassword");
                if (wrongPasswordResult)
                {
                    throw new Exception("نجح تسجيل الدخول بكلمة مرور خاطئة");
                }
                
                Console.WriteLine("✓ اختبار المصادقة نجح");
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل اختبار المصادقة: {ex.Message}");
            }
        }
        
        /// <summary>
        /// اختبار الأمان
        /// </summary>
        private static void TestSecurity()
        {
            Console.WriteLine("اختبار الأمان...");
            
            try
            {
                // اختبار تشفير كلمة المرور
                string password = "testpassword123";
                string hashedPassword = Utils.SecurityHelper.HashPassword(password);
                
                if (string.IsNullOrEmpty(hashedPassword))
                {
                    throw new Exception("فشل في تشفير كلمة المرور");
                }
                
                if (hashedPassword == password)
                {
                    throw new Exception("كلمة المرور لم يتم تشفيرها");
                }
                
                // اختبار التحقق من كلمة المرور
                bool verifyResult = Utils.SecurityHelper.VerifyPassword(password, hashedPassword);
                if (!verifyResult)
                {
                    throw new Exception("فشل في التحقق من كلمة المرور المشفرة");
                }
                
                // اختبار التحقق من كلمة مرور خاطئة
                bool wrongVerifyResult = Utils.SecurityHelper.VerifyPassword("wrongpassword", hashedPassword);
                if (wrongVerifyResult)
                {
                    throw new Exception("نجح التحقق من كلمة مرور خاطئة");
                }
                
                // اختبار توليد الأرقام العشوائية
                string randomNumber = Utils.SecurityHelper.GenerateRandomNumber();
                if (string.IsNullOrEmpty(randomNumber) || randomNumber.Length != 6)
                {
                    throw new Exception("فشل في توليد رقم عشوائي");
                }
                
                // اختبار توليد رقم مرجعي
                string referenceNumber = Utils.SecurityHelper.GenerateReferenceNumber("TEST");
                if (string.IsNullOrEmpty(referenceNumber) || !referenceNumber.StartsWith("TEST-"))
                {
                    throw new Exception("فشل في توليد رقم مرجعي");
                }
                
                // اختبار التحقق من البريد الإلكتروني
                bool validEmail = Utils.SecurityHelper.IsValidEmail("<EMAIL>");
                if (!validEmail)
                {
                    throw new Exception("فشل في التحقق من بريد إلكتروني صحيح");
                }
                
                bool invalidEmail = Utils.SecurityHelper.IsValidEmail("invalid-email");
                if (invalidEmail)
                {
                    throw new Exception("نجح التحقق من بريد إلكتروني خاطئ");
                }
                
                Console.WriteLine("✓ اختبار الأمان نجح");
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل اختبار الأمان: {ex.Message}");
            }
        }
        
        /// <summary>
        /// اختبار الذكاء الاصطناعي
        /// </summary>
        private static void TestAIAnalysis()
        {
            Console.WriteLine("اختبار الذكاء الاصطناعي...");
            
            try
            {
                // تسجيل الدخول أولاً
                AuthenticationService.Login("admin", "admin123");
                
                // اختبار تحليل الأداء المالي
                var financialReport = AIAnalysisService.AnalyzeFinancialPerformance();
                if (financialReport == null)
                {
                    throw new Exception("فشل في إنشاء تقرير التحليل المالي");
                }
                
                if (financialReport.AnalysisDate == DateTime.MinValue)
                {
                    throw new Exception("تاريخ التحليل غير صحيح");
                }
                
                if (financialReport.Recommendations == null)
                {
                    throw new Exception("قائمة التوصيات فارغة");
                }
                
                // اختبار تحليل اتجاهات المبيعات
                var salesTrendReport = AIAnalysisService.AnalyzeSalesTrends(6);
                if (salesTrendReport == null)
                {
                    throw new Exception("فشل في إنشاء تقرير اتجاهات المبيعات");
                }
                
                if (salesTrendReport.MonthlySales == null)
                {
                    throw new Exception("بيانات المبيعات الشهرية فارغة");
                }
                
                // اختبار كشف الأخطاء المحاسبية
                var accountingErrors = AIAnalysisService.DetectAccountingErrors();
                if (accountingErrors == null)
                {
                    throw new Exception("فشل في كشف الأخطاء المحاسبية");
                }
                
                Console.WriteLine("✓ اختبار الذكاء الاصطناعي نجح");
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل اختبار الذكاء الاصطناعي: {ex.Message}");
            }
            finally
            {
                AuthenticationService.Logout();
            }
        }
        
        /// <summary>
        /// اختبار الأداء
        /// </summary>
        public static void TestPerformance()
        {
            Console.WriteLine("اختبار الأداء...");
            
            try
            {
                var startTime = DateTime.Now;
                
                // اختبار سرعة الاتصال بقاعدة البيانات
                for (int i = 0; i < 100; i++)
                {
                    using (var connection = DatabaseHelper.GetConnection())
                    {
                        connection.Open();
                    }
                }
                
                var endTime = DateTime.Now;
                var duration = endTime - startTime;
                
                Console.WriteLine($"✓ اختبار الأداء نجح - الوقت المستغرق: {duration.TotalMilliseconds} مللي ثانية");
                
                if (duration.TotalSeconds > 5)
                {
                    Console.WriteLine("تحذير: الأداء بطيء نسبياً");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل اختبار الأداء: {ex.Message}");
            }
        }
    }
}
