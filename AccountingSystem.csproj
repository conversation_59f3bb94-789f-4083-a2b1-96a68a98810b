<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>AccountingSystem</RootNamespace>
    <AssemblyName>AccountingSystem</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Dapper, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\Dapper.2.1.66\lib\net461\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Bcl.AsyncInterfaces.9.0.6\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.SQLite">
      <HintPath>System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Pipelines.9.0.6\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Text.Encodings.Web.9.0.6\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Text.Json.9.0.6\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Newtonsoft.Json">
      <HintPath>Newtonsoft.Json.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controls\DashboardControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\AddEditPaymentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AddEditReceiptForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CompanyInfoForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomerPaymentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomersManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomersManagementForm.Designer.cs">
      <DependentUpon>CustomersManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CustomerStatementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\InvoiceAnalyticsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\InvoicesManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\InvoicesManagementForm.Designer.cs">
      <DependentUpon>InvoicesManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\InvoicesReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainDashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainDashboardForm.Designer.cs">
      <DependentUpon>MainDashboardForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MainFormSimple.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PaymentsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PaymentsReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ProductsManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ProductsManagementForm.Designer.cs" />
    <Compile Include="Forms\ReceiptsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ReceiptsManagementForm.Designer.cs" />
    <Compile Include="Forms\ReceiptsReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UsersManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="InvoicesSystemFixed.cs" />
    <Compile Include="Models\DataModels.cs" />
    <Compile Include="Models\ReportModels.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Models\User.cs" />
    <Compile Include="Models\Account.cs" />
    <Compile Include="Models\JournalEntry.cs" />
    <Compile Include="Models\Receipt.cs" />
    <Compile Include="Models\Payment.cs" />
    <Compile Include="Models\Product.cs" />
    <Compile Include="Models\Customer.cs" />
    <Compile Include="Models\Invoice.cs" />
    <Compile Include="Data\DatabaseHelper.cs" />
    <Compile Include="Services\AuthenticationService.cs" />
    <Compile Include="Services\AccountService.cs" />
    <Compile Include="Services\AuthService.cs" />
    <Compile Include="Services\DataService.cs" />
    <Compile Include="Services\InvoiceReportService.cs" />
    <Compile Include="Services\JournalService.cs" />
    <Compile Include="Services\AIAnalysisService.cs" />
    <Compile Include="Services\SettingsService.cs" />
    <Compile Include="Utils\ExportHelper.cs" />
    <Compile Include="Utils\PrintHelper.cs" />
    <Compile Include="Utils\SecurityHelper.cs" />
    <Compile Include="Utils\ReportGenerator.cs" />
    <Compile Include="Utils\BackupHelper.cs" />
    <Compile Include="Utils\ConfigHelper.cs" />
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AccountsTreeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AddEditAccountForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\JournalEntryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\JournalEntriesListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\TrialBalanceForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AddEditCustomerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ProductsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AddEditProductForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\SimpleDatabaseHelper.cs" />
    <Compile Include="Services\SimpleAuthenticationService.cs" />
    <Compile Include="Tests\SystemTests.cs" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Utils\App.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>