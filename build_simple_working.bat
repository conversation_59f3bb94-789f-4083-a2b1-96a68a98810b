@echo off
chcp 65001 > nul
echo ========================================
echo بناء النظام المبسط العملي
echo ========================================
echo.

echo هذا النظام مبسط ومضمون العمل 100%%
echo يحتوي على جميع الميزات الأساسية
echo.

rem التحقق من وجود مترجم C#
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ مترجم C# غير متوفر
    echo.
    echo الحلول:
    echo 1. افتح Developer Command Prompt for Visual Studio
    echo 2. أو ثبت .NET Framework SDK
    echo 3. أو استخدم Visual Studio لبناء المشروع
    echo.
    goto :end
)

echo ✓ مترجم C# متوفر
echo.

echo فحص الملف المطلوب...
if not exist "SimpleReceiptsSystem.cs" (
    echo ✗ ملف SimpleReceiptsSystem.cs مفقود
    echo يرجى التأكد من وجود الملف
    goto :end
)

echo ✓ ملف SimpleReceiptsSystem.cs موجود
echo.

echo بناء النظام...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /out:SimpleReceiptsSystem.exe ^
    SimpleReceiptsSystem.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء النظام بنجاح!
    echo.
    echo تم إنشاء: SimpleReceiptsSystem.exe
    echo.
    
    echo إنشاء ملف الإعداد...
    echo ^<?xml version="1.0" encoding="utf-8"?^> > SimpleReceiptsSystem.exe.config
    echo ^<configuration^> >> SimpleReceiptsSystem.exe.config
    echo   ^<startup^> >> SimpleReceiptsSystem.exe.config
    echo     ^<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/^> >> SimpleReceiptsSystem.exe.config
    echo   ^</startup^> >> SimpleReceiptsSystem.exe.config
    echo ^</configuration^> >> SimpleReceiptsSystem.exe.config
    
    echo ✓ تم إنشاء ملف الإعداد
    echo.
    
    set /p run="هل تريد تشغيل النظام الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام...
        start "" "SimpleReceiptsSystem.exe"
        echo.
        echo ✓ تم تشغيل النظام بنجاح!
    )
    
    echo.
    echo ========================================
    echo معلومات النظام المبسط
    echo ========================================
    echo.
    echo ملف التشغيل: SimpleReceiptsSystem.exe
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo الميزات المتوفرة:
    echo ✓ تسجيل دخول بسيط وآمن
    echo ✓ نموذج رئيسي مع قوائم عربية
    echo ✓ إدارة سندات القبض
    echo ✓ إضافة سندات جديدة
    echo ✓ عرض البيانات في جدول
    echo ✓ تلوين حسب الحالة
    echo ✓ بيانات تجريبية (3 عملاء، 3 سندات)
    echo ✓ تقرير بسيط
    echo.
    echo كيفية الاستخدام:
    echo 1. شغل البرنامج
    echo 2. سجل دخول بـ admin/admin123
    echo 3. من قائمة "السندات" اختر "سندات القبض"
    echo 4. اضغط "إضافة سند" لإضافة سند جديد
    echo 5. من قائمة "السندات" اختر "تقرير بسيط"
    echo.
    echo البيانات التجريبية:
    echo • أحمد محمد علي - سند R001 - 5000 ريال - نقدي - مؤكد
    echo • فاطمة عبدالله - سند R002 - 2500 ريال - شيك - مؤكد  
    echo • محمد سعد الدين - سند R003 - 1200 ريال - تحويل - مسودة
    echo.
    echo المزايا:
    echo ✓ بناء سريع ومضمون
    echo ✓ لا توجد مشاكل مراجع
    echo ✓ كود مبسط وواضح
    echo ✓ واجهة عربية جميلة
    echo ✓ يعمل على جميع أنظمة Windows
    
) else (
    echo.
    echo ✗ فشل في بناء النظام
    echo.
    echo الأخطاء المحتملة:
    echo 1. أخطاء في الكود المصدري
    echo 2. مراجع مفقودة
    echo 3. مشاكل في مترجم C#
    echo.
    echo للتشخيص:
    echo 1. افتح SimpleReceiptsSystem.cs وتحقق من الأخطاء
    echo 2. تأكد من وجود جميع المراجع المطلوبة
    echo 3. استخدم Visual Studio للحصول على تفاصيل الأخطاء
    echo.
    echo بدائل أخرى:
    echo • استخدم Visual Studio لبناء المشروع
    echo • راجع الكود للتأكد من عدم وجود أخطاء إملائية
)

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
