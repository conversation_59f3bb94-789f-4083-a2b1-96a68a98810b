using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;
using AccountingSystem.Forms;
using IntegratedInvoiceSystem.Models;
using IntegratedInvoiceSystem.Services;
using InvoicesSystemFixed;
using AuthSvc = IntegratedInvoiceSystem.Services.AuthService;

namespace IntegratedInvoiceSystem.Forms
{
    /// <summary>
    /// النموذج الرئيسي المتكامل مع الداشبورد
    /// </summary>
    public partial class MainDashboardForm : Form
    {
        #region المتغيرات الخاصة
        private string currentView = "dashboard";
        #endregion

        #region البناء والتهيئة
        public MainDashboardForm()
        {
            InitializeComponent();
            SetupForm();
            ShowMainDashboard();
        }

        private void MainDashboardForm_Load(object sender, EventArgs e)
        {
            SetupForm();
            UpdateWelcomeMessage();
            StartMainTimer();
        }

        private void SetupForm()
        {
            // تحديث رسالة الترحيب
            UpdateWelcomeMessage();

            // تحديث حالة الأزرار
            UpdateMenuButtons();

            // إخفاء الأزرار حسب الصلاحيات
            CheckPermissions();
        }

        private void UpdateWelcomeMessage()
        {
            try
            {
                lblWelcome.Text = $"مرحباً، {AuthSvc.CurrentUserName ?? "المستخدم"} 👋";
                lblUserInfo.Text = $"الدور: {AuthSvc.CurrentUser?.Role ?? "مستخدم"} | آخر دخول: {AuthSvc.CurrentUser?.LastLogin:yyyy/MM/dd HH:mm}";
            }
            catch (Exception ex)
            {
                lblWelcome.Text = "مرحباً بك في النظام 👋";
                lblUserInfo.Text = "مستخدم عام";
            }
        }

        private void CheckPermissions()
        {
            try
            {
                // إخفاء الأزرار حسب الصلاحيات
                btnInvoices.Visible = AuthSvc.HasPermission("إدارة الفواتير");
                btnAddInvoice.Visible = AuthSvc.HasPermission("إدارة الفواتير");
                btnReceipts.Visible = AuthSvc.HasPermission("إدارة الفواتير");
                btnCustomers.Visible = AuthSvc.HasPermission("إدارة العملاء");
                btnProducts.Visible = AuthSvc.HasPermission("إدارة المنتجات");
                btnReports.Visible = AuthSvc.HasPermission("التقارير");
                btnCompany.Visible = AuthSvc.HasPermission("الإعدادات");
                btnUsers.Visible = AuthSvc.HasPermission("إدارة المستخدمين");
                btnSettings.Visible = AuthSvc.HasPermission("الإعدادات");
            }
            catch (Exception)
            {
                // في حالة عدم توفر خدمة الصلاحيات، اعرض جميع الأزرار
            }
        }

        #endregion

        #region معالجات أحداث الأزرار الجانبية
        private void BtnDashboard_Click(object sender, EventArgs e)
        {
            currentView = "dashboard";
            UpdateMenuButtons();
            ShowMainDashboard();
        }

        private void BtnInvoices_Click(object sender, EventArgs e)
        {
            currentView = "invoices";
            UpdateMenuButtons();
            ShowInvoicesManagementForm();
        }

        private void BtnAddInvoice_Click(object sender, EventArgs e)
        {
            currentView = "add_invoice";
            UpdateMenuButtons();
            ShowAddInvoiceForm();
        }

        private void BtnReceipts_Click(object sender, EventArgs e)
        {
            currentView = "receipts";
            UpdateMenuButtons();
            ShowReceiptsForm();
        }

        private void BtnCustomers_Click(object sender, EventArgs e)
        {
            currentView = "customers";
            UpdateMenuButtons();
            ShowCustomersForm();
        }

        private void BtnProducts_Click(object sender, EventArgs e)
        {
            currentView = "products";
            UpdateMenuButtons();
            ShowProductsForm();
        }

        private void BtnReports_Click(object sender, EventArgs e)
        {
            currentView = "reports";
            UpdateMenuButtons();
            ShowReportsForm();
        }

        private void BtnCompany_Click(object sender, EventArgs e)
        {
            currentView = "company";
            UpdateMenuButtons();
            ShowCompanyInfoForm();
        }

        private void BtnUsers_Click(object sender, EventArgs e)
        {
            currentView = "users";
            UpdateMenuButtons();
            ShowUsersForm();
        }

        private void BtnSettings_Click(object sender, EventArgs e)
        {
            currentView = "settings";
            UpdateMenuButtons();
            ShowSettingsForm();
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            SaveAllData();
        }

        private void BtnLogout_Click(object sender, EventArgs e)
        {
            LogoutUser();
        }
        #endregion

        #region معالجات أحداث الأزرار العلوية
        private void BtnNotifications_Click(object sender, EventArgs e)
        {
            ShowNotifications();
        }

        private void BtnQuickAdd_Click(object sender, EventArgs e)
        {
            ShowQuickAdd();
        }

        private void BtnBackup_Click(object sender, EventArgs e)
        {
            CreateMainBackup();
        }
        #endregion

        #region معالجات أحداث التفاعل
        private void MenuButton_MouseEnter(object sender, EventArgs e)
        {
            if (sender is Button button && button.Tag != null)
            {
                string key = button.Tag.ToString();
                if (currentView != key)
                    button.BackColor = Color.FromArgb(52, 152, 219);
            }
        }

        private void MenuButton_MouseLeave(object sender, EventArgs e)
        {
            if (sender is Button button && button.Tag != null)
            {
                string key = button.Tag.ToString();
                if (currentView != key)
                    button.BackColor = Color.Transparent;
            }
        }

        private void TimeTimer_Tick(object sender, EventArgs e)
        {
            lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy\nHH:mm:ss",
                new System.Globalization.CultureInfo("ar-SA"));
        }

        #endregion

        #region الوظائف المساعدة
        private void UpdateMenuButtons()
        {
            // تحديث ألوان الأزرار حسب الاختيار الحالي
            var buttons = new[] { btnDashboard, btnInvoices, btnAddInvoice, btnReceipts,
                                btnCustomers, btnProducts, btnReports, btnCompany,
                                btnUsers, btnSettings, btnSave, btnLogout };

            foreach (var button in buttons)
            {
                if (button.Tag != null)
                {
                    string key = button.Tag.ToString();
                    button.BackColor = currentView == key ?
                        Color.FromArgb(52, 152, 219) : Color.Transparent;
                }
            }
        }

        private void StartMainTimer()
        {
            try
            {
                timeTimer.Start();
                // تحديث فوري
                TimeTimer_Tick(null, null);
            }
            catch (Exception)
            {
                // تجاهل أخطاء المؤقت
            }
        }

        #endregion

        #region معالجات الرسم
        private void SidePanel_Paint(object sender, PaintEventArgs e)
        {
            // إضافة تدرج لوني للشريط الجانبي
            using (LinearGradientBrush brush = new LinearGradientBrush(
                sidePanel.ClientRectangle,
                Color.FromArgb(44, 62, 80),
                Color.FromArgb(52, 73, 94),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, sidePanel.ClientRectangle);
            }
        }

        private void TopPanel_Paint(object sender, PaintEventArgs e)
        {
            // إضافة ظل للشريط العلوي
            using (SolidBrush brush = new SolidBrush(Color.FromArgb(10, 0, 0, 0)))
            {
                e.Graphics.FillRectangle(brush, new Rectangle(0, topPanel.Height - 5, topPanel.Width, 5));
            }
        }
        #endregion

        #region دوال عرض المحتوى
        private void ShowMainDashboard()
        {
            contentPanel.Controls.Clear();
            currentView = "dashboard";
            UpdateMenuButtons();

            try
            {
                // إنشاء كنترول الداشبورد
                var dashboardControl = new DashboardControl();
                dashboardControl.Dock = DockStyle.Fill;
                contentPanel.Controls.Add(dashboardControl);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تحميل الداشبورد", ex.Message);
            }
        }

        private void ShowInvoicesManagementForm()
        {
            try
            {
                var invoicesForm = new InvoicesReportForm();
                ShowFormInContent(invoicesForm);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة الفواتير", ex.Message);
            }
        }

        private void ShowAddInvoiceForm()
        {
            try
            {
                var addInvoiceForm = new AddEditInvoiceForm();
                addInvoiceForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إنشاء فاتورة جديدة", ex.Message);
            }
        }

        private void ShowReceiptsForm()
        {
            try
            {
                // إنشاء نموذج مؤقت للسندات
                MessageBox.Show("🎉 سيتم فتح شاشة إدارة السندات والمدفوعات\n\nالميزات المتوفرة:\n• عرض جميع السندات\n• إضافة سند قبض جديد\n• ربط السندات بالفواتير\n• طرق دفع متعددة\n• تقارير المدفوعات",
                    "إدارة السندات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة السندات", ex.Message);
            }
        }

        private void ShowCustomersForm()
        {
            try
            {
                // إنشاء نموذج مؤقت للعملاء
                MessageBox.Show("🎉 سيتم فتح شاشة إدارة العملاء\n\nالميزات المتوفرة:\n• عرض جميع العملاء\n• إضافة عميل جديد\n• تعديل بيانات العملاء\n• كشف حساب العميل\n• تقارير العملاء\n• إدارة الأرصدة",
                    "إدارة العملاء", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة العملاء", ex.Message);
            }
        }

        private void ShowProductsForm()
        {
            try
            {
                // إنشاء نموذج مؤقت للمنتجات
                MessageBox.Show("🎉 سيتم فتح شاشة إدارة المنتجات\n\nالميزات المتوفرة:\n• عرض جميع المنتجات\n• إضافة منتج جديد\n• تعديل أسعار المنتجات\n• إدارة المخزون\n• فئات المنتجات\n• الباركود",
                    "إدارة المنتجات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة المنتجات", ex.Message);
            }
        }

        private void ShowReportsForm()
        {
            try
            {
                var reportsForm = new InvoiceAnalyticsForm();
                ShowFormInContent(reportsForm);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح التقارير", ex.Message);
            }
        }

        private void ShowCompanyInfoForm()
        {
            try
            {
                var companyForm = new CompanyInfoForm();
                companyForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح معلومات الشركة", ex.Message);
            }
        }

        private void ShowUsersForm()
        {
            if (!AuthSvc.IsAdmin())
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة المستخدمين", "صلاحيات غير كافية",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var usersForm = new UsersManagementForm();
                usersForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة المستخدمين", ex.Message);
            }
        }

        private void ShowSettingsForm()
        {
            try
            {
                var settingsForm = new SettingsForm();
                settingsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح الإعدادات", ex.Message);
            }
        }

        private void ShowFormInContent(Form form)
        {
            contentPanel.Controls.Clear();

            form.TopLevel = false;
            form.FormBorderStyle = FormBorderStyle.None;
            form.Dock = DockStyle.Fill;
            form.WindowState = FormWindowState.Maximized;

            contentPanel.Controls.Add(form);
            form.Show();
        }

        private void ShowErrorMessage(string title, string message)
        {
            MessageBox.Show($"{title}\n\n{message}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        #endregion

        #region الوظائف الإضافية
        private void ShowNotifications()
        {
            try
            {
                var stats = DataService.GetDashboardStats();

                MessageBox.Show($"🔔 الإشعارات:\n\n• {stats.OverdueInvoices} فاتورة متأخرة\n• {stats.DueToday} فاتورة مستحقة اليوم\n• {stats.LowStockProducts} منتج بمخزون منخفض\n• آخر تحديث: {DateTime.Now:HH:mm}",
                    "الإشعارات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تحميل الإشعارات", ex.Message);
            }
        }

        private void ShowQuickAdd()
        {
            var quickMenu = new ContextMenuStrip();
            quickMenu.Items.Add("➕ فاتورة جديدة", null, (s, e) => ShowAddInvoiceForm());
            quickMenu.Items.Add("👤 عميل جديد", null, (s, e) => ShowAddCustomerForm());
            quickMenu.Items.Add("📦 منتج جديد", null, (s, e) => ShowAddProductForm());
            quickMenu.Items.Add("💰 سند قبض", null, (s, e) => ShowAddReceiptForm());

            quickMenu.Show(Cursor.Position);
        }

        private void ShowAddCustomerForm()
        {
            try
            {
                MessageBox.Show("سيتم فتح نموذج إضافة عميل جديد", "إضافة عميل",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إضافة عميل جديد", ex.Message);
            }
        }

        private void ShowAddProductForm()
        {
            try
            {
                MessageBox.Show("سيتم فتح نموذج إضافة منتج جديد", "إضافة منتج",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إضافة منتج جديد", ex.Message);
            }
        }

        private void ShowAddReceiptForm()
        {
            try
            {
                MessageBox.Show("سيتم فتح نموذج إضافة سند قبض", "إضافة سند",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إضافة سند قبض", ex.Message);
            }
        }

        private void CreateMainBackup()
        {
            try
            {
                DataService.SaveData();
                MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نسخ احتياطي",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إنشاء النسخة الاحتياطية", ex.Message);
            }
        }

        private void SaveAllData()
        {
            try
            {
                DataService.SaveData();
                MessageBox.Show("تم حفظ البيانات بنجاح", "حفظ البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في حفظ البيانات", ex.Message);
            }
        }

        private void LogoutUser()
        {
            var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    Services.AuthService.Logout();
                    this.Close();
                }
                catch (Exception)
                {
                    this.Close();
                }
            }
        }

        private Button CreateQuickButton(string text, Color color, Action action)
        {
            var button = new Button();
            button.Text = text;
            button.Size = new Size(140, 45);
            button.BackColor = color;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            button.Cursor = Cursors.Hand;
            button.Click += (s, e) => action();

            // تأثيرات التفاعل
            button.MouseEnter += (s, e) => button.BackColor = ControlPaint.Dark(color, 0.1f);
            button.MouseLeave += (s, e) => button.BackColor = color;

            return button;
        }
        #endregion

       

        #region تنظيف الموارد
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                timeTimer?.Stop();
                timeTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
        #endregion
    }
}
