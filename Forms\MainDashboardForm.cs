using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Models;
using IntegratedInvoiceSystem.Services;

namespace IntegratedInvoiceSystem.Forms
{
    /// <summary>
    /// النموذج الرئيسي المتكامل مع الداشبورد
    /// </summary>
    public partial class MainDashboardForm : Form
    {
        #region المتغيرات الخاصة
        private string currentView = "dashboard";
        #endregion

        #region البناء والتهيئة
        public MainDashboardForm()
        {
            InitializeComponent();
            SetupForm();
            ShowDashboard();
        }

        private void MainDashboardForm_Load(object sender, EventArgs e)
        {
            SetupForm();
            UpdateWelcomeMessage();
            StartTimer();
        }

        private void SetupForm()
        {
            // تحديث رسالة الترحيب
            UpdateWelcomeMessage();

            // تحديث حالة الأزرار
            UpdateMenuButtons();

            // إخفاء الأزرار حسب الصلاحيات
            CheckPermissions();
        }

        private void UpdateWelcomeMessage()
        {
            lblWelcome.Text = $"مرحباً، {AuthService.CurrentUserName} 👋";
            lblUserInfo.Text = $"الدور: {AuthService.CurrentUser?.Role} | آخر دخول: {AuthService.CurrentUser?.LastLogin:yyyy/MM/dd HH:mm}";
        }

        private void CheckPermissions()
        {
            // إخفاء الأزرار حسب الصلاحيات
            btnInvoices.Visible = AuthService.HasPermission("إدارة الفواتير");
            btnAddInvoice.Visible = AuthService.HasPermission("إدارة الفواتير");
            btnReceipts.Visible = AuthService.HasPermission("إدارة الفواتير");
            btnCustomers.Visible = AuthService.HasPermission("إدارة العملاء");
            btnProducts.Visible = AuthService.HasPermission("إدارة المنتجات");
            btnReports.Visible = AuthService.HasPermission("التقارير");
            btnCompany.Visible = AuthService.HasPermission("الإعدادات");
            btnUsers.Visible = AuthService.HasPermission("إدارة المستخدمين");
            btnSettings.Visible = AuthService.HasPermission("الإعدادات");
        }

        #endregion

        #region معالجات أحداث الأزرار الجانبية
        private void BtnDashboard_Click(object sender, EventArgs e)
        {
            currentView = "dashboard";
            UpdateMenuButtons();
            ShowDashboard();
        }

        private void BtnInvoices_Click(object sender, EventArgs e)
        {
            currentView = "invoices";
            UpdateMenuButtons();
            ShowInvoicesManagement();
        }

        private void BtnAddInvoice_Click(object sender, EventArgs e)
        {
            currentView = "add_invoice";
            UpdateMenuButtons();
            ShowAddInvoice();
        }

        private void BtnReceipts_Click(object sender, EventArgs e)
        {
            currentView = "receipts";
            UpdateMenuButtons();
            ShowReceipts();
        }

        private void BtnCustomers_Click(object sender, EventArgs e)
        {
            currentView = "customers";
            UpdateMenuButtons();
            ShowCustomers();
        }

        private void BtnProducts_Click(object sender, EventArgs e)
        {
            currentView = "products";
            UpdateMenuButtons();
            ShowProducts();
        }

        private void BtnReports_Click(object sender, EventArgs e)
        {
            currentView = "reports";
            UpdateMenuButtons();
            ShowReports();
        }

        private void BtnCompany_Click(object sender, EventArgs e)
        {
            currentView = "company";
            UpdateMenuButtons();
            ShowCompanyInfo();
        }

        private void BtnUsers_Click(object sender, EventArgs e)
        {
            currentView = "users";
            UpdateMenuButtons();
            ShowUsers();
        }

        private void BtnSettings_Click(object sender, EventArgs e)
        {
            currentView = "settings";
            UpdateMenuButtons();
            ShowSettings();
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            SaveData();
        }

        private void BtnLogout_Click(object sender, EventArgs e)
        {
            Logout();
        }
        #endregion

        #region معالجات أحداث الأزرار العلوية
        private void BtnNotifications_Click(object sender, EventArgs e)
        {
            ShowNotifications();
        }

        private void BtnQuickAdd_Click(object sender, EventArgs e)
        {
            ShowQuickAdd();
        }

        private void BtnBackup_Click(object sender, EventArgs e)
        {
            CreateBackup();
        }
        #endregion

        #region معالجات أحداث التفاعل
        private void MenuButton_MouseEnter(object sender, EventArgs e)
        {
            if (sender is Button button && button.Tag != null)
            {
                string key = button.Tag.ToString();
                if (currentView != key)
                    button.BackColor = Color.FromArgb(52, 152, 219);
            }
        }

        private void MenuButton_MouseLeave(object sender, EventArgs e)
        {
            if (sender is Button button && button.Tag != null)
            {
                string key = button.Tag.ToString();
                if (currentView != key)
                    button.BackColor = Color.Transparent;
            }
        }

        private void TimeTimer_Tick(object sender, EventArgs e)
        {
            lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy\nHH:mm:ss",
                new System.Globalization.CultureInfo("ar-SA"));
        }

        #endregion

        #region الوظائف المساعدة
        private void UpdateMenuButtons()
        {
            // تحديث ألوان الأزرار حسب الاختيار الحالي
            var buttons = new[] { btnDashboard, btnInvoices, btnAddInvoice, btnReceipts,
                                btnCustomers, btnProducts, btnReports, btnCompany,
                                btnUsers, btnSettings, btnSave, btnLogout };

            foreach (var button in buttons)
            {
                if (button.Tag != null)
                {
                    string key = button.Tag.ToString();
                    button.BackColor = currentView == key ?
                        Color.FromArgb(52, 152, 219) : Color.Transparent;
                }
            }
        }

        private void StartTimer()
        {
            timeTimer.Start();
            // تحديث فوري
            TimeTimer_Tick(null, null);
        }

        #endregion

        #region معالجات الرسم
        private void SidePanel_Paint(object sender, PaintEventArgs e)
        {
            // إضافة تدرج لوني للشريط الجانبي
            using (LinearGradientBrush brush = new LinearGradientBrush(
                sidePanel.ClientRectangle,
                Color.FromArgb(44, 62, 80),
                Color.FromArgb(52, 73, 94),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, sidePanel.ClientRectangle);
            }
        }

        private void TopPanel_Paint(object sender, PaintEventArgs e)
        {
            // إضافة ظل للشريط العلوي
            using (SolidBrush brush = new SolidBrush(Color.FromArgb(10, 0, 0, 0)))
            {
                e.Graphics.FillRectangle(brush, new Rectangle(0, topPanel.Height - 5, topPanel.Width, 5));
            }
        }
        #endregion

        #region دوال عرض المحتوى
        private void ShowDashboard()
        {
            contentPanel.Controls.Clear();
            currentView = "dashboard";
            UpdateMenuButtons();

            try
            {
                // إنشاء كنترول الداشبورد
                var dashboardControl = new DashboardControl();
                dashboardControl.Dock = DockStyle.Fill;
                contentPanel.Controls.Add(dashboardControl);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تحميل الداشبورد", ex.Message);
            }
        }

        private void ShowInvoicesManagement()
        {
            try
            {
                var invoicesForm = new InvoicesReportForm();
                ShowFormInContent(invoicesForm);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة الفواتير", ex.Message);
            }
        }

        private void ShowAddInvoice()
        {
            try
            {
                var addInvoiceForm = new AddEditInvoiceForm();
                addInvoiceForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إنشاء فاتورة جديدة", ex.Message);
            }
        }

        private void ShowReceipts()
        {
            try
            {
                var receiptsForm = new ReceiptsForm();
                ShowFormInContent(receiptsForm);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة السندات", ex.Message);
            }
        }

        private void ShowCustomers()
        {
            try
            {
                var customersForm = new CustomersForm();
                ShowFormInContent(customersForm);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة العملاء", ex.Message);
            }
        }

        private void ShowProducts()
        {
            try
            {
                var productsForm = new ProductsForm();
                ShowFormInContent(productsForm);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة المنتجات", ex.Message);
            }
        }

        private void ShowReports()
        {
            try
            {
                var reportsForm = new InvoiceAnalyticsForm();
                ShowFormInContent(reportsForm);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح التقارير", ex.Message);
            }
        }

        private void ShowCompanyInfo()
        {
            try
            {
                var companyForm = new CompanyInfoForm();
                companyForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح معلومات الشركة", ex.Message);
            }
        }

        private void ShowUsers()
        {
            if (!AuthService.IsAdmin())
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة المستخدمين", "صلاحيات غير كافية",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var usersForm = new UsersManagementForm();
                usersForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح إدارة المستخدمين", ex.Message);
            }
        }

        private void ShowSettings()
        {
            try
            {
                var settingsForm = new SettingsForm();
                settingsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في فتح الإعدادات", ex.Message);
            }
        }

        private void ShowFormInContent(Form form)
        {
            contentPanel.Controls.Clear();

            form.TopLevel = false;
            form.FormBorderStyle = FormBorderStyle.None;
            form.Dock = DockStyle.Fill;
            form.WindowState = FormWindowState.Maximized;

            contentPanel.Controls.Add(form);
            form.Show();
        }

        private void ShowErrorMessage(string title, string message)
        {
            MessageBox.Show($"{title}\n\n{message}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        #endregion

        #region الوظائف الإضافية
        private void ShowNotifications()
        {
            try
            {
                var stats = DataService.GetDashboardStats();

                MessageBox.Show($"🔔 الإشعارات:\n\n• {stats.OverdueInvoices} فاتورة متأخرة\n• {stats.DueToday} فاتورة مستحقة اليوم\n• {stats.LowStockProducts} منتج بمخزون منخفض\n• آخر تحديث: {DateTime.Now:HH:mm}",
                    "الإشعارات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تحميل الإشعارات", ex.Message);
            }
        }

        private void ShowQuickAdd()
        {
            var quickMenu = new ContextMenuStrip();
            quickMenu.Items.Add("➕ فاتورة جديدة", null, (s, e) => ShowAddInvoice());
            quickMenu.Items.Add("👤 عميل جديد", null, (s, e) => ShowAddCustomer());
            quickMenu.Items.Add("📦 منتج جديد", null, (s, e) => ShowAddProduct());
            quickMenu.Items.Add("💰 سند قبض", null, (s, e) => ShowAddReceipt());

            quickMenu.Show(Cursor.Position);
        }

        private void ShowAddCustomer()
        {
            try
            {
                var addCustomerForm = new AddEditCustomerForm();
                addCustomerForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إضافة عميل جديد", ex.Message);
            }
        }

        private void ShowAddProduct()
        {
            try
            {
                var addProductForm = new AddEditProductForm();
                addProductForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إضافة منتج جديد", ex.Message);
            }
        }

        private void ShowAddReceipt()
        {
            try
            {
                var addReceiptForm = new AddEditReceiptForm();
                addReceiptForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إضافة سند قبض", ex.Message);
            }
        }

        private void CreateBackup()
        {
            try
            {
                DataService.SaveData();
                MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نسخ احتياطي",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في إنشاء النسخة الاحتياطية", ex.Message);
            }
        }

        private void SaveData()
        {
            try
            {
                DataService.SaveData();
                MessageBox.Show("تم حفظ البيانات بنجاح", "حفظ البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في حفظ البيانات", ex.Message);
            }
        }

        private void Logout()
        {
            var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                AuthService.Logout();
                this.Close();
            }
        }

        private Button CreateQuickButton(string text, Color color, Action action)
        {
            var button = new Button();
            button.Text = text;
            button.Size = new Size(140, 45);
            button.BackColor = color;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            button.Cursor = Cursors.Hand;
            button.Click += (s, e) => action();

            // تأثيرات التفاعل
            button.MouseEnter += (s, e) => button.BackColor = ControlPaint.Dark(color, 0.1f);
            button.MouseLeave += (s, e) => button.BackColor = color;

            return button;
        }
        #endregion

        #region الأحداث والرسم
        private void SidePanel_Paint(object sender, PaintEventArgs e)
        {
            // تدرج للشريط الجانبي
            using (LinearGradientBrush brush = new LinearGradientBrush(
                sidePanel.ClientRectangle,
                Color.FromArgb(44, 62, 80),
                Color.FromArgb(52, 73, 94),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, sidePanel.ClientRectangle);
            }
        }

        private void TopPanel_Paint(object sender, PaintEventArgs e)
        {
            // ظل للشريط العلوي
            using (Pen pen = new Pen(Color.FromArgb(189, 195, 199), 3))
            {
                e.Graphics.DrawLine(pen, 0, topPanel.Height - 3, topPanel.Width, topPanel.Height - 3);
            }
        }

        private void StartTimer()
        {
            timeTimer = new Timer();
            timeTimer.Interval = 1000;
            timeTimer.Tick += (s, e) =>
            {
                lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy\nHH:mm:ss",
                    new System.Globalization.CultureInfo("ar-SA"));
            };
            timeTimer.Start();

            // تحديث فوري
            lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy\nHH:mm:ss",
                new System.Globalization.CultureInfo("ar-SA"));
        }
        #endregion

        #region دوال عرض المحتوى
        private void ShowDashboard()
        {
            contentPanel.Controls.Clear();
            currentView = "dashboard";
            UpdateMenuButtons();

            var dashboardControl = new DashboardControl();
            dashboardControl.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(dashboardControl);
        }

        private void ShowInvoicesManagement()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة الفواتير المتكاملة\n\nالميزات المتوفرة:\n• عرض جميع الفواتير مع فلترة متقدمة\n• إضافة وتعديل وحذف الفواتير\n• طباعة الفواتير\n• تسجيل المدفوعات\n• تتبع حالات الفواتير",
                "إدارة الفواتير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowAddInvoice()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إضافة فاتورة جديدة\n\nالميزات المتوفرة:\n• نموذج شامل لإدخال بيانات الفاتورة\n• إضافة أصناف متعددة\n• حساب تلقائي للضرائب والخصومات\n• اختيار العميل من قائمة\n• حفظ كمسودة أو تأكيد",
                "إضافة فاتورة جديدة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReceipts()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة السندات والمدفوعات\n\nالميزات المتوفرة:\n• عرض جميع السندات\n• إضافة سند قبض جديد\n• ربط السندات بالفواتير\n• طرق دفع متعددة\n• تقارير المدفوعات",
                "إدارة السندات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCustomers()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة العملاء\n\nالميزات المتوفرة:\n• عرض جميع العملاء\n• إضافة عميل جديد\n• تعديل بيانات العملاء\n• كشف حساب العميل\n• تقارير العملاء\n• إدارة الأرصدة",
                "إدارة العملاء", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowProducts()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة المنتجات\n\nالميزات المتوفرة:\n• عرض جميع المنتجات\n• إضافة منتج جديد\n• تعديل أسعار المنتجات\n• إدارة المخزون\n• فئات المنتجات\n• الباركود",
                "إدارة المنتجات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReports()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة التقارير والإحصائيات\n\nالتقارير المتوفرة:\n• تقرير المبيعات\n• تقرير المدفوعات\n• تقرير العملاء\n• تقرير الأرباح\n• تقرير المخزون\n• تقارير مخصصة",
                "التقارير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCompanyInfo()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة معلومات الشركة\n\nالإعدادات المتوفرة:\n• اسم الشركة وعنوانها\n• معلومات الاتصال\n• الرقم الضريبي\n• السجل التجاري\n• شعار الشركة",
                "معلومات الشركة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowUsers()
        {
            if (!AuthService.IsAdmin())
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة المستخدمين", "صلاحيات غير كافية",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("🎉 سيتم فتح شاشة إدارة المستخدمين\n\nالميزات المتوفرة:\n• عرض جميع المستخدمين\n• إضافة مستخدم جديد\n• تعديل الصلاحيات\n• تغيير كلمات المرور\n• تفعيل/إلغاء تفعيل المستخدمين",
                "إدارة المستخدمين", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowSettings()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة الإعدادات العامة\n\nالإعدادات المتوفرة:\n• إعدادات الضرائب\n• إعدادات الطباعة\n• إعدادات النسخ الاحتياطي\n• إعدادات التنبيهات\n• إعدادات العملة",
                "الإعدادات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowNotifications()
        {
            var stats = DataService.GetDashboardStats();

            MessageBox.Show($"🔔 الإشعارات:\n\n• {stats.OverdueInvoices} فاتورة متأخرة\n• {stats.DueToday} فاتورة مستحقة اليوم\n• {stats.LowStockProducts} منتج بمخزون منخفض\n• آخر تحديث: {DateTime.Now:HH:mm}",
                "الإشعارات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowQuickAdd()
        {
            var quickMenu = new ContextMenuStrip();
            quickMenu.Items.Add("➕ فاتورة جديدة", null, (s, e) => ShowAddInvoice());
            quickMenu.Items.Add("👤 عميل جديد", null, (s, e) => MessageBox.Show("سيتم فتح نموذج إضافة عميل جديد", "إضافة عميل"));
            quickMenu.Items.Add("📦 منتج جديد", null, (s, e) => MessageBox.Show("سيتم فتح نموذج إضافة منتج جديد", "إضافة منتج"));
            quickMenu.Items.Add("💰 سند قبض", null, (s, e) => MessageBox.Show("سيتم فتح نموذج إضافة سند قبض", "إضافة سند"));

            quickMenu.Show(Cursor.Position);
        }

        private void CreateBackup()
        {
            try
            {
                DataService.SaveData();
                MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نسخ احتياطي",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveData()
        {
            try
            {
                DataService.SaveData();
                MessageBox.Show("تم حفظ البيانات بنجاح", "حفظ البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Logout()
        {
            var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                AuthService.Logout();
                this.Close();
            }
        }
        #endregion

        #region تنظيف الموارد
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                timeTimer?.Stop();
                timeTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
        #endregion
    }
}
