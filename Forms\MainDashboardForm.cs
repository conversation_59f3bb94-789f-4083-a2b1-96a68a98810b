using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Models;
using IntegratedInvoiceSystem.Services;

namespace IntegratedInvoiceSystem.Forms
{
    /// <summary>
    /// النموذج الرئيسي المتكامل مع الداشبورد
    /// </summary>
    public class MainDashboardForm : Form
    {
        #region المتغيرات الخاصة
        private Panel sidePanel;
        private Panel topPanel;
        private Panel contentPanel;
        private Label lblWelcome;
        private Label lblDateTime;
        private Label lblUserInfo;
        private Timer timeTimer;
        private string currentView = "dashboard";
        #endregion

        #region البناء والتهيئة
        public MainDashboardForm()
        {
            InitializeForm();
            SetupSidePanel();
            SetupTopPanel();
            ShowDashboard();
            StartTimer();
        }

        private void InitializeForm()
        {
            this.Text = "النظام المتكامل للفواتير - الواجهة الرئيسية";
            this.Size = new Size(1800, 1100);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(236, 240, 241);
            this.Icon = SystemIcons.Application;

            // الشريط الجانبي
            sidePanel = new Panel();
            sidePanel.Width = 350;
            sidePanel.Dock = DockStyle.Right;
            sidePanel.BackColor = Color.FromArgb(44, 62, 80);
            sidePanel.Paint += SidePanel_Paint;

            // الشريط العلوي
            topPanel = new Panel();
            topPanel.Height = 100;
            topPanel.Dock = DockStyle.Top;
            topPanel.BackColor = Color.White;
            topPanel.Paint += TopPanel_Paint;

            // منطقة المحتوى
            contentPanel = new Panel();
            contentPanel.Dock = DockStyle.Fill;
            contentPanel.BackColor = Color.FromArgb(236, 240, 241);
            contentPanel.Padding = new Padding(30);

            this.Controls.Add(contentPanel);
            this.Controls.Add(topPanel);
            this.Controls.Add(sidePanel);
        }

        private void SetupSidePanel()
        {
            // شعار النظام
            var logoPanel = new Panel();
            logoPanel.Height = 140;
            logoPanel.Dock = DockStyle.Top;
            logoPanel.BackColor = Color.FromArgb(52, 73, 94);

            var lblLogo = new Label();
            lblLogo.Text = "💼\nالنظام المتكامل\nللفواتير";
            lblLogo.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            lblLogo.ForeColor = Color.White;
            lblLogo.TextAlign = ContentAlignment.MiddleCenter;
            lblLogo.Dock = DockStyle.Fill;

            logoPanel.Controls.Add(lblLogo);
            sidePanel.Controls.Add(logoPanel);

            // قائمة التنقل المتكاملة
            var menuItems = new[]
            {
                new { Text = "🏠 الداشبورد الرئيسي", Action = new Action(() => ShowDashboard()), Key = "dashboard", Permission = "" },
                new { Text = "📊 إدارة الفواتير", Action = new Action(() => ShowInvoicesManagement()), Key = "invoices", Permission = "إدارة الفواتير" },
                new { Text = "➕ إضافة فاتورة جديدة", Action = new Action(() => ShowAddInvoice()), Key = "add_invoice", Permission = "إدارة الفواتير" },
                new { Text = "💰 السندات والمدفوعات", Action = new Action(() => ShowReceipts()), Key = "receipts", Permission = "إدارة الفواتير" },
                new { Text = "👥 إدارة العملاء", Action = new Action(() => ShowCustomers()), Key = "customers", Permission = "إدارة العملاء" },
                new { Text = "📦 إدارة المنتجات", Action = new Action(() => ShowProducts()), Key = "products", Permission = "إدارة المنتجات" },
                new { Text = "📈 التقارير والإحصائيات", Action = new Action(() => ShowReports()), Key = "reports", Permission = "التقارير" },
                new { Text = "🏢 معلومات الشركة", Action = new Action(() => ShowCompanyInfo()), Key = "company", Permission = "الإعدادات" },
                new { Text = "👤 إدارة المستخدمين", Action = new Action(() => ShowUsers()), Key = "users", Permission = "إدارة المستخدمين" },
                new { Text = "⚙️ الإعدادات العامة", Action = new Action(() => ShowSettings()), Key = "settings", Permission = "الإعدادات" },
                new { Text = "💾 حفظ البيانات", Action = new Action(() => SaveData()), Key = "save", Permission = "" },
                new { Text = "🚪 تسجيل خروج", Action = new Action(() => Logout()), Key = "logout", Permission = "" }
            };

            int yPos = 160;
            foreach (var item in menuItems)
            {
                // التحقق من الصلاحيات
                if (!string.IsNullOrEmpty(item.Permission) && !AuthService.HasPermission(item.Permission))
                    continue;

                var menuButton = CreateMenuButton(item.Text, item.Action, item.Key);
                menuButton.Location = new Point(15, yPos);
                sidePanel.Controls.Add(menuButton);
                yPos += 70;
            }
        }

        private Button CreateMenuButton(string text, Action action, string key)
        {
            var button = new Button();
            button.Text = text;
            button.Size = new Size(320, 60);
            button.BackColor = currentView == key ? Color.FromArgb(52, 152, 219) : Color.Transparent;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            button.TextAlign = ContentAlignment.MiddleLeft;
            button.Padding = new Padding(30, 0, 0, 0);
            button.Cursor = Cursors.Hand;
            button.Tag = key;
            button.Click += (s, e) => 
            {
                currentView = key;
                UpdateMenuButtons();
                action();
            };

            // تأثيرات التفاعل
            button.MouseEnter += (s, e) => 
            {
                if (currentView != key)
                    button.BackColor = Color.FromArgb(52, 152, 219);
            };
            button.MouseLeave += (s, e) => 
            {
                if (currentView != key)
                    button.BackColor = Color.Transparent;
            };

            return button;
        }

        private void UpdateMenuButtons()
        {
            foreach (Control control in sidePanel.Controls)
            {
                if (control is Button button && button.Tag != null)
                {
                    string key = button.Tag.ToString();
                    button.BackColor = currentView == key ? Color.FromArgb(52, 152, 219) : Color.Transparent;
                }
            }
        }

        private void SetupTopPanel()
        {
            // رسالة الترحيب
            lblWelcome = new Label();
            lblWelcome.Text = $"مرحباً، {AuthService.CurrentUserName} 👋";
            lblWelcome.Font = new Font("Segoe UI", 22F, FontStyle.Bold);
            lblWelcome.ForeColor = Color.FromArgb(44, 62, 80);
            lblWelcome.Location = new Point(40, 20);
            lblWelcome.Size = new Size(500, 50);

            // معلومات المستخدم
            lblUserInfo = new Label();
            lblUserInfo.Text = $"الدور: {AuthService.CurrentUser?.Role} | آخر دخول: {AuthService.CurrentUser?.LastLogin:yyyy/MM/dd HH:mm}";
            lblUserInfo.Font = new Font("Segoe UI", 12F);
            lblUserInfo.ForeColor = Color.FromArgb(127, 140, 141);
            lblUserInfo.Location = new Point(40, 70);
            lblUserInfo.Size = new Size(600, 25);

            // التاريخ والوقت
            lblDateTime = new Label();
            lblDateTime.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            lblDateTime.ForeColor = Color.FromArgb(52, 152, 219);
            lblDateTime.Location = new Point(topPanel.Width - 500, 20);
            lblDateTime.Size = new Size(450, 35);
            lblDateTime.TextAlign = ContentAlignment.MiddleRight;
            lblDateTime.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            // أزرار سريعة
            var btnNotifications = CreateQuickButton("🔔 الإشعارات", Color.FromArgb(231, 76, 60), ShowNotifications);
            btnNotifications.Location = new Point(topPanel.Width - 500, 55);
            btnNotifications.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            var btnQuickAdd = CreateQuickButton("⚡ إضافة سريعة", Color.FromArgb(46, 204, 113), ShowQuickAdd);
            btnQuickAdd.Location = new Point(topPanel.Width - 360, 55);
            btnQuickAdd.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            var btnBackup = CreateQuickButton("💾 نسخ احتياطي", Color.FromArgb(155, 89, 182), CreateBackup);
            btnBackup.Location = new Point(topPanel.Width - 210, 55);
            btnBackup.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            topPanel.Controls.AddRange(new Control[] {
                lblWelcome, lblUserInfo, lblDateTime, btnNotifications, btnQuickAdd, btnBackup
            });
        }

        private Button CreateQuickButton(string text, Color color, Action action)
        {
            var button = new Button();
            button.Text = text;
            button.Size = new Size(140, 45);
            button.BackColor = color;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            button.Cursor = Cursors.Hand;
            button.Click += (s, e) => action();

            // تأثيرات التفاعل
            button.MouseEnter += (s, e) => button.BackColor = ControlPaint.Dark(color, 0.1f);
            button.MouseLeave += (s, e) => button.BackColor = color;

            return button;
        }
        #endregion

        #region الأحداث والرسم
        private void SidePanel_Paint(object sender, PaintEventArgs e)
        {
            // تدرج للشريط الجانبي
            using (LinearGradientBrush brush = new LinearGradientBrush(
                sidePanel.ClientRectangle,
                Color.FromArgb(44, 62, 80),
                Color.FromArgb(52, 73, 94),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, sidePanel.ClientRectangle);
            }
        }

        private void TopPanel_Paint(object sender, PaintEventArgs e)
        {
            // ظل للشريط العلوي
            using (Pen pen = new Pen(Color.FromArgb(189, 195, 199), 3))
            {
                e.Graphics.DrawLine(pen, 0, topPanel.Height - 3, topPanel.Width, topPanel.Height - 3);
            }
        }

        private void StartTimer()
        {
            timeTimer = new Timer();
            timeTimer.Interval = 1000;
            timeTimer.Tick += (s, e) => 
            {
                lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy\nHH:mm:ss", 
                    new System.Globalization.CultureInfo("ar-SA"));
            };
            timeTimer.Start();
            
            // تحديث فوري
            lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy\nHH:mm:ss", 
                new System.Globalization.CultureInfo("ar-SA"));
        }
        #endregion

        #region دوال عرض المحتوى
        private void ShowDashboard()
        {
            contentPanel.Controls.Clear();
            currentView = "dashboard";
            UpdateMenuButtons();

            var dashboardControl = new DashboardControl();
            dashboardControl.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(dashboardControl);
        }

        private void ShowInvoicesManagement()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة الفواتير المتكاملة\n\nالميزات المتوفرة:\n• عرض جميع الفواتير مع فلترة متقدمة\n• إضافة وتعديل وحذف الفواتير\n• طباعة الفواتير\n• تسجيل المدفوعات\n• تتبع حالات الفواتير", 
                "إدارة الفواتير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowAddInvoice()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إضافة فاتورة جديدة\n\nالميزات المتوفرة:\n• نموذج شامل لإدخال بيانات الفاتورة\n• إضافة أصناف متعددة\n• حساب تلقائي للضرائب والخصومات\n• اختيار العميل من قائمة\n• حفظ كمسودة أو تأكيد", 
                "إضافة فاتورة جديدة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReceipts()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة السندات والمدفوعات\n\nالميزات المتوفرة:\n• عرض جميع السندات\n• إضافة سند قبض جديد\n• ربط السندات بالفواتير\n• طرق دفع متعددة\n• تقارير المدفوعات", 
                "إدارة السندات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCustomers()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة العملاء\n\nالميزات المتوفرة:\n• عرض جميع العملاء\n• إضافة عميل جديد\n• تعديل بيانات العملاء\n• كشف حساب العميل\n• تقارير العملاء\n• إدارة الأرصدة", 
                "إدارة العملاء", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowProducts()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة المنتجات\n\nالميزات المتوفرة:\n• عرض جميع المنتجات\n• إضافة منتج جديد\n• تعديل أسعار المنتجات\n• إدارة المخزون\n• فئات المنتجات\n• الباركود", 
                "إدارة المنتجات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReports()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة التقارير والإحصائيات\n\nالتقارير المتوفرة:\n• تقرير المبيعات\n• تقرير المدفوعات\n• تقرير العملاء\n• تقرير الأرباح\n• تقرير المخزون\n• تقارير مخصصة", 
                "التقارير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCompanyInfo()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة معلومات الشركة\n\nالإعدادات المتوفرة:\n• اسم الشركة وعنوانها\n• معلومات الاتصال\n• الرقم الضريبي\n• السجل التجاري\n• شعار الشركة", 
                "معلومات الشركة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowUsers()
        {
            if (!AuthService.IsAdmin())
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة المستخدمين", "صلاحيات غير كافية", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("🎉 سيتم فتح شاشة إدارة المستخدمين\n\nالميزات المتوفرة:\n• عرض جميع المستخدمين\n• إضافة مستخدم جديد\n• تعديل الصلاحيات\n• تغيير كلمات المرور\n• تفعيل/إلغاء تفعيل المستخدمين", 
                "إدارة المستخدمين", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowSettings()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة الإعدادات العامة\n\nالإعدادات المتوفرة:\n• إعدادات الضرائب\n• إعدادات الطباعة\n• إعدادات النسخ الاحتياطي\n• إعدادات التنبيهات\n• إعدادات العملة", 
                "الإعدادات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowNotifications()
        {
            var stats = DataService.GetDashboardStats();
            
            MessageBox.Show($"🔔 الإشعارات:\n\n• {stats.OverdueInvoices} فاتورة متأخرة\n• {stats.DueToday} فاتورة مستحقة اليوم\n• {stats.LowStockProducts} منتج بمخزون منخفض\n• آخر تحديث: {DateTime.Now:HH:mm}", 
                "الإشعارات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowQuickAdd()
        {
            var quickMenu = new ContextMenuStrip();
            quickMenu.Items.Add("➕ فاتورة جديدة", null, (s, e) => ShowAddInvoice());
            quickMenu.Items.Add("👤 عميل جديد", null, (s, e) => MessageBox.Show("سيتم فتح نموذج إضافة عميل جديد", "إضافة عميل"));
            quickMenu.Items.Add("📦 منتج جديد", null, (s, e) => MessageBox.Show("سيتم فتح نموذج إضافة منتج جديد", "إضافة منتج"));
            quickMenu.Items.Add("💰 سند قبض", null, (s, e) => MessageBox.Show("سيتم فتح نموذج إضافة سند قبض", "إضافة سند"));
            
            quickMenu.Show(Cursor.Position);
        }

        private void CreateBackup()
        {
            try
            {
                DataService.SaveData();
                MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نسخ احتياطي", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveData()
        {
            try
            {
                DataService.SaveData();
                MessageBox.Show("تم حفظ البيانات بنجاح", "حفظ البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Logout()
        {
            var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد الخروج", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                AuthService.Logout();
                this.Close();
            }
        }
        #endregion

        #region تنظيف الموارد
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                timeTimer?.Stop();
                timeTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
        #endregion
    }
}
