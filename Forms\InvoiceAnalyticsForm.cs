using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Models;
using IntegratedInvoiceSystem.Services;
using AccountingSystem.Utils;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج تحليلات الفواتير المتقدم
    /// </summary>
    public partial class InvoiceAnalyticsForm : Form
    {
        #region Fields
        private TabControl tabControl;
        private TabPage tabOverview, tabTrends, tabCustomers, tabPerformance;

        // Data
        private List<Invoice> invoices;
        private List<Customer> customers;
        private List<Receipt> receipts;

        // Overview Controls
        private Panel pnlKPIs;
        private Panel pnlCharts;

        // Trends Controls
        private Panel pnlTrendCharts;
        private DateTimePicker dtpTrendFrom, dtpTrendTo;
        private ComboBox cmbTrendPeriod;

        // Customers Controls
        private Panel pnlCustomerAnalysis;
        private DataGridView dgvTopCustomers;

        // Performance Controls
        private Panel pnlPerformanceMetrics;
        private DataGridView dgvPerformanceKPIs;

        #endregion

        #region Constructor
        public InvoiceAnalyticsForm()
        {
            InitializeComponent();
            LoadData();
            SetupForm();
            LoadAnalytics();
        }
        #endregion

        #region Form Setup
        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "تحليلات الفواتير المتقدمة";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.Font = new Font("Tahoma", 9F);
            this.WindowState = FormWindowState.Maximized;

            // Create tab control
            CreateTabControl();
            CreateOverviewTab();
            CreateTrendsTab();
            CreateCustomersTab();
            CreatePerformanceTab();

            this.ResumeLayout(false);
        }

        private void CreateTabControl()
        {
            tabControl = new TabControl();
            tabControl.Dock = DockStyle.Fill;
            tabControl.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            tabControl.RightToLeftLayout = true;

            // Create tabs
            tabOverview = new TabPage("نظرة عامة");
            tabTrends = new TabPage("الاتجاهات والتوقعات");
            tabCustomers = new TabPage("تحليل العملاء");
            tabPerformance = new TabPage("مؤشرات الأداء");

            tabControl.TabPages.AddRange(new TabPage[] { tabOverview, tabTrends, tabCustomers, tabPerformance });
            this.Controls.Add(tabControl);
        }

        private void CreateOverviewTab()
        {
            // KPIs Panel
            pnlKPIs = new Panel();
            pnlKPIs.Height = 120;
            pnlKPIs.Dock = DockStyle.Top;
            pnlKPIs.BackColor = Color.FromArgb(248, 249, 250);
            pnlKPIs.Padding = new Padding(10);

            // Charts Panel
            pnlCharts = new Panel();
            pnlCharts.Dock = DockStyle.Fill;
            pnlCharts.BackColor = Color.White;
            pnlCharts.AutoScroll = true;

            tabOverview.Controls.AddRange(new Control[] { pnlKPIs, pnlCharts });
        }

        private void CreateTrendsTab()
        {
            // Filter Panel
            Panel pnlTrendFilter = new Panel();
            pnlTrendFilter.Height = 60;
            pnlTrendFilter.Dock = DockStyle.Top;
            pnlTrendFilter.BackColor = Color.FromArgb(240, 248, 255);
            pnlTrendFilter.Padding = new Padding(10);

            Label lblTrendFrom = new Label();
            lblTrendFrom.Text = "من تاريخ:";
            lblTrendFrom.Location = new Point(950, 15);
            lblTrendFrom.Size = new Size(60, 23);
            lblTrendFrom.TextAlign = ContentAlignment.MiddleRight;

            dtpTrendFrom = new DateTimePicker();
            dtpTrendFrom.Location = new Point(780, 15);
            dtpTrendFrom.Size = new Size(160, 23);
            dtpTrendFrom.Value = DateTime.Today.AddMonths(-12);

            Label lblTrendTo = new Label();
            lblTrendTo.Text = "إلى تاريخ:";
            lblTrendTo.Location = new Point(700, 15);
            lblTrendTo.Size = new Size(60, 23);
            lblTrendTo.TextAlign = ContentAlignment.MiddleRight;

            dtpTrendTo = new DateTimePicker();
            dtpTrendTo.Location = new Point(530, 15);
            dtpTrendTo.Size = new Size(160, 23);
            dtpTrendTo.Value = DateTime.Today;

            Label lblPeriod = new Label();
            lblPeriod.Text = "الفترة:";
            lblPeriod.Location = new Point(450, 15);
            lblPeriod.Size = new Size(60, 23);
            lblPeriod.TextAlign = ContentAlignment.MiddleRight;

            cmbTrendPeriod = new ComboBox();
            cmbTrendPeriod.Location = new Point(280, 15);
            cmbTrendPeriod.Size = new Size(160, 23);
            cmbTrendPeriod.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbTrendPeriod.Items.AddRange(new string[] { "يومي", "أسبوعي", "شهري", "ربع سنوي", "سنوي" });
            cmbTrendPeriod.SelectedIndex = 2; // شهري

            Button btnRefreshTrends = new Button();
            btnRefreshTrends.Text = "تحديث";
            btnRefreshTrends.Location = new Point(180, 15);
            btnRefreshTrends.Size = new Size(80, 30);
            btnRefreshTrends.BackColor = Color.FromArgb(0, 123, 255);
            btnRefreshTrends.ForeColor = Color.White;
            btnRefreshTrends.FlatStyle = FlatStyle.Flat;
            btnRefreshTrends.Click += BtnRefreshTrends_Click;

            pnlTrendFilter.Controls.AddRange(new Control[] {
                lblTrendFrom, dtpTrendFrom, lblTrendTo, dtpTrendTo,
                lblPeriod, cmbTrendPeriod, btnRefreshTrends
            });

            // Trend Charts Panel
            pnlTrendCharts = new Panel();
            pnlTrendCharts.Dock = DockStyle.Fill;
            pnlTrendCharts.BackColor = Color.White;
            pnlTrendCharts.AutoScroll = true;

            tabTrends.Controls.AddRange(new Control[] { pnlTrendFilter, pnlTrendCharts });
        }

        private void CreateCustomersTab()
        {
            // Customer Analysis Panel
            pnlCustomerAnalysis = new Panel();
            pnlCustomerAnalysis.Height = 400;
            pnlCustomerAnalysis.Dock = DockStyle.Top;
            pnlCustomerAnalysis.BackColor = Color.White;
            pnlCustomerAnalysis.AutoScroll = true;

            // Top Customers Grid
            dgvTopCustomers = new DataGridView();
            dgvTopCustomers.Dock = DockStyle.Fill;
            dgvTopCustomers.BackgroundColor = Color.White;
            dgvTopCustomers.BorderStyle = BorderStyle.None;
            dgvTopCustomers.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvTopCustomers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvTopCustomers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvTopCustomers.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            dgvTopCustomers.ColumnHeadersHeight = 40;
            dgvTopCustomers.DefaultCellStyle.Font = new Font("Tahoma", 9F);
            dgvTopCustomers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvTopCustomers.RowHeadersVisible = false;
            dgvTopCustomers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvTopCustomers.MultiSelect = false;
            dgvTopCustomers.ReadOnly = true;
            dgvTopCustomers.AllowUserToAddRows = false;
            dgvTopCustomers.AllowUserToDeleteRows = false;
            dgvTopCustomers.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            SetupCustomersGridColumns();

            tabCustomers.Controls.AddRange(new Control[] { pnlCustomerAnalysis, dgvTopCustomers });
        }

        private void CreatePerformanceTab()
        {
            // Performance Metrics Panel
            pnlPerformanceMetrics = new Panel();
            pnlPerformanceMetrics.Height = 300;
            pnlPerformanceMetrics.Dock = DockStyle.Top;
            pnlPerformanceMetrics.BackColor = Color.White;
            pnlPerformanceMetrics.AutoScroll = true;

            // Performance KPIs Grid
            dgvPerformanceKPIs = new DataGridView();
            dgvPerformanceKPIs.Dock = DockStyle.Fill;
            dgvPerformanceKPIs.BackgroundColor = Color.White;
            dgvPerformanceKPIs.BorderStyle = BorderStyle.None;
            dgvPerformanceKPIs.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvPerformanceKPIs.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvPerformanceKPIs.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvPerformanceKPIs.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            dgvPerformanceKPIs.ColumnHeadersHeight = 40;
            dgvPerformanceKPIs.DefaultCellStyle.Font = new Font("Tahoma", 9F);
            dgvPerformanceKPIs.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvPerformanceKPIs.RowHeadersVisible = false;
            dgvPerformanceKPIs.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvPerformanceKPIs.MultiSelect = false;
            dgvPerformanceKPIs.ReadOnly = true;
            dgvPerformanceKPIs.AllowUserToAddRows = false;
            dgvPerformanceKPIs.AllowUserToDeleteRows = false;
            dgvPerformanceKPIs.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            SetupPerformanceGridColumns();

            tabPerformance.Controls.AddRange(new Control[] { pnlPerformanceMetrics, dgvPerformanceKPIs });
        }

        private void SetupCustomersGridColumns()
        {
            dgvTopCustomers.Columns.Clear();
            dgvTopCustomers.Columns.Add("CustomerName", "اسم العميل");
            dgvTopCustomers.Columns.Add("InvoiceCount", "عدد الفواتير");
            dgvTopCustomers.Columns.Add("TotalAmount", "إجمالي المبلغ");
            dgvTopCustomers.Columns.Add("PaidAmount", "المبلغ المدفوع");
            dgvTopCustomers.Columns.Add("OutstandingAmount", "المبلغ المستحق");
            dgvTopCustomers.Columns.Add("CollectionRate", "معدل التحصيل %");
            dgvTopCustomers.Columns.Add("AverageInvoiceValue", "متوسط قيمة الفاتورة");
            dgvTopCustomers.Columns.Add("LastInvoiceDate", "آخر فاتورة");

            // Format columns
            dgvTopCustomers.Columns["TotalAmount"].DefaultCellStyle.Format = "N2";
            dgvTopCustomers.Columns["PaidAmount"].DefaultCellStyle.Format = "N2";
            dgvTopCustomers.Columns["OutstandingAmount"].DefaultCellStyle.Format = "N2";
            dgvTopCustomers.Columns["CollectionRate"].DefaultCellStyle.Format = "N1";
            dgvTopCustomers.Columns["AverageInvoiceValue"].DefaultCellStyle.Format = "N2";
            dgvTopCustomers.Columns["LastInvoiceDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
        }

        private void SetupPerformanceGridColumns()
        {
            dgvPerformanceKPIs.Columns.Clear();
            dgvPerformanceKPIs.Columns.Add("Metric", "المؤشر");
            dgvPerformanceKPIs.Columns.Add("CurrentPeriod", "الفترة الحالية");
            dgvPerformanceKPIs.Columns.Add("PreviousPeriod", "الفترة السابقة");
            dgvPerformanceKPIs.Columns.Add("Change", "التغيير");
            dgvPerformanceKPIs.Columns.Add("ChangePercent", "نسبة التغيير %");
            dgvPerformanceKPIs.Columns.Add("Target", "الهدف");
            dgvPerformanceKPIs.Columns.Add("Achievement", "نسبة الإنجاز %");

            // Format columns
            dgvPerformanceKPIs.Columns["CurrentPeriod"].DefaultCellStyle.Format = "N2";
            dgvPerformanceKPIs.Columns["PreviousPeriod"].DefaultCellStyle.Format = "N2";
            dgvPerformanceKPIs.Columns["Change"].DefaultCellStyle.Format = "N2";
            dgvPerformanceKPIs.Columns["ChangePercent"].DefaultCellStyle.Format = "N1";
            dgvPerformanceKPIs.Columns["Target"].DefaultCellStyle.Format = "N2";
            dgvPerformanceKPIs.Columns["Achievement"].DefaultCellStyle.Format = "N1";
        }

        private void SetupForm()
        {
            // Additional setup if needed
        }
        #endregion

        #region Data Loading
        private void LoadData()
        {
            try
            {
                // Load data from DataService or database
                invoices = DataService.Invoices ?? new List<Invoice>();
                customers = DataService.Customers ?? new List<Customer>();
                receipts = DataService.Receipts ?? new List<Receipt>();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Initialize empty lists if loading fails
                invoices = new List<Invoice>();
                customers = new List<Customer>();
                receipts = new List<Receipt>();
            }
        }

        private void LoadAnalytics()
        {
            try
            {
                LoadOverviewAnalytics();
                LoadTrendsAnalytics();
                LoadCustomersAnalytics();
                LoadPerformanceAnalytics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التحليلات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region Event Handlers
        private void BtnRefreshTrends_Click(object sender, EventArgs e)
        {
            LoadTrendsAnalytics();
        }
        #endregion

        #region Analytics Methods
        private void LoadOverviewAnalytics()
        {
            // Clear existing controls
            pnlKPIs.Controls.Clear();
            pnlCharts.Controls.Clear();

            // Create KPI cards
            CreateOverviewKPIs();

            // Create overview charts
            CreateOverviewCharts();
        }

        private void CreateOverviewKPIs()
        {
            var totalInvoices = invoices.Count;
            var totalAmount = invoices.Sum(i => i.TotalAmount);
            var paidAmount = invoices.Sum(i => i.PaidAmount);
            var outstandingAmount = totalAmount - paidAmount;
            var collectionRate = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;
            var averageInvoiceValue = totalInvoices > 0 ? totalAmount / totalInvoices : 0;

            // KPI Cards
            CreateKPICard("إجمالي الفواتير", totalInvoices.ToString("N0"), Color.FromArgb(0, 123, 255), 10, 10);
            CreateKPICard("إجمالي المبلغ", totalAmount.ToString("N2") + " ريال", Color.FromArgb(40, 167, 69), 280, 10);
            CreateKPICard("معدل التحصيل", collectionRate.ToString("N1") + "%", Color.FromArgb(23, 162, 184), 550, 10);
            CreateKPICard("متوسط قيمة الفاتورة", averageInvoiceValue.ToString("N2") + " ريال", Color.FromArgb(255, 193, 7), 820, 10);
            CreateKPICard("المبلغ المستحق", outstandingAmount.ToString("N2") + " ريال", Color.FromArgb(220, 53, 69), 1090, 10);
        }

        private void CreateKPICard(string title, string value, Color color, int x, int y)
        {
            Panel card = new Panel();
            card.Size = new Size(250, 90);
            card.Location = new Point(x, y);
            card.BackColor = color;
            card.ForeColor = Color.White;

            Label lblTitle = new Label();
            lblTitle.Text = title;
            lblTitle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblTitle.Location = new Point(10, 10);
            lblTitle.Size = new Size(230, 25);
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;

            Label lblValue = new Label();
            lblValue.Text = value;
            lblValue.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            lblValue.Location = new Point(10, 35);
            lblValue.Size = new Size(230, 45);
            lblValue.TextAlign = ContentAlignment.MiddleCenter;

            card.Controls.AddRange(new Control[] { lblTitle, lblValue });
            pnlKPIs.Controls.Add(card);
        }

        private void CreateOverviewCharts()
        {
            // Status Distribution Chart
            CreateStatusDistributionChart();

            // Monthly Revenue Chart
            CreateMonthlyRevenueChart();

            // Top Customers Chart
            CreateTopCustomersChart();

            // Collection Rate Trend Chart
            CreateCollectionRateTrendChart();
        }

        private void CreateStatusDistributionChart()
        {
            var statusPanel = new Panel();
            statusPanel.Size = new Size(600, 350);
            statusPanel.Location = new Point(10, 10);
            statusPanel.BorderStyle = BorderStyle.FixedSingle;
            statusPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "توزيع الفواتير حسب الحالة";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var statusGroups = invoices.GroupBy(i => i.Status)
                .Select(g => new { Status = g.Key, Count = g.Count(), Amount = g.Sum(i => i.TotalAmount) })
                .OrderByDescending(g => g.Amount)
                .ToList();

            int y = 50;
            int maxWidth = 500;
            var maxAmount = statusGroups.Max(g => g.Amount);

            foreach (var group in statusGroups)
            {
                // Status label
                var statusLabel = new Label();
                statusLabel.Text = group.Status;
                statusLabel.Location = new Point(20, y);
                statusLabel.Size = new Size(100, 25);
                statusLabel.Font = new Font("Tahoma", 10F, FontStyle.Bold);
                statusPanel.Controls.Add(statusLabel);

                // Progress bar
                var progressBar = new ProgressBar();
                progressBar.Location = new Point(130, y + 2);
                progressBar.Size = new Size(300, 20);
                progressBar.Maximum = 100;
                progressBar.Value = maxAmount > 0 ? (int)((group.Amount / maxAmount) * 100) : 0;
                statusPanel.Controls.Add(progressBar);

                // Amount label
                var amountLabel = new Label();
                amountLabel.Text = $"{group.Count} فاتورة - {group.Amount:N2} ريال";
                amountLabel.Location = new Point(440, y);
                amountLabel.Size = new Size(150, 25);
                amountLabel.Font = new Font("Tahoma", 9F);
                statusPanel.Controls.Add(amountLabel);

                y += 35;
            }

            statusPanel.Controls.Add(titleLabel);
            pnlCharts.Controls.Add(statusPanel);
        }

        private void CreateMonthlyRevenueChart()
        {
            var revenuePanel = new Panel();
            revenuePanel.Size = new Size(600, 350);
            revenuePanel.Location = new Point(620, 10);
            revenuePanel.BorderStyle = BorderStyle.FixedSingle;
            revenuePanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "الإيرادات الشهرية (آخر 12 شهر)";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var monthlyData = invoices
                .Where(i => i.InvoiceDate >= DateTime.Today.AddMonths(-12))
                .GroupBy(i => new { Year = i.InvoiceDate.Year, Month = i.InvoiceDate.Month })
                .Select(g => new
                {
                    Period = $"{g.Key.Year}/{g.Key.Month:00}",
                    Amount = g.Sum(i => i.TotalAmount),
                    Count = g.Count()
                })
                .OrderBy(g => g.Period)
                .ToList();

            int y = 50;
            var maxAmount = monthlyData.Any() ? monthlyData.Max(d => d.Amount) : 1;

            foreach (var data in monthlyData)
            {
                // Period label
                var periodLabel = new Label();
                periodLabel.Text = data.Period;
                periodLabel.Location = new Point(20, y);
                periodLabel.Size = new Size(80, 25);
                periodLabel.Font = new Font("Tahoma", 9F);
                revenuePanel.Controls.Add(periodLabel);

                // Revenue bar
                var revenueBar = new ProgressBar();
                revenueBar.Location = new Point(110, y + 2);
                revenueBar.Size = new Size(300, 20);
                revenueBar.Maximum = 100;
                revenueBar.Value = maxAmount > 0 ? (int)((data.Amount / maxAmount) * 100) : 0;
                revenuePanel.Controls.Add(revenueBar);

                // Amount label
                var amountLabel = new Label();
                amountLabel.Text = $"{data.Count} فاتورة - {data.Amount:N2} ريال";
                amountLabel.Location = new Point(420, y);
                amountLabel.Size = new Size(170, 25);
                amountLabel.Font = new Font("Tahoma", 9F);
                revenuePanel.Controls.Add(amountLabel);

                y += 25;
            }

            revenuePanel.Controls.Add(titleLabel);
            pnlCharts.Controls.Add(revenuePanel);
        }

        private void CreateTopCustomersChart()
        {
            var customersPanel = new Panel();
            customersPanel.Size = new Size(600, 350);
            customersPanel.Location = new Point(10, 370);
            customersPanel.BorderStyle = BorderStyle.FixedSingle;
            customersPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "أعلى العملاء (حسب إجمالي المبلغ)";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var topCustomersData = invoices
                .GroupBy(i => i.CustomerId)
                .Select(g => new
                {
                    CustomerId = g.Key,
                    CustomerName = customers.FirstOrDefault(c => c.Id == g.Key)?.CustomerName ?? "غير محدد",
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    InvoiceCount = g.Count()
                })
                .OrderByDescending(c => c.TotalAmount)
                .Take(10)
                .ToList();

            int y = 50;
            var maxAmount = topCustomersData.Any() ? topCustomersData.Max(c => c.TotalAmount) : 1;

            foreach (var customer in topCustomersData)
            {
                // Customer name label
                var nameLabel = new Label();
                nameLabel.Text = customer.CustomerName;
                nameLabel.Location = new Point(20, y);
                nameLabel.Size = new Size(150, 25);
                nameLabel.Font = new Font("Tahoma", 9F, FontStyle.Bold);
                customersPanel.Controls.Add(nameLabel);

                // Amount bar
                var amountBar = new ProgressBar();
                amountBar.Location = new Point(180, y + 2);
                amountBar.Size = new Size(250, 20);
                amountBar.Maximum = 100;
                amountBar.Value = maxAmount > 0 ? (int)((customer.TotalAmount / maxAmount) * 100) : 0;
                customersPanel.Controls.Add(amountBar);

                // Amount label
                var amountLabel = new Label();
                amountLabel.Text = $"{customer.InvoiceCount} فاتورة - {customer.TotalAmount:N2} ريال";
                amountLabel.Location = new Point(440, y);
                amountLabel.Size = new Size(150, 25);
                amountLabel.Font = new Font("Tahoma", 9F);
                customersPanel.Controls.Add(amountLabel);

                y += 30;
            }

            customersPanel.Controls.Add(titleLabel);
            pnlCharts.Controls.Add(customersPanel);
        }

        private void CreateCollectionRateTrendChart()
        {
            var collectionPanel = new Panel();
            collectionPanel.Size = new Size(600, 350);
            collectionPanel.Location = new Point(620, 370);
            collectionPanel.BorderStyle = BorderStyle.FixedSingle;
            collectionPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "اتجاه معدل التحصيل (آخر 12 شهر)";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var collectionData = invoices
                .Where(i => i.InvoiceDate >= DateTime.Today.AddMonths(-12))
                .GroupBy(i => new { Year = i.InvoiceDate.Year, Month = i.InvoiceDate.Month })
                .Select(g => new
                {
                    Period = $"{g.Key.Year}/{g.Key.Month:00}",
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    PaidAmount = g.Sum(i => i.PaidAmount),
                    CollectionRate = g.Sum(i => i.TotalAmount) > 0 ? (g.Sum(i => i.PaidAmount) / g.Sum(i => i.TotalAmount)) * 100 : 0
                })
                .OrderBy(g => g.Period)
                .ToList();

            int y = 50;

            foreach (var data in collectionData)
            {
                // Period label
                var periodLabel = new Label();
                periodLabel.Text = data.Period;
                periodLabel.Location = new Point(20, y);
                periodLabel.Size = new Size(80, 25);
                periodLabel.Font = new Font("Tahoma", 9F);
                collectionPanel.Controls.Add(periodLabel);

                // Collection rate bar
                var rateBar = new ProgressBar();
                rateBar.Location = new Point(110, y + 2);
                rateBar.Size = new Size(300, 20);
                rateBar.Maximum = 100;
                rateBar.Value = (int)Math.Min(data.CollectionRate, 100);
                collectionPanel.Controls.Add(rateBar);

                // Rate label
                var rateLabel = new Label();
                rateLabel.Text = $"{data.CollectionRate:N1}%";
                rateLabel.Location = new Point(420, y);
                rateLabel.Size = new Size(80, 25);
                rateLabel.Font = new Font("Tahoma", 9F);
                collectionPanel.Controls.Add(rateLabel);

                // Amount label
                var amountLabel = new Label();
                amountLabel.Text = $"{data.PaidAmount:N0}/{data.TotalAmount:N0}";
                amountLabel.Location = new Point(500, y);
                amountLabel.Size = new Size(90, 25);
                amountLabel.Font = new Font("Tahoma", 8F);
                collectionPanel.Controls.Add(amountLabel);

                y += 25;
            }

            collectionPanel.Controls.Add(titleLabel);
            pnlCharts.Controls.Add(collectionPanel);
        }

        private void LoadTrendsAnalytics()
        {
            try
            {
                // Clear existing controls
                pnlTrendCharts.Controls.Clear();

                // Create trend analysis charts
                CreateSalesTrendChart();
                CreateCollectionTrendChart();
                CreateSeasonalityChart();
                CreateForecastChart();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تحليل الاتجاهات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadCustomersAnalytics()
        {
            try
            {
                // Clear existing data
                dgvTopCustomers.Rows.Clear();

                // Load customer analytics data
                var customerAnalytics = invoices
                    .GroupBy(i => i.CustomerId)
                    .Select(g => new
                    {
                        CustomerId = g.Key,
                        CustomerName = customers.FirstOrDefault(c => c.Id == g.Key)?.CustomerName ?? "غير محدد",
                        InvoiceCount = g.Count(),
                        TotalAmount = g.Sum(i => i.TotalAmount),
                        PaidAmount = g.Sum(i => i.PaidAmount),
                        OutstandingAmount = g.Sum(i => i.TotalAmount - i.PaidAmount),
                        CollectionRate = g.Sum(i => i.TotalAmount) > 0 ? (g.Sum(i => i.PaidAmount) / g.Sum(i => i.TotalAmount)) * 100 : 0,
                        AverageInvoiceValue = g.Count() > 0 ? g.Sum(i => i.TotalAmount) / g.Count() : 0,
                        LastInvoiceDate = g.Max(i => i.InvoiceDate)
                    })
                    .OrderByDescending(c => c.TotalAmount)
                    .ToList();

                // Populate the grid
                foreach (var customer in customerAnalytics)
                {
                    dgvTopCustomers.Rows.Add(
                        customer.CustomerName,
                        customer.InvoiceCount,
                        customer.TotalAmount,
                        customer.PaidAmount,
                        customer.OutstandingAmount,
                        customer.CollectionRate,
                        customer.AverageInvoiceValue,
                        customer.LastInvoiceDate
                    );
                }

                // Create customer analysis charts
                CreateCustomerSegmentationChart();
                CreateCustomerRiskAnalysisChart();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تحليل العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadPerformanceAnalytics()
        {
            try
            {
                // Clear existing data
                dgvPerformanceKPIs.Rows.Clear();

                // Calculate current period (this month)
                var currentPeriodStart = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                var currentPeriodEnd = currentPeriodStart.AddMonths(1).AddDays(-1);

                // Calculate previous period (last month)
                var previousPeriodStart = currentPeriodStart.AddMonths(-1);
                var previousPeriodEnd = currentPeriodStart.AddDays(-1);

                // Get current period data
                var currentInvoices = invoices.Where(i => i.InvoiceDate >= currentPeriodStart && i.InvoiceDate <= currentPeriodEnd);
                var previousInvoices = invoices.Where(i => i.InvoiceDate >= previousPeriodStart && i.InvoiceDate <= previousPeriodEnd);

                // Calculate KPIs
                var kpis = new List<PerformanceKPI>
                {
                    CalculateKPI("إجمالي المبيعات", currentInvoices.Sum(i => i.TotalAmount), previousInvoices.Sum(i => i.TotalAmount), 1000000),
                    CalculateKPI("عدد الفواتير", currentInvoices.Count(), previousInvoices.Count(), 100),
                    CalculateKPI("متوسط قيمة الفاتورة", currentInvoices.Any() ? currentInvoices.Average(i => i.TotalAmount) : 0, previousInvoices.Any() ? previousInvoices.Average(i => i.TotalAmount) : 0, 10000),
                    CalculateKPI("معدل التحصيل %", currentInvoices.Sum(i => i.TotalAmount) > 0 ? (currentInvoices.Sum(i => i.PaidAmount) / currentInvoices.Sum(i => i.TotalAmount)) * 100 : 0, previousInvoices.Sum(i => i.TotalAmount) > 0 ? (previousInvoices.Sum(i => i.PaidAmount) / previousInvoices.Sum(i => i.TotalAmount)) * 100 : 0, 95),
                    CalculateKPI("المبلغ المستحق", currentInvoices.Sum(i => i.TotalAmount - i.PaidAmount), previousInvoices.Sum(i => i.TotalAmount - i.PaidAmount), 50000),
                    CalculateKPI("عدد العملاء الجدد", GetNewCustomersCount(currentPeriodStart, currentPeriodEnd), GetNewCustomersCount(previousPeriodStart, previousPeriodEnd), 10)
                };

                // Populate the grid
                foreach (var kpi in kpis)
                {
                    dgvPerformanceKPIs.Rows.Add(
                        kpi.Metric,
                        kpi.CurrentPeriod,
                        kpi.PreviousPeriod,
                        kpi.Change,
                        kpi.ChangePercent,
                        kpi.Target,
                        kpi.Achievement
                    );
                }

                // Create performance charts
                CreatePerformanceComparisonChart();
                CreateGoalsAchievementChart();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل مؤشرات الأداء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion
        #region Helper Methods
        private PerformanceKPI CalculateKPI(string metric, decimal currentValue, decimal previousValue, decimal target)
        {
            var change = currentValue - previousValue;
            var changePercent = previousValue != 0 ? (change / previousValue) * 100 : 0;
            var achievement = target != 0 ? (currentValue / target) * 100 : 0;

            return new PerformanceKPI
            {
                Metric = metric,
                CurrentPeriod = currentValue,
                PreviousPeriod = previousValue,
                Change = change,
                ChangePercent = changePercent,
                Target = target,
                Achievement = achievement
            };
        }

        private int GetNewCustomersCount(DateTime startDate, DateTime endDate)
        {
            var newCustomers = customers.Where(c =>
                invoices.Where(i => i.CustomerId == c.Id && i.InvoiceDate >= startDate && i.InvoiceDate <= endDate).Any() &&
                !invoices.Where(i => i.CustomerId == c.Id && i.InvoiceDate < startDate).Any()
            ).Count();

            return newCustomers;
        }

        private void CreateSalesTrendChart()
        {
            var trendPanel = new Panel();
            trendPanel.Size = new Size(600, 300);
            trendPanel.Location = new Point(10, 10);
            trendPanel.BorderStyle = BorderStyle.FixedSingle;
            trendPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "اتجاه المبيعات";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var fromDate = dtpTrendFrom.Value;
            var toDate = dtpTrendTo.Value;
            var period = cmbTrendPeriod.SelectedItem?.ToString() ?? "شهري";

            var trendData = GetTrendData(fromDate, toDate, period);

            int y = 50;
            var maxAmount = trendData.Any() ? trendData.Max(t => t.Amount) : 1;

            foreach (var data in trendData)
            {
                // Period label
                var periodLabel = new Label();
                periodLabel.Text = data.Period;
                periodLabel.Location = new Point(20, y);
                periodLabel.Size = new Size(100, 25);
                periodLabel.Font = new Font("Tahoma", 9F);
                trendPanel.Controls.Add(periodLabel);

                // Trend bar
                var trendBar = new ProgressBar();
                trendBar.Location = new Point(130, y + 2);
                trendBar.Size = new Size(300, 20);
                trendBar.Maximum = 100;
                trendBar.Value = maxAmount > 0 ? (int)((data.Amount / maxAmount) * 100) : 0;
                trendPanel.Controls.Add(trendBar);

                // Amount label
                var amountLabel = new Label();
                amountLabel.Text = $"{data.Count} فاتورة - {data.Amount:N2} ريال";
                amountLabel.Location = new Point(440, y);
                amountLabel.Size = new Size(150, 25);
                amountLabel.Font = new Font("Tahoma", 9F);
                trendPanel.Controls.Add(amountLabel);

                y += 30;
            }

            trendPanel.Controls.Add(titleLabel);
            pnlTrendCharts.Controls.Add(trendPanel);
        }

        private void CreateCollectionTrendChart()
        {
            var collectionPanel = new Panel();
            collectionPanel.Size = new Size(600, 300);
            collectionPanel.Location = new Point(620, 10);
            collectionPanel.BorderStyle = BorderStyle.FixedSingle;
            collectionPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "اتجاه التحصيل";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var fromDate = dtpTrendFrom.Value;
            var toDate = dtpTrendTo.Value;

            var collectionData = invoices
                .Where(i => i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate)
                .GroupBy(i => new { Year = i.InvoiceDate.Year, Month = i.InvoiceDate.Month })
                .Select(g => new
                {
                    Period = $"{g.Key.Year}/{g.Key.Month:00}",
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    PaidAmount = g.Sum(i => i.PaidAmount),
                    CollectionRate = g.Sum(i => i.TotalAmount) > 0 ? (g.Sum(i => i.PaidAmount) / g.Sum(i => i.TotalAmount)) * 100 : 0
                })
                .OrderBy(g => g.Period)
                .ToList();

            int y = 50;

            foreach (var data in collectionData)
            {
                // Period label
                var periodLabel = new Label();
                periodLabel.Text = data.Period;
                periodLabel.Location = new Point(20, y);
                periodLabel.Size = new Size(80, 25);
                periodLabel.Font = new Font("Tahoma", 9F);
                collectionPanel.Controls.Add(periodLabel);

                // Collection rate bar
                var rateBar = new ProgressBar();
                rateBar.Location = new Point(110, y + 2);
                rateBar.Size = new Size(300, 20);
                rateBar.Maximum = 100;
                rateBar.Value = (int)Math.Min(data.CollectionRate, 100);
                collectionPanel.Controls.Add(rateBar);

                // Rate label
                var rateLabel = new Label();
                rateLabel.Text = $"{data.CollectionRate:N1}%";
                rateLabel.Location = new Point(420, y);
                rateLabel.Size = new Size(80, 25);
                rateLabel.Font = new Font("Tahoma", 9F);
                collectionPanel.Controls.Add(rateLabel);

                y += 30;
            }

            collectionPanel.Controls.Add(titleLabel);
            pnlTrendCharts.Controls.Add(collectionPanel);
        }

        private void CreateSeasonalityChart()
        {
            var seasonalPanel = new Panel();
            seasonalPanel.Size = new Size(600, 300);
            seasonalPanel.Location = new Point(10, 320);
            seasonalPanel.BorderStyle = BorderStyle.FixedSingle;
            seasonalPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "التحليل الموسمي";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var seasonalData = invoices
                .GroupBy(i => i.InvoiceDate.Month)
                .Select(g => new
                {
                    Month = g.Key,
                    MonthName = GetMonthName(g.Key),
                    AverageAmount = g.Average(i => i.TotalAmount),
                    Count = g.Count()
                })
                .OrderBy(g => g.Month)
                .ToList();

            int y = 50;
            var maxAmount = seasonalData.Any() ? seasonalData.Max(s => s.AverageAmount) : 1;

            foreach (var data in seasonalData)
            {
                // Month label
                var monthLabel = new Label();
                monthLabel.Text = data.MonthName;
                monthLabel.Location = new Point(20, y);
                monthLabel.Size = new Size(80, 25);
                monthLabel.Font = new Font("Tahoma", 9F);
                seasonalPanel.Controls.Add(monthLabel);

                // Amount bar
                var amountBar = new ProgressBar();
                amountBar.Location = new Point(110, y + 2);
                amountBar.Size = new Size(300, 20);
                amountBar.Maximum = 100;
                amountBar.Value = maxAmount > 0 ? (int)((data.AverageAmount / maxAmount) * 100) : 0;
                seasonalPanel.Controls.Add(amountBar);

                // Amount label
                var amountLabel = new Label();
                amountLabel.Text = $"{data.Count} فاتورة - {data.AverageAmount:N2} ريال";
                amountLabel.Location = new Point(420, y);
                amountLabel.Size = new Size(170, 25);
                amountLabel.Font = new Font("Tahoma", 9F);
                seasonalPanel.Controls.Add(amountLabel);

                y += 20;
            }

            seasonalPanel.Controls.Add(titleLabel);
            pnlTrendCharts.Controls.Add(seasonalPanel);
        }

        private void CreateForecastChart()
        {
            var forecastPanel = new Panel();
            forecastPanel.Size = new Size(600, 300);
            forecastPanel.Location = new Point(620, 320);
            forecastPanel.BorderStyle = BorderStyle.FixedSingle;
            forecastPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "التوقعات المستقبلية";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            // Simple forecast based on last 6 months trend
            var last6Months = invoices
                .Where(i => i.InvoiceDate >= DateTime.Today.AddMonths(-6))
                .GroupBy(i => new { Year = i.InvoiceDate.Year, Month = i.InvoiceDate.Month })
                .Select(g => new
                {
                    Period = new DateTime(g.Key.Year, g.Key.Month, 1),
                    Amount = g.Sum(i => i.TotalAmount)
                })
                .OrderBy(g => g.Period)
                .ToList();

            if (last6Months.Count >= 2)
            {
                var avgGrowth = 0m;
                for (int i = 1; i < last6Months.Count; i++)
                {
                    if (last6Months[i - 1].Amount > 0)
                    {
                        avgGrowth += (last6Months[i].Amount - last6Months[i - 1].Amount) / last6Months[i - 1].Amount;
                    }
                }
                avgGrowth = avgGrowth / (last6Months.Count - 1);

                var lastAmount = last6Months.Last().Amount;
                var forecastData = new List<dynamic>();

                for (int i = 1; i <= 6; i++)
                {
                    var forecastAmount = lastAmount * (1 + avgGrowth * i);
                    var forecastPeriod = DateTime.Today.AddMonths(i);
                    forecastData.Add(new
                    {
                        Period = $"{forecastPeriod.Year}/{forecastPeriod.Month:00}",
                        Amount = forecastAmount
                    });
                }

                int y = 50;
                var maxAmount = forecastData.Max(f => f.Amount);

                foreach (var data in forecastData)
                {
                    // Period label
                    var periodLabel = new Label();
                    periodLabel.Text = data.Period;
                    periodLabel.Location = new Point(20, y);
                    periodLabel.Size = new Size(80, 25);
                    periodLabel.Font = new Font("Tahoma", 9F);
                    forecastPanel.Controls.Add(periodLabel);

                    // Forecast bar
                    var forecastBar = new ProgressBar();
                    forecastBar.Location = new Point(110, y + 2);
                    forecastBar.Size = new Size(300, 20);
                    forecastBar.Maximum = 100;
                    forecastBar.Value = maxAmount > 0 ? (int)((data.Amount / maxAmount) * 100) : 0;
                    forecastBar.ForeColor = Color.Orange;
                    forecastPanel.Controls.Add(forecastBar);

                    // Amount label
                    var amountLabel = new Label();
                    amountLabel.Text = $"متوقع: {data.Amount:N2} ريال";
                    amountLabel.Location = new Point(420, y);
                    amountLabel.Size = new Size(170, 25);
                    amountLabel.Font = new Font("Tahoma", 9F);
                    amountLabel.ForeColor = Color.Orange;
                    forecastPanel.Controls.Add(amountLabel);

                    y += 30;
                }
            }
            else
            {
                var noDataLabel = new Label();
                noDataLabel.Text = "لا توجد بيانات كافية للتوقع";
                noDataLabel.Location = new Point(200, 100);
                noDataLabel.Size = new Size(200, 30);
                noDataLabel.Font = new Font("Tahoma", 12F);
                noDataLabel.TextAlign = ContentAlignment.MiddleCenter;
                forecastPanel.Controls.Add(noDataLabel);
            }

            forecastPanel.Controls.Add(titleLabel);
            pnlTrendCharts.Controls.Add(forecastPanel);
        }

        private List<dynamic> GetTrendData(DateTime fromDate, DateTime toDate, string period)
        {
            switch (period)
            {
                case "يومي":
                    return invoices
                        .Where(i => i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate)
                        .GroupBy(i => i.InvoiceDate.Date)
                        .Select(g => new
                        {
                            Period = g.Key.ToString("yyyy/MM/dd"),
                            Amount = g.Sum(i => i.TotalAmount),
                            Count = g.Count()
                        })
                        .OrderBy(g => g.Period)
                        .Cast<dynamic>()
                        .ToList();

                case "أسبوعي":
                    return invoices
                        .Where(i => i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate)
                        .GroupBy(i => GetWeekOfYear(i.InvoiceDate))
                        .Select(g => new
                        {
                            Period = $"أسبوع {g.Key}",
                            Amount = g.Sum(i => i.TotalAmount),
                            Count = g.Count()
                        })
                        .OrderBy(g => g.Period)
                        .Cast<dynamic>()
                        .ToList();

                case "ربع سنوي":
                    return invoices
                        .Where(i => i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate)
                        .GroupBy(i => new { Year = i.InvoiceDate.Year, Quarter = (i.InvoiceDate.Month - 1) / 3 + 1 })
                        .Select(g => new
                        {
                            Period = $"ربع {g.Key.Quarter} - {g.Key.Year}",
                            Amount = g.Sum(i => i.TotalAmount),
                            Count = g.Count()
                        })
                        .OrderBy(g => g.Period)
                        .Cast<dynamic>()
                        .ToList();

                case "سنوي":
                    return invoices
                        .Where(i => i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate)
                        .GroupBy(i => i.InvoiceDate.Year)
                        .Select(g => new
                        {
                            Period = g.Key.ToString(),
                            Amount = g.Sum(i => i.TotalAmount),
                            Count = g.Count()
                        })
                        .OrderBy(g => g.Period)
                        .Cast<dynamic>()
                        .ToList();

                default: // شهري
                    return invoices
                        .Where(i => i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate)
                        .GroupBy(i => new { Year = i.InvoiceDate.Year, Month = i.InvoiceDate.Month })
                        .Select(g => new
                        {
                            Period = $"{g.Key.Year}/{g.Key.Month:00}",
                            Amount = g.Sum(i => i.TotalAmount),
                            Count = g.Count()
                        })
                        .OrderBy(g => g.Period)
                        .Cast<dynamic>()
                        .ToList();
            }
        }

        private int GetWeekOfYear(DateTime date)
        {
            var culture = System.Globalization.CultureInfo.CurrentCulture;
            var calendar = culture.Calendar;
            return calendar.GetWeekOfYear(date, culture.DateTimeFormat.CalendarWeekRule, culture.DateTimeFormat.FirstDayOfWeek);
        }

        private string GetMonthName(int month)
        {
            string[] monthNames = {
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            };
            return month >= 1 && month <= 12 ? monthNames[month - 1] : "غير محدد";
        }

        private void CreateCustomerSegmentationChart()
        {
            var segmentPanel = new Panel();
            segmentPanel.Size = new Size(600, 300);
            segmentPanel.Location = new Point(10, 10);
            segmentPanel.BorderStyle = BorderStyle.FixedSingle;
            segmentPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "تصنيف العملاء";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var customerSegments = invoices
                .GroupBy(i => i.CustomerId)
                .Select(g => new
                {
                    CustomerId = g.Key,
                    CustomerName = customers.FirstOrDefault(c => c.Id == g.Key)?.CustomerName ?? "غير محدد",
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    Segment = GetCustomerSegment(g.Sum(i => i.TotalAmount))
                })
                .GroupBy(c => c.Segment)
                .Select(g => new
                {
                    Segment = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(c => c.TotalAmount)
                })
                .OrderByDescending(g => g.TotalAmount)
                .ToList();

            int y = 50;
            var maxAmount = customerSegments.Any() ? customerSegments.Max(s => s.TotalAmount) : 1;

            foreach (var segment in customerSegments)
            {
                // Segment label
                var segmentLabel = new Label();
                segmentLabel.Text = segment.Segment;
                segmentLabel.Location = new Point(20, y);
                segmentLabel.Size = new Size(100, 25);
                segmentLabel.Font = new Font("Tahoma", 10F, FontStyle.Bold);
                segmentPanel.Controls.Add(segmentLabel);

                // Amount bar
                var amountBar = new ProgressBar();
                amountBar.Location = new Point(130, y + 2);
                amountBar.Size = new Size(300, 20);
                amountBar.Maximum = 100;
                amountBar.Value = maxAmount > 0 ? (int)((segment.TotalAmount / maxAmount) * 100) : 0;
                segmentPanel.Controls.Add(amountBar);

                // Details label
                var detailsLabel = new Label();
                detailsLabel.Text = $"{segment.Count} عميل - {segment.TotalAmount:N2} ريال";
                detailsLabel.Location = new Point(440, y);
                detailsLabel.Size = new Size(150, 25);
                detailsLabel.Font = new Font("Tahoma", 9F);
                segmentPanel.Controls.Add(detailsLabel);

                y += 35;
            }

            segmentPanel.Controls.Add(titleLabel);
            pnlCustomerAnalysis.Controls.Add(segmentPanel);
        }

        private void CreateCustomerRiskAnalysisChart()
        {
            var riskPanel = new Panel();
            riskPanel.Size = new Size(600, 300);
            riskPanel.Location = new Point(620, 10);
            riskPanel.BorderStyle = BorderStyle.FixedSingle;
            riskPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "تحليل مخاطر العملاء";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var riskAnalysis = invoices
                .GroupBy(i => i.CustomerId)
                .Select(g => new
                {
                    CustomerId = g.Key,
                    CustomerName = customers.FirstOrDefault(c => c.Id == g.Key)?.CustomerName ?? "غير محدد",
                    OutstandingAmount = g.Sum(i => i.TotalAmount - i.PaidAmount),
                    CollectionRate = g.Sum(i => i.TotalAmount) > 0 ? (g.Sum(i => i.PaidAmount) / g.Sum(i => i.TotalAmount)) * 100 : 0,
                    RiskLevel = GetRiskLevel(g.Sum(i => i.TotalAmount - i.PaidAmount), g.Sum(i => i.TotalAmount) > 0 ? (g.Sum(i => i.PaidAmount) / g.Sum(i => i.TotalAmount)) * 100 : 0)
                })
                .Where(c => c.OutstandingAmount > 0)
                .GroupBy(c => c.RiskLevel)
                .Select(g => new
                {
                    RiskLevel = g.Key,
                    Count = g.Count(),
                    TotalOutstanding = g.Sum(c => c.OutstandingAmount)
                })
                .OrderBy(g => GetRiskOrder(g.RiskLevel))
                .ToList();

            int y = 50;
            var maxAmount = riskAnalysis.Any() ? riskAnalysis.Max(r => r.TotalOutstanding) : 1;

            foreach (var risk in riskAnalysis)
            {
                // Risk level label
                var riskLabel = new Label();
                riskLabel.Text = risk.RiskLevel;
                riskLabel.Location = new Point(20, y);
                riskLabel.Size = new Size(100, 25);
                riskLabel.Font = new Font("Tahoma", 10F, FontStyle.Bold);
                riskLabel.ForeColor = GetRiskColor(risk.RiskLevel);
                riskPanel.Controls.Add(riskLabel);

                // Outstanding bar
                var outstandingBar = new ProgressBar();
                outstandingBar.Location = new Point(130, y + 2);
                outstandingBar.Size = new Size(300, 20);
                outstandingBar.Maximum = 100;
                outstandingBar.Value = maxAmount > 0 ? (int)((risk.TotalOutstanding / maxAmount) * 100) : 0;
                riskPanel.Controls.Add(outstandingBar);

                // Details label
                var detailsLabel = new Label();
                detailsLabel.Text = $"{risk.Count} عميل - {risk.TotalOutstanding:N2} ريال";
                detailsLabel.Location = new Point(440, y);
                detailsLabel.Size = new Size(150, 25);
                detailsLabel.Font = new Font("Tahoma", 9F);
                riskPanel.Controls.Add(detailsLabel);

                y += 35;
            }

            riskPanel.Controls.Add(titleLabel);
            pnlCustomerAnalysis.Controls.Add(riskPanel);
        }

        private void CreatePerformanceComparisonChart()
        {
            var comparisonPanel = new Panel();
            comparisonPanel.Size = new Size(600, 250);
            comparisonPanel.Location = new Point(10, 10);
            comparisonPanel.BorderStyle = BorderStyle.FixedSingle;
            comparisonPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "مقارنة الأداء (الشهر الحالي مقابل السابق)";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            comparisonPanel.Controls.Add(titleLabel);
            pnlPerformanceMetrics.Controls.Add(comparisonPanel);
        }

        private void CreateGoalsAchievementChart()
        {
            var goalsPanel = new Panel();
            goalsPanel.Size = new Size(600, 250);
            goalsPanel.Location = new Point(620, 10);
            goalsPanel.BorderStyle = BorderStyle.FixedSingle;
            goalsPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "إنجاز الأهداف";
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            goalsPanel.Controls.Add(titleLabel);
            pnlPerformanceMetrics.Controls.Add(goalsPanel);
        }

        private string GetCustomerSegment(decimal totalAmount)
        {
            if (totalAmount >= 100000) return "VIP";
            if (totalAmount >= 50000) return "ذهبي";
            if (totalAmount >= 20000) return "فضي";
            if (totalAmount >= 5000) return "عادي";
            return "جديد";
        }

        private string GetRiskLevel(decimal outstandingAmount, decimal collectionRate)
        {
            if (outstandingAmount > 50000 && collectionRate < 50) return "مخاطر عالية";
            if (outstandingAmount > 20000 && collectionRate < 70) return "مخاطر متوسطة";
            if (outstandingAmount > 5000 && collectionRate < 85) return "مخاطر منخفضة";
            return "آمن";
        }

        private int GetRiskOrder(string riskLevel)
        {
            switch (riskLevel)
            {
                case "مخاطر عالية": return 1;
                case "مخاطر متوسطة": return 2;
                case "مخاطر منخفضة": return 3;
                case "آمن": return 4;
                default: return 5;
            }
        }

        private Color GetRiskColor(string riskLevel)
        {
            switch (riskLevel)
            {
                case "مخاطر عالية": return Color.Red;
                case "مخاطر متوسطة": return Color.Orange;
                case "مخاطر منخفضة": return Color.Yellow;
                case "آمن": return Color.Green;
                default: return Color.Gray;
            }
        }
        #endregion
    }
}