﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup useLegacyV2RuntimeActivationPolicy="true">
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>

    <!-- إعداد لدعم Mixed Mode Assemblies -->
    <runtime>
        <loadFromRemoteSources enabled="true" />
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <probing privatePath="x86;x64" />
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-9.0.0.6" newVersion="9.0.0.6" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
    <connectionStrings>
        <add name="DefaultConnection" connectionString="Data Source=database.db;Version=3;" providerName="System.Data.SQLite" />
    </connectionStrings>
    <appSettings>
        <add key="EncryptionKey" value="YourSecretEncryptionKey123!" />
        <add key="SessionTimeout" value="30" />
        <add key="EnableAI" value="true" />
    </appSettings>
</configuration>
