<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup useLegacyV2RuntimeActivationPolicy="true">
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>

    <!-- إعداد لدعم Mixed Mode Assemblies -->
    <runtime>
        <loadFromRemoteSources enabled="true"/>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <probing privatePath="x86;x64"/>
        </assemblyBinding>
    </runtime>
    <connectionStrings>
        <add name="DefaultConnection" connectionString="Data Source=database.db;Version=3;" providerName="System.Data.SQLite" />
    </connectionStrings>
    <appSettings>
        <add key="EncryptionKey" value="YourSecretEncryptionKey123!" />
        <add key="SessionTimeout" value="30" />
        <add key="EnableAI" value="true" />
    </appSettings>
</configuration>
