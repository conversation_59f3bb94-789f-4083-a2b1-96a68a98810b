using System;
using System.Configuration;
using System.Data.SQLite;
using System.IO;
using Dapper;
using AccountingSystem.Utils;

namespace AccountingSystem.Data
{
    /// <summary>
    /// مساعد قاعدة البيانات
    /// </summary>
    public static class DatabaseHelper
    {
        private static string _connectionString;

        static DatabaseHelper()
        {
            try
            {
                // محاولة الحصول على سلسلة الاتصال من App.config أولاً
                var connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;

                if (!string.IsNullOrEmpty(connectionString))
                {
                    _connectionString = connectionString;
                }
                else
                {
                    // استخدام ConfigHelper كبديل
                    _connectionString = ConfigHelper.GetConnectionString();
                }
            }
            catch (Exception)
            {
                // في حالة الخطأ، استخدم سلسلة اتصال افتراضية
                _connectionString = "Data Source=database.db;Version=3;";
            }
        }

        /// <summary>
        /// الحصول على اتصال قاعدة البيانات
        /// </summary>
        public static SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(_connectionString);
        }

        /// <summary>
        /// تهيئة قاعدة البيانات وإنشاء الجداول
        /// </summary>
        public static void InitializeDatabase()
        {
            try
            {
                // إنشاء ملف قاعدة البيانات إذا لم يكن موجوداً
                string dbPath = "database.db";
                if (!File.Exists(dbPath))
                {
                    SQLiteConnection.CreateFile(dbPath);
                }

                using (var connection = GetConnection())
                {
                    connection.Open();

                    // إنشاء الجداول
                    CreateTables(connection);

                    // إدراج البيانات الأولية
                    InsertInitialData(connection);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء الجداول
        /// </summary>
        private static void CreateTables(SQLiteConnection connection)
        {
            // جدول المستخدمين
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT NOT NULL UNIQUE,
                    PasswordHash TEXT NOT NULL,
                    FullName TEXT NOT NULL,
                    Email TEXT,
                    Role TEXT NOT NULL,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedDate TEXT NOT NULL,
                    LastLoginDate TEXT,
                    CreatedBy TEXT,
                    ModifiedDate TEXT,
                    ModifiedBy TEXT
                )");

            // جدول الحسابات
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS Accounts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    AccountCode TEXT NOT NULL UNIQUE,
                    AccountName TEXT NOT NULL,
                    AccountNameEnglish TEXT,
                    ParentAccountId INTEGER,
                    AccountType TEXT NOT NULL,
                    Level INTEGER NOT NULL,
                    IsParent INTEGER NOT NULL DEFAULT 0,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    Balance REAL NOT NULL DEFAULT 0,
                    DebitCredit TEXT NOT NULL,
                    Description TEXT,
                    CreatedDate TEXT NOT NULL,
                    CreatedBy TEXT,
                    ModifiedDate TEXT,
                    ModifiedBy TEXT,
                    FOREIGN KEY (ParentAccountId) REFERENCES Accounts(Id)
                )");

            // جدول القيود اليومية
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS JournalEntries (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    EntryNumber TEXT NOT NULL UNIQUE,
                    EntryDate TEXT NOT NULL,
                    Description TEXT,
                    Reference TEXT,
                    TotalDebit REAL NOT NULL DEFAULT 0,
                    TotalCredit REAL NOT NULL DEFAULT 0,
                    Status TEXT NOT NULL DEFAULT 'مسودة',
                    IsPosted INTEGER NOT NULL DEFAULT 0,
                    PostedDate TEXT,
                    PostedBy TEXT,
                    CreatedDate TEXT NOT NULL,
                    CreatedBy TEXT,
                    ModifiedDate TEXT,
                    ModifiedBy TEXT
                )");

            // جدول تفاصيل القيود اليومية
            connection.Execute(@"
                CREATE TABLE IF NOT EXISTS JournalEntryDetails (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    JournalEntryId INTEGER NOT NULL,
                    AccountId INTEGER NOT NULL,
                    DebitAmount REAL NOT NULL DEFAULT 0,
                    CreditAmount REAL NOT NULL DEFAULT 0,
                    Description TEXT,
                    Reference TEXT,
                    LineNumber INTEGER NOT NULL,
                    FOREIGN KEY (JournalEntryId) REFERENCES JournalEntries(Id),
                    FOREIGN KEY (AccountId) REFERENCES Accounts(Id)
                )");
        }

        /// <summary>
        /// إدراج البيانات الأولية
        /// </summary>
        private static void InsertInitialData(SQLiteConnection connection)
        {
            // التحقق من وجود مستخدم افتراضي
            var userCount = connection.QuerySingle<int>("SELECT COUNT(*) FROM Users");
            if (userCount == 0)
            {
                // إنشاء مستخدم افتراضي
                var defaultPassword = SecurityHelper.HashPassword("admin123");
                connection.Execute(@"
                    INSERT INTO Users (Username, PasswordHash, FullName, Role, CreatedDate, CreatedBy)
                    VALUES (@Username, @PasswordHash, @FullName, @Role, @CreatedDate, @CreatedBy)",
                    new
                    {
                        Username = "admin",
                        PasswordHash = defaultPassword,
                        FullName = "مدير النظام",
                        Role = "Admin",
                        CreatedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        CreatedBy = "System"
                    });
            }

            // التحقق من وجود حسابات افتراضية
            var accountCount = connection.QuerySingle<int>("SELECT COUNT(*) FROM Accounts");
            if (accountCount == 0)
            {
                CreateDefaultAccounts(connection);
            }
        }

        /// <summary>
        /// إنشاء الحسابات الافتراضية
        /// </summary>
        private static void CreateDefaultAccounts(SQLiteConnection connection)
        {
            var createdDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            // الحسابات الرئيسية
            var accounts = new[]
            {
                new { Code = "1", Name = "الأصول", Type = "أصول", Level = 1, IsParent = 1, DebitCredit = "مدين" },
                new { Code = "2", Name = "الخصوم", Type = "خصوم", Level = 1, IsParent = 1, DebitCredit = "دائن" },
                new { Code = "3", Name = "حقوق الملكية", Type = "حقوق ملكية", Level = 1, IsParent = 1, DebitCredit = "دائن" },
                new { Code = "4", Name = "الإيرادات", Type = "إيرادات", Level = 1, IsParent = 1, DebitCredit = "دائن" },
                new { Code = "5", Name = "المصروفات", Type = "مصروفات", Level = 1, IsParent = 1, DebitCredit = "مدين" }
            };

            foreach (var account in accounts)
            {
                connection.Execute(@"
                    INSERT INTO Accounts (AccountCode, AccountName, AccountType, Level, IsParent, DebitCredit, CreatedDate, CreatedBy)
                    VALUES (@Code, @Name, @Type, @Level, @IsParent, @DebitCredit, @CreatedDate, @CreatedBy)",
                    new
                    {
                        Code = account.Code,
                        Name = account.Name,
                        Type = account.Type,
                        Level = account.Level,
                        IsParent = account.IsParent,
                        DebitCredit = account.DebitCredit,
                        CreatedDate = createdDate,
                        CreatedBy = "System"
                    });
            }
        }
    }
}
