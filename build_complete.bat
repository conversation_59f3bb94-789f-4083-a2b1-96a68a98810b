@echo off
chcp 65001 > nul
echo ========================================
echo بناء النظام الكامل مع جميع الميزات
echo ========================================
echo.

echo هذا البناء يتضمن جميع الميزات المكتملة:
echo • إدارة الحسابات والقيود اليومية
echo • إدارة العملاء والمنتجات
echo • ميزان المراجعة
echo • النسخ الاحتياطي
echo • ميزات الذكاء الاصطناعي الأساسية
echo.

echo التحقق من وجود مترجم C#...
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo لم يتم العثور على مترجم C#
    echo.
    echo الحلول المقترحة:
    echo 1. تشغيل Developer Command Prompt for Visual Studio
    echo 2. تثبيت .NET Framework SDK
    echo 3. إضافة مسار مترجم C# إلى متغير PATH
    echo.
    pause
    exit /b 1
)

echo ✓ تم العثور على مترجم C#

echo.
echo التحقق من وجود الملفات المطلوبة...

set missing_files=0

rem فحص الملفات الأساسية
set required_files=Program.cs App.config

for %%f in (%required_files%) do (
    if not exist "%%f" (
        echo ✗ %%f مفقود
        set /a missing_files+=1
    ) else (
        echo ✓ %%f موجود
    )
)

rem فحص المجلدات
set required_folders=Models Data Services Utils Forms Properties

for %%d in (%required_folders%) do (
    if not exist "%%d" (
        echo ✗ مجلد %%d مفقود
        set /a missing_files+=1
    ) else (
        echo ✓ مجلد %%d موجود
    )
)

if %missing_files% GTR 0 (
    echo.
    echo خطأ: يوجد %missing_files% ملف/مجلد مفقود
    echo يرجى التأكد من وجود جميع ملفات المشروع
    pause
    exit /b 1
)

echo.
echo إنشاء قائمة بملفات المصدر...

echo Program.cs > sources_complete.txt
echo Properties\AssemblyInfo.cs >> sources_complete.txt

rem إضافة النماذج (Models)
echo Models\User.cs >> sources_complete.txt
echo Models\Account.cs >> sources_complete.txt
echo Models\JournalEntry.cs >> sources_complete.txt
echo Models\Customer.cs >> sources_complete.txt
echo Models\Product.cs >> sources_complete.txt
echo Models\Receipt.cs >> sources_complete.txt
echo Models\Payment.cs >> sources_complete.txt
echo Models\Invoice.cs >> sources_complete.txt

rem إضافة البيانات (Data)
echo Data\SimpleDatabaseHelper.cs >> sources_complete.txt

rem إضافة الخدمات (Services)
echo Services\SimpleAuthenticationService.cs >> sources_complete.txt
echo Services\AccountService.cs >> sources_complete.txt
echo Services\JournalService.cs >> sources_complete.txt
echo Services\AIAnalysisService.cs >> sources_complete.txt

rem إضافة الأدوات (Utils)
echo Utils\SecurityHelper.cs >> sources_complete.txt
echo Utils\ReportGenerator.cs >> sources_complete.txt
echo Utils\BackupHelper.cs >> sources_complete.txt
echo Utils\ConfigHelper.cs >> sources_complete.txt

rem إضافة النماذج (Forms)
echo Forms\LoginForm.cs >> sources_complete.txt
echo Forms\MainForm.cs >> sources_complete.txt
echo Forms\AccountsTreeForm.cs >> sources_complete.txt
echo Forms\AddEditAccountForm.cs >> sources_complete.txt
echo Forms\JournalEntryForm.cs >> sources_complete.txt
echo Forms\JournalEntriesListForm.cs >> sources_complete.txt
echo Forms\TrialBalanceForm.cs >> sources_complete.txt
echo Forms\CustomersForm.cs >> sources_complete.txt
echo Forms\AddEditCustomerForm.cs >> sources_complete.txt
echo Forms\ProductsForm.cs >> sources_complete.txt
echo Forms\AddEditProductForm.cs >> sources_complete.txt
echo Forms\ReceiptsForm.cs >> sources_complete.txt
echo Forms\AddEditReceiptForm.cs >> sources_complete.txt
echo Forms\ReceiptsReportForm.cs >> sources_complete.txt
echo Forms\PaymentsForm.cs >> sources_complete.txt
echo Forms\AddEditPaymentForm.cs >> sources_complete.txt
echo Forms\PaymentsReportForm.cs >> sources_complete.txt
echo Forms\CustomerStatementForm.cs >> sources_complete.txt
echo Forms\CustomerPaymentForm.cs >> sources_complete.txt

rem إضافة ملفات Properties
echo Properties\Resources.Designer.cs >> sources_complete.txt
echo Properties\Settings.Designer.cs >> sources_complete.txt

echo.
echo بناء المشروع الكامل...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:System.Configuration.dll ^
    /out:AccountingSystemComplete.exe ^
    /filelist:sources_complete.txt

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء المشروع بنجاح!
    echo.

    echo نسخ ملفات الإعداد...
    if exist "App.config" copy "App.config" "AccountingSystemComplete.exe.config" >nul
    if exist "system_config.json" echo ✓ ملف الإعداد موجود

    echo.
    echo ========================================
    echo تم إكمال البناء بنجاح!
    echo ========================================
    echo.
    echo تم إنشاء: AccountingSystemComplete.exe
    echo.

    set /p run="هل تريد تشغيل النظام الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام...
        start "" "AccountingSystemComplete.exe"
        echo.
        echo تم تشغيل النظام بنجاح!
    )

    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo الميزات المتوفرة:
    echo • إدارة الحسابات والقيود اليومية
    echo • إدارة العملاء والمنتجات
    echo • سندات القبض والصرف مع التقارير
    echo • كشف حساب العميل التفصيلي
    echo • نظام تسجيل دفعات العملاء
    echo • ميزان المراجعة
    echo • النسخ الاحتياطي
    echo • تحليل البيانات بالذكاء الاصطناعي
    echo.
    echo ملاحظات:
    echo • يستخدم ملفات نصية لحفظ البيانات
    echo • يتجنب مشكلة Mixed Mode Assembly
    echo • جميع الميزات الأساسية متوفرة
    echo • البيانات تحفظ في مجلد SimpleData

    del sources_complete.txt >nul 2>&1

) else (
    echo.
    echo ✗ فشل في بناء المشروع
    echo.
    echo الأخطاء المحتملة:
    echo 1. أخطاء في الكود
    echo 2. مراجع مفقودة
    echo 3. مشاكل في مترجم C#
    echo.
    echo للحصول على بناء مبسط أكثر، جرب:
    echo build_simple.bat
    echo.
    echo أو للبناء بدون SQLite:
    echo build_without_sqlite.bat

    del sources_complete.txt >nul 2>&1
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
