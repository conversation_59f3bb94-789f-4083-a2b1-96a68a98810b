using System;
using System.Security.Cryptography;
using System.Text;

namespace AccountingSystem.Utils
{
    /// <summary>
    /// مساعد الأمان والتشفير
    /// </summary>
    public static class SecurityHelper
    {
        /// <summary>
        /// تشفير كلمة المرور باستخدام SHA256
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>كلمة المرور المشفرة</returns>
        public static string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentException("كلمة المرور لا يمكن أن تكون فارغة");
            
            using (SHA256 sha256Hash = SHA256.Create())
            {
                // تحويل كلمة المرور إلى bytes
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password + "AccountingSystemSalt"));
                
                // تحويل bytes إلى string
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }
        
        /// <summary>
        /// التحقق من كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور المدخلة</param>
        /// <param name="hashedPassword">كلمة المرور المشفرة المحفوظة</param>
        /// <returns>true إذا كانت كلمة المرور صحيحة</returns>
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            string hashOfInput = HashPassword(password);
            return string.Equals(hashOfInput, hashedPassword, StringComparison.OrdinalIgnoreCase);
        }
        
        /// <summary>
        /// توليد رقم عشوائي للمعرفات
        /// </summary>
        /// <returns>رقم عشوائي</returns>
        public static string GenerateRandomNumber()
        {
            Random random = new Random();
            return random.Next(100000, 999999).ToString();
        }
        
        /// <summary>
        /// توليد رقم مرجعي فريد
        /// </summary>
        /// <param name="prefix">البادئة</param>
        /// <returns>رقم مرجعي فريد</returns>
        public static string GenerateReferenceNumber(string prefix = "REF")
        {
            return $"{prefix}-{DateTime.Now:yyyyMMdd}-{GenerateRandomNumber()}";
        }
        
        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>true إذا كان البريد الإلكتروني صحيحاً</returns>
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;
            
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// تنظيف النص من الأحرف الخطيرة
        /// </summary>
        /// <param name="input">النص المدخل</param>
        /// <returns>النص المنظف</returns>
        public static string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
            
            // إزالة الأحرف الخطيرة
            return input.Replace("'", "''")
                       .Replace("\"", "\"\"")
                       .Replace("<", "&lt;")
                       .Replace(">", "&gt;")
                       .Replace("&", "&amp;");
        }
    }
}
