using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Models;
using IntegratedInvoiceSystem.Services;

namespace IntegratedInvoiceSystem.Forms
{
    /// <summary>
    /// نموذج إدارة السندات والمدفوعات المتكامل
    /// </summary>
    public partial class ReceiptsManagementForm : Form
    {
        #region المتغيرات
        private List<Receipt> receipts;
        private List<Receipt> filteredReceipts;
        private Receipt selectedReceipt;
        #endregion

        #region البناء والتهيئة
        public ReceiptsManagementForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // تهيئة الإعدادات الأساسية
            this.WindowState = FormWindowState.Maximized;
            this.KeyPreview = true;

            // تهيئة التواريخ والقوائم
            dtpDateFrom.Value = DateTime.Now.AddMonths(-1);
            dtpDateTo.Value = DateTime.Now;
            cmbReceiptType.SelectedIndex = 0;
            cmbPaymentMethod.SelectedIndex = 0;

            // تهيئة DataGridView
            SetupDataGridView();
        }

        private void SetupDataGridView()
        {
            dataGridView.AutoGenerateColumns = false;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.ReadOnly = true;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;

            // تنسيق الألوان
            dataGridView.BackgroundColor = Color.White;
            dataGridView.GridColor = Color.FromArgb(189, 195, 199);
            dataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(230, 126, 34);
            dataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dataGridView.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dataGridView.RowHeadersVisible = false;
            dataGridView.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            AddDataGridViewColumns();
        }

        private void AddDataGridViewColumns()
        {
            dataGridView.Columns.Clear();

            // رقم السند
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ReceiptNumber",
                HeaderText = "رقم السند",
                DataPropertyName = "ReceiptNumber",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // تاريخ السند
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ReceiptDate",
                HeaderText = "تاريخ السند",
                DataPropertyName = "ReceiptDate",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "dd/MM/yyyy",
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            });

            // نوع السند
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ReceiptType",
                HeaderText = "نوع السند",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // العميل
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerName",
                HeaderText = "العميل",
                Width = 200
            });

            // رقم الفاتورة
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "InvoiceNumber",
                HeaderText = "رقم الفاتورة",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // المبلغ
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Amount",
                HeaderText = "المبلغ",
                DataPropertyName = "Amount",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "C2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // طريقة الدفع
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PaymentMethod",
                HeaderText = "طريقة الدفع",
                DataPropertyName = "PaymentMethod",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // رقم المرجع
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ReferenceNumber",
                HeaderText = "رقم المرجع",
                DataPropertyName = "ReferenceNumber",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // الحالة
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                DataPropertyName = "Status",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // الملاحظات
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Notes",
                HeaderText = "الملاحظات",
                DataPropertyName = "Notes",
                Width = 200
            });
        }
        #endregion

        #region تحميل البيانات
        private void ReceiptsManagementForm_Load(object sender, EventArgs e)
        {
            LoadReceipts();
        }

        private void LoadReceipts()
        {
            try
            {
                lblStatusText.Text = "جاري تحميل السندات...";

                // تحميل السندات من الخدمة
                receipts = DataService.Receipts ?? new List<Receipt>();

                // تطبيق الفلترة
                ApplyFilters();

                lblStatusText.Text = "تم تحميل البيانات بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل السندات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatusText.Text = "خطأ في تحميل البيانات";
            }
        }

        private void ApplyFilters()
        {
            try
            {
                filteredReceipts = receipts.Where(receipt =>
                {
                    // فلتر النص
                    bool textMatch = string.IsNullOrEmpty(txtSearch.Text) ||
                        receipt.ReceiptNumber.Contains(txtSearch.Text) ||
                        GetCustomerName(receipt.CustomerId).Contains(txtSearch.Text) ||
                        receipt.ReferenceNumber.Contains(txtSearch.Text);

                    // فلتر نوع السند
                    bool typeMatch = cmbReceiptType.SelectedIndex == 0 ||
                        GetReceiptType(receipt) == cmbReceiptType.Text;

                    // فلتر طريقة الدفع
                    bool methodMatch = cmbPaymentMethod.SelectedIndex == 0 ||
                        receipt.PaymentMethod == cmbPaymentMethod.Text;

                    // فلتر التاريخ
                    bool dateMatch = receipt.ReceiptDate.Date >= dtpDateFrom.Value.Date &&
                        receipt.ReceiptDate.Date <= dtpDateTo.Value.Date;

                    return textMatch && typeMatch && methodMatch && dateMatch;
                }).ToList();

                // ربط البيانات
                BindData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BindData()
        {
            try
            {
                var bindingList = filteredReceipts.Select(receipt => new
                {
                    Id = receipt.Id,
                    ReceiptNumber = receipt.ReceiptNumber,
                    ReceiptDate = receipt.ReceiptDate,
                    ReceiptType = GetReceiptType(receipt),
                    CustomerName = GetCustomerName(receipt.CustomerId),
                    InvoiceNumber = GetInvoiceNumber(receipt.InvoiceId),
                    Amount = receipt.Amount,
                    PaymentMethod = receipt.PaymentMethod,
                    ReferenceNumber = receipt.ReferenceNumber,
                    Status = receipt.Status,
                    Notes = receipt.Notes
                }).ToList();

                dataGridView.DataSource = bindingList;

                // تحديث عداد السجلات والمبلغ الإجمالي
                lblRecordCount.Text = $"عدد السجلات: {filteredReceipts.Count}";
                lblTotalAmount.Text = $"إجمالي المبلغ: {filteredReceipts.Sum(r => r.Amount):C2}";

                // تلوين الصفوف حسب الحالة
                ColorizeRows();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ربط البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ColorizeRows()
        {
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                if (row.Cells["Status"].Value != null)
                {
                    string status = row.Cells["Status"].Value.ToString();
                    switch (status)
                    {
                        case "مؤكد":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(220, 255, 220);
                            break;
                        case "معلق":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
                            break;
                        case "ملغي":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 220, 220);
                            break;
                    }
                }
            }
        }

        private string GetCustomerName(int customerId)
        {
            var customer = DataService.GetCustomers().FirstOrDefault(c => c.Id == customerId);
            return customer?.CustomerName ?? "غير محدد";
        }

        private string GetInvoiceNumber(int? invoiceId)
        {
            if (!invoiceId.HasValue) return "-";
            var invoice = DataService.GetInvoices().FirstOrDefault(i => i.Id == invoiceId.Value);
            return invoice?.InvoiceNumber ?? "-";
        }

        private string GetReceiptType(Receipt receipt)
        {
            return receipt.Amount >= 0 ? "سند قبض" : "سند صرف";
        }
        #endregion

        #region معالجات الأحداث - الأزرار
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نموذج إضافة سند جديد", "إضافة سند",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج إضافة السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            EditSelectedReceipt();
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DeleteSelectedReceipt();
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            PrintSelectedReceipt();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadReceipts();
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        #endregion

        #region معالجات الأحداث - البحث والفلترة
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CmbReceiptType_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CmbPaymentMethod_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void DtpDateFrom_ValueChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void DtpDateTo_ValueChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void BtnClearSearch_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbReceiptType.SelectedIndex = 0;
            cmbPaymentMethod.SelectedIndex = 0;
            dtpDateFrom.Value = DateTime.Now.AddMonths(-1);
            dtpDateTo.Value = DateTime.Now;
            ApplyFilters();
        }
        #endregion

        #region معالجات الأحداث - DataGridView
        private void DataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                var selectedRow = dataGridView.SelectedRows[0];
                if (selectedRow.Cells["Id"].Value != null)
                {
                    int receiptId = Convert.ToInt32(selectedRow.Cells["Id"].Value);
                    selectedReceipt = receipts.FirstOrDefault(r => r.Id == receiptId);

                    // تحديث حالة الأزرار
                    UpdateButtonStates();
                }
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedReceipt();
            }
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = selectedReceipt != null;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection && selectedReceipt.Status != "مؤكد";
            btnPrint.Enabled = hasSelection;

            // تحديث القائمة المنسدلة
            menuEdit.Enabled = hasSelection;
            menuDelete.Enabled = hasSelection && selectedReceipt.Status != "مؤكد";
            menuPrint.Enabled = hasSelection;
            menuDuplicate.Enabled = hasSelection;
            menuViewInvoice.Enabled = hasSelection && selectedReceipt.InvoiceId.HasValue;
        }
        #endregion

        #region معالجات الأحداث - القائمة المنسدلة
        private void MenuEdit_Click(object sender, EventArgs e)
        {
            EditSelectedReceipt();
        }

        private void MenuDelete_Click(object sender, EventArgs e)
        {
            DeleteSelectedReceipt();
        }

        private void MenuPrint_Click(object sender, EventArgs e)
        {
            PrintSelectedReceipt();
        }

        private void MenuDuplicate_Click(object sender, EventArgs e)
        {
            DuplicateSelectedReceipt();
        }

        private void MenuViewInvoice_Click(object sender, EventArgs e)
        {
            ViewRelatedInvoice();
        }
        #endregion

        #region العمليات الأساسية
        private void EditSelectedReceipt()
        {
            if (selectedReceipt == null)
            {
                MessageBox.Show("يرجى اختيار سند للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                MessageBox.Show($"سيتم فتح نموذج تعديل السند رقم {selectedReceipt.ReceiptNumber}", "تعديل سند",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج تعديل السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteSelectedReceipt()
        {
            if (selectedReceipt == null)
            {
                MessageBox.Show("يرجى اختيار سند للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (selectedReceipt.Status == "مؤكد")
            {
                MessageBox.Show("لا يمكن حذف السند المؤكد", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show($"هل أنت متأكد من حذف السند رقم {selectedReceipt.ReceiptNumber}؟",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    DataService.Receipts.Remove(selectedReceipt);
                    LoadReceipts();
                    MessageBox.Show("تم حذف السند بنجاح", "نجح الحذف",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف السند: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void PrintSelectedReceipt()
        {
            if (selectedReceipt == null)
            {
                MessageBox.Show("يرجى اختيار سند للطباعة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                MessageBox.Show($"سيتم طباعة السند رقم {selectedReceipt.ReceiptNumber}", "طباعة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DuplicateSelectedReceipt()
        {
            if (selectedReceipt == null)
            {
                MessageBox.Show("يرجى اختيار سند للنسخ", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                MessageBox.Show($"سيتم نسخ السند رقم {selectedReceipt.ReceiptNumber}", "نسخ سند",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نسخ السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewRelatedInvoice()
        {
            if (selectedReceipt == null || !selectedReceipt.InvoiceId.HasValue)
            {
                MessageBox.Show("لا توجد فاتورة مرتبطة بهذا السند", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var invoice = DataService.GetInvoices().FirstOrDefault(i => i.Id == selectedReceipt.InvoiceId.Value);
                if (invoice != null)
                {
                    MessageBox.Show($"سيتم عرض الفاتورة رقم {invoice.InvoiceNumber}", "عرض الفاتورة",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على الفاتورة المرتبطة", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region معالجات إضافية
        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            switch (keyData)
            {
                case Keys.F1:
                    BtnAdd_Click(null, null);
                    return true;
                case Keys.F2:
                    BtnEdit_Click(null, null);
                    return true;
                case Keys.Delete:
                    BtnDelete_Click(null, null);
                    return true;
                case Keys.F3:
                    BtnPrint_Click(null, null);
                    return true;
                case Keys.F5:
                    BtnRefresh_Click(null, null);
                    return true;
                case Keys.Escape:
                    BtnClose_Click(null, null);
                    return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }
        #endregion
    }
}
