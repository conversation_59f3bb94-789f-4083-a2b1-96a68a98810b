using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Models;
using IntegratedInvoiceSystem.Services;

namespace IntegratedInvoiceSystem.Forms
{
    /// <summary>
    /// نموذج إدارة المنتجات المتكامل
    /// </summary>
    public partial class ProductsManagementForm : Form
    {
        #region المتغيرات
        private List<Product> products;
        private List<Product> filteredProducts;
        private Product selectedProduct;
        #endregion

        #region البناء والتهيئة
        public ProductsManagementForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // تهيئة الإعدادات الأساسية
            this.WindowState = FormWindowState.Maximized;
            this.KeyPreview = true;

            // تهيئة القوائم المنسدلة
            cmbCategory.SelectedIndex = 0;
            cmbStatus.SelectedIndex = 0;

            // تهيئة DataGridView
            SetupDataGridView();
        }

        private void SetupDataGridView()
        {
            dataGridView.AutoGenerateColumns = false;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.ReadOnly = true;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;

            // تنسيق الألوان
            dataGridView.BackgroundColor = Color.White;
            dataGridView.GridColor = Color.FromArgb(189, 195, 199);
            dataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(155, 89, 182);
            dataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dataGridView.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dataGridView.RowHeadersVisible = false;
            dataGridView.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            AddDataGridViewColumns();
        }

        private void AddDataGridViewColumns()
        {
            dataGridView.Columns.Clear();

            // عمود Id مخفي للاستخدام الداخلي
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "Id",
                DataPropertyName = "Id",
                Visible = false
            });

            // كود المنتج
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ProductCode",
                HeaderText = "كود المنتج",
                DataPropertyName = "ProductCode",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // اسم المنتج
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ProductName",
                HeaderText = "اسم المنتج",
                DataPropertyName = "ProductName",
                Width = 200
            });

            // الفئة
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Category",
                HeaderText = "الفئة",
                DataPropertyName = "Category",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // الوحدة
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Unit",
                HeaderText = "الوحدة",
                DataPropertyName = "Unit",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // سعر الشراء
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PurchasePrice",
                HeaderText = "سعر الشراء",
                DataPropertyName = "PurchasePrice",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "C2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // سعر البيع
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SalePrice",
                HeaderText = "سعر البيع",
                DataPropertyName = "SalePrice",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "C2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // المخزون الحالي
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentStock",
                HeaderText = "المخزون الحالي",
                DataPropertyName = "CurrentStock",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "N2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // الحد الأدنى
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MinimumStock",
                HeaderText = "الحد الأدنى",
                DataPropertyName = "MinimumStock",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "N2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // الحالة
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IsActive",
                HeaderText = "الحالة",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // الباركود
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Barcode",
                HeaderText = "الباركود",
                DataPropertyName = "Barcode",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
        }
        #endregion

        #region تحميل البيانات
        private void ProductsManagementForm_Load(object sender, EventArgs e)
        {
            LoadProducts();
        }

        private void LoadProducts()
        {
            try
            {
                lblStatusText.Text = "جاري تحميل المنتجات...";

                // تحميل المنتجات من الخدمة
                products = DataService.GetProducts();

                // تطبيق الفلترة
                ApplyFilters();

                lblStatusText.Text = "تم تحميل البيانات بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatusText.Text = "خطأ في تحميل البيانات";
            }
        }

        private void ApplyFilters()
        {
            try
            {
                filteredProducts = products.Where(product =>
                {
                    // فلتر النص
                    bool textMatch = string.IsNullOrEmpty(txtSearch.Text) ||
                        product.ProductName.Contains(txtSearch.Text) ||
                        product.ProductCode.Contains(txtSearch.Text) ||
                        product.Barcode.Contains(txtSearch.Text);

                    // فلتر الفئة
                    bool categoryMatch = cmbCategory.SelectedIndex == 0 ||
                        product.Category == cmbCategory.Text;

                    // فلتر الحالة
                    bool statusMatch = cmbStatus.SelectedIndex == 0 ||
                        (cmbStatus.Text == "نشط" && product.IsActive) ||
                        (cmbStatus.Text == "غير نشط" && !product.IsActive);

                    // فلتر المخزون المنخفض
                    bool lowStockMatch = !chkLowStock.Checked ||
                        product.CurrentStock <= product.MinimumStock;

                    return textMatch && categoryMatch && statusMatch && lowStockMatch;
                }).ToList();

                // ربط البيانات
                BindData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BindData()
        {
            try
            {
                var bindingList = filteredProducts.Select(product => new
                {
                    Id = product.Id,
                    ProductCode = product.ProductCode,
                    ProductName = product.ProductName,
                    Category = product.Category ?? "أخرى",
                    Unit = product.Unit ?? "قطعة",
                    PurchasePrice = product.PurchasePrice,
                    SalePrice = product.SalePrice,
                    CurrentStock = product.CurrentStock,
                    MinimumStock = product.MinimumStock,
                    IsActive = product.IsActive ? "نشط" : "غير نشط",
                    Barcode = product.Barcode
                }).ToList();

                dataGridView.DataSource = bindingList;

                // تحديث عداد السجلات
                lblRecordCount.Text = $"عدد السجلات: {filteredProducts.Count}";

                // تلوين الصفوف حسب الحالة
                ColorizeRows();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ربط البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ColorizeRows()
        {
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                if (row.Cells["IsActive"].Value != null && row.Cells["CurrentStock"].Value != null && row.Cells["MinimumStock"].Value != null)
                {
                    string status = row.Cells["IsActive"].Value.ToString();
                    decimal currentStock = Convert.ToDecimal(row.Cells["CurrentStock"].Value);
                    decimal minimumStock = Convert.ToDecimal(row.Cells["MinimumStock"].Value);

                    if (status == "غير نشط")
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(100, 100, 100);
                    }
                    else if (currentStock <= minimumStock)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 245, 245);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(200, 50, 50);
                    }
                    else if (currentStock <= minimumStock * 1.5m)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 250, 240);
                    }
                }
            }
        }
        #endregion

        #region معالجات الأحداث - الأزرار
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نموذج إضافة منتج جديد", "إضافة منتج",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // var addForm = new AddEditProductForm();
                // if (addForm.ShowDialog() == DialogResult.OK)
                // {
                //     LoadProducts();
                // }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج إضافة المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            EditSelectedProduct();
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DeleteSelectedProduct();
        }

        private void BtnUpdateStock_Click(object sender, EventArgs e)
        {
            UpdateProductStock();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadProducts();
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        #endregion

        #region معالجات الأحداث - البحث والفلترة
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CmbStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ChkLowStock_CheckedChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void BtnClearSearch_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbCategory.SelectedIndex = 0;
            cmbStatus.SelectedIndex = 0;
            chkLowStock.Checked = false;
            ApplyFilters();
        }
        #endregion

        #region معالجات الأحداث - DataGridView
        private void DataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                var selectedRow = dataGridView.SelectedRows[0];
                if (selectedRow.Cells["Id"].Value != null)
                {
                    int productId = Convert.ToInt32(selectedRow.Cells["Id"].Value);
                    selectedProduct = products.FirstOrDefault(p => p.Id == productId);

                    // تحديث حالة الأزرار
                    UpdateButtonStates();
                }
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedProduct();
            }
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = selectedProduct != null;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
            btnUpdateStock.Enabled = hasSelection;

            // تحديث القائمة المنسدلة
            menuEdit.Enabled = hasSelection;
            menuDelete.Enabled = hasSelection;
            menuUpdateStock.Enabled = hasSelection;
            menuViewHistory.Enabled = hasSelection;
            menuActivate.Enabled = hasSelection && selectedProduct != null && !selectedProduct.IsActive;
            menuDeactivate.Enabled = hasSelection && selectedProduct != null && selectedProduct.IsActive;
        }
        #endregion

        #region معالجات الأحداث - القائمة المنسدلة
        private void MenuEdit_Click(object sender, EventArgs e)
        {
            EditSelectedProduct();
        }

        private void MenuDelete_Click(object sender, EventArgs e)
        {
            DeleteSelectedProduct();
        }

        private void MenuUpdateStock_Click(object sender, EventArgs e)
        {
            UpdateProductStock();
        }

        private void MenuViewHistory_Click(object sender, EventArgs e)
        {
            ViewStockHistory();
        }

        private void MenuActivate_Click(object sender, EventArgs e)
        {
            ToggleProductStatus(true);
        }

        private void MenuDeactivate_Click(object sender, EventArgs e)
        {
            ToggleProductStatus(false);
        }
        #endregion

        #region العمليات الأساسية
        private void EditSelectedProduct()
        {
            if (selectedProduct == null)
            {
                MessageBox.Show("يرجى اختيار منتج للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                MessageBox.Show($"سيتم فتح نموذج تعديل المنتج {selectedProduct.ProductName}", "تعديل منتج",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // var editForm = new AddEditProductForm(selectedProduct);
                // if (editForm.ShowDialog() == DialogResult.OK)
                // {
                //     LoadProducts();
                // }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج تعديل المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteSelectedProduct()
        {
            if (selectedProduct == null)
            {
                MessageBox.Show("يرجى اختيار منتج للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // التحقق من وجود فواتير مرتبطة
            var relatedInvoices = DataService.GetInvoices()
                .Where(i => i.Items.Any(item => item.ProductId == selectedProduct.Id))
                .ToList();

            if (relatedInvoices.Any())
            {
                MessageBox.Show("لا يمكن حذف المنتج لوجود فواتير مرتبطة به", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show($"هل أنت متأكد من حذف المنتج {selectedProduct.ProductName}؟",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    DataService.DeleteProduct(selectedProduct.Id);
                    LoadProducts();
                    MessageBox.Show("تم حذف المنتج بنجاح", "نجح الحذف",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void UpdateProductStock()
        {
            if (selectedProduct == null)
            {
                MessageBox.Show("يرجى اختيار منتج لتحديث المخزون", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                MessageBox.Show($"سيتم فتح نموذج تحديث مخزون المنتج {selectedProduct.ProductName}", "تحديث المخزون",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // var stockForm = new UpdateStockForm(selectedProduct);
                // if (stockForm.ShowDialog() == DialogResult.OK)
                // {
                //     LoadProducts();
                // }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج تحديث المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewStockHistory()
        {
            if (selectedProduct == null)
            {
                MessageBox.Show("يرجى اختيار منتج لعرض تاريخ المخزون", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                MessageBox.Show($"سيتم عرض تاريخ مخزون المنتج {selectedProduct.ProductName}", "تاريخ المخزون",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // var historyForm = new StockHistoryForm(selectedProduct);
                // historyForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تاريخ المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ToggleProductStatus(bool isActive)
        {
            if (selectedProduct == null)
            {
                MessageBox.Show("يرجى اختيار منتج", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string action = isActive ? "تفعيل" : "إلغاء تفعيل";
            var result = MessageBox.Show($"هل تريد {action} المنتج {selectedProduct.ProductName}؟",
                $"تأكيد {action}", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    selectedProduct.IsActive = isActive;
                    DataService.UpdateProduct(selectedProduct);
                    LoadProducts();
                    MessageBox.Show($"تم {action} المنتج بنجاح", $"نجح {action}",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في {action} المنتج: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        #endregion

        #region معالجات إضافية
        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            switch (keyData)
            {
                case Keys.F1:
                    BtnAdd_Click(null, null);
                    return true;
                case Keys.F2:
                    BtnEdit_Click(null, null);
                    return true;
                case Keys.Delete:
                    BtnDelete_Click(null, null);
                    return true;
                case Keys.F3:
                    BtnUpdateStock_Click(null, null);
                    return true;
                case Keys.F5:
                    BtnRefresh_Click(null, null);
                    return true;
                case Keys.Escape:
                    BtnClose_Click(null, null);
                    return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }
        #endregion
    }
}
