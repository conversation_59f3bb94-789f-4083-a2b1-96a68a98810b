using System;

namespace AccountingSystem.Models
{
    /// <summary>
    /// نموذج سند الصرف
    /// </summary>
    public class Payment
    {
        public int Id { get; set; }
        public string PaymentNumber { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentType { get; set; } // رواتب، مصاريف تشغيلية، إيجارات، مشتريات، أخرى
        public string Beneficiary { get; set; } // المستفيد
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; } // نقدي، شيك، تحويل بنكي، بطاقة ائتمان
        public string ReferenceNumber { get; set; }
        public string Description { get; set; }
        public string Status { get; set; } // مسودة، مؤكد، ملغي
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }

        // خصائص إضافية للنظام المحاسبي
        public int? PayerAccountId { get; set; } // الحساب الدافع
        public int? PayeeAccountId { get; set; } // الحساب المستفيد
        public string Currency { get; set; }
        public string CheckNumber { get; set; }
        public DateTime? CheckDate { get; set; }
        public string BankName { get; set; }
        public string Reference { get; set; }
        public bool IsPosted { get; set; }
        public int? JournalEntryId { get; set; }

        // خصائص إضافية للعرض
        public Account PayerAccount { get; set; }
        public Account PayeeAccount { get; set; }
        public JournalEntry JournalEntry { get; set; }
    }
}
