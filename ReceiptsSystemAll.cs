using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace AccountingSystem
{
    // نماذج البيانات
    public class Customer
    {
        public int Id { get; set; }
        public string CustomerCode { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public decimal CurrentBalance { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
    }

    public class Receipt
    {
        public int Id { get; set; }
        public string ReceiptNumber { get; set; }
        public DateTime ReceiptDate { get; set; }
        public int CustomerId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string ReferenceNumber { get; set; }
        public string Notes { get; set; }
        public string Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
    }

    public class Payment
    {
        public int Id { get; set; }
        public string PaymentNumber { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentType { get; set; }
        public string Beneficiary { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string ReferenceNumber { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
    }

    public class Invoice
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public int CustomerId { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
    }

    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public bool IsActive { get; set; }
    }

    // خدمة المصادقة
    public static class AuthService
    {
        public static User CurrentUser { get; private set; }

        public static bool Login(string username, string password)
        {
            if (username == "admin" && password == "admin123")
            {
                CurrentUser = new User
                {
                    Id = 1,
                    Username = "admin",
                    FullName = "مدير النظام",
                    Email = "<EMAIL>",
                    IsActive = true
                };
                return true;
            }
            return false;
        }

        public static void Logout()
        {
            CurrentUser = null;
        }
    }

    // نموذج تسجيل الدخول
    public partial class LoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Label lblUsername;
        private Label lblPassword;

        public LoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.txtUsername = new TextBox();
            this.txtPassword = new TextBox();
            this.btnLogin = new Button();
            this.lblUsername = new Label();
            this.lblPassword = new Label();

            this.SuspendLayout();

            // Form
            this.Text = "تسجيل الدخول - نظام السندات";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;

            // Username Label
            this.lblUsername.Text = "اسم المستخدم:";
            this.lblUsername.Location = new Point(300, 80);
            this.lblUsername.Size = new Size(80, 23);
            this.lblUsername.TextAlign = ContentAlignment.MiddleRight;

            // Username TextBox
            this.txtUsername.Location = new Point(50, 80);
            this.txtUsername.Size = new Size(200, 23);
            this.txtUsername.Text = "admin";

            // Password Label
            this.lblPassword.Text = "كلمة المرور:";
            this.lblPassword.Location = new Point(300, 120);
            this.lblPassword.Size = new Size(80, 23);
            this.lblPassword.TextAlign = ContentAlignment.MiddleRight;

            // Password TextBox
            this.txtPassword.Location = new Point(50, 120);
            this.txtPassword.Size = new Size(200, 23);
            this.txtPassword.UseSystemPasswordChar = true;
            this.txtPassword.Text = "admin123";

            // Login Button
            this.btnLogin.Text = "دخول";
            this.btnLogin.Location = new Point(150, 180);
            this.btnLogin.Size = new Size(100, 35);
            this.btnLogin.BackColor = Color.FromArgb(76, 175, 80);
            this.btnLogin.ForeColor = Color.White;
            this.btnLogin.FlatStyle = FlatStyle.Flat;
            this.btnLogin.Click += BtnLogin_Click;

            // Add controls
            this.Controls.Add(this.lblUsername);
            this.Controls.Add(this.txtUsername);
            this.Controls.Add(this.lblPassword);
            this.Controls.Add(this.txtPassword);
            this.Controls.Add(this.btnLogin);

            this.ResumeLayout(false);

            this.AcceptButton = this.btnLogin;
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (AuthService.Login(txtUsername.Text, txtPassword.Text))
            {
                this.Hide();
                var mainForm = new MainForm();
                mainForm.ShowDialog();
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    // النموذج الرئيسي
    public partial class MainForm : Form
    {
        private MenuStrip menuStrip;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel userLabel;
        private ToolStripStatusLabel dateLabel;

        public MainForm()
        {
            InitializeComponent();
            SetupMenus();
        }

        private void InitializeComponent()
        {
            this.menuStrip = new MenuStrip();
            this.statusStrip = new StatusStrip();
            this.userLabel = new ToolStripStatusLabel();
            this.dateLabel = new ToolStripStatusLabel();

            this.SuspendLayout();

            // Form
            this.Text = "نظام إدارة السندات والمدفوعات";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;

            // Menu Strip
            this.menuStrip.Location = new Point(0, 0);
            this.menuStrip.Size = new Size(1200, 24);
            this.menuStrip.RightToLeft = RightToLeft.Yes;

            // Status Strip
            this.statusStrip.Location = new Point(0, 776);
            this.statusStrip.Size = new Size(1200, 22);
            this.statusStrip.RightToLeft = RightToLeft.Yes;

            // User Label
            this.userLabel.Text = $"المستخدم: {AuthService.CurrentUser?.FullName}";

            // Date Label
            this.dateLabel.Text = $"التاريخ: {DateTime.Now:yyyy/MM/dd HH:mm:ss}";

            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.userLabel,
                this.dateLabel
            });

            // Add controls
            this.MainMenuStrip = this.menuStrip;
            this.Controls.Add(this.menuStrip);
            this.Controls.Add(this.statusStrip);

            this.ResumeLayout(false);
        }

        private void SetupMenus()
        {
            // قائمة الملف
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("تسجيل خروج", null, Logout_Click));
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("خروج", null, Exit_Click));

            // قائمة السندات
            var receiptsMenu = new ToolStripMenuItem("السندات");
            receiptsMenu.DropDownItems.Add(new ToolStripMenuItem("سندات القبض", null, Receipts_Click));
            receiptsMenu.DropDownItems.Add(new ToolStripMenuItem("سندات الصرف", null, Payments_Click));

            // قائمة التقارير
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("كشف حساب العميل", null, CustomerStatement_Click));

            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("حول البرنامج", null, About_Click));

            // إضافة القوائم
            this.menuStrip.Items.AddRange(new ToolStripItem[] {
                fileMenu,
                receiptsMenu,
                reportsMenu,
                helpMenu
            });
        }

        private void Logout_Click(object sender, EventArgs e)
        {
            AuthService.Logout();
            this.Close();
        }

        private void Exit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void Receipts_Click(object sender, EventArgs e)
        {
            try
            {
                var receiptsForm = new ReceiptsForm();
                receiptsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح سندات القبض: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Payments_Click(object sender, EventArgs e)
        {
            try
            {
                var paymentsForm = new PaymentsForm();
                paymentsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح سندات الصرف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CustomerStatement_Click(object sender, EventArgs e)
        {
            try
            {
                var statementForm = new CustomerStatementForm();
                statementForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح كشف حساب العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void About_Click(object sender, EventArgs e)
        {
            MessageBox.Show("نظام إدارة السندات والمدفوعات\nالإصدار 1.0\n\nنظام شامل لإدارة سندات القبض والصرف",
                "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    // نموذج سندات القبض
    public partial class ReceiptsForm : Form
    {
        private DataGridView dgvReceipts;
        private Button btnAdd;
        private Button btnClose;
        private List<Receipt> receipts;
        private List<Customer> customers;

        public ReceiptsForm()
        {
            try
            {
                InitializeComponent();
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نموذج سندات القبض: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeComponent()
        {
            this.dgvReceipts = new DataGridView();
            this.btnAdd = new Button();
            this.btnClose = new Button();

            this.SuspendLayout();

            // Form
            this.Text = "إدارة سندات القبض";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;

            // DataGridView
            this.dgvReceipts.Location = new Point(20, 20);
            this.dgvReceipts.Size = new Size(960, 450);
            this.dgvReceipts.AllowUserToAddRows = false;
            this.dgvReceipts.ReadOnly = true;
            this.dgvReceipts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvReceipts.RightToLeft = RightToLeft.Yes;

            SetupGrid();

            // Add Button
            this.btnAdd.Text = "إضافة سند";
            this.btnAdd.Location = new Point(880, 490);
            this.btnAdd.Size = new Size(100, 30);
            this.btnAdd.BackColor = Color.FromArgb(76, 175, 80);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.FlatStyle = FlatStyle.Flat;
            this.btnAdd.Click += BtnAdd_Click;

            // Close Button
            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(20, 490);
            this.btnClose.Size = new Size(100, 30);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Click += (s, e) => this.Close();

            // Add controls
            this.Controls.Add(this.dgvReceipts);
            this.Controls.Add(this.btnAdd);
            this.Controls.Add(this.btnClose);

            this.ResumeLayout(false);
        }

        private void SetupGrid()
        {
            dgvReceipts.Columns.Clear();
            dgvReceipts.Columns.Add("ReceiptNumber", "رقم السند");
            dgvReceipts.Columns.Add("ReceiptDate", "التاريخ");
            dgvReceipts.Columns.Add("CustomerName", "العميل");
            dgvReceipts.Columns.Add("Amount", "المبلغ");
            dgvReceipts.Columns.Add("PaymentMethod", "طريقة الدفع");
            dgvReceipts.Columns.Add("Status", "الحالة");

            dgvReceipts.Columns["ReceiptDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            dgvReceipts.Columns["Amount"].DefaultCellStyle.Format = "N2";
            dgvReceipts.Columns["Amount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        }

        private void LoadData()
        {
            // بيانات تجريبية للعملاء
            customers = new List<Customer>
            {
                new Customer { Id = 1, CustomerName = "أحمد محمد علي", CustomerCode = "C001" },
                new Customer { Id = 2, CustomerName = "فاطمة عبدالله", CustomerCode = "C002" },
                new Customer { Id = 3, CustomerName = "محمد سعد الدين", CustomerCode = "C003" }
            };

            // بيانات تجريبية للسندات
            receipts = new List<Receipt>
            {
                new Receipt { Id = 1, ReceiptNumber = "R001", ReceiptDate = DateTime.Now.AddDays(-5), CustomerId = 1, Amount = 5000, PaymentMethod = "نقدي", Status = "مؤكد" },
                new Receipt { Id = 2, ReceiptNumber = "R002", ReceiptDate = DateTime.Now.AddDays(-3), CustomerId = 2, Amount = 2500, PaymentMethod = "شيك", Status = "مؤكد" },
                new Receipt { Id = 3, ReceiptNumber = "R003", ReceiptDate = DateTime.Now.AddDays(-1), CustomerId = 3, Amount = 1200, PaymentMethod = "تحويل بنكي", Status = "مسودة" }
            };

            RefreshGrid();
        }

        private void RefreshGrid()
        {
            dgvReceipts.Rows.Clear();
            foreach (var receipt in receipts)
            {
                var customer = customers.FirstOrDefault(c => c.Id == receipt.CustomerId);
                var row = dgvReceipts.Rows.Add();
                dgvReceipts.Rows[row].Cells["ReceiptNumber"].Value = receipt.ReceiptNumber;
                dgvReceipts.Rows[row].Cells["ReceiptDate"].Value = receipt.ReceiptDate;
                dgvReceipts.Rows[row].Cells["CustomerName"].Value = customer?.CustomerName ?? "غير محدد";
                dgvReceipts.Rows[row].Cells["Amount"].Value = receipt.Amount;
                dgvReceipts.Rows[row].Cells["PaymentMethod"].Value = receipt.PaymentMethod;
                dgvReceipts.Rows[row].Cells["Status"].Value = receipt.Status;

                // تلوين حسب الحالة
                if (receipt.Status == "مؤكد")
                    dgvReceipts.Rows[row].DefaultCellStyle.BackColor = Color.LightGreen;
                else if (receipt.Status == "مسودة")
                    dgvReceipts.Rows[row].DefaultCellStyle.BackColor = Color.LightYellow;
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم إضافة نموذج إضافة السند هنا", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    // نموذج سندات الصرف
    public partial class PaymentsForm : Form
    {
        private DataGridView dgvPayments;
        private Button btnAdd;
        private Button btnClose;
        private List<Payment> payments;

        public PaymentsForm()
        {
            try
            {
                InitializeComponent();
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نموذج سندات الصرف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeComponent()
        {
            this.dgvPayments = new DataGridView();
            this.btnAdd = new Button();
            this.btnClose = new Button();

            this.SuspendLayout();

            // Form
            this.Text = "إدارة سندات الصرف";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;

            // DataGridView
            this.dgvPayments.Location = new Point(20, 20);
            this.dgvPayments.Size = new Size(960, 450);
            this.dgvPayments.AllowUserToAddRows = false;
            this.dgvPayments.ReadOnly = true;
            this.dgvPayments.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvPayments.RightToLeft = RightToLeft.Yes;

            SetupGrid();

            // Add Button
            this.btnAdd.Text = "إضافة سند";
            this.btnAdd.Location = new Point(880, 490);
            this.btnAdd.Size = new Size(100, 30);
            this.btnAdd.BackColor = Color.FromArgb(76, 175, 80);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.FlatStyle = FlatStyle.Flat;
            this.btnAdd.Click += BtnAdd_Click;

            // Close Button
            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(20, 490);
            this.btnClose.Size = new Size(100, 30);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Click += (s, e) => this.Close();

            // Add controls
            this.Controls.Add(this.dgvPayments);
            this.Controls.Add(this.btnAdd);
            this.Controls.Add(this.btnClose);

            this.ResumeLayout(false);
        }

        private void SetupGrid()
        {
            dgvPayments.Columns.Clear();
            dgvPayments.Columns.Add("PaymentNumber", "رقم السند");
            dgvPayments.Columns.Add("PaymentDate", "التاريخ");
            dgvPayments.Columns.Add("PaymentType", "نوع الصرف");
            dgvPayments.Columns.Add("Beneficiary", "المستفيد");
            dgvPayments.Columns.Add("Amount", "المبلغ");
            dgvPayments.Columns.Add("Status", "الحالة");

            dgvPayments.Columns["PaymentDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            dgvPayments.Columns["Amount"].DefaultCellStyle.Format = "N2";
            dgvPayments.Columns["Amount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        }

        private void LoadData()
        {
            // بيانات تجريبية لسندات الصرف
            payments = new List<Payment>
            {
                new Payment { Id = 1, PaymentNumber = "P001", PaymentDate = DateTime.Now.AddDays(-7), PaymentType = "رواتب", Beneficiary = "أحمد محمد", Amount = 8000, Status = "مؤكد" },
                new Payment { Id = 2, PaymentNumber = "P002", PaymentDate = DateTime.Now.AddDays(-5), PaymentType = "مصاريف تشغيلية", Beneficiary = "شركة الكهرباء", Amount = 1500, Status = "مؤكد" },
                new Payment { Id = 3, PaymentNumber = "P003", PaymentDate = DateTime.Now.AddDays(-2), PaymentType = "إيجارات", Beneficiary = "مالك العقار", Amount = 12000, Status = "مسودة" }
            };

            RefreshGrid();
        }

        private void RefreshGrid()
        {
            dgvPayments.Rows.Clear();
            foreach (var payment in payments)
            {
                var row = dgvPayments.Rows.Add();
                dgvPayments.Rows[row].Cells["PaymentNumber"].Value = payment.PaymentNumber;
                dgvPayments.Rows[row].Cells["PaymentDate"].Value = payment.PaymentDate;
                dgvPayments.Rows[row].Cells["PaymentType"].Value = payment.PaymentType;
                dgvPayments.Rows[row].Cells["Beneficiary"].Value = payment.Beneficiary;
                dgvPayments.Rows[row].Cells["Amount"].Value = payment.Amount;
                dgvPayments.Rows[row].Cells["Status"].Value = payment.Status;

                // تلوين حسب الحالة
                if (payment.Status == "مؤكد")
                    dgvPayments.Rows[row].DefaultCellStyle.BackColor = Color.LightGreen;
                else if (payment.Status == "مسودة")
                    dgvPayments.Rows[row].DefaultCellStyle.BackColor = Color.LightYellow;
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم إضافة نموذج إضافة سند الصرف هنا", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    // نموذج كشف حساب العميل
    public partial class CustomerStatementForm : Form
    {
        private ComboBox cmbCustomer;
        private DataGridView dgvInvoices;
        private DataGridView dgvPayments;
        private TabControl tabControl;
        private Button btnGenerate;
        private Button btnClose;
        private Label lblCustomer;
        private TextBox txtSummary;

        private List<Customer> customers;
        private List<Invoice> invoices;
        private List<Receipt> receipts;

        public CustomerStatementForm()
        {
            try
            {
                InitializeComponent();
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نموذج كشف حساب العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeComponent()
        {
            this.cmbCustomer = new ComboBox();
            this.dgvInvoices = new DataGridView();
            this.dgvPayments = new DataGridView();
            this.tabControl = new TabControl();
            this.btnGenerate = new Button();
            this.btnClose = new Button();
            this.lblCustomer = new Label();
            this.txtSummary = new TextBox();

            this.SuspendLayout();

            // Form
            this.Text = "كشف حساب العميل التفصيلي";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;

            // Customer Label
            this.lblCustomer.Text = "العميل:";
            this.lblCustomer.Location = new Point(1100, 20);
            this.lblCustomer.Size = new Size(60, 23);
            this.lblCustomer.TextAlign = ContentAlignment.MiddleRight;

            // Customer ComboBox
            this.cmbCustomer.Location = new Point(900, 20);
            this.cmbCustomer.Size = new Size(180, 23);
            this.cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList;

            // Generate Button
            this.btnGenerate.Text = "إنشاء الكشف";
            this.btnGenerate.Location = new Point(780, 20);
            this.btnGenerate.Size = new Size(100, 25);
            this.btnGenerate.BackColor = Color.FromArgb(25, 118, 210);
            this.btnGenerate.ForeColor = Color.White;
            this.btnGenerate.FlatStyle = FlatStyle.Flat;
            this.btnGenerate.Click += BtnGenerate_Click;

            // Summary TextBox
            this.txtSummary.Location = new Point(20, 60);
            this.txtSummary.Size = new Size(1160, 60);
            this.txtSummary.Multiline = true;
            this.txtSummary.ReadOnly = true;
            this.txtSummary.BackColor = Color.LightBlue;

            // Tab Control
            this.tabControl.Location = new Point(20, 140);
            this.tabControl.Size = new Size(1160, 450);

            // Invoices Tab
            var tabInvoices = new TabPage("الفواتير");
            this.dgvInvoices.Location = new Point(10, 10);
            this.dgvInvoices.Size = new Size(1140, 410);
            this.dgvInvoices.AllowUserToAddRows = false;
            this.dgvInvoices.ReadOnly = true;
            this.dgvInvoices.RightToLeft = RightToLeft.Yes;
            SetupInvoicesGrid();
            tabInvoices.Controls.Add(this.dgvInvoices);

            // Payments Tab
            var tabPayments = new TabPage("المدفوعات");
            this.dgvPayments.Location = new Point(10, 10);
            this.dgvPayments.Size = new Size(1140, 410);
            this.dgvPayments.AllowUserToAddRows = false;
            this.dgvPayments.ReadOnly = true;
            this.dgvPayments.RightToLeft = RightToLeft.Yes;
            SetupPaymentsGrid();
            tabPayments.Controls.Add(this.dgvPayments);

            this.tabControl.TabPages.Add(tabInvoices);
            this.tabControl.TabPages.Add(tabPayments);

            // Close Button
            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(20, 610);
            this.btnClose.Size = new Size(100, 30);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Click += (s, e) => this.Close();

            // Add controls
            this.Controls.Add(this.lblCustomer);
            this.Controls.Add(this.cmbCustomer);
            this.Controls.Add(this.btnGenerate);
            this.Controls.Add(this.txtSummary);
            this.Controls.Add(this.tabControl);
            this.Controls.Add(this.btnClose);

            this.ResumeLayout(false);
        }

        private void SetupInvoicesGrid()
        {
            dgvInvoices.Columns.Clear();
            dgvInvoices.Columns.Add("InvoiceNumber", "رقم الفاتورة");
            dgvInvoices.Columns.Add("InvoiceDate", "التاريخ");
            dgvInvoices.Columns.Add("DueDate", "تاريخ الاستحقاق");
            dgvInvoices.Columns.Add("TotalAmount", "إجمالي الفاتورة");
            dgvInvoices.Columns.Add("PaidAmount", "المدفوع");
            dgvInvoices.Columns.Add("RemainingAmount", "المتبقي");
            dgvInvoices.Columns.Add("Status", "الحالة");

            dgvInvoices.Columns["InvoiceDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            dgvInvoices.Columns["DueDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            dgvInvoices.Columns["TotalAmount"].DefaultCellStyle.Format = "N2";
            dgvInvoices.Columns["PaidAmount"].DefaultCellStyle.Format = "N2";
            dgvInvoices.Columns["RemainingAmount"].DefaultCellStyle.Format = "N2";
        }

        private void SetupPaymentsGrid()
        {
            dgvPayments.Columns.Clear();
            dgvPayments.Columns.Add("ReceiptNumber", "رقم السند");
            dgvPayments.Columns.Add("ReceiptDate", "التاريخ");
            dgvPayments.Columns.Add("Amount", "المبلغ");
            dgvPayments.Columns.Add("PaymentMethod", "طريقة الدفع");
            dgvPayments.Columns.Add("ReferenceNumber", "رقم المرجع");
            dgvPayments.Columns.Add("Status", "الحالة");

            dgvPayments.Columns["ReceiptDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            dgvPayments.Columns["Amount"].DefaultCellStyle.Format = "N2";
        }

        private void LoadData()
        {
            // بيانات تجريبية للعملاء
            customers = new List<Customer>
            {
                new Customer { Id = 1, CustomerName = "أحمد محمد علي", CustomerCode = "C001", CurrentBalance = 5000 },
                new Customer { Id = 2, CustomerName = "فاطمة عبدالله", CustomerCode = "C002", CurrentBalance = -2500 },
                new Customer { Id = 3, CustomerName = "محمد سعد الدين", CustomerCode = "C003", CurrentBalance = 0 }
            };

            // بيانات تجريبية للفواتير
            invoices = new List<Invoice>
            {
                new Invoice { Id = 1, InvoiceNumber = "INV001", InvoiceDate = DateTime.Now.AddDays(-30), DueDate = DateTime.Now.AddDays(-15), CustomerId = 1, TotalAmount = 8000, PaidAmount = 3000, Status = "جزئي" },
                new Invoice { Id = 2, InvoiceNumber = "INV002", InvoiceDate = DateTime.Now.AddDays(-20), DueDate = DateTime.Now.AddDays(-5), CustomerId = 1, TotalAmount = 5500, PaidAmount = 5500, Status = "مدفوع" },
                new Invoice { Id = 3, InvoiceNumber = "INV003", InvoiceDate = DateTime.Now.AddDays(-15), DueDate = DateTime.Now, CustomerId = 2, TotalAmount = 3200, PaidAmount = 700, Status = "جزئي" }
            };

            // بيانات تجريبية للمدفوعات
            receipts = new List<Receipt>
            {
                new Receipt { Id = 1, ReceiptNumber = "R001", ReceiptDate = DateTime.Now.AddDays(-28), CustomerId = 1, Amount = 3000, PaymentMethod = "نقدي", Status = "مؤكد" },
                new Receipt { Id = 2, ReceiptNumber = "R002", ReceiptDate = DateTime.Now.AddDays(-18), CustomerId = 1, Amount = 5500, PaymentMethod = "تحويل بنكي", Status = "مؤكد" },
                new Receipt { Id = 3, ReceiptNumber = "R003", ReceiptDate = DateTime.Now.AddDays(-10), CustomerId = 2, Amount = 700, PaymentMethod = "شيك", Status = "مؤكد" }
            };

            // تحميل العملاء في ComboBox
            cmbCustomer.Items.Clear();
            foreach (var customer in customers)
            {
                cmbCustomer.Items.Add($"{customer.CustomerCode} - {customer.CustomerName}");
            }
            if (cmbCustomer.Items.Count > 0)
                cmbCustomer.SelectedIndex = 0;
        }

        private void BtnGenerate_Click(object sender, EventArgs e)
        {
            if (cmbCustomer.SelectedIndex < 0) return;

            var selectedCustomer = customers[cmbCustomer.SelectedIndex];

            // عرض ملخص العميل
            txtSummary.Text = $"كود العميل: {selectedCustomer.CustomerCode}\r\n" +
                             $"اسم العميل: {selectedCustomer.CustomerName}\r\n" +
                             $"الرصيد الحالي: {selectedCustomer.CurrentBalance:N2} ريال";

            // تحميل فواتير العميل
            LoadCustomerInvoices(selectedCustomer.Id);

            // تحميل مدفوعات العميل
            LoadCustomerPayments(selectedCustomer.Id);
        }

        private void LoadCustomerInvoices(int customerId)
        {
            dgvInvoices.Rows.Clear();
            var customerInvoices = invoices.Where(i => i.CustomerId == customerId);

            foreach (var invoice in customerInvoices)
            {
                var row = dgvInvoices.Rows.Add();
                dgvInvoices.Rows[row].Cells["InvoiceNumber"].Value = invoice.InvoiceNumber;
                dgvInvoices.Rows[row].Cells["InvoiceDate"].Value = invoice.InvoiceDate;
                dgvInvoices.Rows[row].Cells["DueDate"].Value = invoice.DueDate;
                dgvInvoices.Rows[row].Cells["TotalAmount"].Value = invoice.TotalAmount;
                dgvInvoices.Rows[row].Cells["PaidAmount"].Value = invoice.PaidAmount;
                dgvInvoices.Rows[row].Cells["RemainingAmount"].Value = invoice.TotalAmount - invoice.PaidAmount;
                dgvInvoices.Rows[row].Cells["Status"].Value = invoice.Status;

                // تلوين حسب الحالة
                if (invoice.Status == "مدفوع")
                    dgvInvoices.Rows[row].DefaultCellStyle.BackColor = Color.LightGreen;
                else if (invoice.Status == "جزئي")
                    dgvInvoices.Rows[row].DefaultCellStyle.BackColor = Color.LightYellow;
                else if (invoice.DueDate < DateTime.Now)
                    dgvInvoices.Rows[row].DefaultCellStyle.BackColor = Color.LightCoral;
            }
        }

        private void LoadCustomerPayments(int customerId)
        {
            dgvPayments.Rows.Clear();
            var customerPayments = receipts.Where(r => r.CustomerId == customerId);

            foreach (var payment in customerPayments)
            {
                var row = dgvPayments.Rows.Add();
                dgvPayments.Rows[row].Cells["ReceiptNumber"].Value = payment.ReceiptNumber;
                dgvPayments.Rows[row].Cells["ReceiptDate"].Value = payment.ReceiptDate;
                dgvPayments.Rows[row].Cells["Amount"].Value = payment.Amount;
                dgvPayments.Rows[row].Cells["PaymentMethod"].Value = payment.PaymentMethod;
                dgvPayments.Rows[row].Cells["ReferenceNumber"].Value = payment.ReferenceNumber;
                dgvPayments.Rows[row].Cells["Status"].Value = payment.Status;

                // تلوين حسب الحالة
                if (payment.Status == "مؤكد")
                    dgvPayments.Rows[row].DefaultCellStyle.BackColor = Color.LightGreen;
                else
                    dgvPayments.Rows[row].DefaultCellStyle.BackColor = Color.LightYellow;
            }
        }
    }

    // نقطة دخول التطبيق
    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new LoginForm());
        }
    }
}
