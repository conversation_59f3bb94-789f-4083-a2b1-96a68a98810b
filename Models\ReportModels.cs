using System;
using System.Collections.Generic;

namespace IntegratedInvoiceSystem.Models
{
    /// <summary>
    /// ملخص فواتير العميل
    /// </summary>
    public class CustomerInvoiceSummary
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public int InvoiceCount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public decimal CollectionRate { get; set; }
        public decimal AverageInvoiceValue { get; set; }
        public DateTime LastInvoiceDate { get; set; }
    }

    /// <summary>
    /// تحليل أعمار الفواتير
    /// </summary>
    public class InvoiceAgingAnalysis
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public decimal Current { get; set; } // 0-30 days
        public decimal Days31to60 { get; set; }
        public decimal Days61to90 { get; set; }
        public decimal Days91to120 { get; set; }
        public decimal Over120Days { get; set; }
        public decimal TotalOutstanding { get; set; }
    }

    /// <summary>
    /// إحصائيات الفواتير
    /// </summary>
    public class InvoiceStatistics
    {
        public int TotalInvoices { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public decimal CollectionRate { get; set; }
        public decimal AverageInvoiceValue { get; set; }
        public double AverageDaysToPayment { get; set; }
        public int OverdueInvoices { get; set; }
        public decimal OverdueAmount { get; set; }
    }

    /// <summary>
    /// الاتجاه الشهري
    /// </summary>
    public class MonthlyTrend
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string Period { get; set; }
        public int InvoiceCount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal CollectionRate { get; set; }
    }

    /// <summary>
    /// توزيع الحالات
    /// </summary>
    public class StatusDistribution
    {
        public string Status { get; set; }
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// مؤشر الأداء الرئيسي
    /// </summary>
    public class PerformanceKPI
    {
        public string Metric { get; set; }
        public decimal CurrentPeriod { get; set; }
        public decimal PreviousPeriod { get; set; }
        public decimal Change { get; set; }
        public decimal ChangePercent { get; set; }
        public decimal Target { get; set; }
        public decimal Achievement { get; set; }
    }

    /// <summary>
    /// تقرير مبيعات مفصل
    /// </summary>
    public class DetailedSalesReport
    {
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public string CustomerName { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string Status { get; set; }
        public int DaysOverdue { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// تحليل العميل المتقدم
    /// </summary>
    public class AdvancedCustomerAnalysis
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public DateTime FirstInvoiceDate { get; set; }
        public DateTime LastInvoiceDate { get; set; }
        public int TotalInvoices { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageInvoiceValue { get; set; }
        public decimal CollectionRate { get; set; }
        public int DaysSinceLastInvoice { get; set; }
        public string CustomerSegment { get; set; } // VIP, Regular, New, Inactive
        public decimal OutstandingBalance { get; set; }
        public int OverdueInvoices { get; set; }
        public decimal OverdueAmount { get; set; }
    }

    /// <summary>
    /// تحليل الربحية
    /// </summary>
    public class ProfitabilityAnalysis
    {
        public string Period { get; set; }
        public decimal Revenue { get; set; }
        public decimal CostOfGoodsSold { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public decimal OperatingExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal NetProfitMargin { get; set; }
    }

    /// <summary>
    /// تحليل المنتجات
    /// </summary>
    public class ProductAnalysis
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string Category { get; set; }
        public int QuantitySold { get; set; }
        public decimal Revenue { get; set; }
        public decimal AverageSellingPrice { get; set; }
        public decimal ProfitMargin { get; set; }
        public int InvoiceCount { get; set; }
        public DateTime LastSaleDate { get; set; }
    }

    /// <summary>
    /// تقرير التدفق النقدي
    /// </summary>
    public class CashFlowReport
    {
        public DateTime Date { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal InvoicesIssued { get; set; }
        public decimal PaymentsReceived { get; set; }
        public decimal ExpensesPaid { get; set; }
        public decimal NetCashFlow { get; set; }
        public decimal ClosingBalance { get; set; }
    }

    /// <summary>
    /// تحليل الموسمية
    /// </summary>
    public class SeasonalityAnalysis
    {
        public int Month { get; set; }
        public string MonthName { get; set; }
        public decimal AverageRevenue { get; set; }
        public int AverageInvoiceCount { get; set; }
        public decimal SeasonalityIndex { get; set; } // 100 = average, >100 = above average
        public decimal YearOverYearGrowth { get; set; }
    }

    /// <summary>
    /// تحليل المخاطر
    /// </summary>
    public class RiskAnalysis
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public decimal OutstandingAmount { get; set; }
        public int DaysOverdue { get; set; }
        public decimal CollectionRate { get; set; }
        public string RiskLevel { get; set; } // Low, Medium, High, Critical
        public decimal RiskScore { get; set; } // 0-100
        public string RecommendedAction { get; set; }
    }

    /// <summary>
    /// مقارنة الفترات
    /// </summary>
    public class PeriodComparison
    {
        public string Metric { get; set; }
        public decimal CurrentPeriod { get; set; }
        public decimal PreviousPeriod { get; set; }
        public decimal Variance { get; set; }
        public decimal VariancePercent { get; set; }
        public string Trend { get; set; } // Increasing, Decreasing, Stable
        public string Analysis { get; set; }
    }

    /// <summary>
    /// تقرير الأهداف والإنجازات
    /// </summary>
    public class GoalsAndAchievements
    {
        public string Goal { get; set; }
        public decimal Target { get; set; }
        public decimal Actual { get; set; }
        public decimal Achievement { get; set; } // Percentage
        public string Status { get; set; } // Achieved, On Track, Behind, Critical
        public decimal Variance { get; set; }
        public string TimeRemaining { get; set; }
        public string ActionRequired { get; set; }
    }

    /// <summary>
    /// تحليل قنوات المبيعات
    /// </summary>
    public class SalesChannelAnalysis
    {
        public string Channel { get; set; }
        public int InvoiceCount { get; set; }
        public decimal Revenue { get; set; }
        public decimal AverageOrderValue { get; set; }
        public decimal ConversionRate { get; set; }
        public decimal CustomerAcquisitionCost { get; set; }
        public decimal CustomerLifetimeValue { get; set; }
        public decimal ROI { get; set; }
    }

    /// <summary>
    /// تقرير الاستثناءات
    /// </summary>
    public class ExceptionReport
    {
        public string ExceptionType { get; set; }
        public string Description { get; set; }
        public DateTime Date { get; set; }
        public string Reference { get; set; }
        public decimal Amount { get; set; }
        public string Status { get; set; }
        public string AssignedTo { get; set; }
        public string Priority { get; set; }
        public string Resolution { get; set; }
    }
}
