using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AccountingSystem.Models;
using AccountingSystem.Services;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج إدارة سندات القبض
    /// </summary>
    public partial class ReceiptsForm : Form
    {
        private DataGridView dgvReceipts;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnPrint;
        private Button btnReport;
        private Button btnClose;
        private TextBox txtSearch;
        private Label lblSearch;
        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private Label lblFromDate;
        private Label lblToDate;
        private ComboBox cmbCustomer;
        private Label lblCustomer;
        private Label lblTotalAmount;
        private TextBox txtTotalAmount;
        
        private List<Receipt> receipts;
        private List<Customer> customers;
        
        public ReceiptsForm()
        {
            InitializeComponent();
            LoadData();
        }
        
        private void InitializeComponent()
        {
            this.dgvReceipts = new DataGridView();
            this.btnAdd = new Button();
            this.btnEdit = new Button();
            this.btnDelete = new Button();
            this.btnPrint = new Button();
            this.btnReport = new Button();
            this.btnClose = new Button();
            this.txtSearch = new TextBox();
            this.lblSearch = new Label();
            this.dtpFromDate = new DateTimePicker();
            this.dtpToDate = new DateTimePicker();
            this.lblFromDate = new Label();
            this.lblToDate = new Label();
            this.cmbCustomer = new ComboBox();
            this.lblCustomer = new Label();
            this.lblTotalAmount = new Label();
            this.txtTotalAmount = new TextBox();
            
            this.SuspendLayout();
            
            // Form
            this.Text = "إدارة سندات القبض";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Search Controls
            this.lblSearch.Text = "البحث:";
            this.lblSearch.Location = new Point(1100, 20);
            this.lblSearch.Size = new Size(60, 23);
            this.lblSearch.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtSearch.Location = new Point(950, 20);
            this.txtSearch.Size = new Size(140, 23);
            this.txtSearch.TextChanged += TxtSearch_TextChanged;
            
            // Date Range Controls
            this.lblFromDate.Text = "من تاريخ:";
            this.lblFromDate.Location = new Point(880, 20);
            this.lblFromDate.Size = new Size(60, 23);
            this.lblFromDate.TextAlign = ContentAlignment.MiddleRight;
            
            this.dtpFromDate.Location = new Point(750, 20);
            this.dtpFromDate.Size = new Size(120, 23);
            this.dtpFromDate.Format = DateTimePickerFormat.Short;
            this.dtpFromDate.Value = DateTime.Now.AddMonths(-1);
            this.dtpFromDate.ValueChanged += DateFilter_Changed;
            
            this.lblToDate.Text = "إلى تاريخ:";
            this.lblToDate.Location = new Point(690, 20);
            this.lblToDate.Size = new Size(60, 23);
            this.lblToDate.TextAlign = ContentAlignment.MiddleRight;
            
            this.dtpToDate.Location = new Point(560, 20);
            this.dtpToDate.Size = new Size(120, 23);
            this.dtpToDate.Format = DateTimePickerFormat.Short;
            this.dtpToDate.Value = DateTime.Now;
            this.dtpToDate.ValueChanged += DateFilter_Changed;
            
            // Customer Filter
            this.lblCustomer.Text = "العميل:";
            this.lblCustomer.Location = new Point(500, 20);
            this.lblCustomer.Size = new Size(50, 23);
            this.lblCustomer.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbCustomer.Location = new Point(350, 20);
            this.cmbCustomer.Size = new Size(140, 23);
            this.cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbCustomer.SelectedIndexChanged += CmbCustomer_SelectedIndexChanged;
            
            // Action Buttons
            this.btnAdd.Text = "إضافة سند";
            this.btnAdd.Location = new Point(1050, 60);
            this.btnAdd.Size = new Size(100, 30);
            this.btnAdd.BackColor = Color.FromArgb(76, 175, 80);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.FlatStyle = FlatStyle.Flat;
            this.btnAdd.Click += BtnAdd_Click;
            
            this.btnEdit.Text = "تعديل";
            this.btnEdit.Location = new Point(930, 60);
            this.btnEdit.Size = new Size(100, 30);
            this.btnEdit.BackColor = Color.FromArgb(255, 152, 0);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.Click += BtnEdit_Click;
            
            this.btnDelete.Text = "حذف";
            this.btnDelete.Location = new Point(810, 60);
            this.btnDelete.Size = new Size(100, 30);
            this.btnDelete.BackColor = Color.FromArgb(244, 67, 54);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.Click += BtnDelete_Click;
            
            this.btnPrint.Text = "طباعة";
            this.btnPrint.Location = new Point(690, 60);
            this.btnPrint.Size = new Size(100, 30);
            this.btnPrint.BackColor = Color.FromArgb(103, 58, 183);
            this.btnPrint.ForeColor = Color.White;
            this.btnPrint.FlatStyle = FlatStyle.Flat;
            this.btnPrint.Click += BtnPrint_Click;
            
            this.btnReport.Text = "تقرير";
            this.btnReport.Location = new Point(570, 60);
            this.btnReport.Size = new Size(100, 30);
            this.btnReport.BackColor = Color.FromArgb(0, 150, 136);
            this.btnReport.ForeColor = Color.White;
            this.btnReport.FlatStyle = FlatStyle.Flat;
            this.btnReport.Click += BtnReport_Click;
            
            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(20, 60);
            this.btnClose.Size = new Size(100, 30);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Click += BtnClose_Click;
            
            // DataGridView
            this.dgvReceipts.Location = new Point(20, 110);
            this.dgvReceipts.Size = new Size(1160, 500);
            this.dgvReceipts.AllowUserToAddRows = false;
            this.dgvReceipts.AllowUserToDeleteRows = false;
            this.dgvReceipts.ReadOnly = true;
            this.dgvReceipts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvReceipts.RightToLeft = RightToLeft.Yes;
            this.dgvReceipts.Font = new Font("Tahoma", 10F);
            this.dgvReceipts.DoubleClick += DgvReceipts_DoubleClick;
            
            SetupDataGridView();
            
            // Total Amount
            this.lblTotalAmount.Text = "إجمالي المبلغ:";
            this.lblTotalAmount.Location = new Point(1050, 630);
            this.lblTotalAmount.Size = new Size(100, 23);
            this.lblTotalAmount.TextAlign = ContentAlignment.MiddleRight;
            this.lblTotalAmount.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            this.txtTotalAmount.Location = new Point(900, 630);
            this.txtTotalAmount.Size = new Size(140, 23);
            this.txtTotalAmount.ReadOnly = true;
            this.txtTotalAmount.BackColor = Color.LightGray;
            this.txtTotalAmount.TextAlign = HorizontalAlignment.Right;
            this.txtTotalAmount.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            // Add controls to form
            this.Controls.Add(this.lblSearch);
            this.Controls.Add(this.txtSearch);
            this.Controls.Add(this.lblFromDate);
            this.Controls.Add(this.dtpFromDate);
            this.Controls.Add(this.lblToDate);
            this.Controls.Add(this.dtpToDate);
            this.Controls.Add(this.lblCustomer);
            this.Controls.Add(this.cmbCustomer);
            this.Controls.Add(this.btnAdd);
            this.Controls.Add(this.btnEdit);
            this.Controls.Add(this.btnDelete);
            this.Controls.Add(this.btnPrint);
            this.Controls.Add(this.btnReport);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.dgvReceipts);
            this.Controls.Add(this.lblTotalAmount);
            this.Controls.Add(this.txtTotalAmount);
            
            this.ResumeLayout(false);
        }
        
        private void SetupDataGridView()
        {
            dgvReceipts.Columns.Clear();
            
            dgvReceipts.Columns.Add("Id", "المعرف");
            dgvReceipts.Columns["Id"].Width = 60;
            dgvReceipts.Columns["Id"].Visible = false;
            
            dgvReceipts.Columns.Add("ReceiptNumber", "رقم السند");
            dgvReceipts.Columns["ReceiptNumber"].Width = 100;
            
            dgvReceipts.Columns.Add("ReceiptDate", "التاريخ");
            dgvReceipts.Columns["ReceiptDate"].Width = 100;
            dgvReceipts.Columns["ReceiptDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            
            dgvReceipts.Columns.Add("CustomerName", "العميل");
            dgvReceipts.Columns["CustomerName"].Width = 200;
            
            dgvReceipts.Columns.Add("Amount", "المبلغ");
            dgvReceipts.Columns["Amount"].Width = 120;
            dgvReceipts.Columns["Amount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvReceipts.Columns["Amount"].DefaultCellStyle.Format = "N2";
            
            dgvReceipts.Columns.Add("PaymentMethod", "طريقة الدفع");
            dgvReceipts.Columns["PaymentMethod"].Width = 120;
            
            dgvReceipts.Columns.Add("ReferenceNumber", "رقم المرجع");
            dgvReceipts.Columns["ReferenceNumber"].Width = 120;
            
            dgvReceipts.Columns.Add("Notes", "ملاحظات");
            dgvReceipts.Columns["Notes"].Width = 200;
            
            dgvReceipts.Columns.Add("Status", "الحالة");
            dgvReceipts.Columns["Status"].Width = 80;
        }
        
        private void LoadData()
        {
            try
            {
                LoadCustomers();
                LoadReceipts();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void LoadCustomers()
        {
            // تحميل العملاء (بيانات تجريبية)
            customers = new List<Customer>
            {
                new Customer { Id = 1, CustomerName = "أحمد محمد علي", CustomerCode = "C001" },
                new Customer { Id = 2, CustomerName = "فاطمة عبدالله", CustomerCode = "C002" },
                new Customer { Id = 3, CustomerName = "محمد سعد الدين", CustomerCode = "C003" },
                new Customer { Id = 4, CustomerName = "نورا أحمد", CustomerCode = "C004" },
                new Customer { Id = 5, CustomerName = "خالد العتيبي", CustomerCode = "C005" }
            };
            
            cmbCustomer.Items.Clear();
            cmbCustomer.Items.Add("جميع العملاء");
            foreach (var customer in customers)
            {
                cmbCustomer.Items.Add($"{customer.CustomerCode} - {customer.CustomerName}");
            }
            cmbCustomer.SelectedIndex = 0;
        }
        
        private void LoadReceipts()
        {
            // إنشاء بيانات تجريبية لسندات القبض
            receipts = new List<Receipt>
            {
                new Receipt { Id = 1, ReceiptNumber = "R001", ReceiptDate = DateTime.Now.AddDays(-10), CustomerId = 1, Amount = 5000.00m, PaymentMethod = "نقدي", ReferenceNumber = "REF001", Notes = "دفعة من فاتورة رقم 100", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-10), CreatedBy = "admin" },
                new Receipt { Id = 2, ReceiptNumber = "R002", ReceiptDate = DateTime.Now.AddDays(-8), CustomerId = 2, Amount = 2500.00m, PaymentMethod = "شيك", ReferenceNumber = "CHK123", Notes = "سداد جزئي", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-8), CreatedBy = "admin" },
                new Receipt { Id = 3, ReceiptNumber = "R003", ReceiptDate = DateTime.Now.AddDays(-5), CustomerId = 3, Amount = 1200.00m, PaymentMethod = "تحويل بنكي", ReferenceNumber = "TRF456", Notes = "سداد كامل للفاتورة 102", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-5), CreatedBy = "admin" },
                new Receipt { Id = 4, ReceiptNumber = "R004", ReceiptDate = DateTime.Now.AddDays(-3), CustomerId = 4, Amount = 3500.00m, PaymentMethod = "نقدي", ReferenceNumber = "REF004", Notes = "دفعة مقدمة", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-3), CreatedBy = "admin" },
                new Receipt { Id = 5, ReceiptNumber = "R005", ReceiptDate = DateTime.Now.AddDays(-1), CustomerId = 1, Amount = 800.00m, PaymentMethod = "بطاقة ائتمان", ReferenceNumber = "CC789", Notes = "سداد متأخرات", Status = "مسودة", CreatedDate = DateTime.Now.AddDays(-1), CreatedBy = "admin" }
            };
            
            RefreshDataGridView();
        }
        
        private void RefreshDataGridView()
        {
            dgvReceipts.Rows.Clear();
            
            var filteredReceipts = ApplyFilters();
            decimal totalAmount = 0;
            
            foreach (var receipt in filteredReceipts.OrderByDescending(r => r.ReceiptDate))
            {
                var customer = customers.FirstOrDefault(c => c.Id == receipt.CustomerId);
                var customerName = customer?.CustomerName ?? "غير محدد";
                
                var rowIndex = dgvReceipts.Rows.Add();
                var row = dgvReceipts.Rows[rowIndex];
                
                row.Cells["Id"].Value = receipt.Id;
                row.Cells["ReceiptNumber"].Value = receipt.ReceiptNumber;
                row.Cells["ReceiptDate"].Value = receipt.ReceiptDate;
                row.Cells["CustomerName"].Value = customerName;
                row.Cells["Amount"].Value = receipt.Amount;
                row.Cells["PaymentMethod"].Value = receipt.PaymentMethod;
                row.Cells["ReferenceNumber"].Value = receipt.ReferenceNumber;
                row.Cells["Notes"].Value = receipt.Notes;
                row.Cells["Status"].Value = receipt.Status;
                
                // تلوين الصفوف حسب الحالة
                if (receipt.Status == "مؤكد")
                {
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else if (receipt.Status == "مسودة")
                {
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                }
                else if (receipt.Status == "ملغي")
                {
                    row.DefaultCellStyle.BackColor = Color.LightCoral;
                }
                
                totalAmount += receipt.Amount;
            }
            
            txtTotalAmount.Text = totalAmount.ToString("N2");
        }
        
        private List<Receipt> ApplyFilters()
        {
            var filtered = receipts.AsEnumerable();
            
            // فلتر التاريخ
            filtered = filtered.Where(r => r.ReceiptDate >= dtpFromDate.Value.Date && 
                                          r.ReceiptDate <= dtpToDate.Value.Date);
            
            // فلتر العميل
            if (cmbCustomer.SelectedIndex > 0)
            {
                var selectedCustomerId = customers[cmbCustomer.SelectedIndex - 1].Id;
                filtered = filtered.Where(r => r.CustomerId == selectedCustomerId);
            }
            
            // فلتر البحث
            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                string searchText = txtSearch.Text.ToLower();
                filtered = filtered.Where(r => 
                    r.ReceiptNumber.ToLower().Contains(searchText) ||
                    r.ReferenceNumber.ToLower().Contains(searchText) ||
                    (r.Notes != null && r.Notes.ToLower().Contains(searchText))
                );
            }
            
            return filtered.ToList();
        }
        
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            RefreshDataGridView();
        }
        
        private void DateFilter_Changed(object sender, EventArgs e)
        {
            RefreshDataGridView();
        }
        
        private void CmbCustomer_SelectedIndexChanged(object sender, EventArgs e)
        {
            RefreshDataGridView();
        }
        
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new AddEditReceiptForm(customers);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    var newReceipt = addForm.Receipt;
                    newReceipt.Id = receipts.Count > 0 ? receipts.Max(r => r.Id) + 1 : 1;
                    newReceipt.ReceiptNumber = $"R{newReceipt.Id:000}";
                    receipts.Add(newReceipt);
                    RefreshDataGridView();
                    
                    MessageBox.Show("تم إضافة السند بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة السند: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnEdit_Click(object sender, EventArgs e)
        {
            EditSelectedReceipt();
        }
        
        private void DgvReceipts_DoubleClick(object sender, EventArgs e)
        {
            EditSelectedReceipt();
        }
        
        private void EditSelectedReceipt()
        {
            try
            {
                if (dgvReceipts.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار سند للتعديل", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                int receiptId = (int)dgvReceipts.SelectedRows[0].Cells["Id"].Value;
                var receipt = receipts.FirstOrDefault(r => r.Id == receiptId);
                
                if (receipt != null)
                {
                    if (receipt.Status == "مؤكد")
                    {
                        MessageBox.Show("لا يمكن تعديل سند مؤكد", "تنبيه", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    
                    var editForm = new AddEditReceiptForm(customers, receipt);
                    if (editForm.ShowDialog() == DialogResult.OK)
                    {
                        var updatedReceipt = editForm.Receipt;
                        var index = receipts.FindIndex(r => r.Id == receiptId);
                        if (index >= 0)
                        {
                            receipts[index] = updatedReceipt;
                            RefreshDataGridView();
                            
                            MessageBox.Show("تم تعديل السند بنجاح", "نجح", 
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل السند: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvReceipts.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار سند للحذف", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                int receiptId = (int)dgvReceipts.SelectedRows[0].Cells["Id"].Value;
                var receipt = receipts.FirstOrDefault(r => r.Id == receiptId);
                
                if (receipt != null)
                {
                    if (receipt.Status == "مؤكد")
                    {
                        MessageBox.Show("لا يمكن حذف سند مؤكد", "تنبيه", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    
                    var result = MessageBox.Show($"هل أنت متأكد من حذف السند '{receipt.ReceiptNumber}'؟", 
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        receipts.Remove(receipt);
                        RefreshDataGridView();
                        
                        MessageBox.Show("تم حذف السند بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف السند: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvReceipts.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار سند للطباعة", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                int receiptId = (int)dgvReceipts.SelectedRows[0].Cells["Id"].Value;
                var receipt = receipts.FirstOrDefault(r => r.Id == receiptId);
                
                if (receipt != null)
                {
                    PrintReceipt(receipt);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة السند: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void PrintReceipt(Receipt receipt)
        {
            var customer = customers.FirstOrDefault(c => c.Id == receipt.CustomerId);
            var customerName = customer?.CustomerName ?? "غير محدد";
            
            var reportLines = new List<string>();
            
            reportLines.Add("سند قبض");
            reportLines.Add("========");
            reportLines.Add($"رقم السند: {receipt.ReceiptNumber}");
            reportLines.Add($"التاريخ: {receipt.ReceiptDate:yyyy/MM/dd}");
            reportLines.Add($"العميل: {customerName}");
            reportLines.Add($"المبلغ: {receipt.Amount:N2} ريال");
            reportLines.Add($"طريقة الدفع: {receipt.PaymentMethod}");
            reportLines.Add($"رقم المرجع: {receipt.ReferenceNumber}");
            reportLines.Add($"ملاحظات: {receipt.Notes}");
            reportLines.Add($"الحالة: {receipt.Status}");
            reportLines.Add("");
            reportLines.Add($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
            
            string tempFile = System.IO.Path.GetTempFileName() + ".txt";
            System.IO.File.WriteAllLines(tempFile, reportLines, System.Text.Encoding.UTF8);
            
            try
            {
                System.Diagnostics.Process.Start("notepad.exe", "/p " + tempFile);
            }
            catch
            {
                MessageBox.Show($"تم حفظ السند في: {tempFile}", "سند القبض", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        
        private void BtnReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportForm = new ReceiptsReportForm(ApplyFilters(), customers);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
