# دليل المستخدم - نظام المحاسبة الذكي

## مقدمة

نظام المحاسبة الذكي هو نظام محاسبي شامل مطور باستخدام تقنيات حديثة ومدعوم بميزات الذكاء الاصطناعي. يهدف النظام إلى تبسيط العمليات المحاسبية وتوفير تحليلات ذكية للمساعدة في اتخاذ القرارات المالية.

## البدء السريع

### متطلبات النظام
- نظام التشغيل: Windows 7 أو أحدث
- .NET Framework 4.8
- مساحة تخزين: 100 ميجابايت على الأقل
- ذاكرة الوصول العشوائي: 2 جيجابايت على الأقل

### التثبيت
1. تحميل ملفات النظام
2. تشغيل ملف `install_requirements.bat` لتثبيت المتطلبات
3. تشغيل ملف `build_and_run.bat` لبناء وتشغيل النظام

### تسجيل الدخول الأول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

⚠️ **تنبيه:** يُنصح بتغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

## الواجهة الرئيسية

### شريط القوائم
يحتوي شريط القوائم على الأقسام التالية:

#### 1. قائمة الملف
- **تسجيل خروج:** إنهاء الجلسة الحالية
- **خروج:** إغلاق البرنامج

#### 2. قائمة الحسابات
- **شجرة الحسابات:** عرض وإدارة شجرة الحسابات
- **إضافة حساب جديد:** إنشاء حساب محاسبي جديد

#### 3. قائمة القيود اليومية
- **عرض القيود:** استعراض القيود اليومية المسجلة
- **إضافة قيد جديد:** إنشاء قيد يومي جديد

#### 4. قائمة السندات
- **سندات القبض:** إدارة سندات القبض
- **سندات الصرف:** إدارة سندات الصرف

#### 5. قائمة العملاء
- **إدارة العملاء:** عرض وتعديل بيانات العملاء
- **إضافة عميل جديد:** تسجيل عميل جديد

#### 6. قائمة المنتجات
- **إدارة المنتجات:** عرض وتعديل بيانات المنتجات
- **إضافة منتج جديد:** تسجيل منتج جديد

#### 7. قائمة الفواتير
- **فواتير المبيعات:** إدارة فواتير المبيعات
- **فواتير المشتريات:** إدارة فواتير المشتريات
- **إضافة فاتورة جديدة:** إنشاء فاتورة جديدة

#### 8. قائمة التقارير
- **ميزان المراجعة:** عرض ميزان المراجعة
- **قائمة الدخل:** تقرير قائمة الدخل
- **الميزانية العمومية:** تقرير الميزانية العمومية

#### 9. قائمة الذكاء الاصطناعي
- **تحليل البيانات المالية:** تحليل شامل للوضع المالي
- **التوصيات الذكية:** توصيات مبنية على البيانات

#### 10. قائمة الإعدادات
- **إدارة المستخدمين:** إضافة وتعديل المستخدمين
- **إعدادات النظام:** تخصيص إعدادات النظام
- **نسخ احتياطي:** إنشاء نسخة احتياطية من البيانات
- **استعادة نسخة احتياطية:** استعادة البيانات من نسخة احتياطية

## إدارة الحسابات

### شجرة الحسابات

#### عرض شجرة الحسابات
1. من القائمة الرئيسية، اختر **الحسابات** > **شجرة الحسابات**
2. ستظهر نافذة تعرض الحسابات في شكل شجرة هرمية
3. يمكن توسيع وطي الحسابات الرئيسية لعرض الحسابات الفرعية

#### إضافة حساب جديد
1. في نافذة شجرة الحسابات، انقر على **إضافة**
2. أو من القائمة الرئيسية، اختر **الحسابات** > **إضافة حساب جديد**
3. املأ البيانات المطلوبة:
   - **كود الحساب:** رقم فريد للحساب
   - **اسم الحساب:** الاسم باللغة العربية
   - **الاسم بالإنجليزية:** (اختياري)
   - **الحساب الأب:** اختر الحساب الرئيسي إن وجد
   - **نوع الحساب:** أصول، خصوم، حقوق ملكية، إيرادات، أو مصروفات
   - **طبيعة الحساب:** مدين أو دائن (يتم تحديدها تلقائياً حسب النوع)
   - **الوصف:** وصف مختصر للحساب
4. حدد ما إذا كان الحساب رئيسياً أم لا
5. تأكد من تفعيل الحساب
6. انقر **حفظ**

#### تعديل حساب
1. في شجرة الحسابات، اختر الحساب المراد تعديله
2. انقر **تعديل** أو انقر نقراً مزدوجاً على الحساب
3. عدّل البيانات المطلوبة
4. انقر **حفظ**

#### حذف حساب
1. اختر الحساب المراد حذفه
2. انقر **حذف**
3. أكد عملية الحذف

⚠️ **ملاحظة:** لا يمكن حذف حساب يحتوي على حسابات فرعية أو له معاملات مسجلة.

## القيود اليومية

### عرض القيود اليومية
1. من القائمة الرئيسية، اختر **القيود اليومية** > **عرض القيود**
2. استخدم فلاتر البحث:
   - **من تاريخ / إلى تاريخ:** لتحديد فترة زمنية
   - **الحالة:** لعرض قيود معينة (مسودة، مؤكد، الكل)
3. انقر **بحث** لتطبيق الفلاتر

### إضافة قيد يومي جديد
1. من القائمة الرئيسية، اختر **القيود اليومية** > **إضافة قيد جديد**
2. املأ بيانات القيد:
   - **رقم القيد:** يتم توليده تلقائياً
   - **التاريخ:** تاريخ القيد
   - **الوصف:** وصف مختصر للقيد
   - **المرجع:** رقم مرجعي (اختياري)

#### إضافة تفاصيل القيد
1. في جدول التفاصيل، اختر **الحساب** من القائمة المنسدلة
2. أدخل **البيان** (وصف السطر)
3. أدخل المبلغ في عمود **مدين** أو **دائن** (ليس كلاهما)
4. أدخل **المرجع** إن وجد
5. كرر العملية لإضافة أسطر أخرى

#### حفظ القيد
- **حفظ:** حفظ القيد كمسودة
- **ترحيل:** حفظ القيد وترحيله (لا يمكن تعديله بعد الترحيل)

⚠️ **مهم:** يجب أن يكون القيد متوازناً (إجمالي المدين = إجمالي الدائن)

### تعديل قيد يومي
1. في قائمة القيود، اختر القيد المراد تعديله
2. انقر **تعديل**
3. عدّل البيانات المطلوبة
4. انقر **حفظ**

⚠️ **ملاحظة:** لا يمكن تعديل القيود المرحلة.

## التقارير

### ميزان المراجعة
1. من القائمة الرئيسية، اختر **التقارير** > **ميزان المراجعة**
2. حدد **كما في تاريخ**
3. انقر **إنشاء التقرير**
4. يمكنك:
   - **طباعة** التقرير
   - **تصدير** التقرير إلى ملف CSV أو نصي

### خصائص ميزان المراجعة
- يعرض جميع الحسابات التي لها أرصدة
- يصنف الحسابات حسب النوع بألوان مختلفة
- يحسب الإجماليات تلقائياً
- يظهر الفرق بين إجمالي المدين والدائن

## ميزات الذكاء الاصطناعي

### تحليل البيانات المالية
1. من القائمة الرئيسية، اختر **الذكاء الاصطناعي** > **تحليل البيانات المالية**
2. سيقوم النظام بتحليل:
   - الإيرادات والمصروفات
   - الأصول والخصوم
   - النسب المالية (هامش الربح، العائد على الأصول، نسبة الديون)
   - اتجاهات المبيعات

### التوصيات الذكية
يقدم النظام توصيات مبنية على تحليل البيانات مثل:
- تحسين هامش الربح
- إدارة التدفق النقدي
- تقليل المصروفات
- تحسين كفاءة استخدام الأصول

### كشف الأخطاء المحاسبية
يكتشف النظام تلقائياً:
- القيود غير المتوازنة
- الحسابات بأرصدة غير منطقية
- الفواتير المدفوعة أكثر من قيمتها

## النسخ الاحتياطي

### إنشاء نسخة احتياطية
1. من القائمة الرئيسية، اختر **الإعدادات** > **نسخ احتياطي**
2. حدد مكان حفظ النسخة الاحتياطية
3. انقر **حفظ**

### استعادة نسخة احتياطية
1. من القائمة الرئيسية، اختر **الإعدادات** > **استعادة نسخة احتياطية**
2. اختر ملف النسخة الاحتياطية
3. أكد عملية الاستعادة

⚠️ **تحذير:** ستستبدل عملية الاستعادة البيانات الحالية بالكامل.

## النسخ الاحتياطي التلقائي
- ينشئ النظام نسخة احتياطية تلقائية يومياً
- تحفظ النسخ التلقائية في مجلد `Backups/Auto`
- يحتفظ النظام بآخر 7 نسخ تلقائية

## نصائح وإرشادات

### أفضل الممارسات
1. **النسخ الاحتياطي المنتظم:** أنشئ نسخة احتياطية يومياً
2. **مراجعة القيود:** راجع القيود قبل ترحيلها
3. **تنظيم الحسابات:** استخدم أكواد منطقية للحسابات
4. **التوثيق:** أضف أوصافاً واضحة للقيود والحسابات

### حل المشاكل الشائعة

#### القيد غير متوازن
- تأكد من أن إجمالي المدين يساوي إجمالي الدائن
- راجع المبالغ المدخلة في كل سطر

#### لا يمكن حذف حساب
- تأكد من عدم وجود حسابات فرعية
- تأكد من عدم وجود معاملات مسجلة على الحساب

#### خطأ في قاعدة البيانات
- أعد تشغيل البرنامج
- استعد نسخة احتياطية حديثة إذا لزم الأمر

## الدعم الفني

### الحصول على المساعدة
- راجع هذا الدليل أولاً
- تحقق من ملف README.md للمعلومات التقنية
- أبلغ عن الأخطاء مع وصف مفصل للمشكلة

### معلومات النظام
- **الإصدار:** 1.0.0
- **تاريخ الإصدار:** 2024
- **المطور:** فريق التطوير

---

**ملاحظة:** هذا النظام مطور لأغراض تعليمية وتجريبية. يُنصح بإجراء اختبارات شاملة قبل الاستخدام في بيئة الإنتاج.
