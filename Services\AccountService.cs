using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using AccountingSystem.Data;
using AccountingSystem.Models;

namespace AccountingSystem.Services
{
    /// <summary>
    /// خدمة إدارة الحسابات
    /// </summary>
    public class AccountService
    {
        /// <summary>
        /// الحصول على جميع الحسابات
        /// </summary>
        /// <returns>قائمة الحسابات</returns>
        public static List<Account> GetAllAccounts()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    return connection.Query<Account>("SELECT * FROM Accounts ORDER BY AccountCode").ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على الحسابات: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// الحصول على الحسابات النشطة فقط
        /// </summary>
        /// <returns>قائمة الحسابات النشطة</returns>
        public static List<Account> GetActiveAccounts()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    return connection.Query<Account>(@"
                        SELECT * FROM Accounts 
                        WHERE IsActive = 1 
                        ORDER BY AccountCode").ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على الحسابات النشطة: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// الحصول على الحسابات الفرعية (غير الرئيسية)
        /// </summary>
        /// <returns>قائمة الحسابات الفرعية</returns>
        public static List<Account> GetSubAccounts()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    return connection.Query<Account>(@"
                        SELECT * FROM Accounts 
                        WHERE IsParent = 0 AND IsActive = 1 
                        ORDER BY AccountCode").ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على الحسابات الفرعية: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// الحصول على حساب بالمعرف
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>الحساب</returns>
        public static Account GetAccountById(int accountId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    return connection.QueryFirstOrDefault<Account>(@"
                        SELECT * FROM Accounts WHERE Id = @Id", new { Id = accountId });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على الحساب: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// الحصول على حساب بالكود
        /// </summary>
        /// <param name="accountCode">كود الحساب</param>
        /// <returns>الحساب</returns>
        public static Account GetAccountByCode(string accountCode)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    return connection.QueryFirstOrDefault<Account>(@"
                        SELECT * FROM Accounts WHERE AccountCode = @AccountCode", 
                        new { AccountCode = accountCode });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على الحساب: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// الحصول على الحسابات حسب النوع
        /// </summary>
        /// <param name="accountType">نوع الحساب</param>
        /// <returns>قائمة الحسابات</returns>
        public static List<Account> GetAccountsByType(string accountType)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    return connection.Query<Account>(@"
                        SELECT * FROM Accounts 
                        WHERE AccountType = @AccountType AND IsActive = 1 
                        ORDER BY AccountCode", 
                        new { AccountType = accountType }).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على الحسابات حسب النوع: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// تحديث رصيد الحساب
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="amount">المبلغ (موجب للزيادة، سالب للنقصان)</param>
        /// <param name="isDebit">هل هو مدين؟</param>
        public static void UpdateAccountBalance(int accountId, decimal amount, bool isDebit)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // الحصول على الحساب
                    var account = connection.QueryFirstOrDefault<Account>(@"
                        SELECT * FROM Accounts WHERE Id = @Id", new { Id = accountId });
                    
                    if (account == null)
                    {
                        throw new Exception("الحساب غير موجود");
                    }
                    
                    // حساب الرصيد الجديد
                    decimal newBalance = account.Balance;
                    
                    if (account.DebitCredit == "مدين")
                    {
                        // الحسابات المدينة: المدين يزيد الرصيد، الدائن ينقص الرصيد
                        newBalance += isDebit ? amount : -amount;
                    }
                    else
                    {
                        // الحسابات الدائنة: الدائن يزيد الرصيد، المدين ينقص الرصيد
                        newBalance += isDebit ? -amount : amount;
                    }
                    
                    // تحديث الرصيد
                    connection.Execute(@"
                        UPDATE Accounts 
                        SET Balance = @Balance, ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy 
                        WHERE Id = @Id",
                        new
                        {
                            Id = accountId,
                            Balance = newBalance,
                            ModifiedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                            ModifiedBy = AuthenticationService.CurrentUser?.Username
                        });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث رصيد الحساب: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// حساب إجمالي الأرصدة حسب النوع
        /// </summary>
        /// <param name="accountType">نوع الحساب</param>
        /// <returns>إجمالي الأرصدة</returns>
        public static decimal GetTotalBalanceByType(string accountType)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    var total = connection.QuerySingleOrDefault<decimal?>(@"
                        SELECT SUM(Balance) FROM Accounts 
                        WHERE AccountType = @AccountType AND IsActive = 1", 
                        new { AccountType = accountType });
                    
                    return total ?? 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب إجمالي الأرصدة: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// البحث في الحسابات
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة الحسابات المطابقة</returns>
        public static List<Account> SearchAccounts(string searchTerm)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    return connection.Query<Account>(@"
                        SELECT * FROM Accounts 
                        WHERE (AccountCode LIKE @SearchTerm 
                            OR AccountName LIKE @SearchTerm 
                            OR AccountNameEnglish LIKE @SearchTerm)
                            AND IsActive = 1 
                        ORDER BY AccountCode", 
                        new { SearchTerm = $"%{searchTerm}%" }).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث في الحسابات: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// التحقق من إمكانية حذف الحساب
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>true إذا كان يمكن حذف الحساب</returns>
        public static bool CanDeleteAccount(int accountId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // التحقق من وجود حسابات فرعية
                    var hasChildren = connection.QuerySingle<int>(@"
                        SELECT COUNT(*) FROM Accounts WHERE ParentAccountId = @AccountId", 
                        new { AccountId = accountId }) > 0;
                    
                    if (hasChildren)
                        return false;
                    
                    // التحقق من وجود قيود يومية
                    var hasJournalEntries = connection.QuerySingle<int>(@"
                        SELECT COUNT(*) FROM JournalEntryDetails WHERE AccountId = @AccountId", 
                        new { AccountId = accountId }) > 0;
                    
                    if (hasJournalEntries)
                        return false;
                    
                    // التحقق من وجود فواتير
                    var hasInvoices = connection.QuerySingle<int>(@"
                        SELECT COUNT(*) FROM Customers WHERE AccountId = @AccountId", 
                        new { AccountId = accountId }) > 0;
                    
                    if (hasInvoices)
                        return false;
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في التحقق من إمكانية حذف الحساب: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// إنشاء تقرير ميزان المراجعة
        /// </summary>
        /// <param name="asOfDate">كما في تاريخ</param>
        /// <returns>قائمة الحسابات مع الأرصدة</returns>
        public static List<TrialBalanceItem> GetTrialBalance(DateTime asOfDate)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    var accounts = connection.Query<Account>(@"
                        SELECT * FROM Accounts 
                        WHERE IsActive = 1 AND IsParent = 0 
                        ORDER BY AccountCode").ToList();
                    
                    var trialBalance = new List<TrialBalanceItem>();
                    
                    foreach (var account in accounts)
                    {
                        // حساب الرصيد كما في التاريخ المحدد
                        var balance = CalculateAccountBalanceAsOfDate(account.Id, asOfDate, connection);
                        
                        if (balance != 0) // إظهار الحسابات التي لها أرصدة فقط
                        {
                            var item = new TrialBalanceItem
                            {
                                AccountCode = account.AccountCode,
                                AccountName = account.AccountName,
                                AccountType = account.AccountType,
                                DebitBalance = account.DebitCredit == "مدين" && balance > 0 ? balance : 0,
                                CreditBalance = account.DebitCredit == "دائن" && balance > 0 ? balance : 0
                            };
                            
                            trialBalance.Add(item);
                        }
                    }
                    
                    return trialBalance;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء ميزان المراجعة: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// حساب رصيد الحساب كما في تاريخ محدد
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="asOfDate">كما في تاريخ</param>
        /// <param name="connection">اتصال قاعدة البيانات</param>
        /// <returns>رصيد الحساب</returns>
        private static decimal CalculateAccountBalanceAsOfDate(int accountId, DateTime asOfDate, System.Data.IDbConnection connection)
        {
            try
            {
                var result = connection.QueryFirstOrDefault<dynamic>(@"
                    SELECT 
                        COALESCE(SUM(DebitAmount), 0) as TotalDebit,
                        COALESCE(SUM(CreditAmount), 0) as TotalCredit
                    FROM JournalEntryDetails jed
                    INNER JOIN JournalEntries je ON jed.JournalEntryId = je.Id
                    WHERE jed.AccountId = @AccountId 
                        AND je.EntryDate <= @AsOfDate 
                        AND je.IsPosted = 1",
                    new { AccountId = accountId, AsOfDate = asOfDate.ToString("yyyy-MM-dd") });
                
                if (result == null)
                    return 0;
                
                decimal totalDebit = result.TotalDebit ?? 0;
                decimal totalCredit = result.TotalCredit ?? 0;
                
                // الحصول على طبيعة الحساب
                var account = connection.QueryFirstOrDefault<Account>(@"
                    SELECT DebitCredit FROM Accounts WHERE Id = @Id", new { Id = accountId });
                
                if (account == null)
                    return 0;
                
                // حساب الرصيد حسب طبيعة الحساب
                if (account.DebitCredit == "مدين")
                {
                    return totalDebit - totalCredit;
                }
                else
                {
                    return totalCredit - totalDebit;
                }
            }
            catch (Exception)
            {
                return 0;
            }
        }
    }
    
    /// <summary>
    /// عنصر ميزان المراجعة
    /// </summary>
    public class TrialBalanceItem
    {
        public string AccountCode { get; set; }
        public string AccountName { get; set; }
        public string AccountType { get; set; }
        public decimal DebitBalance { get; set; }
        public decimal CreditBalance { get; set; }
    }
}
