using System;
using System.Linq;
using Dapper;
using AccountingSystem.Data;
using AccountingSystem.Models;
using AccountingSystem.Utils;

namespace AccountingSystem.Services
{
    /// <summary>
    /// خدمة المصادقة وإدارة المستخدمين
    /// </summary>
    public class AuthenticationService
    {
        /// <summary>
        /// المستخدم الحالي المسجل دخوله
        /// </summary>
        public static User CurrentUser { get; private set; }
        
        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>true إذا تم تسجيل الدخول بنجاح</returns>
        public static bool Login(string username, string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                    return false;
                
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // البحث عن المستخدم
                    var user = connection.QueryFirstOrDefault<User>(@"
                        SELECT * FROM Users 
                        WHERE Username = @Username AND IsActive = 1",
                        new { Username = username });
                    
                    if (user == null)
                        return false;
                    
                    // التحقق من كلمة المرور
                    if (!SecurityHelper.VerifyPassword(password, user.PasswordHash))
                        return false;
                    
                    // تحديث تاريخ آخر تسجيل دخول
                    connection.Execute(@"
                        UPDATE Users 
                        SET LastLoginDate = @LastLoginDate 
                        WHERE Id = @Id",
                        new 
                        { 
                            LastLoginDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                            Id = user.Id 
                        });
                    
                    // حفظ المستخدم الحالي
                    CurrentUser = user;
                    CurrentUser.LastLoginDate = DateTime.Now;
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تسجيل الدخول: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public static void Logout()
        {
            CurrentUser = null;
        }
        
        /// <summary>
        /// التحقق من تسجيل دخول المستخدم
        /// </summary>
        /// <returns>true إذا كان المستخدم مسجل دخوله</returns>
        public static bool IsLoggedIn()
        {
            return CurrentUser != null;
        }
        
        /// <summary>
        /// التحقق من صلاحيات المستخدم
        /// </summary>
        /// <param name="requiredRole">الدور المطلوب</param>
        /// <returns>true إذا كان المستخدم يملك الصلاحية</returns>
        public static bool HasPermission(string requiredRole)
        {
            if (!IsLoggedIn())
                return false;
            
            // المدير له جميع الصلاحيات
            if (CurrentUser.Role == "Admin")
                return true;
            
            return CurrentUser.Role == requiredRole;
        }
        
        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>معرف المستخدم الجديد</returns>
        public static int CreateUser(User user, string password)
        {
            try
            {
                if (user == null)
                    throw new ArgumentException("بيانات المستخدم مطلوبة");
                
                if (string.IsNullOrWhiteSpace(password))
                    throw new ArgumentException("كلمة المرور مطلوبة");
                
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // التحقق من عدم وجود اسم المستخدم مسبقاً
                    var existingUser = connection.QueryFirstOrDefault<User>(@"
                        SELECT * FROM Users WHERE Username = @Username",
                        new { Username = user.Username });
                    
                    if (existingUser != null)
                        throw new Exception("اسم المستخدم موجود مسبقاً");
                    
                    // تشفير كلمة المرور
                    var hashedPassword = SecurityHelper.HashPassword(password);
                    
                    // إدراج المستخدم الجديد
                    var userId = connection.QuerySingle<int>(@"
                        INSERT INTO Users (Username, PasswordHash, FullName, Email, Role, IsActive, CreatedDate, CreatedBy)
                        VALUES (@Username, @PasswordHash, @FullName, @Email, @Role, @IsActive, @CreatedDate, @CreatedBy);
                        SELECT last_insert_rowid();",
                        new
                        {
                            Username = user.Username,
                            PasswordHash = hashedPassword,
                            FullName = user.FullName,
                            Email = user.Email,
                            Role = user.Role,
                            IsActive = user.IsActive ? 1 : 0,
                            CreatedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                            CreatedBy = CurrentUser?.Username ?? "System"
                        });
                    
                    return userId;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء المستخدم: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="oldPassword">كلمة المرور القديمة</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>true إذا تم تغيير كلمة المرور بنجاح</returns>
        public static bool ChangePassword(int userId, string oldPassword, string newPassword)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // الحصول على المستخدم
                    var user = connection.QueryFirstOrDefault<User>(@"
                        SELECT * FROM Users WHERE Id = @Id",
                        new { Id = userId });
                    
                    if (user == null)
                        return false;
                    
                    // التحقق من كلمة المرور القديمة
                    if (!SecurityHelper.VerifyPassword(oldPassword, user.PasswordHash))
                        return false;
                    
                    // تشفير كلمة المرور الجديدة
                    var hashedNewPassword = SecurityHelper.HashPassword(newPassword);
                    
                    // تحديث كلمة المرور
                    connection.Execute(@"
                        UPDATE Users 
                        SET PasswordHash = @PasswordHash, ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy
                        WHERE Id = @Id",
                        new
                        {
                            PasswordHash = hashedNewPassword,
                            ModifiedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                            ModifiedBy = CurrentUser?.Username ?? "System",
                            Id = userId
                        });
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تغيير كلمة المرور: {ex.Message}", ex);
            }
        }
    }
}
