using System;

namespace AccountingSystem.Models
{
    /// <summary>
    /// نموذج سند القبض
    /// </summary>
    public class Receipt
    {
        public int Id { get; set; }
        public string ReceiptNumber { get; set; }
        public DateTime ReceiptDate { get; set; }
        public int CustomerId { get; set; } // العميل
        public int? PayerAccountId { get; set; } // الحساب الدافع
        public int? ReceiverAccountId { get; set; } // الحساب المستلم
        public decimal Amount { get; set; }
        public string Currency { get; set; }
        public string PaymentMethod { get; set; } // نقدي، شيك، تحويل بنكي، بطاقة ائتمان
        public string ReferenceNumber { get; set; }
        public string Notes { get; set; }
        public string CheckNumber { get; set; }
        public DateTime? CheckDate { get; set; }
        public string BankName { get; set; }
        public string Description { get; set; }
        public string Reference { get; set; }
        public string Status { get; set; } // مسودة، مؤكد، ملغي
        public bool IsPosted { get; set; }
        public int? JournalEntryId { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }

        // خصائص إضافية للعرض
        public Customer Customer { get; set; }
        public Account PayerAccount { get; set; }
        public Account ReceiverAccount { get; set; }
        public JournalEntry JournalEntry { get; set; }
    }
}
