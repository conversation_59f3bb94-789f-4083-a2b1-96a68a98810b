using System;
using AccountingSystem.Data;
using AccountingSystem.Models;
using AccountingSystem.Utils;

namespace AccountingSystem.Services
{
    /// <summary>
    /// خدمة المصادقة المبسطة (بدون SQLite)
    /// </summary>
    public class SimpleAuthenticationService
    {
        /// <summary>
        /// المستخدم الحالي المسجل دخوله
        /// </summary>
        public static User CurrentUser { get; private set; }
        
        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>true إذا تم تسجيل الدخول بنجاح</returns>
        public static bool Login(string username, string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                    return false;
                
                // البحث عن المستخدم
                var user = SimpleDatabaseHelper.GetUserByUsername(username);
                
                if (user == null || !user.IsActive)
                    return false;
                
                // التحقق من كلمة المرور
                if (!SecurityHelper.VerifyPassword(password, user.PasswordHash))
                    return false;
                
                // تحديث تاريخ آخر تسجيل دخول
                SimpleDatabaseHelper.UpdateLastLoginDate(user.Id);
                
                // حفظ المستخدم الحالي
                CurrentUser = user;
                CurrentUser.LastLoginDate = DateTime.Now;
                
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تسجيل الدخول: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public static void Logout()
        {
            CurrentUser = null;
        }
        
        /// <summary>
        /// التحقق من تسجيل دخول المستخدم
        /// </summary>
        /// <returns>true إذا كان المستخدم مسجل دخوله</returns>
        public static bool IsLoggedIn()
        {
            return CurrentUser != null;
        }
        
        /// <summary>
        /// التحقق من صلاحيات المستخدم
        /// </summary>
        /// <param name="requiredRole">الدور المطلوب</param>
        /// <returns>true إذا كان المستخدم يملك الصلاحية</returns>
        public static bool HasPermission(string requiredRole)
        {
            if (!IsLoggedIn())
                return false;
            
            // المدير له جميع الصلاحيات
            if (CurrentUser.Role == "Admin")
                return true;
            
            return CurrentUser.Role == requiredRole;
        }
        
        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>معرف المستخدم الجديد</returns>
        public static int CreateUser(User user, string password)
        {
            try
            {
                if (user == null)
                    throw new ArgumentException("بيانات المستخدم مطلوبة");
                
                if (string.IsNullOrWhiteSpace(password))
                    throw new ArgumentException("كلمة المرور مطلوبة");
                
                // التحقق من عدم وجود اسم المستخدم مسبقاً
                var existingUser = SimpleDatabaseHelper.GetUserByUsername(user.Username);
                if (existingUser != null)
                    throw new Exception("اسم المستخدم موجود مسبقاً");
                
                // تشفير كلمة المرور
                user.PasswordHash = SecurityHelper.HashPassword(password);
                user.CreatedDate = DateTime.Now;
                user.CreatedBy = CurrentUser?.Username ?? "System";
                
                // حفظ المستخدم الجديد
                SimpleDatabaseHelper.SaveUser(user);
                
                return user.Id;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء المستخدم: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="oldPassword">كلمة المرور القديمة</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>true إذا تم تغيير كلمة المرور بنجاح</returns>
        public static bool ChangePassword(int userId, string oldPassword, string newPassword)
        {
            try
            {
                var users = SimpleDatabaseHelper.GetAllUsers();
                var user = users.Find(u => u.Id == userId);
                
                if (user == null)
                    return false;
                
                // التحقق من كلمة المرور القديمة
                if (!SecurityHelper.VerifyPassword(oldPassword, user.PasswordHash))
                    return false;
                
                // تشفير كلمة المرور الجديدة
                user.PasswordHash = SecurityHelper.HashPassword(newPassword);
                user.ModifiedDate = DateTime.Now;
                user.ModifiedBy = CurrentUser?.Username ?? "System";
                
                // حفظ التغييرات
                SimpleDatabaseHelper.SaveUser(user);
                
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تغيير كلمة المرور: {ex.Message}", ex);
            }
        }
    }
}
