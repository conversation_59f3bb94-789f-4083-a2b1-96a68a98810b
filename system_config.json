{"SystemSettings": {"ApplicationName": "نظام المحاسبة الذكي", "Version": "1.0.0", "CompanyName": "شركة التطوير", "DatabasePath": "database.db", "BackupPath": "Backups", "ReportsPath": "Reports", "LogsPath": "Logs"}, "SecuritySettings": {"PasswordMinLength": 6, "PasswordRequireNumbers": true, "PasswordRequireSpecialChars": false, "SessionTimeoutMinutes": 30, "MaxLoginAttempts": 3, "LockoutDurationMinutes": 15}, "DatabaseSettings": {"AutoBackup": true, "BackupIntervalDays": 1, "KeepBackupDays": 30, "ConnectionTimeout": 30, "CommandTimeout": 60}, "AISettings": {"EnableAI": true, "AnalysisFrequencyDays": 7, "RecommendationThreshold": 0.7, "ErrorDetectionEnabled": true, "TrendAnalysisMonths": 12}, "ReportSettings": {"DefaultCurrency": "ريال سعودي", "CurrencySymbol": "ر.س", "DateFormat": "yyyy/MM/dd", "NumberFormat": "N2", "ReportLanguage": "Arabic", "PrintMargins": {"Top": 20, "Bottom": 20, "Left": 20, "Right": 20}}, "UISettings": {"Theme": "<PERSON><PERSON><PERSON>", "FontFamily": "<PERSON><PERSON><PERSON>", "FontSize": 10, "RightToLeft": true, "ShowToolTips": true, "AutoSave": true, "AutoSaveIntervalMinutes": 5}, "EmailSettings": {"SMTPServer": "", "SMTPPort": 587, "EnableSSL": true, "Username": "", "Password": "", "FromEmail": "", "FromName": "نظام المحاسبة الذكي"}, "IntegrationSettings": {"EnableWebAPI": false, "APIPort": 8080, "EnableExternalIntegration": false, "ExternalAPIKey": "", "SyncInterval": 60}, "AuditSettings": {"EnableAuditLog": true, "LogUserActions": true, "LogDataChanges": true, "LogSystemEvents": true, "KeepLogDays": 90}, "PerformanceSettings": {"CacheEnabled": true, "CacheSizeLimit": 100, "DatabasePoolSize": 10, "QueryTimeout": 30, "EnablePaging": true, "PageSize": 50}, "NotificationSettings": {"EnableNotifications": true, "ShowSystemNotifications": true, "ShowWarnings": true, "ShowErrors": true, "NotificationDuration": 5000}, "DefaultAccounts": {"CashAccount": "1001", "BankAccount": "1002", "AccountsReceivable": "1101", "AccountsPayable": "2001", "SalesAccount": "4001", "PurchaseAccount": "5001", "SalesReturnAccount": "4002", "PurchaseReturnAccount": "5002"}, "TaxSettings": {"DefaultTaxRate": 15.0, "TaxAccountCode": "2101", "EnableTaxCalculation": true, "TaxRoundingMethod": "Round"}, "InventorySettings": {"EnableInventoryTracking": true, "CostingMethod": "FIFO", "AllowNegativeStock": false, "AutoUpdateCosts": true, "InventoryValuationMethod": "LowerOfCostOrMarket"}}