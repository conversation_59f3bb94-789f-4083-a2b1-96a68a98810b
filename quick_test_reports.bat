@echo off
chcp 65001 > nul
echo.
echo ========================================
echo اختبار سريع لتقارير الفواتير الجديدة
echo ========================================
echo.

echo 🚀 بناء واختبار النموذج التجريبي...
echo.

:: البحث عن مترجم C#
set "csc_path="
for %%i in (csc.exe) do set "csc_path=%%~$PATH:i"

if not defined csc_path (
    echo 🔍 البحث عن مترجم C# في مواقع Visual Studio...
    
    for /d %%d in ("C:\Program Files*\Microsoft Visual Studio\*\*\MSBuild\*\Bin\Roslyn") do (
        if exist "%%d\csc.exe" (
            set "csc_path=%%d\csc.exe"
            goto found_csc
        )
    )
    
    for /d %%d in ("C:\Program Files*\dotnet\sdk\*") do (
        if exist "%%d\Roslyn\bincore\csc.exe" (
            set "csc_path=%%d\Roslyn\bincore\csc.exe"
            goto found_csc
        )
    )
    
    :found_csc
)

if not defined csc_path (
    echo ❌ لم يتم العثور على مترجم C#
    echo.
    echo 💡 الحلول البديلة:
    echo 1. تثبيت Visual Studio Community (مجاني)
    echo 2. تثبيت .NET Framework SDK
    echo 3. استخدام Visual Studio Code مع C# extension
    echo.
    echo 🌐 روابط التحميل:
    echo • Visual Studio: https://visualstudio.microsoft.com/downloads/
    echo • .NET SDK: https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على مترجم C#: %csc_path%
echo.

echo 📦 تجميع النموذج التجريبي...

"%csc_path%" /target:exe ^
    /reference:System.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Linq.dll ^
    /out:TestInvoiceReports.exe ^
    test_new_reports_standalone.cs

if %errorlevel% neq 0 (
    echo ❌ فشل في تجميع النموذج التجريبي
    echo.
    echo 🔧 محاولة إصلاح المشكلة...
    
    :: محاولة بدون System.Linq
    "%csc_path%" /target:exe ^
        /reference:System.dll ^
        /reference:System.Windows.Forms.dll ^
        /reference:System.Drawing.dll ^
        /out:TestInvoiceReportsSimple.exe ^
        test_new_reports_standalone.cs
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في الإصلاح أيضاً
        echo.
        echo 📋 الأخطاء المحتملة:
        echo • إصدار .NET Framework غير متوافق
        echo • مراجع مفقودة
        echo • مشاكل في الكود
        echo.
        echo 💡 جرب:
        echo 1. فتح الملف في Visual Studio
        echo 2. استخدام Developer Command Prompt
        echo 3. تحديث .NET Framework
        echo.
        pause
        exit /b 1
    ) else (
        echo ✅ تم التجميع المبسط بنجاح!
        set "exe_name=TestInvoiceReportsSimple.exe"
    )
) else (
    echo ✅ تم تجميع النموذج التجريبي بنجاح!
    set "exe_name=TestInvoiceReports.exe"
)

echo.
echo 🎉 النموذج التجريبي جاهز للاختبار!
echo.
echo ========================================
echo ما ستراه في النموذج التجريبي:
echo ========================================
echo.
echo 📊 مؤشرات الأداء (KPIs):
echo • إجمالي الفواتير: 7 فواتير
echo • إجمالي المبلغ: 85,500 ريال
echo • المبلغ المدفوع: 32,500 ريال
echo • المبلغ المستحق: 53,000 ريال
echo.
echo 📋 التبويبات المتوفرة:
echo • التقرير الإجمالي: ملخص حسب العميل
echo • التقرير المفصل: قائمة شاملة بالفواتير
echo.
echo 🔍 الفلاتر المتاحة:
echo • فلترة حسب التاريخ (من/إلى)
echo • فلترة حسب العميل
echo • فلترة حسب حالة الفاتورة
echo.
echo 🎨 الميزات البصرية:
echo • تلوين الفواتير حسب الحالة:
echo   - أخضر: مدفوعة
echo   - أصفر: مدفوعة جزئياً
echo   - أحمر: متأخرة
echo • مؤشرات أداء ملونة
echo • واجهة عربية كاملة
echo.
echo ========================================
echo البيانات التجريبية المتضمنة:
echo ========================================
echo.
echo 👥 العملاء (5):
echo • شركة الأعمال المتقدمة
echo • مؤسسة التقنية الحديثة
echo • شركة الحلول المبتكرة
echo • مكتب الاستشارات الهندسية
echo • شركة التجارة العامة
echo.
echo 📄 الفواتير (7):
echo • INV-2024-001: 15,000 ريال (مدفوعة)
echo • INV-2024-002: 8,500 ريال (مدفوعة جزئياً)
echo • INV-2024-003: 12,000 ريال (غير مدفوعة)
echo • INV-2024-004: 6,500 ريال (متأخرة)
echo • INV-2024-005: 22,000 ريال (مدفوعة جزئياً)
echo • INV-2024-006: 3,500 ريال (مدفوعة)
echo • INV-2024-007: 18,000 ريال (مؤكدة)
echo.

if exist "%exe_name%" (
    echo 🚀 هل تريد تشغيل النموذج التجريبي الآن؟ (Y/N)
    set /p "run_choice=الاختيار: "
    
    if /i "%run_choice%"=="Y" (
        echo.
        echo 🎉 تشغيل النموذج التجريبي لتقارير الفواتير...
        echo.
        echo 💡 نصائح للاستخدام:
        echo • جرب تغيير الفلاتر واضغط "إنشاء التقرير"
        echo • انتقل بين التبويبات لرؤية التقارير المختلفة
        echo • لاحظ الألوان التفاعلية للفواتير
        echo • هذا مثال مبسط للنموذج الكامل
        echo.
        start "" "%exe_name%"
        
        echo ⏳ انتظار إغلاق النموذج التجريبي...
        timeout /t 3 > nul
        
        echo.
        echo 📝 تقييم التجربة:
        echo.
        echo ✅ إذا ظهر النموذج بشكل صحيح:
        echo   - النظام الجديد يعمل بنجاح
        echo   - يمكن تطبيق الإصلاحات على النظام الرئيسي
        echo   - التقارير ستظهر بالشكل المطلوب
        echo.
        echo ❌ إذا لم يظهر النموذج أو ظهرت أخطاء:
        echo   - راجع ملف TROUBLESHOOTING_INVOICE_REPORTS.md
        echo   - تحقق من متطلبات النظام
        echo   - جرب الحلول البديلة المقترحة
        echo.
    )
)

echo.
echo ========================================
echo الخطوات التالية:
echo ========================================
echo.
echo 1️⃣ إذا نجح النموذج التجريبي:
echo   • شغل update_invoice_reports_integration.bat
echo   • اختبر النظام الرئيسي
echo   • تأكد من ظهور التقارير الجديدة
echo.
echo 2️⃣ إذا فشل النموذج التجريبي:
echo   • راجع دليل استكشاف الأخطاء
echo   • تحقق من متطلبات النظام
echo   • جرب الحلول البديلة
echo.
echo 3️⃣ للحصول على النموذج الكامل:
echo   • استخدم النظام المتكامل
echo   • فعل جميع الميزات المتقدمة
echo   • استفد من التحليلات الشاملة
echo.
echo 📞 للدعم الفني:
echo   • راجع ملف INVOICE_REPORTS_COMPREHENSIVE_FIX.md
echo   • اتبع دليل TROUBLESHOOTING_INVOICE_REPORTS.md
echo   • تحقق من ملفات التوثيق الأخرى
echo.

echo ✅ انتهى الاختبار السريع!
echo.
pause
