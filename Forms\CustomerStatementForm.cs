using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AccountingSystem.Models;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج كشف حساب العميل التفصيلي
    /// </summary>
    public partial class CustomerStatementForm : Form
    {
        private ComboBox cmbCustomer;
        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private Button btnGenerate;
        private Button btnPrint;
        private Button btnExport;
        private Button btnPayment;
        private Button btnClose;

        private Label lblCustomer;
        private Label lblFromDate;
        private Label lblToDate;
        private Label lblCustomerInfo;
        private Label lblOpeningBalance;
        private Label lblTotalInvoices;
        private Label lblTotalPayments;
        private Label lblClosingBalance;

        private TextBox txtCustomerInfo;
        private TextBox txtOpeningBalance;
        private TextBox txtTotalInvoices;
        private TextBox txtTotalPayments;
        private TextBox txtClosingBalance;

        private DataGridView dgvTransactions;
        private TabControl tabControl;
        private TabPage tabInvoices;
        private TabPage tabPayments;
        private DataGridView dgvInvoices;
        private DataGridView dgvPayments;

        private List<Customer> customers;
        private List<Invoice> invoices;
        private List<Receipt> receipts;
        private Customer selectedCustomer;

        public CustomerStatementForm()
        {
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.cmbCustomer = new ComboBox();
            this.dtpFromDate = new DateTimePicker();
            this.dtpToDate = new DateTimePicker();
            this.btnGenerate = new Button();
            this.btnPrint = new Button();
            this.btnExport = new Button();
            this.btnPayment = new Button();
            this.btnClose = new Button();

            this.lblCustomer = new Label();
            this.lblFromDate = new Label();
            this.lblToDate = new Label();
            this.lblCustomerInfo = new Label();
            this.lblOpeningBalance = new Label();
            this.lblTotalInvoices = new Label();
            this.lblTotalPayments = new Label();
            this.lblClosingBalance = new Label();

            this.txtCustomerInfo = new TextBox();
            this.txtOpeningBalance = new TextBox();
            this.txtTotalInvoices = new TextBox();
            this.txtTotalPayments = new TextBox();
            this.txtClosingBalance = new TextBox();

            this.dgvTransactions = new DataGridView();
            this.tabControl = new TabControl();
            this.tabInvoices = new TabPage();
            this.tabPayments = new TabPage();
            this.dgvInvoices = new DataGridView();
            this.dgvPayments = new DataGridView();

            this.SuspendLayout();

            // Form
            this.Text = "كشف حساب العميل التفصيلي";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Customer Selection
            this.lblCustomer.Text = "العميل:";
            this.lblCustomer.Location = new Point(1100, 20);
            this.lblCustomer.Size = new Size(60, 23);
            this.lblCustomer.TextAlign = ContentAlignment.MiddleRight;

            this.cmbCustomer.Location = new Point(900, 20);
            this.cmbCustomer.Size = new Size(180, 23);
            this.cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbCustomer.SelectedIndexChanged += CmbCustomer_SelectedIndexChanged;

            // Date Range
            this.lblFromDate.Text = "من تاريخ:";
            this.lblFromDate.Location = new Point(820, 20);
            this.lblFromDate.Size = new Size(60, 23);
            this.lblFromDate.TextAlign = ContentAlignment.MiddleRight;

            this.dtpFromDate.Location = new Point(690, 20);
            this.dtpFromDate.Size = new Size(120, 23);
            this.dtpFromDate.Format = DateTimePickerFormat.Short;
            this.dtpFromDate.Value = DateTime.Now.AddMonths(-3);

            this.lblToDate.Text = "إلى تاريخ:";
            this.lblToDate.Location = new Point(630, 20);
            this.lblToDate.Size = new Size(60, 23);
            this.lblToDate.TextAlign = ContentAlignment.MiddleRight;

            this.dtpToDate.Location = new Point(500, 20);
            this.dtpToDate.Size = new Size(120, 23);
            this.dtpToDate.Format = DateTimePickerFormat.Short;
            this.dtpToDate.Value = DateTime.Now;

            // Action Buttons
            this.btnGenerate.Text = "إنشاء الكشف";
            this.btnGenerate.Location = new Point(1050, 60);
            this.btnGenerate.Size = new Size(100, 30);
            this.btnGenerate.BackColor = Color.FromArgb(25, 118, 210);
            this.btnGenerate.ForeColor = Color.White;
            this.btnGenerate.FlatStyle = FlatStyle.Flat;
            this.btnGenerate.Click += BtnGenerate_Click;

            this.btnPrint.Text = "طباعة";
            this.btnPrint.Location = new Point(930, 60);
            this.btnPrint.Size = new Size(100, 30);
            this.btnPrint.BackColor = Color.FromArgb(103, 58, 183);
            this.btnPrint.ForeColor = Color.White;
            this.btnPrint.FlatStyle = FlatStyle.Flat;
            this.btnPrint.Click += BtnPrint_Click;

            this.btnExport.Text = "تصدير";
            this.btnExport.Location = new Point(810, 60);
            this.btnExport.Size = new Size(100, 30);
            this.btnExport.BackColor = Color.FromArgb(76, 175, 80);
            this.btnExport.ForeColor = Color.White;
            this.btnExport.FlatStyle = FlatStyle.Flat;
            this.btnExport.Click += BtnExport_Click;

            this.btnPayment.Text = "تسجيل دفعة";
            this.btnPayment.Location = new Point(690, 60);
            this.btnPayment.Size = new Size(100, 30);
            this.btnPayment.BackColor = Color.FromArgb(255, 152, 0);
            this.btnPayment.ForeColor = Color.White;
            this.btnPayment.FlatStyle = FlatStyle.Flat;
            this.btnPayment.Click += BtnPayment_Click;

            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(20, 60);
            this.btnClose.Size = new Size(100, 30);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Click += BtnClose_Click;

            // Customer Info Panel
            this.lblCustomerInfo.Text = "معلومات العميل:";
            this.lblCustomerInfo.Location = new Point(1050, 110);
            this.lblCustomerInfo.Size = new Size(100, 23);
            this.lblCustomerInfo.TextAlign = ContentAlignment.MiddleRight;
            this.lblCustomerInfo.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.txtCustomerInfo.Location = new Point(20, 110);
            this.txtCustomerInfo.Size = new Size(1000, 60);
            this.txtCustomerInfo.Multiline = true;
            this.txtCustomerInfo.ReadOnly = true;
            this.txtCustomerInfo.BackColor = Color.LightBlue;
            this.txtCustomerInfo.Font = new Font("Tahoma", 10F);

            // Summary Panel
            this.lblOpeningBalance.Text = "الرصيد الافتتاحي:";
            this.lblOpeningBalance.Location = new Point(1050, 190);
            this.lblOpeningBalance.Size = new Size(100, 23);
            this.lblOpeningBalance.TextAlign = ContentAlignment.MiddleRight;
            this.lblOpeningBalance.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.txtOpeningBalance.Location = new Point(900, 190);
            this.txtOpeningBalance.Size = new Size(120, 23);
            this.txtOpeningBalance.ReadOnly = true;
            this.txtOpeningBalance.BackColor = Color.LightGray;
            this.txtOpeningBalance.TextAlign = HorizontalAlignment.Right;
            this.txtOpeningBalance.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.lblTotalInvoices.Text = "إجمالي الفواتير:";
            this.lblTotalInvoices.Location = new Point(750, 190);
            this.lblTotalInvoices.Size = new Size(100, 23);
            this.lblTotalInvoices.TextAlign = ContentAlignment.MiddleRight;
            this.lblTotalInvoices.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.txtTotalInvoices.Location = new Point(600, 190);
            this.txtTotalInvoices.Size = new Size(120, 23);
            this.txtTotalInvoices.ReadOnly = true;
            this.txtTotalInvoices.BackColor = Color.LightGray;
            this.txtTotalInvoices.TextAlign = HorizontalAlignment.Right;
            this.txtTotalInvoices.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.lblTotalPayments.Text = "إجمالي المدفوعات:";
            this.lblTotalPayments.Location = new Point(450, 190);
            this.lblTotalPayments.Size = new Size(100, 23);
            this.lblTotalPayments.TextAlign = ContentAlignment.MiddleRight;
            this.lblTotalPayments.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.txtTotalPayments.Location = new Point(300, 190);
            this.txtTotalPayments.Size = new Size(120, 23);
            this.txtTotalPayments.ReadOnly = true;
            this.txtTotalPayments.BackColor = Color.LightGray;
            this.txtTotalPayments.TextAlign = HorizontalAlignment.Right;
            this.txtTotalPayments.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.lblClosingBalance.Text = "الرصيد الختامي:";
            this.lblClosingBalance.Location = new Point(150, 190);
            this.lblClosingBalance.Size = new Size(100, 23);
            this.lblClosingBalance.TextAlign = ContentAlignment.MiddleRight;
            this.lblClosingBalance.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.txtClosingBalance.Location = new Point(20, 190);
            this.txtClosingBalance.Size = new Size(120, 23);
            this.txtClosingBalance.ReadOnly = true;
            this.txtClosingBalance.BackColor = Color.LightYellow;
            this.txtClosingBalance.TextAlign = HorizontalAlignment.Right;
            this.txtClosingBalance.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            // Tab Control
            this.tabControl.Location = new Point(20, 230);
            this.tabControl.Size = new Size(1160, 500);
            this.tabControl.Font = new Font("Tahoma", 10F);

            // Transactions Tab
            this.tabInvoices.Text = "الفواتير";
            this.tabInvoices.UseVisualStyleBackColor = true;

            this.dgvInvoices.Location = new Point(10, 10);
            this.dgvInvoices.Size = new Size(1140, 460);
            this.dgvInvoices.AllowUserToAddRows = false;
            this.dgvInvoices.AllowUserToDeleteRows = false;
            this.dgvInvoices.ReadOnly = true;
            this.dgvInvoices.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvInvoices.RightToLeft = RightToLeft.Yes;
            this.dgvInvoices.Font = new Font("Tahoma", 10F);

            this.tabInvoices.Controls.Add(this.dgvInvoices);

            // Payments Tab
            this.tabPayments.Text = "المدفوعات";
            this.tabPayments.UseVisualStyleBackColor = true;

            this.dgvPayments.Location = new Point(10, 10);
            this.dgvPayments.Size = new Size(1140, 460);
            this.dgvPayments.AllowUserToAddRows = false;
            this.dgvPayments.AllowUserToDeleteRows = false;
            this.dgvPayments.ReadOnly = true;
            this.dgvPayments.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvPayments.RightToLeft = RightToLeft.Yes;
            this.dgvPayments.Font = new Font("Tahoma", 10F);

            this.tabPayments.Controls.Add(this.dgvPayments);

            this.tabControl.TabPages.Add(this.tabInvoices);
            this.tabControl.TabPages.Add(this.tabPayments);

            SetupDataGridViews();

            // Add controls to form
            this.Controls.Add(this.lblCustomer);
            this.Controls.Add(this.cmbCustomer);
            this.Controls.Add(this.lblFromDate);
            this.Controls.Add(this.dtpFromDate);
            this.Controls.Add(this.lblToDate);
            this.Controls.Add(this.dtpToDate);
            this.Controls.Add(this.btnGenerate);
            this.Controls.Add(this.btnPrint);
            this.Controls.Add(this.btnExport);
            this.Controls.Add(this.btnPayment);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.lblCustomerInfo);
            this.Controls.Add(this.txtCustomerInfo);
            this.Controls.Add(this.lblOpeningBalance);
            this.Controls.Add(this.txtOpeningBalance);
            this.Controls.Add(this.lblTotalInvoices);
            this.Controls.Add(this.txtTotalInvoices);
            this.Controls.Add(this.lblTotalPayments);
            this.Controls.Add(this.txtTotalPayments);
            this.Controls.Add(this.lblClosingBalance);
            this.Controls.Add(this.txtClosingBalance);
            this.Controls.Add(this.tabControl);

            this.ResumeLayout(false);
        }

        private void SetupDataGridViews()
        {
            SetupInvoicesGrid();
            SetupPaymentsGrid();
        }

        private void SetupInvoicesGrid()
        {
            dgvInvoices.Columns.Clear();

            dgvInvoices.Columns.Add("InvoiceNumber", "رقم الفاتورة");
            dgvInvoices.Columns["InvoiceNumber"].Width = 120;

            dgvInvoices.Columns.Add("InvoiceDate", "التاريخ");
            dgvInvoices.Columns["InvoiceDate"].Width = 100;
            dgvInvoices.Columns["InvoiceDate"].DefaultCellStyle.Format = "yyyy/MM/dd";

            dgvInvoices.Columns.Add("DueDate", "تاريخ الاستحقاق");
            dgvInvoices.Columns["DueDate"].Width = 120;
            dgvInvoices.Columns["DueDate"].DefaultCellStyle.Format = "yyyy/MM/dd";

            dgvInvoices.Columns.Add("TotalAmount", "إجمالي الفاتورة");
            dgvInvoices.Columns["TotalAmount"].Width = 120;
            dgvInvoices.Columns["TotalAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvInvoices.Columns["TotalAmount"].DefaultCellStyle.Format = "N2";

            dgvInvoices.Columns.Add("PaidAmount", "المبلغ المدفوع");
            dgvInvoices.Columns["PaidAmount"].Width = 120;
            dgvInvoices.Columns["PaidAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvInvoices.Columns["PaidAmount"].DefaultCellStyle.Format = "N2";

            dgvInvoices.Columns.Add("RemainingAmount", "المبلغ المتبقي");
            dgvInvoices.Columns["RemainingAmount"].Width = 120;
            dgvInvoices.Columns["RemainingAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvInvoices.Columns["RemainingAmount"].DefaultCellStyle.Format = "N2";

            dgvInvoices.Columns.Add("Status", "الحالة");
            dgvInvoices.Columns["Status"].Width = 100;

            dgvInvoices.Columns.Add("DaysOverdue", "أيام التأخير");
            dgvInvoices.Columns["DaysOverdue"].Width = 100;
            dgvInvoices.Columns["DaysOverdue"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            dgvInvoices.Columns.Add("Notes", "ملاحظات");
            dgvInvoices.Columns["Notes"].Width = 200;
        }

        private void SetupPaymentsGrid()
        {
            dgvPayments.Columns.Clear();

            dgvPayments.Columns.Add("ReceiptNumber", "رقم السند");
            dgvPayments.Columns["ReceiptNumber"].Width = 120;

            dgvPayments.Columns.Add("ReceiptDate", "التاريخ");
            dgvPayments.Columns["ReceiptDate"].Width = 100;
            dgvPayments.Columns["ReceiptDate"].DefaultCellStyle.Format = "yyyy/MM/dd";

            dgvPayments.Columns.Add("Amount", "المبلغ");
            dgvPayments.Columns["Amount"].Width = 120;
            dgvPayments.Columns["Amount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvPayments.Columns["Amount"].DefaultCellStyle.Format = "N2";

            dgvPayments.Columns.Add("PaymentMethod", "طريقة الدفع");
            dgvPayments.Columns["PaymentMethod"].Width = 120;

            dgvPayments.Columns.Add("ReferenceNumber", "رقم المرجع");
            dgvPayments.Columns["ReferenceNumber"].Width = 120;

            dgvPayments.Columns.Add("InvoiceNumber", "رقم الفاتورة");
            dgvPayments.Columns["InvoiceNumber"].Width = 120;

            dgvPayments.Columns.Add("Status", "الحالة");
            dgvPayments.Columns["Status"].Width = 80;

            dgvPayments.Columns.Add("Notes", "ملاحظات");
            dgvPayments.Columns["Notes"].Width = 200;
        }

        private void LoadData()
        {
            try
            {
                LoadCustomers();
                LoadSampleData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadCustomers()
        {
            // تحميل العملاء (بيانات تجريبية)
            customers = new List<Customer>
            {
                new Customer { Id = 1, CustomerName = "أحمد محمد علي", CustomerCode = "C001", Phone = "0501234567", Email = "<EMAIL>", Address = "الرياض، المملكة العربية السعودية", CurrentBalance = 5000.00m },
                new Customer { Id = 2, CustomerName = "فاطمة عبدالله", CustomerCode = "C002", Phone = "0507654321", Email = "<EMAIL>", Address = "جدة، المملكة العربية السعودية", CurrentBalance = -2500.00m },
                new Customer { Id = 3, CustomerName = "محمد سعد الدين", CustomerCode = "C003", Phone = "0551122334", Email = "<EMAIL>", Address = "الدمام، المملكة العربية السعودية", CurrentBalance = 0.00m },
                new Customer { Id = 4, CustomerName = "نورا أحمد", CustomerCode = "C004", Phone = "0554433221", Email = "<EMAIL>", Address = "مكة المكرمة، المملكة العربية السعودية", CurrentBalance = 12000.00m },
                new Customer { Id = 5, CustomerName = "خالد العتيبي", CustomerCode = "C005", Phone = "0556677889", Email = "<EMAIL>", Address = "المدينة المنورة، المملكة العربية السعودية", CurrentBalance = -800.00m }
            };

            cmbCustomer.Items.Clear();
            foreach (var customer in customers)
            {
                cmbCustomer.Items.Add($"{customer.CustomerCode} - {customer.CustomerName}");
            }

            if (cmbCustomer.Items.Count > 0)
                cmbCustomer.SelectedIndex = 0;
        }

        private void LoadSampleData()
        {
            // إنشاء بيانات تجريبية للفواتير
            invoices = new List<Invoice>
            {
                new Invoice { Id = 1, InvoiceNumber = "INV001", InvoiceDate = DateTime.Now.AddDays(-30), DueDate = DateTime.Now.AddDays(-15), CustomerId = 1, TotalAmount = 8000.00m, PaidAmount = 3000.00m, Status = "جزئي", Notes = "فاتورة مبيعات" },
                new Invoice { Id = 2, InvoiceNumber = "INV002", InvoiceDate = DateTime.Now.AddDays(-25), DueDate = DateTime.Now.AddDays(-10), CustomerId = 1, TotalAmount = 5500.00m, PaidAmount = 5500.00m, Status = "مدفوع", Notes = "فاتورة خدمات" },
                new Invoice { Id = 3, InvoiceNumber = "INV003", InvoiceDate = DateTime.Now.AddDays(-20), DueDate = DateTime.Now.AddDays(-5), CustomerId = 2, TotalAmount = 3200.00m, PaidAmount = 700.00m, Status = "جزئي", Notes = "فاتورة مواد" },
                new Invoice { Id = 4, InvoiceNumber = "INV004", InvoiceDate = DateTime.Now.AddDays(-15), DueDate = DateTime.Now, CustomerId = 2, TotalAmount = 4800.00m, PaidAmount = 0.00m, Status = "غير مدفوع", Notes = "فاتورة معدات" },
                new Invoice { Id = 5, InvoiceNumber = "INV005", InvoiceDate = DateTime.Now.AddDays(-10), DueDate = DateTime.Now.AddDays(5), CustomerId = 3, TotalAmount = 2100.00m, PaidAmount = 2100.00m, Status = "مدفوع", Notes = "فاتورة استشارات" },
                new Invoice { Id = 6, InvoiceNumber = "INV006", InvoiceDate = DateTime.Now.AddDays(-5), DueDate = DateTime.Now.AddDays(10), CustomerId = 4, TotalAmount = 15000.00m, PaidAmount = 3000.00m, Status = "جزئي", Notes = "فاتورة مشروع كبير" }
            };

            // إنشاء بيانات تجريبية للمدفوعات
            receipts = new List<Receipt>
            {
                new Receipt { Id = 1, ReceiptNumber = "R001", ReceiptDate = DateTime.Now.AddDays(-28), CustomerId = 1, Amount = 3000.00m, PaymentMethod = "نقدي", ReferenceNumber = "REF001", Notes = "دفعة من فاتورة INV001", Status = "مؤكد" },
                new Receipt { Id = 2, ReceiptNumber = "R002", ReceiptDate = DateTime.Now.AddDays(-23), CustomerId = 1, Amount = 5500.00m, PaymentMethod = "تحويل بنكي", ReferenceNumber = "TRF123", Notes = "سداد كامل لفاتورة INV002", Status = "مؤكد" },
                new Receipt { Id = 3, ReceiptNumber = "R003", ReceiptDate = DateTime.Now.AddDays(-18), CustomerId = 2, Amount = 700.00m, PaymentMethod = "شيك", ReferenceNumber = "CHK456", Notes = "دفعة جزئية من INV003", Status = "مؤكد" },
                new Receipt { Id = 4, ReceiptNumber = "R004", ReceiptDate = DateTime.Now.AddDays(-8), CustomerId = 3, Amount = 2100.00m, PaymentMethod = "بطاقة ائتمان", ReferenceNumber = "CC789", Notes = "سداد كامل لفاتورة INV005", Status = "مؤكد" },
                new Receipt { Id = 5, ReceiptNumber = "R005", ReceiptDate = DateTime.Now.AddDays(-3), CustomerId = 4, Amount = 3000.00m, PaymentMethod = "نقدي", ReferenceNumber = "REF005", Notes = "دفعة مقدمة من INV006", Status = "مؤكد" }
            };
        }

        private void CmbCustomer_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbCustomer.SelectedIndex >= 0)
            {
                selectedCustomer = customers[cmbCustomer.SelectedIndex];
                DisplayCustomerInfo();
            }
        }

        private void DisplayCustomerInfo()
        {
            if (selectedCustomer != null)
            {
                txtCustomerInfo.Text = $"كود العميل: {selectedCustomer.CustomerCode}\r\n" +
                                      $"اسم العميل: {selectedCustomer.CustomerName}\r\n" +
                                      $"الهاتف: {selectedCustomer.Phone}\r\n" +
                                      $"البريد الإلكتروني: {selectedCustomer.Email}\r\n" +
                                      $"العنوان: {selectedCustomer.Address}";
            }
        }

        private void BtnGenerate_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedCustomer == null)
                {
                    MessageBox.Show("يرجى اختيار عميل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                GenerateStatement();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء الكشف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateStatement()
        {
            var customerInvoices = invoices.Where(i => i.CustomerId == selectedCustomer.Id &&
                                                      i.InvoiceDate >= dtpFromDate.Value.Date &&
                                                      i.InvoiceDate <= dtpToDate.Value.Date).ToList();

            var customerReceipts = receipts.Where(r => r.CustomerId == selectedCustomer.Id &&
                                                      r.ReceiptDate >= dtpFromDate.Value.Date &&
                                                      r.ReceiptDate <= dtpToDate.Value.Date).ToList();

            LoadInvoicesGrid(customerInvoices);
            LoadPaymentsGrid(customerReceipts);
            CalculateSummary(customerInvoices, customerReceipts);
        }

        private void LoadInvoicesGrid(List<Invoice> customerInvoices)
        {
            dgvInvoices.Rows.Clear();

            foreach (var invoice in customerInvoices.OrderByDescending(i => i.InvoiceDate))
            {
                var rowIndex = dgvInvoices.Rows.Add();
                var row = dgvInvoices.Rows[rowIndex];

                var remainingAmount = invoice.TotalAmount - invoice.PaidAmount;
                var daysOverdue = invoice.DueDate < DateTime.Now ? (DateTime.Now - invoice.DueDate).Days : 0;

                row.Cells["InvoiceNumber"].Value = invoice.InvoiceNumber;
                row.Cells["InvoiceDate"].Value = invoice.InvoiceDate;
                row.Cells["DueDate"].Value = invoice.DueDate;
                row.Cells["TotalAmount"].Value = invoice.TotalAmount;
                row.Cells["PaidAmount"].Value = invoice.PaidAmount;
                row.Cells["RemainingAmount"].Value = remainingAmount;
                row.Cells["Status"].Value = invoice.Status;
                row.Cells["DaysOverdue"].Value = daysOverdue > 0 ? daysOverdue.ToString() : "";
                row.Cells["Notes"].Value = invoice.Notes;

                // تلوين الصفوف حسب الحالة
                if (invoice.Status == "مدفوع")
                {
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else if (invoice.Status == "جزئي")
                {
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                }
                else if (invoice.Status == "غير مدفوع")
                {
                    if (daysOverdue > 0)
                    {
                        row.DefaultCellStyle.BackColor = Color.LightCoral; // متأخر
                    }
                    else
                    {
                        row.DefaultCellStyle.BackColor = Color.LightBlue; // لم يحن موعد الاستحقاق
                    }
                }
            }
        }

        private void LoadPaymentsGrid(List<Receipt> customerReceipts)
        {
            dgvPayments.Rows.Clear();

            foreach (var receipt in customerReceipts.OrderByDescending(r => r.ReceiptDate))
            {
                var rowIndex = dgvPayments.Rows.Add();
                var row = dgvPayments.Rows[rowIndex];

                // البحث عن رقم الفاتورة المرتبطة (من الملاحظات)
                string invoiceNumber = "";
                if (!string.IsNullOrEmpty(receipt.Notes) && receipt.Notes.Contains("INV"))
                {
                    var words = receipt.Notes.Split(' ');
                    foreach (var word in words)
                    {
                        if (word.StartsWith("INV"))
                        {
                            invoiceNumber = word;
                            break;
                        }
                    }
                }

                row.Cells["ReceiptNumber"].Value = receipt.ReceiptNumber;
                row.Cells["ReceiptDate"].Value = receipt.ReceiptDate;
                row.Cells["Amount"].Value = receipt.Amount;
                row.Cells["PaymentMethod"].Value = receipt.PaymentMethod;
                row.Cells["ReferenceNumber"].Value = receipt.ReferenceNumber;
                row.Cells["InvoiceNumber"].Value = invoiceNumber;
                row.Cells["Status"].Value = receipt.Status;
                row.Cells["Notes"].Value = receipt.Notes;

                // تلوين الصفوف حسب الحالة
                if (receipt.Status == "مؤكد")
                {
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else if (receipt.Status == "مسودة")
                {
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                }
            }
        }

        private void CalculateSummary(List<Invoice> customerInvoices, List<Receipt> customerReceipts)
        {
            decimal openingBalance = selectedCustomer.CurrentBalance;
            decimal totalInvoices = customerInvoices.Sum(i => i.TotalAmount);
            decimal totalPayments = customerReceipts.Where(r => r.Status == "مؤكد").Sum(r => r.Amount);
            decimal closingBalance = openingBalance + totalInvoices - totalPayments;

            txtOpeningBalance.Text = openingBalance.ToString("N2");
            txtTotalInvoices.Text = totalInvoices.ToString("N2");
            txtTotalPayments.Text = totalPayments.ToString("N2");
            txtClosingBalance.Text = closingBalance.ToString("N2");

            // تلوين الرصيد الختامي
            if (closingBalance > 0)
            {
                txtClosingBalance.BackColor = Color.LightGreen;
            }
            else if (closingBalance < 0)
            {
                txtClosingBalance.BackColor = Color.LightCoral;
            }
            else
            {
                txtClosingBalance.BackColor = Color.LightGray;
            }
        }

        private void BtnPayment_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedCustomer == null)
                {
                    MessageBox.Show("يرجى اختيار عميل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var paymentForm = new CustomerPaymentForm(selectedCustomer, invoices.Where(i => i.CustomerId == selectedCustomer.Id).ToList());
                if (paymentForm.ShowDialog() == DialogResult.OK)
                {
                    // إضافة السند الجديد
                    var newReceipt = paymentForm.Receipt;
                    newReceipt.Id = receipts.Count > 0 ? receipts.Max(r => r.Id) + 1 : 1;
                    newReceipt.ReceiptNumber = $"R{newReceipt.Id:000}";
                    receipts.Add(newReceipt);

                    // تحديث الفاتورة إذا كان السداد مرتبط بفاتورة
                    if (paymentForm.SelectedInvoiceId > 0)
                    {
                        var invoice = invoices.FirstOrDefault(i => i.Id == paymentForm.SelectedInvoiceId);
                        if (invoice != null)
                        {
                            invoice.PaidAmount += newReceipt.Amount;
                            if (invoice.PaidAmount >= invoice.TotalAmount)
                            {
                                invoice.Status = "مدفوع";
                            }
                            else if (invoice.PaidAmount > 0)
                            {
                                invoice.Status = "جزئي";
                            }
                        }
                    }

                    // تحديث رصيد العميل
                    selectedCustomer.CurrentBalance -= newReceipt.Amount;

                    // إعادة إنشاء الكشف
                    GenerateStatement();
                    DisplayCustomerInfo();

                    MessageBox.Show("تم تسجيل الدفعة بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدفعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedCustomer == null)
                {
                    MessageBox.Show("يرجى اختيار عميل وإنشاء الكشف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                PrintStatement();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الكشف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintStatement()
        {
            var reportLines = new List<string>();

            // عنوان التقرير
            reportLines.Add("كشف حساب العميل التفصيلي");
            reportLines.Add("==========================");
            reportLines.Add($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
            reportLines.Add($"الفترة: من {dtpFromDate.Value:yyyy/MM/dd} إلى {dtpToDate.Value:yyyy/MM/dd}");
            reportLines.Add("");

            // معلومات العميل
            reportLines.Add("معلومات العميل:");
            reportLines.Add("================");
            reportLines.Add($"كود العميل: {selectedCustomer.CustomerCode}");
            reportLines.Add($"اسم العميل: {selectedCustomer.CustomerName}");
            reportLines.Add($"الهاتف: {selectedCustomer.Phone}");
            reportLines.Add($"البريد الإلكتروني: {selectedCustomer.Email}");
            reportLines.Add($"العنوان: {selectedCustomer.Address}");
            reportLines.Add("");

            // ملخص الحساب
            reportLines.Add("ملخص الحساب:");
            reportLines.Add("=============");
            reportLines.Add($"الرصيد الافتتاحي: {txtOpeningBalance.Text} ريال");
            reportLines.Add($"إجمالي الفواتير: {txtTotalInvoices.Text} ريال");
            reportLines.Add($"إجمالي المدفوعات: {txtTotalPayments.Text} ريال");
            reportLines.Add($"الرصيد الختامي: {txtClosingBalance.Text} ريال");
            reportLines.Add("");

            // تفاصيل الفواتير
            reportLines.Add("تفاصيل الفواتير:");
            reportLines.Add("================");
            reportLines.Add("رقم الفاتورة    التاريخ      الاستحقاق    إجمالي الفاتورة    المدفوع    المتبقي    الحالة");
            reportLines.Add("===================================================================================");

            var customerInvoices = invoices.Where(i => i.CustomerId == selectedCustomer.Id &&
                                                      i.InvoiceDate >= dtpFromDate.Value.Date &&
                                                      i.InvoiceDate <= dtpToDate.Value.Date).ToList();

            foreach (var invoice in customerInvoices.OrderByDescending(i => i.InvoiceDate))
            {
                var remainingAmount = invoice.TotalAmount - invoice.PaidAmount;
                string line = $"{invoice.InvoiceNumber.PadRight(15)} {invoice.InvoiceDate:yyyy/MM/dd} " +
                             $"{invoice.DueDate:yyyy/MM/dd}    {invoice.TotalAmount.ToString("N2").PadLeft(15)} " +
                             $"{invoice.PaidAmount.ToString("N2").PadLeft(10)} {remainingAmount.ToString("N2").PadLeft(10)} " +
                             $"{invoice.Status}";
                reportLines.Add(line);
            }

            reportLines.Add("===================================================================================");

            // تفاصيل المدفوعات
            reportLines.Add("");
            reportLines.Add("تفاصيل المدفوعات:");
            reportLines.Add("=================");
            reportLines.Add("رقم السند    التاريخ      المبلغ        طريقة الدفع    رقم المرجع    الحالة");
            reportLines.Add("=========================================================================");

            var customerReceipts = receipts.Where(r => r.CustomerId == selectedCustomer.Id &&
                                                      r.ReceiptDate >= dtpFromDate.Value.Date &&
                                                      r.ReceiptDate <= dtpToDate.Value.Date).ToList();

            foreach (var receipt in customerReceipts.OrderByDescending(r => r.ReceiptDate))
            {
                string line = $"{receipt.ReceiptNumber.PadRight(12)} {receipt.ReceiptDate:yyyy/MM/dd} " +
                             $"{receipt.Amount.ToString("N2").PadLeft(12)} {receipt.PaymentMethod.PadRight(15)} " +
                             $"{receipt.ReferenceNumber.PadRight(15)} {receipt.Status}";
                reportLines.Add(line);
            }

            reportLines.Add("=========================================================================");

            // حفظ التقرير مؤقتاً وطباعته
            string tempFile = System.IO.Path.GetTempFileName() + ".txt";
            System.IO.File.WriteAllLines(tempFile, reportLines, System.Text.Encoding.UTF8);

            try
            {
                System.Diagnostics.Process.Start("notepad.exe", "/p " + tempFile);
            }
            catch
            {
                MessageBox.Show($"تم حفظ كشف الحساب في: {tempFile}", "كشف حساب العميل",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedCustomer == null)
                {
                    MessageBox.Show("يرجى اختيار عميل وإنشاء الكشف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "ملفات Excel (*.csv)|*.csv|ملفات نصية (*.txt)|*.txt";
                saveDialog.Title = "تصدير كشف حساب العميل";
                saveDialog.FileName = $"كشف_حساب_{selectedCustomer.CustomerCode}_{DateTime.Now:yyyyMMdd}.csv";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToCSV(saveDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير الكشف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToCSV(string filePath)
        {
            var lines = new List<string>();

            // معلومات العميل
            lines.Add($"كشف حساب العميل التفصيلي");
            lines.Add($"كود العميل,{selectedCustomer.CustomerCode}");
            lines.Add($"اسم العميل,{selectedCustomer.CustomerName}");
            lines.Add($"الفترة,من {dtpFromDate.Value:yyyy/MM/dd} إلى {dtpToDate.Value:yyyy/MM/dd}");
            lines.Add("");

            // ملخص الحساب
            lines.Add("ملخص الحساب");
            lines.Add($"الرصيد الافتتاحي,{txtOpeningBalance.Text}");
            lines.Add($"إجمالي الفواتير,{txtTotalInvoices.Text}");
            lines.Add($"إجمالي المدفوعات,{txtTotalPayments.Text}");
            lines.Add($"الرصيد الختامي,{txtClosingBalance.Text}");
            lines.Add("");

            // تفاصيل الفواتير
            lines.Add("تفاصيل الفواتير");
            lines.Add("رقم الفاتورة,التاريخ,تاريخ الاستحقاق,إجمالي الفاتورة,المبلغ المدفوع,المبلغ المتبقي,الحالة,ملاحظات");

            var customerInvoices = invoices.Where(i => i.CustomerId == selectedCustomer.Id &&
                                                      i.InvoiceDate >= dtpFromDate.Value.Date &&
                                                      i.InvoiceDate <= dtpToDate.Value.Date).ToList();

            foreach (var invoice in customerInvoices.OrderByDescending(i => i.InvoiceDate))
            {
                var remainingAmount = invoice.TotalAmount - invoice.PaidAmount;
                lines.Add($"{invoice.InvoiceNumber},{invoice.InvoiceDate:yyyy/MM/dd}," +
                         $"{invoice.DueDate:yyyy/MM/dd},{invoice.TotalAmount:N2}," +
                         $"{invoice.PaidAmount:N2},{remainingAmount:N2}," +
                         $"{invoice.Status},{invoice.Notes}");
            }

            lines.Add("");

            // تفاصيل المدفوعات
            lines.Add("تفاصيل المدفوعات");
            lines.Add("رقم السند,التاريخ,المبلغ,طريقة الدفع,رقم المرجع,الحالة,ملاحظات");

            var customerReceipts = receipts.Where(r => r.CustomerId == selectedCustomer.Id &&
                                                      r.ReceiptDate >= dtpFromDate.Value.Date &&
                                                      r.ReceiptDate <= dtpToDate.Value.Date).ToList();

            foreach (var receipt in customerReceipts.OrderByDescending(r => r.ReceiptDate))
            {
                lines.Add($"{receipt.ReceiptNumber},{receipt.ReceiptDate:yyyy/MM/dd}," +
                         $"{receipt.Amount:N2},{receipt.PaymentMethod}," +
                         $"{receipt.ReferenceNumber},{receipt.Status},{receipt.Notes}");
            }

            System.IO.File.WriteAllLines(filePath, lines, System.Text.Encoding.UTF8);

            MessageBox.Show($"تم تصدير كشف الحساب بنجاح إلى:\n{filePath}", "نجح التصدير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}