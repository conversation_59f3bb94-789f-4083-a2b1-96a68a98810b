using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Models;
using IntegratedInvoiceSystem.Services;

namespace IntegratedInvoiceSystem.Forms
{
    /// <summary>
    /// نموذج إدارة المستخدمين
    /// </summary>
    public partial class UsersManagementForm : Form
    {
        #region المتغيرات
        private DataGridView dgvUsers;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnResetPassword;
        private Button btnClose;
        private TextBox txtSearch;
        private ComboBox cmbRole;
        private CheckBox chkActiveOnly;
        private List<User> users;
        #endregion

        #region البناء والتهيئة
        public UsersManagementForm()
        {
            InitializeComponent();
            LoadUsers();
            RefreshGrid();
        }

        private void InitializeComponent()
        {
            this.Text = "إدارة المستخدمين";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
            SetupLayout();
        }

        private void CreateControls()
        {
            // شريط البحث والفلترة
            var pnlFilter = new Panel();
            pnlFilter.Height = 60;
            pnlFilter.Dock = DockStyle.Top;
            pnlFilter.BackColor = Color.FromArgb(248, 249, 250);

            var lblSearch = new Label();
            lblSearch.Text = "البحث:";
            lblSearch.Location = new Point(900, 20);
            lblSearch.Size = new Size(60, 23);

            txtSearch = new TextBox();
            txtSearch.Location = new Point(700, 20);
            txtSearch.Size = new Size(180, 23);
            txtSearch.TextChanged += TxtSearch_TextChanged;

            var lblRole = new Label();
            lblRole.Text = "الدور:";
            lblRole.Location = new Point(630, 20);
            lblRole.Size = new Size(50, 23);

            cmbRole = new ComboBox();
            cmbRole.Location = new Point(450, 20);
            cmbRole.Size = new Size(160, 23);
            cmbRole.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbRole.Items.AddRange(new string[] { "الكل", "مدير", "محاسب", "مستخدم" });
            cmbRole.SelectedIndex = 0;
            cmbRole.SelectedIndexChanged += CmbRole_SelectedIndexChanged;

            chkActiveOnly = new CheckBox();
            chkActiveOnly.Text = "النشطين فقط";
            chkActiveOnly.Location = new Point(320, 20);
            chkActiveOnly.Size = new Size(120, 23);
            chkActiveOnly.Checked = true;
            chkActiveOnly.CheckedChanged += ChkActiveOnly_CheckedChanged;

            pnlFilter.Controls.AddRange(new Control[] { lblSearch, txtSearch, lblRole, cmbRole, chkActiveOnly });

            // جدول المستخدمين
            dgvUsers = new DataGridView();
            dgvUsers.Dock = DockStyle.Fill;
            dgvUsers.BackgroundColor = Color.White;
            dgvUsers.BorderStyle = BorderStyle.None;
            dgvUsers.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvUsers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvUsers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvUsers.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvUsers.ColumnHeadersHeight = 40;
            dgvUsers.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgvUsers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvUsers.RowHeadersVisible = false;
            dgvUsers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvUsers.MultiSelect = false;
            dgvUsers.ReadOnly = true;
            dgvUsers.AllowUserToAddRows = false;
            dgvUsers.AllowUserToDeleteRows = false;
            dgvUsers.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            SetupGridColumns();

            // شريط الأزرار
            var pnlButtons = new Panel();
            pnlButtons.Height = 60;
            pnlButtons.Dock = DockStyle.Bottom;
            pnlButtons.BackColor = Color.FromArgb(248, 249, 250);

            btnAdd = new Button();
            btnAdd.Text = "إضافة مستخدم";
            btnAdd.Location = new Point(850, 15);
            btnAdd.Size = new Size(120, 35);
            btnAdd.BackColor = Color.FromArgb(46, 204, 113);
            btnAdd.ForeColor = Color.White;
            btnAdd.FlatStyle = FlatStyle.Flat;
            btnAdd.Click += BtnAdd_Click;

            btnEdit = new Button();
            btnEdit.Text = "تعديل";
            btnEdit.Location = new Point(720, 15);
            btnEdit.Size = new Size(120, 35);
            btnEdit.BackColor = Color.FromArgb(52, 152, 219);
            btnEdit.ForeColor = Color.White;
            btnEdit.FlatStyle = FlatStyle.Flat;
            btnEdit.Click += BtnEdit_Click;

            btnDelete = new Button();
            btnDelete.Text = "حذف";
            btnDelete.Location = new Point(590, 15);
            btnDelete.Size = new Size(120, 35);
            btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            btnDelete.ForeColor = Color.White;
            btnDelete.FlatStyle = FlatStyle.Flat;
            btnDelete.Click += BtnDelete_Click;

            btnResetPassword = new Button();
            btnResetPassword.Text = "إعادة تعيين كلمة المرور";
            btnResetPassword.Location = new Point(420, 15);
            btnResetPassword.Size = new Size(160, 35);
            btnResetPassword.BackColor = Color.FromArgb(155, 89, 182);
            btnResetPassword.ForeColor = Color.White;
            btnResetPassword.FlatStyle = FlatStyle.Flat;
            btnResetPassword.Click += BtnResetPassword_Click;

            btnClose = new Button();
            btnClose.Text = "إغلاق";
            btnClose.Location = new Point(20, 15);
            btnClose.Size = new Size(120, 35);
            btnClose.BackColor = Color.FromArgb(108, 117, 125);
            btnClose.ForeColor = Color.White;
            btnClose.FlatStyle = FlatStyle.Flat;
            btnClose.Click += BtnClose_Click;

            pnlButtons.Controls.AddRange(new Control[] { btnAdd, btnEdit, btnDelete, btnResetPassword, btnClose });

            // إضافة العناصر للنموذج
            this.Controls.Add(dgvUsers);
            this.Controls.Add(pnlFilter);
            this.Controls.Add(pnlButtons);
        }

        private void SetupGridColumns()
        {
            dgvUsers.Columns.Clear();
            dgvUsers.Columns.Add("Id", "المعرف");
            dgvUsers.Columns.Add("Username", "اسم المستخدم");
            dgvUsers.Columns.Add("FullName", "الاسم الكامل");
            dgvUsers.Columns.Add("Role", "الدور");
            dgvUsers.Columns.Add("Email", "البريد الإلكتروني");
            dgvUsers.Columns.Add("IsActive", "نشط");
            dgvUsers.Columns.Add("LastLogin", "آخر دخول");
            dgvUsers.Columns.Add("CreatedDate", "تاريخ الإنشاء");

            // تنسيق الأعمدة
            dgvUsers.Columns["Id"].Width = 60;
            dgvUsers.Columns["Username"].Width = 120;
            dgvUsers.Columns["FullName"].Width = 150;
            dgvUsers.Columns["Role"].Width = 100;
            dgvUsers.Columns["Email"].Width = 180;
            dgvUsers.Columns["IsActive"].Width = 80;
            dgvUsers.Columns["LastLogin"].Width = 140;
            dgvUsers.Columns["CreatedDate"].Width = 140;

            dgvUsers.Columns["LastLogin"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm";
            dgvUsers.Columns["CreatedDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
        }

        private void SetupLayout()
        {
            this.BackColor = Color.FromArgb(248, 249, 250);
        }
        #endregion

        #region تحميل البيانات
        private void LoadUsers()
        {
            try
            {
                // تحميل المستخدمين من قاعدة البيانات أو الخدمة
                users = new List<User>
                {
                    new User { Id = 1, Username = "admin", FullName = "المدير العام", Role = "مدير", Email = "<EMAIL>", IsActive = true, LastLogin = DateTime.Now.AddHours(-2), CreatedDate = DateTime.Now.AddMonths(-6) },
                    new User { Id = 2, Username = "accountant", FullName = "أحمد المحاسب", Role = "محاسب", Email = "<EMAIL>", IsActive = true, LastLogin = DateTime.Now.AddDays(-1), CreatedDate = DateTime.Now.AddMonths(-3) },
                    new User { Id = 3, Username = "user1", FullName = "سارة المستخدم", Role = "مستخدم", Email = "<EMAIL>", IsActive = true, LastLogin = DateTime.Now.AddDays(-3), CreatedDate = DateTime.Now.AddMonths(-1) },
                    new User { Id = 4, Username = "user2", FullName = "محمد المبيعات", Role = "مستخدم", Email = "<EMAIL>", IsActive = false, LastLogin = DateTime.Now.AddDays(-10), CreatedDate = DateTime.Now.AddMonths(-2) }
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                users = new List<User>();
            }
        }

        private void RefreshGrid()
        {
            try
            {
                dgvUsers.Rows.Clear();

                var filteredUsers = GetFilteredUsers();

                foreach (var user in filteredUsers)
                {
                    dgvUsers.Rows.Add(
                        user.Id,
                        user.Username,
                        user.FullName,
                        user.Role,
                        user.Email,
                        user.IsActive ? "نعم" : "لا",
                        user.LastLogin,
                        user.CreatedDate
                    );
                }

                // تلوين الصفوف حسب الحالة
                foreach (DataGridViewRow row in dgvUsers.Rows)
                {
                    if (row.Cells["IsActive"].Value.ToString() == "لا")
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(248, 215, 218);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(114, 28, 36);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الجدول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private List<User> GetFilteredUsers()
        {
            var filtered = users.AsQueryable();

            // فلترة حسب النشاط
            if (chkActiveOnly.Checked)
            {
                filtered = filtered.Where(u => u.IsActive);
            }

            // فلترة حسب الدور
            if (cmbRole.SelectedIndex > 0)
            {
                string selectedRole = cmbRole.SelectedItem.ToString();
                filtered = filtered.Where(u => u.Role == selectedRole);
            }

            // فلترة حسب البحث
            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                string searchText = txtSearch.Text.ToLower();
                filtered = filtered.Where(u =>
                    u.Username.ToLower().Contains(searchText) ||
                    u.FullName.ToLower().Contains(searchText) ||
                    u.Email.ToLower().Contains(searchText));
            }

            return filtered.ToList();
        }
        #endregion

        #region معالجات الأحداث
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            RefreshGrid();
        }

        private void CmbRole_SelectedIndexChanged(object sender, EventArgs e)
        {
            RefreshGrid();
        }

        private void ChkActiveOnly_CheckedChanged(object sender, EventArgs e)
        {
            RefreshGrid();
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                var addUserForm = new AddEditUserForm();
                if (addUserForm.ShowDialog() == DialogResult.OK)
                {
                    LoadUsers();
                    RefreshGrid();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة مستخدم جديد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مستخدم للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                int userId = Convert.ToInt32(dgvUsers.SelectedRows[0].Cells["Id"].Value);
                var user = users.FirstOrDefault(u => u.Id == userId);

                if (user != null)
                {
                    var editUserForm = new AddEditUserForm(user);
                    if (editUserForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadUsers();
                        RefreshGrid();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مستخدم للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا المستخدم؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    int userId = Convert.ToInt32(dgvUsers.SelectedRows[0].Cells["Id"].Value);
                    var user = users.FirstOrDefault(u => u.Id == userId);

                    if (user != null)
                    {
                        users.Remove(user);
                        RefreshGrid();
                        MessageBox.Show("تم حذف المستخدم بنجاح", "نجح الحذف",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnResetPassword_Click(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مستخدم لإعادة تعيين كلمة المرور", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين كلمة المرور؟\nسيتم تعيين كلمة المرور إلى: 123456",
                "تأكيد إعادة التعيين", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    string username = dgvUsers.SelectedRows[0].Cells["Username"].Value.ToString();

                    // هنا يتم إعادة تعيين كلمة المرور في قاعدة البيانات
                    MessageBox.Show($"تم إعادة تعيين كلمة المرور للمستخدم: {username}\nكلمة المرور الجديدة: 123456",
                        "نجح التعيين", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        #endregion
    }

    #region نموذج إضافة/تعديل المستخدم
    public partial class AddEditUserForm : Form
    {
        private User currentUser;
        private bool isEditMode;

        private TextBox txtUsername;
        private TextBox txtFullName;
        private TextBox txtEmail;
        private ComboBox cmbRole;
        private TextBox txtPassword;
        private TextBox txtConfirmPassword;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;

        public AddEditUserForm(User user = null)
        {
            currentUser = user;
            isEditMode = user != null;
            InitializeComponent();

            if (isEditMode)
            {
                LoadUserData();
            }
        }

        private void InitializeComponent()
        {
            this.Text = isEditMode ? "تعديل مستخدم" : "إضافة مستخدم جديد";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateUserControls();
        }

        private void CreateUserControls()
        {
            // اسم المستخدم
            var lblUsername = new Label();
            lblUsername.Text = "اسم المستخدم:";
            lblUsername.Location = new Point(350, 30);
            lblUsername.Size = new Size(100, 23);

            txtUsername = new TextBox();
            txtUsername.Location = new Point(150, 30);
            txtUsername.Size = new Size(180, 23);
            txtUsername.Enabled = !isEditMode; // لا يمكن تعديل اسم المستخدم

            // الاسم الكامل
            var lblFullName = new Label();
            lblFullName.Text = "الاسم الكامل:";
            lblFullName.Location = new Point(350, 70);
            lblFullName.Size = new Size(100, 23);

            txtFullName = new TextBox();
            txtFullName.Location = new Point(150, 70);
            txtFullName.Size = new Size(180, 23);

            // البريد الإلكتروني
            var lblEmail = new Label();
            lblEmail.Text = "البريد الإلكتروني:";
            lblEmail.Location = new Point(350, 110);
            lblEmail.Size = new Size(100, 23);

            txtEmail = new TextBox();
            txtEmail.Location = new Point(150, 110);
            txtEmail.Size = new Size(180, 23);

            // الدور
            var lblRole = new Label();
            lblRole.Text = "الدور:";
            lblRole.Location = new Point(350, 150);
            lblRole.Size = new Size(100, 23);

            cmbRole = new ComboBox();
            cmbRole.Location = new Point(150, 150);
            cmbRole.Size = new Size(180, 23);
            cmbRole.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbRole.Items.AddRange(new string[] { "مدير", "محاسب", "مستخدم" });

            // كلمة المرور
            var lblPassword = new Label();
            lblPassword.Text = isEditMode ? "كلمة المرور الجديدة:" : "كلمة المرور:";
            lblPassword.Location = new Point(350, 190);
            lblPassword.Size = new Size(120, 23);

            txtPassword = new TextBox();
            txtPassword.Location = new Point(150, 190);
            txtPassword.Size = new Size(180, 23);
            txtPassword.PasswordChar = '*';

            // تأكيد كلمة المرور
            var lblConfirmPassword = new Label();
            lblConfirmPassword.Text = "تأكيد كلمة المرور:";
            lblConfirmPassword.Location = new Point(350, 230);
            lblConfirmPassword.Size = new Size(120, 23);

            txtConfirmPassword = new TextBox();
            txtConfirmPassword.Location = new Point(150, 230);
            txtConfirmPassword.Size = new Size(180, 23);
            txtConfirmPassword.PasswordChar = '*';

            // نشط
            chkIsActive = new CheckBox();
            chkIsActive.Text = "المستخدم نشط";
            chkIsActive.Location = new Point(150, 270);
            chkIsActive.Size = new Size(120, 23);
            chkIsActive.Checked = true;

            // أزرار الحفظ والإلغاء
            btnSave = new Button();
            btnSave.Text = "حفظ";
            btnSave.Location = new Point(250, 320);
            btnSave.Size = new Size(100, 35);
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button();
            btnCancel.Text = "إلغاء";
            btnCancel.Location = new Point(140, 320);
            btnCancel.Size = new Size(100, 35);
            btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.Click += BtnCancel_Click;

            // إضافة العناصر للنموذج
            this.Controls.AddRange(new Control[] {
                lblUsername, txtUsername,
                lblFullName, txtFullName,
                lblEmail, txtEmail,
                lblRole, cmbRole,
                lblPassword, txtPassword,
                lblConfirmPassword, txtConfirmPassword,
                chkIsActive,
                btnSave, btnCancel
            });
        }

        private void LoadUserData()
        {
            if (currentUser != null)
            {
                txtUsername.Text = currentUser.Username;
                txtFullName.Text = currentUser.FullName;
                txtEmail.Text = currentUser.Email;
                cmbRole.Text = currentUser.Role;
                chkIsActive.Checked = currentUser.IsActive;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveUser();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFullName.Focus();
                return false;
            }

            if (!isEditMode || !string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                if (txtPassword.Text != txtConfirmPassword.Text)
                {
                    MessageBox.Show("كلمة المرور وتأكيدها غير متطابقتين", "تحقق من البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return false;
                }

                if (txtPassword.Text.Length < 6)
                {
                    MessageBox.Show("كلمة المرور يجب أن تكون 6 أحرف على الأقل", "تحقق من البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return false;
                }
            }

            return true;
        }

        private void SaveUser()
        {
            try
            {
                if (isEditMode)
                {
                    // تحديث المستخدم الحالي
                    currentUser.FullName = txtFullName.Text;
                    currentUser.Email = txtEmail.Text;
                    currentUser.Role = cmbRole.Text;
                    currentUser.IsActive = chkIsActive.Checked;

                    if (!string.IsNullOrWhiteSpace(txtPassword.Text))
                    {
                        // تحديث كلمة المرور
                        currentUser.Password = txtPassword.Text; // يجب تشفيرها في التطبيق الحقيقي
                    }
                }
                else
                {
                    // إنشاء مستخدم جديد
                    var newUser = new User
                    {
                        Username = txtUsername.Text,
                        FullName = txtFullName.Text,
                        Email = txtEmail.Text,
                        Role = cmbRole.Text,
                        Password = txtPassword.Text, // يجب تشفيرها في التطبيق الحقيقي
                        IsActive = chkIsActive.Checked,
                        CreatedDate = DateTime.Now
                    };

                    // إضافة المستخدم لقاعدة البيانات
                }

                MessageBox.Show("تم حفظ بيانات المستخدم بنجاح", "نجح الحفظ",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ بيانات المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
    #endregion
}
