using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Dapper;
using AccountingSystem.Data;
using AccountingSystem.Models;
using AccountingSystem.Services;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج شجرة الحسابات
    /// </summary>
    public partial class AccountsTreeForm : Form
    {
        private TreeView treeAccounts;
        private Panel panelButtons;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private Button btnClose;
        private Panel panelDetails;
        private Label lblAccountCode;
        private Label lblAccountName;
        private Label lblAccountType;
        private Label lblBalance;
        private TextBox txtAccountCode;
        private TextBox txtAccountName;
        private ComboBox cmbAccountType;
        private TextBox txtBalance;
        private CheckBox chkIsActive;
        private List<Account> accounts;
        
        public AccountsTreeForm()
        {
            InitializeComponent();
            LoadAccounts();
            SetupTreeView();
        }
        
        private void InitializeComponent()
        {
            this.treeAccounts = new TreeView();
            this.panelButtons = new Panel();
            this.btnAdd = new Button();
            this.btnEdit = new Button();
            this.btnDelete = new Button();
            this.btnRefresh = new Button();
            this.btnClose = new Button();
            this.panelDetails = new Panel();
            this.lblAccountCode = new Label();
            this.lblAccountName = new Label();
            this.lblAccountType = new Label();
            this.lblBalance = new Label();
            this.txtAccountCode = new TextBox();
            this.txtAccountName = new TextBox();
            this.cmbAccountType = new ComboBox();
            this.txtBalance = new TextBox();
            this.chkIsActive = new CheckBox();
            
            this.SuspendLayout();
            
            // Form
            this.Text = "شجرة الحسابات";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // TreeView
            this.treeAccounts.Location = new Point(12, 12);
            this.treeAccounts.Size = new Size(600, 550);
            this.treeAccounts.Font = new Font("Tahoma", 10F);
            this.treeAccounts.RightToLeft = RightToLeft.Yes;
            this.treeAccounts.RightToLeftLayout = true;
            this.treeAccounts.AfterSelect += TreeAccounts_AfterSelect;
            this.treeAccounts.NodeMouseDoubleClick += TreeAccounts_NodeMouseDoubleClick;
            
            // Panel Buttons
            this.panelButtons.Location = new Point(12, 570);
            this.panelButtons.Size = new Size(600, 50);
            
            // Buttons
            this.btnAdd.Text = "إضافة";
            this.btnAdd.Size = new Size(80, 35);
            this.btnAdd.Location = new Point(500, 8);
            this.btnAdd.Click += BtnAdd_Click;
            
            this.btnEdit.Text = "تعديل";
            this.btnEdit.Size = new Size(80, 35);
            this.btnEdit.Location = new Point(410, 8);
            this.btnEdit.Click += BtnEdit_Click;
            
            this.btnDelete.Text = "حذف";
            this.btnDelete.Size = new Size(80, 35);
            this.btnDelete.Location = new Point(320, 8);
            this.btnDelete.Click += BtnDelete_Click;
            
            this.btnRefresh.Text = "تحديث";
            this.btnRefresh.Size = new Size(80, 35);
            this.btnRefresh.Location = new Point(230, 8);
            this.btnRefresh.Click += BtnRefresh_Click;
            
            this.btnClose.Text = "إغلاق";
            this.btnClose.Size = new Size(80, 35);
            this.btnClose.Location = new Point(10, 8);
            this.btnClose.Click += BtnClose_Click;
            
            // Panel Details
            this.panelDetails.Location = new Point(630, 12);
            this.panelDetails.Size = new Size(350, 550);
            this.panelDetails.BorderStyle = BorderStyle.FixedSingle;
            
            // Labels and Controls in Details Panel
            int yPos = 20;
            int spacing = 40;
            
            this.lblAccountCode.Text = "كود الحساب:";
            this.lblAccountCode.Location = new Point(250, yPos);
            this.lblAccountCode.Size = new Size(80, 23);
            
            this.txtAccountCode.Location = new Point(20, yPos);
            this.txtAccountCode.Size = new Size(220, 23);
            this.txtAccountCode.ReadOnly = true;
            
            yPos += spacing;
            this.lblAccountName.Text = "اسم الحساب:";
            this.lblAccountName.Location = new Point(250, yPos);
            this.lblAccountName.Size = new Size(80, 23);
            
            this.txtAccountName.Location = new Point(20, yPos);
            this.txtAccountName.Size = new Size(220, 23);
            this.txtAccountName.ReadOnly = true;
            
            yPos += spacing;
            this.lblAccountType.Text = "نوع الحساب:";
            this.lblAccountType.Location = new Point(250, yPos);
            this.lblAccountType.Size = new Size(80, 23);
            
            this.cmbAccountType.Location = new Point(20, yPos);
            this.cmbAccountType.Size = new Size(220, 23);
            this.cmbAccountType.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbAccountType.Items.AddRange(new string[] { "أصول", "خصوم", "حقوق ملكية", "إيرادات", "مصروفات" });
            this.cmbAccountType.Enabled = false;
            
            yPos += spacing;
            this.lblBalance.Text = "الرصيد:";
            this.lblBalance.Location = new Point(250, yPos);
            this.lblBalance.Size = new Size(80, 23);
            
            this.txtBalance.Location = new Point(20, yPos);
            this.txtBalance.Size = new Size(220, 23);
            this.txtBalance.ReadOnly = true;
            this.txtBalance.TextAlign = HorizontalAlignment.Right;
            
            yPos += spacing;
            this.chkIsActive.Text = "نشط";
            this.chkIsActive.Location = new Point(250, yPos);
            this.chkIsActive.Size = new Size(80, 23);
            this.chkIsActive.Enabled = false;
            
            // Add controls to panels
            this.panelButtons.Controls.AddRange(new Control[] {
                this.btnAdd, this.btnEdit, this.btnDelete, this.btnRefresh, this.btnClose
            });
            
            this.panelDetails.Controls.AddRange(new Control[] {
                this.lblAccountCode, this.txtAccountCode,
                this.lblAccountName, this.txtAccountName,
                this.lblAccountType, this.cmbAccountType,
                this.lblBalance, this.txtBalance,
                this.chkIsActive
            });
            
            // Add controls to form
            this.Controls.Add(this.treeAccounts);
            this.Controls.Add(this.panelButtons);
            this.Controls.Add(this.panelDetails);
            
            this.ResumeLayout(false);
        }
        
        private void LoadAccounts()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    accounts = connection.Query<Account>("SELECT * FROM Accounts ORDER BY AccountCode").ToList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الحسابات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                accounts = new List<Account>();
            }
        }
        
        private void SetupTreeView()
        {
            treeAccounts.Nodes.Clear();
            
            // إضافة الحسابات الرئيسية أولاً
            var rootAccounts = accounts.Where(a => a.ParentAccountId == null || a.ParentAccountId == 0).ToList();
            
            foreach (var account in rootAccounts)
            {
                var node = CreateTreeNode(account);
                treeAccounts.Nodes.Add(node);
                AddChildNodes(node, account.Id);
            }
            
            treeAccounts.ExpandAll();
        }
        
        private TreeNode CreateTreeNode(Account account)
        {
            string nodeText = $"{account.AccountCode} - {account.AccountName}";
            if (!account.IsParent)
            {
                nodeText += $" ({account.Balance:N2})";
            }
            
            var node = new TreeNode(nodeText);
            node.Tag = account;
            
            // تلوين العقد حسب نوع الحساب
            switch (account.AccountType)
            {
                case "أصول":
                    node.ForeColor = Color.Blue;
                    break;
                case "خصوم":
                    node.ForeColor = Color.Red;
                    break;
                case "حقوق ملكية":
                    node.ForeColor = Color.Green;
                    break;
                case "إيرادات":
                    node.ForeColor = Color.DarkGreen;
                    break;
                case "مصروفات":
                    node.ForeColor = Color.DarkRed;
                    break;
            }
            
            if (!account.IsActive)
            {
                node.ForeColor = Color.Gray;
            }
            
            return node;
        }
        
        private void AddChildNodes(TreeNode parentNode, int parentAccountId)
        {
            var childAccounts = accounts.Where(a => a.ParentAccountId == parentAccountId).ToList();
            
            foreach (var account in childAccounts)
            {
                var childNode = CreateTreeNode(account);
                parentNode.Nodes.Add(childNode);
                AddChildNodes(childNode, account.Id);
            }
        }
        
        private void TreeAccounts_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node?.Tag is Account account)
            {
                DisplayAccountDetails(account);
            }
        }
        
        private void DisplayAccountDetails(Account account)
        {
            txtAccountCode.Text = account.AccountCode;
            txtAccountName.Text = account.AccountName;
            cmbAccountType.Text = account.AccountType;
            txtBalance.Text = account.Balance.ToString("N2");
            chkIsActive.Checked = account.IsActive;
        }
        
        private void TreeAccounts_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            BtnEdit_Click(sender, e);
        }
        
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new AddEditAccountForm();
            
            // إذا كان هناك حساب محدد، اجعله الحساب الأب
            if (treeAccounts.SelectedNode?.Tag is Account selectedAccount)
            {
                addForm.ParentAccount = selectedAccount;
            }
            
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                BtnRefresh_Click(sender, e);
            }
        }
        
        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (treeAccounts.SelectedNode?.Tag is Account account)
            {
                var editForm = new AddEditAccountForm(account);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    BtnRefresh_Click(sender, e);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار حساب للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        
        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (treeAccounts.SelectedNode?.Tag is Account account)
            {
                // التحقق من وجود حسابات فرعية
                var hasChildren = accounts.Any(a => a.ParentAccountId == account.Id);
                if (hasChildren)
                {
                    MessageBox.Show("لا يمكن حذف حساب يحتوي على حسابات فرعية", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                
                var result = MessageBox.Show($"هل أنت متأكد من حذف الحساب '{account.AccountName}'؟", 
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    try
                    {
                        using (var connection = DatabaseHelper.GetConnection())
                        {
                            connection.Open();
                            connection.Execute("DELETE FROM Accounts WHERE Id = @Id", new { Id = account.Id });
                        }
                        
                        MessageBox.Show("تم حذف الحساب بنجاح", "نجح الحذف", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        BtnRefresh_Click(sender, e);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الحساب: {ex.Message}", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار حساب للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadAccounts();
            SetupTreeView();
            
            // مسح تفاصيل الحساب
            txtAccountCode.Clear();
            txtAccountName.Clear();
            cmbAccountType.SelectedIndex = -1;
            txtBalance.Clear();
            chkIsActive.Checked = false;
        }
        
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
