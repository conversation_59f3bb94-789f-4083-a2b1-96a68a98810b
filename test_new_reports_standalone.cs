using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

// اختبار مستقل للنموذج الجديد لتقارير الفواتير
namespace TestInvoiceReports
{
    // نماذج البيانات البسيطة للاختبار
    public class Invoice
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public int CustomerId { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
    }

    public class Customer
    {
        public int Id { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public string Address { get; set; }
    }

    // خدمة البيانات التجريبية
    public static class TestDataService
    {
        public static List<Invoice> Invoices { get; set; }
        public static List<Customer> Customers { get; set; }

        static TestDataService()
        {
            LoadTestData();
        }

        private static void LoadTestData()
        {
            Customers = new List<Customer>
            {
                new Customer { Id = 1, CustomerName = "شركة الأعمال المتقدمة", Phone = "************", Address = "الرياض" },
                new Customer { Id = 2, CustomerName = "مؤسسة التقنية الحديثة", Phone = "************", Address = "جدة" },
                new Customer { Id = 3, CustomerName = "شركة الحلول المبتكرة", Phone = "************", Address = "الدمام" },
                new Customer { Id = 4, CustomerName = "مكتب الاستشارات الهندسية", Phone = "************", Address = "الرياض" },
                new Customer { Id = 5, CustomerName = "شركة التجارة العامة", Phone = "************", Address = "مكة" }
            };

            Invoices = new List<Invoice>
            {
                new Invoice { Id = 1, InvoiceNumber = "INV-2024-001", InvoiceDate = DateTime.Now.AddDays(-45), DueDate = DateTime.Now.AddDays(-15), CustomerId = 1, TotalAmount = 15000, PaidAmount = 15000, Status = "مدفوعة", Notes = "فاتورة أجهزة كمبيوتر" },
                new Invoice { Id = 2, InvoiceNumber = "INV-2024-002", InvoiceDate = DateTime.Now.AddDays(-35), DueDate = DateTime.Now.AddDays(-5), CustomerId = 2, TotalAmount = 8500, PaidAmount = 4000, Status = "مدفوعة جزئياً", Notes = "فاتورة برمجيات" },
                new Invoice { Id = 3, InvoiceNumber = "INV-2024-003", InvoiceDate = DateTime.Now.AddDays(-25), DueDate = DateTime.Now.AddDays(5), CustomerId = 3, TotalAmount = 12000, PaidAmount = 0, Status = "غير مدفوعة", Notes = "فاتورة استشارات" },
                new Invoice { Id = 4, InvoiceNumber = "INV-2024-004", InvoiceDate = DateTime.Now.AddDays(-50), DueDate = DateTime.Now.AddDays(-20), CustomerId = 1, TotalAmount = 6500, PaidAmount = 0, Status = "متأخرة", Notes = "فاتورة صيانة" },
                new Invoice { Id = 5, InvoiceNumber = "INV-2024-005", InvoiceDate = DateTime.Now.AddDays(-15), DueDate = DateTime.Now.AddDays(15), CustomerId = 4, TotalAmount = 22000, PaidAmount = 10000, Status = "مدفوعة جزئياً", Notes = "فاتورة مشروع كبير" },
                new Invoice { Id = 6, InvoiceNumber = "INV-2024-006", InvoiceDate = DateTime.Now.AddDays(-10), DueDate = DateTime.Now.AddDays(20), CustomerId = 5, TotalAmount = 3500, PaidAmount = 3500, Status = "مدفوعة", Notes = "فاتورة مواد مكتبية" },
                new Invoice { Id = 7, InvoiceNumber = "INV-2024-007", InvoiceDate = DateTime.Now.AddDays(-5), DueDate = DateTime.Now.AddDays(25), CustomerId = 2, TotalAmount = 18000, PaidAmount = 0, Status = "مؤكدة", Notes = "فاتورة تطوير موقع" }
            };
        }
    }

    // نموذج تقرير الفواتير المبسط للاختبار
    public class SimpleInvoicesReportForm : Form
    {
        private TabControl tabControl;
        private DataGridView dgvSummary, dgvDetailed;
        private Panel pnlKPIs;
        private DateTimePicker dtpFromDate, dtpToDate;
        private ComboBox cmbCustomer, cmbStatus;
        private Button btnGenerate;

        public SimpleInvoicesReportForm()
        {
            InitializeForm();
            LoadData();
        }

        private void InitializeForm()
        {
            this.Text = "🎉 تقرير الفواتير الشامل - نسخة تجريبية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.Font = new Font("Tahoma", 9F);

            CreateControls();
        }

        private void CreateControls()
        {
            // إنشاء التبويبات
            tabControl = new TabControl();
            tabControl.Dock = DockStyle.Fill;
            tabControl.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            var tabSummary = new TabPage("📊 التقرير الإجمالي");
            var tabDetailed = new TabPage("📋 التقرير المفصل");

            // لوحة الفلاتر
            var pnlFilters = new Panel();
            pnlFilters.Height = 80;
            pnlFilters.Dock = DockStyle.Top;
            pnlFilters.BackColor = Color.FromArgb(240, 248, 255);

            // فلاتر التاريخ
            var lblFromDate = new Label { Text = "من تاريخ:", Location = new Point(950, 15), Size = new Size(60, 23) };
            dtpFromDate = new DateTimePicker { Location = new Point(780, 15), Size = new Size(160, 23), Value = DateTime.Today.AddMonths(-1) };

            var lblToDate = new Label { Text = "إلى تاريخ:", Location = new Point(700, 15), Size = new Size(60, 23) };
            dtpToDate = new DateTimePicker { Location = new Point(530, 15), Size = new Size(160, 23), Value = DateTime.Today };

            // فلتر العميل
            var lblCustomer = new Label { Text = "العميل:", Location = new Point(450, 15), Size = new Size(60, 23) };
            cmbCustomer = new ComboBox { Location = new Point(280, 15), Size = new Size(160, 23), DropDownStyle = ComboBoxStyle.DropDownList };

            // فلتر الحالة
            var lblStatus = new Label { Text = "الحالة:", Location = new Point(200, 15), Size = new Size(60, 23) };
            cmbStatus = new ComboBox { Location = new Point(30, 15), Size = new Size(160, 23), DropDownStyle = ComboBoxStyle.DropDownList };

            // زر إنشاء التقرير
            btnGenerate = new Button
            {
                Text = "🔄 إنشاء التقرير",
                Location = new Point(950, 45),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnGenerate.Click += BtnGenerate_Click;

            pnlFilters.Controls.AddRange(new Control[] { lblFromDate, dtpFromDate, lblToDate, dtpToDate, lblCustomer, cmbCustomer, lblStatus, cmbStatus, btnGenerate });

            // لوحة مؤشرات الأداء
            CreateKPIsPanel();

            // جدول التقرير الإجمالي
            CreateSummaryGrid();

            // جدول التقرير المفصل
            CreateDetailedGrid();

            tabSummary.Controls.AddRange(new Control[] { pnlFilters, pnlKPIs, dgvSummary });
            tabDetailed.Controls.Add(dgvDetailed);

            tabControl.TabPages.AddRange(new TabPage[] { tabSummary, tabDetailed });
            this.Controls.Add(tabControl);
        }

        private void CreateKPIsPanel()
        {
            pnlKPIs = new Panel();
            pnlKPIs.Height = 100;
            pnlKPIs.Dock = DockStyle.Top;
            pnlKPIs.BackColor = Color.FromArgb(248, 249, 250);

            // مؤشرات الأداء
            CreateKPICard("📊 إجمالي الفواتير", "7", Color.FromArgb(0, 123, 255), 10);
            CreateKPICard("💰 إجمالي المبلغ", "85,500 ريال", Color.FromArgb(40, 167, 69), 280);
            CreateKPICard("✅ المبلغ المدفوع", "32,500 ريال", Color.FromArgb(23, 162, 184), 550);
            CreateKPICard("⏰ المبلغ المستحق", "53,000 ريال", Color.FromArgb(220, 53, 69), 820);
        }

        private void CreateKPICard(string title, string value, Color color, int x)
        {
            var card = new Panel
            {
                Size = new Size(250, 80),
                Location = new Point(x, 10),
                BackColor = color,
                ForeColor = Color.White
            };

            var lblTitle = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(230, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var lblValue = new Label
            {
                Text = value,
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                Location = new Point(10, 35),
                Size = new Size(230, 35),
                TextAlign = ContentAlignment.MiddleCenter
            };

            card.Controls.AddRange(new Control[] { lblTitle, lblValue });
            pnlKPIs.Controls.Add(card);
        }

        private void CreateSummaryGrid()
        {
            dgvSummary = new DataGridView();
            dgvSummary.Dock = DockStyle.Fill;
            dgvSummary.BackgroundColor = Color.White;
            dgvSummary.ReadOnly = true;
            dgvSummary.AllowUserToAddRows = false;
            dgvSummary.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvSummary.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            dgvSummary.Columns.Add("CustomerName", "اسم العميل");
            dgvSummary.Columns.Add("InvoiceCount", "عدد الفواتير");
            dgvSummary.Columns.Add("TotalAmount", "إجمالي المبلغ");
            dgvSummary.Columns.Add("PaidAmount", "المبلغ المدفوع");
            dgvSummary.Columns.Add("OutstandingAmount", "المبلغ المستحق");
        }

        private void CreateDetailedGrid()
        {
            dgvDetailed = new DataGridView();
            dgvDetailed.Dock = DockStyle.Fill;
            dgvDetailed.BackgroundColor = Color.White;
            dgvDetailed.ReadOnly = true;
            dgvDetailed.AllowUserToAddRows = false;
            dgvDetailed.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvDetailed.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            dgvDetailed.Columns.Add("InvoiceNumber", "رقم الفاتورة");
            dgvDetailed.Columns.Add("InvoiceDate", "تاريخ الفاتورة");
            dgvDetailed.Columns.Add("CustomerName", "اسم العميل");
            dgvDetailed.Columns.Add("TotalAmount", "إجمالي المبلغ");
            dgvDetailed.Columns.Add("PaidAmount", "المبلغ المدفوع");
            dgvDetailed.Columns.Add("Status", "الحالة");
        }

        private void LoadData()
        {
            // تحميل بيانات العملاء في ComboBox
            cmbCustomer.Items.Add("جميع العملاء");
            foreach (var customer in TestDataService.Customers)
            {
                cmbCustomer.Items.Add(customer.CustomerName);
            }
            cmbCustomer.SelectedIndex = 0;

            // تحميل حالات الفواتير
            cmbStatus.Items.AddRange(new string[] { "جميع الحالات", "مدفوعة", "مدفوعة جزئياً", "غير مدفوعة", "متأخرة", "مؤكدة" });
            cmbStatus.SelectedIndex = 0;

            // تحميل البيانات الأولية
            LoadReportData();
        }

        private void BtnGenerate_Click(object sender, EventArgs e)
        {
            LoadReportData();
            MessageBox.Show("✅ تم إنشاء التقرير بنجاح!\n\n🎉 هذا مثال على النموذج الجديد المحسن", 
                "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LoadReportData()
        {
            // تحميل التقرير الإجمالي
            dgvSummary.Rows.Clear();
            var customerSummary = TestDataService.Invoices
                .GroupBy(i => i.CustomerId)
                .Select(g => new
                {
                    CustomerName = TestDataService.Customers.FirstOrDefault(c => c.Id == g.Key)?.CustomerName ?? "غير محدد",
                    InvoiceCount = g.Count(),
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    PaidAmount = g.Sum(i => i.PaidAmount),
                    OutstandingAmount = g.Sum(i => i.TotalAmount - i.PaidAmount)
                });

            foreach (var summary in customerSummary)
            {
                var row = dgvSummary.Rows.Add();
                dgvSummary.Rows[row].Cells["CustomerName"].Value = summary.CustomerName;
                dgvSummary.Rows[row].Cells["InvoiceCount"].Value = summary.InvoiceCount;
                dgvSummary.Rows[row].Cells["TotalAmount"].Value = summary.TotalAmount.ToString("N2");
                dgvSummary.Rows[row].Cells["PaidAmount"].Value = summary.PaidAmount.ToString("N2");
                dgvSummary.Rows[row].Cells["OutstandingAmount"].Value = summary.OutstandingAmount.ToString("N2");
            }

            // تحميل التقرير المفصل
            dgvDetailed.Rows.Clear();
            foreach (var invoice in TestDataService.Invoices.OrderByDescending(i => i.InvoiceDate))
            {
                var customer = TestDataService.Customers.FirstOrDefault(c => c.Id == invoice.CustomerId);
                var row = dgvDetailed.Rows.Add();
                dgvDetailed.Rows[row].Cells["InvoiceNumber"].Value = invoice.InvoiceNumber;
                dgvDetailed.Rows[row].Cells["InvoiceDate"].Value = invoice.InvoiceDate.ToString("yyyy/MM/dd");
                dgvDetailed.Rows[row].Cells["CustomerName"].Value = customer?.CustomerName ?? "غير محدد";
                dgvDetailed.Rows[row].Cells["TotalAmount"].Value = invoice.TotalAmount.ToString("N2");
                dgvDetailed.Rows[row].Cells["PaidAmount"].Value = invoice.PaidAmount.ToString("N2");
                dgvDetailed.Rows[row].Cells["Status"].Value = invoice.Status;

                // تلوين حسب الحالة
                switch (invoice.Status)
                {
                    case "مدفوعة":
                        dgvDetailed.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(212, 237, 218);
                        break;
                    case "مدفوعة جزئياً":
                        dgvDetailed.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(255, 243, 205);
                        break;
                    case "متأخرة":
                        dgvDetailed.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(248, 215, 218);
                        break;
                }
            }
        }
    }

    // نقطة دخول التطبيق التجريبي
    class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            MessageBox.Show("🎉 مرحباً بك في النموذج التجريبي لتقارير الفواتير الشامل!\n\n" +
                           "الميزات المتوفرة:\n" +
                           "✅ تقارير إجمالية ومفصلة\n" +
                           "✅ مؤشرات أداء تفاعلية\n" +
                           "✅ فلترة متقدمة\n" +
                           "✅ تلوين حسب الحالة\n" +
                           "✅ واجهة عصرية وجذابة\n\n" +
                           "هذا مثال مبسط للنموذج الكامل المحسن!", 
                           "تقارير الفواتير الشامل", MessageBoxButtons.OK, MessageBoxIcon.Information);
            
            Application.Run(new SimpleInvoicesReportForm());
        }
    }
}
