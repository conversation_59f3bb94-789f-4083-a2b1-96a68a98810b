@echo off
echo.
echo ========================================
echo Invoice Reports Test - Simple Version
echo ========================================
echo.

echo Building test application...

:: Find C# compiler
set "csc_path="
for %%i in (csc.exe) do set "csc_path=%%~$PATH:i"

if not defined csc_path (
    echo Looking for C# compiler in Visual Studio locations...
    
    for /d %%d in ("C:\Program Files*\Microsoft Visual Studio\*\*\MSBuild\*\Bin\Roslyn") do (
        if exist "%%d\csc.exe" (
            set "csc_path=%%d\csc.exe"
            goto found_csc
        )
    )
    
    for /d %%d in ("C:\Program Files*\dotnet\sdk\*") do (
        if exist "%%d\Roslyn\bincore\csc.exe" (
            set "csc_path=%%d\Roslyn\bincore\csc.exe"
            goto found_csc
        )
    )
    
    :found_csc
)

if not defined csc_path (
    echo ERROR: C# compiler not found
    echo Please install Visual Studio or .NET Framework SDK
    pause
    exit /b 1
)

echo Found C# compiler: %csc_path%
echo.

echo Compiling test application...

"%csc_path%" /target:exe ^
    /reference:System.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Linq.dll ^
    /out:TestInvoiceReports.exe ^
    test_new_reports_standalone.cs

if %errorlevel% neq 0 (
    echo Compilation failed. Trying simplified version...
    
    "%csc_path%" /target:exe ^
        /reference:System.dll ^
        /reference:System.Windows.Forms.dll ^
        /reference:System.Drawing.dll ^
        /out:TestInvoiceReportsSimple.exe ^
        test_new_reports_standalone.cs
    
    if %errorlevel% neq 0 (
        echo Compilation failed completely.
        echo Check .NET Framework version and dependencies.
        pause
        exit /b 1
    ) else (
        echo Simplified compilation successful!
        set "exe_name=TestInvoiceReportsSimple.exe"
    )
) else (
    echo Compilation successful!
    set "exe_name=TestInvoiceReports.exe"
)

echo.
echo Test application ready: %exe_name%
echo.
echo Features to test:
echo - Comprehensive invoice reports
echo - KPI indicators
echo - Advanced filtering
echo - Interactive coloring
echo - Modern Arabic interface
echo.

if exist "%exe_name%" (
    echo Do you want to run the test application now? (Y/N)
    set /p "run_choice=Choice: "
    
    if /i "%run_choice%"=="Y" (
        echo.
        echo Running invoice reports test application...
        start "" "%exe_name%"
        echo.
        echo If the application runs successfully:
        echo - The new reports system is working
        echo - You can proceed with the main system integration
        echo.
        echo If there are errors:
        echo - Check the troubleshooting guide
        echo - Verify system requirements
        echo - Try alternative solutions
        echo.
    )
)

echo.
echo Test completed!
pause
