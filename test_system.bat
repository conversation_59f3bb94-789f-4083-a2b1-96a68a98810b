@echo off
chcp 65001 > nul
echo ========================================
echo اختبار نظام المحاسبة الذكي
echo ========================================
echo.

echo هذا الاختبار سيتحقق من:
echo 1. وجود الملفات المطلوبة
echo 2. صحة بنية المشروع
echo 3. إمكانية تشغيل النظام
echo.

set errors=0
set warnings=0

echo بدء الاختبار...
echo.

echo ========================================
echo 1. فحص الملفات الأساسية
echo ========================================

set required_files=Program.cs App.config AccountingSystem.csproj packages.config README.md

for %%f in (%required_files%) do (
    if exist "%%f" (
        echo ✓ %%f موجود
    ) else (
        echo ✗ %%f مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 2. فحص مجلدات المشروع
echo ========================================

set required_folders=Models Data Services Utils Forms Properties

for %%d in (%required_folders%) do (
    if exist "%%d" (
        echo ✓ مجلد %%d موجود
    ) else (
        echo ✗ مجلد %%d مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 3. فحص ملفات النماذج
echo ========================================

set form_files=Forms\LoginForm.cs Forms\MainForm.cs Forms\AccountsTreeForm.cs Forms\AddEditAccountForm.cs Forms\JournalEntryForm.cs Forms\JournalEntriesListForm.cs Forms\TrialBalanceForm.cs

for %%f in (%form_files%) do (
    if exist "%%f" (
        echo ✓ %%f موجود
    ) else (
        echo ✗ %%f مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 4. فحص ملفات النماذج
echo ========================================

set model_files=Models\User.cs Models\Account.cs Models\JournalEntry.cs Models\Receipt.cs Models\Payment.cs Models\Product.cs Models\Customer.cs Models\Invoice.cs

for %%f in (%model_files%) do (
    if exist "%%f" (
        echo ✓ %%f موجود
    ) else (
        echo ✗ %%f مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 5. فحص ملفات الخدمات
echo ========================================

set service_files=Services\AuthenticationService.cs Services\AccountService.cs Services\JournalService.cs Services\AIAnalysisService.cs

for %%f in (%service_files%) do (
    if exist "%%f" (
        echo ✓ %%f موجود
    ) else (
        echo ✗ %%f مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 6. فحص ملفات البيانات والأدوات
echo ========================================

set util_files=Data\DatabaseHelper.cs Utils\SecurityHelper.cs Utils\ReportGenerator.cs Utils\BackupHelper.cs Utils\ConfigHelper.cs

for %%f in (%util_files%) do (
    if exist "%%f" (
        echo ✓ %%f موجود
    ) else (
        echo ✗ %%f مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 7. فحص ملفات التشغيل
echo ========================================

set run_files=build_and_run.bat install_requirements.bat start_system.bat quick_setup.bat

for %%f in (%run_files%) do (
    if exist "%%f" (
        echo ✓ %%f موجود
    ) else (
        echo ⚠ %%f مفقود (اختياري)
        set /a warnings+=1
    )
)

echo.
echo ========================================
echo 8. فحص ملفات التوثيق
echo ========================================

set doc_files=README.md دليل_المستخدم.md system_config.json

for %%f in (%doc_files%) do (
    if exist "%%f" (
        echo ✓ %%f موجود
    ) else (
        echo ⚠ %%f مفقود (اختياري)
        set /a warnings+=1
    )
)

echo.
echo ========================================
echo 9. فحص المجلدات الاختيارية
echo ========================================

set optional_folders=Backups Reports Logs bin Tests

for %%d in (%optional_folders%) do (
    if exist "%%d" (
        echo ✓ مجلد %%d موجود
    ) else (
        echo ⚠ مجلد %%d غير موجود (سيتم إنشاؤه تلقائياً)
    )
)

echo.
echo ========================================
echo 10. فحص ملف قاعدة البيانات
echo ========================================

if exist "database.db" (
    echo ✓ ملف قاعدة البيانات موجود
) else (
    echo ⚠ ملف قاعدة البيانات غير موجود (سيتم إنشاؤه عند التشغيل الأول)
)

echo.
echo ========================================
echo نتائج الاختبار
echo ========================================

if %errors% EQU 0 (
    echo ✓ نجح الاختبار! جميع الملفات الأساسية موجودة
    echo.
    if %warnings% GTR 0 (
        echo ⚠ يوجد %warnings% تحذير حول ملفات اختيارية
    )
    echo.
    echo النظام جاهز للتشغيل!
    echo.
    echo للتشغيل:
    echo 1. تشغيل build_and_run.bat لبناء وتشغيل النظام
    echo 2. أو تشغيل start_system.bat إذا كان مبني مسبقاً
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
) else (
    echo ✗ فشل الاختبار! يوجد %errors% ملف مفقود
    echo.
    echo الملفات المفقودة ضرورية لتشغيل النظام
    echo يرجى التأكد من وجود جميع ملفات المشروع
)

echo.
echo تفاصيل إضافية:
echo - إجمالي الأخطاء: %errors%
echo - إجمالي التحذيرات: %warnings%
echo - تاريخ الاختبار: %date% %time%

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
