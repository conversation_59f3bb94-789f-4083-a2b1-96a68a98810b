using System;
using System.Windows.Forms;
using AccountingSystem.Data;
using AccountingSystem.Services;

namespace AccountingSystem
{
    /// <summary>
    /// برنامج مبسط لاختبار النظام بدون مكتبات خارجية
    /// </summary>
    internal static class SimpleProgram
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                // تهيئة قاعدة البيانات المبسطة
                SimpleDatabaseHelper.InitializeDatabase();

                // عرض نموذج تسجيل دخول مبسط
                Application.Run(new SimpleLoginForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    /// <summary>
    /// نموذج تسجيل دخول مبسط
    /// </summary>
    public partial class SimpleLoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnExit;
        private Label lblTitle;
        private Label lblUsername;
        private Label lblPassword;

        public SimpleLoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.txtUsername = new TextBox();
            this.txtPassword = new TextBox();
            this.btnLogin = new Button();
            this.btnExit = new Button();
            this.lblTitle = new Label();
            this.lblUsername = new Label();
            this.lblPassword = new Label();

            this.SuspendLayout();

            // Form
            this.Text = "نظام المحاسبة الذكي - إصدار تجريبي";
            this.Size = new System.Drawing.Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Title Label
            this.lblTitle.Text = "نظام المحاسبة الذكي";
            this.lblTitle.Font = new System.Drawing.Font("Tahoma", 16F, System.Drawing.FontStyle.Bold);
            this.lblTitle.ForeColor = System.Drawing.Color.FromArgb(25, 118, 210);
            this.lblTitle.Location = new System.Drawing.Point(50, 30);
            this.lblTitle.Size = new System.Drawing.Size(300, 35);
            this.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;

            // Username Label
            this.lblUsername.Text = "اسم المستخدم:";
            this.lblUsername.Font = new System.Drawing.Font("Tahoma", 10F);
            this.lblUsername.Location = new System.Drawing.Point(270, 100);
            this.lblUsername.Size = new System.Drawing.Size(100, 23);
            this.lblUsername.TextAlign = System.Drawing.ContentAlignment.MiddleRight;

            // Username TextBox
            this.txtUsername.Font = new System.Drawing.Font("Tahoma", 10F);
            this.txtUsername.Location = new System.Drawing.Point(50, 100);
            this.txtUsername.Size = new System.Drawing.Size(200, 23);
            this.txtUsername.Text = "admin";

            // Password Label
            this.lblPassword.Text = "كلمة المرور:";
            this.lblPassword.Font = new System.Drawing.Font("Tahoma", 10F);
            this.lblPassword.Location = new System.Drawing.Point(270, 140);
            this.lblPassword.Size = new System.Drawing.Size(100, 23);
            this.lblPassword.TextAlign = System.Drawing.ContentAlignment.MiddleRight;

            // Password TextBox
            this.txtPassword.Font = new System.Drawing.Font("Tahoma", 10F);
            this.txtPassword.Location = new System.Drawing.Point(50, 140);
            this.txtPassword.Size = new System.Drawing.Size(200, 23);
            this.txtPassword.UseSystemPasswordChar = true;
            this.txtPassword.Text = "admin123";

            // Login Button
            this.btnLogin.Text = "تسجيل الدخول";
            this.btnLogin.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.btnLogin.Location = new System.Drawing.Point(200, 190);
            this.btnLogin.Size = new System.Drawing.Size(120, 35);
            this.btnLogin.BackColor = System.Drawing.Color.FromArgb(25, 118, 210);
            this.btnLogin.ForeColor = System.Drawing.Color.White;
            this.btnLogin.FlatStyle = FlatStyle.Flat;
            this.btnLogin.FlatAppearance.BorderSize = 0;
            this.btnLogin.Click += new EventHandler(this.btnLogin_Click);

            // Exit Button
            this.btnExit.Text = "خروج";
            this.btnExit.Font = new System.Drawing.Font("Tahoma", 10F);
            this.btnExit.Location = new System.Drawing.Point(50, 190);
            this.btnExit.Size = new System.Drawing.Size(120, 35);
            this.btnExit.BackColor = System.Drawing.Color.FromArgb(244, 67, 54);
            this.btnExit.ForeColor = System.Drawing.Color.White;
            this.btnExit.FlatStyle = FlatStyle.Flat;
            this.btnExit.FlatAppearance.BorderSize = 0;
            this.btnExit.Click += new EventHandler(this.btnExit_Click);

            // Add controls to form
            this.Controls.Add(this.lblTitle);
            this.Controls.Add(this.lblUsername);
            this.Controls.Add(this.txtUsername);
            this.Controls.Add(this.lblPassword);
            this.Controls.Add(this.txtPassword);
            this.Controls.Add(this.btnLogin);
            this.Controls.Add(this.btnExit);

            this.ResumeLayout(false);

            // Set default button
            this.AcceptButton = this.btnLogin;
            this.CancelButton = this.btnExit;
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            try
            {
                string username = txtUsername.Text.Trim();
                string password = txtPassword.Text;

                if (string.IsNullOrEmpty(username))
                {
                    MessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrEmpty(password))
                {
                    MessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return;
                }

                // تسجيل دخول باستخدام الخدمة المبسطة
                if (SimpleAuthenticationService.Login(username, password))
                {
                    this.Hide();
                    var mainForm = new SimpleMainForm();
                    mainForm.FormClosed += (s, args) => this.Close();
                    mainForm.Show();
                }
                else
                {
                    MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدخول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }
    }

    /// <summary>
    /// النموذج الرئيسي المبسط
    /// </summary>
    public partial class SimpleMainForm : Form
    {
        private MenuStrip menuStrip;
        private Label lblWelcome;
        private Label lblInfo;

        public SimpleMainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.menuStrip = new MenuStrip();
            this.lblWelcome = new Label();
            this.lblInfo = new Label();

            this.SuspendLayout();

            // Form
            this.Text = "نظام المحاسبة الذكي - النموذج الرئيسي";
            this.Size = new System.Drawing.Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Menu Strip
            this.menuStrip.BackColor = System.Drawing.Color.FromArgb(25, 118, 210);
            this.menuStrip.ForeColor = System.Drawing.Color.White;
            this.menuStrip.Font = new System.Drawing.Font("Tahoma", 10F);
            this.menuStrip.RightToLeft = RightToLeft.Yes;

            CreateMenuItems();

            // Welcome Label
            this.lblWelcome.Text = "مرحباً بك في نظام المحاسبة الذكي";
            this.lblWelcome.Font = new System.Drawing.Font("Tahoma", 20F, System.Drawing.FontStyle.Bold);
            this.lblWelcome.ForeColor = System.Drawing.Color.FromArgb(25, 118, 210);
            this.lblWelcome.Location = new System.Drawing.Point(50, 100);
            this.lblWelcome.Size = new System.Drawing.Size(700, 40);
            this.lblWelcome.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;

            // Info Label
            this.lblInfo.Text = "هذا إصدار تجريبي مبسط من النظام\n\n" +
                               "الميزات المتاحة:\n" +
                               "• واجهة تسجيل دخول آمنة\n" +
                               "• قوائم النظام الأساسية\n" +
                               "• هيكل قاعدة البيانات\n" +
                               "• نظام الأمان والتشفير\n\n" +
                               "للحصول على النسخة الكاملة مع جميع الميزات،\n" +
                               "يرجى تثبيت المكتبات المطلوبة وبناء المشروع الكامل.";
            this.lblInfo.Font = new System.Drawing.Font("Tahoma", 12F);
            this.lblInfo.Location = new System.Drawing.Point(50, 200);
            this.lblInfo.Size = new System.Drawing.Size(700, 300);
            this.lblInfo.TextAlign = System.Drawing.ContentAlignment.TopCenter;

            // Add controls to form
            this.MainMenuStrip = this.menuStrip;
            this.Controls.Add(this.lblWelcome);
            this.Controls.Add(this.lblInfo);
            this.Controls.Add(this.menuStrip);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void CreateMenuItems()
        {
            // قائمة الملف
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("تسجيل خروج", null, Logout_Click));
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("خروج", null, Exit_Click));

            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("حول البرنامج", null, About_Click));

            // إضافة القوائم
            this.menuStrip.Items.AddRange(new ToolStripItem[] {
                fileMenu,
                helpMenu
            });
        }

        private void Logout_Click(object sender, EventArgs e)
        {
            SimpleAuthenticationService.Logout();
            this.Hide();
            var loginForm = new SimpleLoginForm();
            loginForm.Show();
        }

        private void Exit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void About_Click(object sender, EventArgs e)
        {
            MessageBox.Show("نظام المحاسبة الذكي - الإصدار التجريبي 1.0\n\n" +
                           "تم تطويره باستخدام C# و .NET Framework\n" +
                           "مع دعم قواعد البيانات SQLite والذكاء الاصطناعي\n\n" +
                           "هذا إصدار مبسط للاختبار والتجريب",
                           "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
