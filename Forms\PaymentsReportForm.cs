using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AccountingSystem.Models;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج تقرير سندات الصرف
    /// </summary>
    public partial class PaymentsReportForm : Form
    {
        private DataGridView dgvReport;
        private Button btnPrint;
        private Button btnExport;
        private Button btnClose;
        private Label lblTitle;
        private Label lblTotalPayments;
        private Label lblTotalAmount;
        private Label lblByPaymentType;
        private TextBox txtTotalPayments;
        private TextBox txtTotalAmount;
        private DataGridView dgvPaymentTypes;

        private List<Payment> payments;

        public PaymentsReportForm(List<Payment> paymentsList)
        {
            payments = paymentsList;
            InitializeComponent();
            LoadReport();
        }

        private void InitializeComponent()
        {
            this.dgvReport = new DataGridView();
            this.btnPrint = new Button();
            this.btnExport = new Button();
            this.btnClose = new Button();
            this.lblTitle = new Label();
            this.lblTotalPayments = new Label();
            this.lblTotalAmount = new Label();
            this.lblByPaymentType = new Label();
            this.txtTotalPayments = new TextBox();
            this.txtTotalAmount = new TextBox();
            this.dgvPaymentTypes = new DataGridView();

            this.SuspendLayout();

            // Form
            this.Text = "تقرير سندات الصرف";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Title
            this.lblTitle.Text = "تقرير سندات الصرف";
            this.lblTitle.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(25, 118, 210);
            this.lblTitle.Location = new Point(350, 20);
            this.lblTitle.Size = new Size(300, 35);
            this.lblTitle.TextAlign = ContentAlignment.MiddleCenter;

            // Summary Controls
            this.lblTotalPayments.Text = "إجمالي عدد السندات:";
            this.lblTotalPayments.Location = new Point(800, 70);
            this.lblTotalPayments.Size = new Size(120, 23);
            this.lblTotalPayments.TextAlign = ContentAlignment.MiddleRight;
            this.lblTotalPayments.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.txtTotalPayments.Location = new Point(650, 70);
            this.txtTotalPayments.Size = new Size(120, 23);
            this.txtTotalPayments.ReadOnly = true;
            this.txtTotalPayments.BackColor = Color.LightGray;
            this.txtTotalPayments.TextAlign = HorizontalAlignment.Right;
            this.txtTotalPayments.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.lblTotalAmount.Text = "إجمالي المبلغ:";
            this.lblTotalAmount.Location = new Point(500, 70);
            this.lblTotalAmount.Size = new Size(100, 23);
            this.lblTotalAmount.TextAlign = ContentAlignment.MiddleRight;
            this.lblTotalAmount.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.txtTotalAmount.Location = new Point(350, 70);
            this.txtTotalAmount.Size = new Size(140, 23);
            this.txtTotalAmount.ReadOnly = true;
            this.txtTotalAmount.BackColor = Color.LightGray;
            this.txtTotalAmount.TextAlign = HorizontalAlignment.Right;
            this.txtTotalAmount.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            // Main Report DataGridView
            this.dgvReport.Location = new Point(20, 110);
            this.dgvReport.Size = new Size(960, 350);
            this.dgvReport.AllowUserToAddRows = false;
            this.dgvReport.AllowUserToDeleteRows = false;
            this.dgvReport.ReadOnly = true;
            this.dgvReport.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvReport.RightToLeft = RightToLeft.Yes;
            this.dgvReport.Font = new Font("Tahoma", 10F);

            SetupMainReportGrid();

            // Payment Types Summary
            this.lblByPaymentType.Text = "ملخص حسب نوع الصرف:";
            this.lblByPaymentType.Location = new Point(800, 480);
            this.lblByPaymentType.Size = new Size(150, 23);
            this.lblByPaymentType.TextAlign = ContentAlignment.MiddleRight;
            this.lblByPaymentType.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.dgvPaymentTypes.Location = new Point(500, 510);
            this.dgvPaymentTypes.Size = new Size(480, 120);
            this.dgvPaymentTypes.AllowUserToAddRows = false;
            this.dgvPaymentTypes.AllowUserToDeleteRows = false;
            this.dgvPaymentTypes.ReadOnly = true;
            this.dgvPaymentTypes.RightToLeft = RightToLeft.Yes;
            this.dgvPaymentTypes.Font = new Font("Tahoma", 10F);

            SetupPaymentTypesGrid();

            // Action Buttons
            this.btnPrint.Text = "طباعة";
            this.btnPrint.Location = new Point(350, 580);
            this.btnPrint.Size = new Size(100, 35);
            this.btnPrint.BackColor = Color.FromArgb(103, 58, 183);
            this.btnPrint.ForeColor = Color.White;
            this.btnPrint.FlatStyle = FlatStyle.Flat;
            this.btnPrint.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            this.btnPrint.Click += BtnPrint_Click;

            this.btnExport.Text = "تصدير";
            this.btnExport.Location = new Point(220, 580);
            this.btnExport.Size = new Size(100, 35);
            this.btnExport.BackColor = Color.FromArgb(76, 175, 80);
            this.btnExport.ForeColor = Color.White;
            this.btnExport.FlatStyle = FlatStyle.Flat;
            this.btnExport.Font = new Font("Tahoma", 10F);
            this.btnExport.Click += BtnExport_Click;

            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(90, 580);
            this.btnClose.Size = new Size(100, 35);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Font = new Font("Tahoma", 10F);
            this.btnClose.Click += BtnClose_Click;

            // Add controls to form
            this.Controls.Add(this.lblTitle);
            this.Controls.Add(this.lblTotalPayments);
            this.Controls.Add(this.txtTotalPayments);
            this.Controls.Add(this.lblTotalAmount);
            this.Controls.Add(this.txtTotalAmount);
            this.Controls.Add(this.dgvReport);
            this.Controls.Add(this.lblByPaymentType);
            this.Controls.Add(this.dgvPaymentTypes);
            this.Controls.Add(this.btnPrint);
            this.Controls.Add(this.btnExport);
            this.Controls.Add(this.btnClose);

            this.ResumeLayout(false);
        }

        private void SetupMainReportGrid()
        {
            dgvReport.Columns.Clear();

            dgvReport.Columns.Add("PaymentNumber", "رقم السند");
            dgvReport.Columns["PaymentNumber"].Width = 100;

            dgvReport.Columns.Add("PaymentDate", "التاريخ");
            dgvReport.Columns["PaymentDate"].Width = 100;
            dgvReport.Columns["PaymentDate"].DefaultCellStyle.Format = "yyyy/MM/dd";

            dgvReport.Columns.Add("PaymentType", "نوع الصرف");
            dgvReport.Columns["PaymentType"].Width = 120;

            dgvReport.Columns.Add("Beneficiary", "المستفيد");
            dgvReport.Columns["Beneficiary"].Width = 200;

            dgvReport.Columns.Add("Amount", "المبلغ");
            dgvReport.Columns["Amount"].Width = 120;
            dgvReport.Columns["Amount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvReport.Columns["Amount"].DefaultCellStyle.Format = "N2";

            dgvReport.Columns.Add("PaymentMethod", "طريقة الدفع");
            dgvReport.Columns["PaymentMethod"].Width = 120;

            dgvReport.Columns.Add("ReferenceNumber", "رقم المرجع");
            dgvReport.Columns["ReferenceNumber"].Width = 120;

            dgvReport.Columns.Add("Status", "الحالة");
            dgvReport.Columns["Status"].Width = 80;

            dgvReport.Columns.Add("Description", "الوصف");
            dgvReport.Columns["Description"].Width = 200;
        }

        private void SetupPaymentTypesGrid()
        {
            dgvPaymentTypes.Columns.Clear();

            dgvPaymentTypes.Columns.Add("PaymentType", "نوع الصرف");
            dgvPaymentTypes.Columns["PaymentType"].Width = 150;

            dgvPaymentTypes.Columns.Add("Count", "عدد السندات");
            dgvPaymentTypes.Columns["Count"].Width = 100;
            dgvPaymentTypes.Columns["Count"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            dgvPaymentTypes.Columns.Add("TotalAmount", "إجمالي المبلغ");
            dgvPaymentTypes.Columns["TotalAmount"].Width = 150;
            dgvPaymentTypes.Columns["TotalAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvPaymentTypes.Columns["TotalAmount"].DefaultCellStyle.Format = "N2";

            dgvPaymentTypes.Columns.Add("Percentage", "النسبة %");
            dgvPaymentTypes.Columns["Percentage"].Width = 80;
            dgvPaymentTypes.Columns["Percentage"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvPaymentTypes.Columns["Percentage"].DefaultCellStyle.Format = "N1";
        }

        private void LoadReport()
        {
            try
            {
                LoadMainReport();
                LoadPaymentTypesSummary();
                LoadSummaryData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadMainReport()
        {
            dgvReport.Rows.Clear();

            foreach (var payment in payments.OrderByDescending(p => p.PaymentDate))
            {
                var rowIndex = dgvReport.Rows.Add();
                var row = dgvReport.Rows[rowIndex];

                row.Cells["PaymentNumber"].Value = payment.PaymentNumber;
                row.Cells["PaymentDate"].Value = payment.PaymentDate;
                row.Cells["PaymentType"].Value = payment.PaymentType;
                row.Cells["Beneficiary"].Value = payment.Beneficiary;
                row.Cells["Amount"].Value = payment.Amount;
                row.Cells["PaymentMethod"].Value = payment.PaymentMethod;
                row.Cells["ReferenceNumber"].Value = payment.ReferenceNumber;
                row.Cells["Status"].Value = payment.Status;
                row.Cells["Description"].Value = payment.Description;

                // تلوين الصفوف حسب الحالة
                if (payment.Status == "مؤكد")
                {
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else if (payment.Status == "مسودة")
                {
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                }
                else if (payment.Status == "ملغي")
                {
                    row.DefaultCellStyle.BackColor = Color.LightCoral;
                }
            }
        }

        private void LoadPaymentTypesSummary()
        {
            dgvPaymentTypes.Rows.Clear();

            var paymentTypeGroups = payments
                .Where(p => p.Status == "مؤكد")
                .GroupBy(p => p.PaymentType)
                .Select(g => new
                {
                    PaymentType = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(p => p.Amount)
                })
                .OrderByDescending(g => g.TotalAmount);

            decimal grandTotal = payments.Where(p => p.Status == "مؤكد").Sum(p => p.Amount);

            foreach (var group in paymentTypeGroups)
            {
                var rowIndex = dgvPaymentTypes.Rows.Add();
                var row = dgvPaymentTypes.Rows[rowIndex];

                row.Cells["PaymentType"].Value = group.PaymentType;
                row.Cells["Count"].Value = group.Count;
                row.Cells["TotalAmount"].Value = group.TotalAmount;
                row.Cells["Percentage"].Value = grandTotal > 0 ? (group.TotalAmount / grandTotal) * 100 : 0;
            }
        }

        private void LoadSummaryData()
        {
            var confirmedPayments = payments.Where(p => p.Status == "مؤكد").ToList();

            txtTotalPayments.Text = confirmedPayments.Count.ToString();
            txtTotalAmount.Text = confirmedPayments.Sum(p => p.Amount).ToString("N2");
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                PrintReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintReport()
        {
            var reportLines = new List<string>();

            // عنوان التقرير
            reportLines.Add("تقرير سندات الصرف");
            reportLines.Add("==================");
            reportLines.Add($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
            reportLines.Add($"إجمالي عدد السندات: {txtTotalPayments.Text}");
            reportLines.Add($"إجمالي المبلغ: {txtTotalAmount.Text} ريال");
            reportLines.Add("");

            // تفاصيل السندات
            reportLines.Add("تفاصيل السندات:");
            reportLines.Add("رقم السند    التاريخ      نوع الصرف        المستفيد                المبلغ        طريقة الدفع    الحالة");
            reportLines.Add("====================================================================================================");

            foreach (var payment in payments.OrderByDescending(p => p.PaymentDate))
            {
                string line = $"{payment.PaymentNumber.PadRight(12)} {payment.PaymentDate:yyyy/MM/dd} " +
                             $"{payment.PaymentType.PadRight(15)} {payment.Beneficiary.PadRight(25)} " +
                             $"{payment.Amount.ToString("N2").PadLeft(12)} {payment.PaymentMethod.PadRight(15)} " +
                             $"{payment.Status}";
                reportLines.Add(line);
            }

            reportLines.Add("====================================================================================================");

            // ملخص أنواع الصرف
            reportLines.Add("");
            reportLines.Add("ملخص حسب نوع الصرف:");
            reportLines.Add("نوع الصرف           عدد السندات    إجمالي المبلغ    النسبة %");
            reportLines.Add("================================================================");

            var paymentTypeGroups = payments
                .Where(p => p.Status == "مؤكد")
                .GroupBy(p => p.PaymentType)
                .Select(g => new
                {
                    PaymentType = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(p => p.Amount)
                })
                .OrderByDescending(g => g.TotalAmount);

            decimal grandTotal = payments.Where(p => p.Status == "مؤكد").Sum(p => p.Amount);

            foreach (var group in paymentTypeGroups)
            {
                decimal percentage = grandTotal > 0 ? (group.TotalAmount / grandTotal) * 100 : 0;
                string line = $"{group.PaymentType.PadRight(18)} {group.Count.ToString().PadLeft(12)} " +
                             $"{group.TotalAmount.ToString("N2").PadLeft(15)} {percentage.ToString("N1").PadLeft(10)}";
                reportLines.Add(line);
            }

            // حفظ التقرير مؤقتاً وطباعته
            string tempFile = System.IO.Path.GetTempFileName() + ".txt";
            System.IO.File.WriteAllLines(tempFile, reportLines, System.Text.Encoding.UTF8);

            try
            {
                System.Diagnostics.Process.Start("notepad.exe", "/p " + tempFile);
            }
            catch
            {
                MessageBox.Show($"تم حفظ التقرير في: {tempFile}", "تقرير سندات الصرف",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "ملفات CSV (*.csv)|*.csv|ملفات نصية (*.txt)|*.txt";
                saveDialog.Title = "تصدير تقرير سندات الصرف";
                saveDialog.FileName = $"تقرير_سندات_الصرف_{DateTime.Now:yyyyMMdd}.csv";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToCSV(saveDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToCSV(string filePath)
        {
            var lines = new List<string>();

            // رأس الملف
            lines.Add("رقم السند,التاريخ,نوع الصرف,المستفيد,المبلغ,طريقة الدفع,رقم المرجع,الحالة,الوصف");

            // البيانات
            foreach (var payment in payments.OrderByDescending(p => p.PaymentDate))
            {
                lines.Add($"{payment.PaymentNumber},{payment.PaymentDate:yyyy/MM/dd},{payment.PaymentType}," +
                         $"{payment.Beneficiary},{payment.Amount:N2},{payment.PaymentMethod}," +
                         $"{payment.ReferenceNumber},{payment.Status},{payment.Description}");
            }

            System.IO.File.WriteAllLines(filePath, lines, System.Text.Encoding.UTF8);

            MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{filePath}", "نجح التصدير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
