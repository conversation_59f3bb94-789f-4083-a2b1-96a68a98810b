@echo off
chcp 65001 > nul
echo ========================================
echo بناء نظام السندات المحسن
echo ========================================
echo.

echo هذا البناء يحل مشكلة المراجع المفقودة
echo.

rem التحقق من وجود مترجم C#
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ مترجم C# غير متوفر
    echo يرجى تشغيل Developer Command Prompt for Visual Studio
    echo أو تثبيت .NET Framework SDK
    goto :end
)

echo ✓ مترجم C# متوفر
echo.

echo بناء النظام بملف واحد...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Configuration.dll ^
    /out:ReceiptsSystemFixed.exe ^
    Models\Account.cs ^
    Models\JournalEntry.cs ^
    Models\User.cs ^
    Models\Customer.cs ^
    Models\Product.cs ^
    Models\Receipt.cs ^
    Models\Payment.cs ^
    Models\Invoice.cs ^
    Services\SimpleAuthenticationService.cs ^
    Data\SimpleDatabaseHelper.cs ^
    Utils\SecurityHelper.cs ^
    Utils\ConfigHelper.cs ^
    Properties\Resources.Designer.cs ^
    Properties\Settings.Designer.cs ^
    Forms\LoginForm.cs ^
    Forms\AddEditReceiptForm.cs ^
    Forms\ReceiptsReportForm.cs ^
    Forms\AddEditPaymentForm.cs ^
    Forms\PaymentsReportForm.cs ^
    Forms\CustomerPaymentForm.cs ^
    Forms\ReceiptsForm.cs ^
    Forms\PaymentsForm.cs ^
    Forms\CustomerStatementForm.cs ^
    Forms\MainFormSimple.cs ^
    ProgramSimple.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء النظام بنجاح!
    echo.
    echo تم إنشاء: ReceiptsSystemFixed.exe
    echo.
    
    echo إنشاء ملفات الإعداد...
    
    rem إنشاء ملف App.config
    echo ^<?xml version="1.0" encoding="utf-8"?^> > ReceiptsSystemFixed.exe.config
    echo ^<configuration^> >> ReceiptsSystemFixed.exe.config
    echo   ^<startup^> >> ReceiptsSystemFixed.exe.config
    echo     ^<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/^> >> ReceiptsSystemFixed.exe.config
    echo   ^</startup^> >> ReceiptsSystemFixed.exe.config
    echo ^</configuration^> >> ReceiptsSystemFixed.exe.config
    
    rem إنشاء مجلد البيانات
    if not exist "SimpleData" mkdir "SimpleData"
    
    rem إنشاء ملف الإعداد
    if not exist "system_config.json" (
        echo { > system_config.json
        echo   "DatabasePath": "SimpleData", >> system_config.json
        echo   "CompanyName": "شركة المحاسبة الذكية", >> system_config.json
        echo   "CompanyAddress": "المملكة العربية السعودية", >> system_config.json
        echo   "Currency": "ريال سعودي", >> system_config.json
        echo   "DateFormat": "yyyy/MM/dd", >> system_config.json
        echo   "Language": "ar-SA", >> system_config.json
        echo   "BackupEnabled": true, >> system_config.json
        echo   "AutoBackupDays": 7 >> system_config.json
        echo } >> system_config.json
    )
    
    echo ✓ تم إنشاء ملفات الإعداد
    echo.
    
    set /p run="هل تريد تشغيل النظام الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام...
        start "" "ReceiptsSystemFixed.exe"
        echo.
        echo ✓ تم تشغيل النظام بنجاح!
    )
    
    echo.
    echo ========================================
    echo معلومات النظام
    echo ========================================
    echo.
    echo ملف التشغيل: ReceiptsSystemFixed.exe
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo الميزات المتوفرة:
    echo ✓ إدارة سندات القبض مع التقارير
    echo ✓ إدارة سندات الصرف مع التقارير
    echo ✓ كشف حساب العميل التفصيلي
    echo ✓ نظام تسجيل دفعات العملاء
    echo ✓ فلترة وبحث متقدم
    echo ✓ طباعة وتصدير التقارير
    echo ✓ واجهة عربية كاملة
    echo.
    echo القوائم المتوفرة:
    echo • السندات ^> سندات القبض
    echo • السندات ^> سندات الصرف  
    echo • التقارير ^> كشف حساب العميل
    echo • التقارير ^> تقرير سندات القبض
    echo • التقارير ^> تقرير سندات الصرف
    
) else (
    echo.
    echo ✗ فشل في بناء النظام
    echo.
    echo الأخطاء المحتملة:
    echo 1. أخطاء في الكود المصدري
    echo 2. مراجع مفقودة
    echo 3. مشاكل في مترجم C#
    echo.
    echo للتشخيص:
    echo 1. شغل test_receipts_build.bat للفحص
    echo 2. تأكد من وجود جميع الملفات
    echo 3. استخدم Developer Command Prompt
    echo.
    echo بدائل أخرى:
    echo • build_step_by_step.bat - بناء تدريجي
    echo • build_simple.bat - بناء مبسط
)

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
