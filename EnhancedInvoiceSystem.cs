using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace EnhancedInvoiceSystem
{
    // نماذج البيانات المحسنة
    public class Customer
    {
        public int Id { get; set; }
        public string CustomerCode { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public decimal CurrentBalance { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class Product
    {
        public int Id { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string Category { get; set; }
        public decimal UnitPrice { get; set; }
        public int StockQuantity { get; set; }
        public string Unit { get; set; }
        public bool IsActive { get; set; }
    }

    public class Invoice
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public int CustomerId { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public List<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();

        public decimal RemainingAmount => TotalAmount - PaidAmount;
        public bool IsOverdue => DueDate < DateTime.Now && Status != "مدفوعة" && Status != "ملغية";
        public int DaysOverdue => IsOverdue ? (DateTime.Now - DueDate).Days : 0;
    }

    public class InvoiceItem
    {
        public int Id { get; set; }
        public int InvoiceId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountPercent { get; set; }
        public decimal TotalPrice => (Quantity * UnitPrice) * (1 - DiscountPercent / 100);
    }

    public class Receipt
    {
        public int Id { get; set; }
        public string ReceiptNumber { get; set; }
        public DateTime ReceiptDate { get; set; }
        public int CustomerId { get; set; }
        public int? InvoiceId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string ReferenceNumber { get; set; }
        public string Notes { get; set; }
        public string Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
    }

    // خدمة البيانات المحسنة
    public static class DataService
    {
        public static List<Customer> Customers { get; set; }
        public static List<Product> Products { get; set; }
        public static List<Invoice> Invoices { get; set; }
        public static List<Receipt> Receipts { get; set; }

        static DataService()
        {
            LoadSampleData();
        }

        private static void LoadSampleData()
        {
            // بيانات العملاء
            Customers = new List<Customer>
            {
                new Customer { Id = 1, CustomerCode = "C001", CustomerName = "أحمد محمد علي", Phone = "0501234567", Email = "<EMAIL>", Address = "الرياض، حي النخيل", CurrentBalance = 15000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-30) },
                new Customer { Id = 2, CustomerCode = "C002", CustomerName = "فاطمة عبدالله", Phone = "0507654321", Email = "<EMAIL>", Address = "جدة، حي الصفا", CurrentBalance = -2500, IsActive = true, CreatedDate = DateTime.Now.AddDays(-25) },
                new Customer { Id = 3, CustomerCode = "C003", CustomerName = "محمد سعد الدين", Phone = "0551122334", Email = "<EMAIL>", Address = "الدمام، حي الفيصلية", CurrentBalance = 8000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-20) },
                new Customer { Id = 4, CustomerCode = "C004", CustomerName = "نورا أحمد", Phone = "0554433221", Email = "<EMAIL>", Address = "مكة، حي العزيزية", CurrentBalance = 0, IsActive = true, CreatedDate = DateTime.Now.AddDays(-15) },
                new Customer { Id = 5, CustomerCode = "C005", CustomerName = "خالد العتيبي", Phone = "0556677889", Email = "<EMAIL>", Address = "المدينة، حي قباء", CurrentBalance = 25000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-10) },
                new Customer { Id = 6, CustomerCode = "C006", CustomerName = "سارة الأحمد", Phone = "0559988776", Email = "<EMAIL>", Address = "الطائف، حي الشفا", CurrentBalance = 12000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-5) }
            };

            // بيانات المنتجات
            Products = new List<Product>
            {
                new Product { Id = 1, ProductCode = "P001", ProductName = "جهاز كمبيوتر محمول", Category = "إلكترونيات", UnitPrice = 3500, StockQuantity = 25, Unit = "جهاز", IsActive = true },
                new Product { Id = 2, ProductCode = "P002", ProductName = "طابعة ليزر", Category = "إلكترونيات", UnitPrice = 800, StockQuantity = 15, Unit = "جهاز", IsActive = true },
                new Product { Id = 3, ProductCode = "P003", ProductName = "خدمة استشارية", Category = "خدمات", UnitPrice = 500, StockQuantity = 999, Unit = "ساعة", IsActive = true },
                new Product { Id = 4, ProductCode = "P004", ProductName = "مواد خام", Category = "مواد", UnitPrice = 150, StockQuantity = 200, Unit = "كيلو", IsActive = true },
                new Product { Id = 5, ProductCode = "P005", ProductName = "معدات مكتبية", Category = "مكتبية", UnitPrice = 250, StockQuantity = 50, Unit = "قطعة", IsActive = true },
                new Product { Id = 6, ProductCode = "P006", ProductName = "برمجيات", Category = "تقنية", UnitPrice = 1200, StockQuantity = 100, Unit = "رخصة", IsActive = true }
            };

            // بيانات الفواتير
            Invoices = new List<Invoice>
            {
                new Invoice
                {
                    Id = 1, InvoiceNumber = "INV-2024-001", InvoiceDate = DateTime.Now.AddDays(-45),
                    DueDate = DateTime.Now.AddDays(-15), CustomerId = 1, SubTotal = 10000, TaxAmount = 1500,
                    TotalAmount = 11500, PaidAmount = 5000, Status = "مدفوعة جزئياً",
                    Notes = "فاتورة مبيعات أجهزة كمبيوتر", CreatedDate = DateTime.Now.AddDays(-45), CreatedBy = "admin",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 1, ProductId = 1, ProductName = "جهاز كمبيوتر محمول", Quantity = 2, UnitPrice = 3500, DiscountPercent = 5 },
                        new InvoiceItem { Id = 2, ProductId = 2, ProductName = "طابعة ليزر", Quantity = 4, UnitPrice = 800, DiscountPercent = 0 }
                    }
                },
                new Invoice
                {
                    Id = 2, InvoiceNumber = "INV-2024-002", InvoiceDate = DateTime.Now.AddDays(-30),
                    DueDate = DateTime.Now.AddDays(-10), CustomerId = 2, SubTotal = 5000, TaxAmount = 750,
                    TotalAmount = 5750, PaidAmount = 5750, Status = "مدفوعة",
                    Notes = "فاتورة خدمات استشارية", CreatedDate = DateTime.Now.AddDays(-30), CreatedBy = "admin",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 3, ProductId = 3, ProductName = "خدمة استشارية", Quantity = 10, UnitPrice = 500, DiscountPercent = 0 }
                    }
                },
                new Invoice
                {
                    Id = 3, InvoiceNumber = "INV-2024-003", InvoiceDate = DateTime.Now.AddDays(-25),
                    DueDate = DateTime.Now.AddDays(-5), CustomerId = 3, SubTotal = 8000, TaxAmount = 1200,
                    TotalAmount = 9200, PaidAmount = 0, Status = "متأخرة",
                    Notes = "فاتورة مواد خام", CreatedDate = DateTime.Now.AddDays(-25), CreatedBy = "admin",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 4, ProductId = 4, ProductName = "مواد خام", Quantity = 50, UnitPrice = 150, DiscountPercent = 6.25m }
                    }
                },
                new Invoice
                {
                    Id = 4, InvoiceNumber = "INV-2024-004", InvoiceDate = DateTime.Now.AddDays(-20),
                    DueDate = DateTime.Now.AddDays(10), CustomerId = 4, SubTotal = 12000, TaxAmount = 1800,
                    TotalAmount = 13800, PaidAmount = 0, Status = "مؤكدة",
                    Notes = "فاتورة معدات مكتبية", CreatedDate = DateTime.Now.AddDays(-20), CreatedBy = "admin",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 5, ProductId = 5, ProductName = "معدات مكتبية", Quantity = 48, UnitPrice = 250, DiscountPercent = 0 }
                    }
                },
                new Invoice
                {
                    Id = 5, InvoiceNumber = "INV-2024-005", InvoiceDate = DateTime.Now.AddDays(-15),
                    DueDate = DateTime.Now.AddDays(15), CustomerId = 5, SubTotal = 20000, TaxAmount = 3000,
                    TotalAmount = 23000, PaidAmount = 10000, Status = "مدفوعة جزئياً",
                    Notes = "فاتورة مشروع تطوير", CreatedDate = DateTime.Now.AddDays(-15), CreatedBy = "admin",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 6, ProductId = 6, ProductName = "برمجيات", Quantity = 15, UnitPrice = 1200, DiscountPercent = 10 },
                        new InvoiceItem { Id = 7, ProductId = 3, ProductName = "خدمة استشارية", Quantity = 10, UnitPrice = 500, DiscountPercent = 0 }
                    }
                },
                new Invoice
                {
                    Id = 6, InvoiceNumber = "INV-2024-006", InvoiceDate = DateTime.Now.AddDays(-10),
                    DueDate = DateTime.Now.AddDays(20), CustomerId = 1, SubTotal = 3000, TaxAmount = 450,
                    TotalAmount = 3450, PaidAmount = 0, Status = "مسودة",
                    Notes = "فاتورة صيانة", CreatedDate = DateTime.Now.AddDays(-10), CreatedBy = "admin",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 8, ProductId = 3, ProductName = "خدمة استشارية", Quantity = 6, UnitPrice = 500, DiscountPercent = 0 }
                    }
                },
                new Invoice
                {
                    Id = 7, InvoiceNumber = "INV-2024-007", InvoiceDate = DateTime.Now.AddDays(-60),
                    DueDate = DateTime.Now.AddDays(-30), CustomerId = 2, SubTotal = 7500, TaxAmount = 1125,
                    TotalAmount = 8625, PaidAmount = 0, Status = "متأخرة",
                    Notes = "فاتورة تدريب", CreatedDate = DateTime.Now.AddDays(-60), CreatedBy = "admin",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 9, ProductId = 3, ProductName = "خدمة استشارية", Quantity = 15, UnitPrice = 500, DiscountPercent = 0 }
                    }
                },
                new Invoice
                {
                    Id = 8, InvoiceNumber = "INV-2024-008", InvoiceDate = DateTime.Now.AddDays(-5),
                    DueDate = DateTime.Now.AddDays(25), CustomerId = 6, SubTotal = 15600, TaxAmount = 2340,
                    TotalAmount = 17940, PaidAmount = 17940, Status = "مدفوعة",
                    Notes = "فاتورة مواد تسويقية", CreatedDate = DateTime.Now.AddDays(-5), CreatedBy = "admin",
                    Items = new List<InvoiceItem>
                    {
                        new InvoiceItem { Id = 10, ProductId = 1, ProductName = "جهاز كمبيوتر محمول", Quantity = 4, UnitPrice = 3500, DiscountPercent = 10 },
                        new InvoiceItem { Id = 11, ProductId = 2, ProductName = "طابعة ليزر", Quantity = 2, UnitPrice = 800, DiscountPercent = 0 }
                    }
                }
            };

            // بيانات السندات
            Receipts = new List<Receipt>
            {
                new Receipt { Id = 1, ReceiptNumber = "R001", ReceiptDate = DateTime.Now.AddDays(-28), CustomerId = 1, InvoiceId = 1, Amount = 5000, PaymentMethod = "نقدي", ReferenceNumber = "CASH001", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-28), CreatedBy = "admin" },
                new Receipt { Id = 2, ReceiptNumber = "R002", ReceiptDate = DateTime.Now.AddDays(-18), CustomerId = 2, InvoiceId = 2, Amount = 5750, PaymentMethod = "تحويل بنكي", ReferenceNumber = "TRF002", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-18), CreatedBy = "admin" },
                new Receipt { Id = 3, ReceiptNumber = "R003", ReceiptDate = DateTime.Now.AddDays(-10), CustomerId = 5, InvoiceId = 5, Amount = 10000, PaymentMethod = "شيك", ReferenceNumber = "CHK003", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-10), CreatedBy = "admin" },
                new Receipt { Id = 4, ReceiptNumber = "R004", ReceiptDate = DateTime.Now.AddDays(-3), CustomerId = 6, InvoiceId = 8, Amount = 17940, PaymentMethod = "تحويل بنكي", ReferenceNumber = "TRF004", Status = "مؤكد", CreatedDate = DateTime.Now.AddDays(-3), CreatedBy = "admin" },
                new Receipt { Id = 5, ReceiptNumber = "R005", ReceiptDate = DateTime.Now.AddDays(-1), CustomerId = 1, InvoiceId = null, Amount = 2000, PaymentMethod = "نقدي", ReferenceNumber = "CASH005", Status = "مسودة", CreatedDate = DateTime.Now.AddDays(-1), CreatedBy = "admin" }
            };
        }

        public static void AddInvoice(Invoice invoice)
        {
            invoice.Id = Invoices.Count > 0 ? Invoices.Max(i => i.Id) + 1 : 1;
            invoice.InvoiceNumber = $"INV-2024-{invoice.Id:000}";
            invoice.CreatedDate = DateTime.Now;
            invoice.CreatedBy = AuthService.CurrentUser;
            Invoices.Add(invoice);
        }

        public static void UpdateInvoice(Invoice invoice)
        {
            var existingInvoice = Invoices.FirstOrDefault(i => i.Id == invoice.Id);
            if (existingInvoice != null)
            {
                var index = Invoices.IndexOf(existingInvoice);
                Invoices[index] = invoice;
            }
        }

        public static void DeleteInvoice(int invoiceId)
        {
            var invoice = Invoices.FirstOrDefault(i => i.Id == invoiceId);
            if (invoice != null)
            {
                Invoices.Remove(invoice);
            }
        }
    }

    // خدمة المصادقة
    public static class AuthService
    {
        public static string CurrentUser { get; private set; } = "admin";

        public static bool Login(string username, string password)
        {
            if (username == "admin" && password == "admin123")
            {
                CurrentUser = username;
                return true;
            }
            return false;
        }

        public static void Logout()
        {
            CurrentUser = null;
        }
    }

    // نموذج تسجيل الدخول المحسن
    public class LoginForm : Form
    {
        private Panel mainPanel;
        private Panel loginPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Label lblTitle;

        public LoginForm()
        {
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "تسجيل الدخول - نظام الفواتير المحسن";
            this.Size = new Size(900, 650);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.RightToLeft = RightToLeft.Yes;

            // الخلفية الرئيسية مع تدرج
            mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Paint += MainPanel_Paint;

            // لوحة تسجيل الدخول
            loginPanel = new Panel();
            loginPanel.Size = new Size(450, 400);
            loginPanel.Location = new Point(225, 125);
            loginPanel.BackColor = Color.White;
            loginPanel.Paint += LoginPanel_Paint;

            // العنوان الرئيسي
            lblTitle = new Label();
            lblTitle.Text = "نظام الفواتير المحسن";
            lblTitle.Font = new Font("Segoe UI", 28F, FontStyle.Bold);
            lblTitle.ForeColor = Color.FromArgb(41, 128, 185);
            lblTitle.Location = new Point(50, 50);
            lblTitle.Size = new Size(350, 60);
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;

            var lblSubtitle = new Label();
            lblSubtitle.Text = "داشبورد متكامل مع إدارة شاملة للفواتير";
            lblSubtitle.Font = new Font("Segoe UI", 14F);
            lblSubtitle.ForeColor = Color.FromArgb(127, 140, 141);
            lblSubtitle.Location = new Point(50, 110);
            lblSubtitle.Size = new Size(350, 35);
            lblSubtitle.TextAlign = ContentAlignment.MiddleCenter;

            // اسم المستخدم
            var lblUsername = new Label();
            lblUsername.Text = "اسم المستخدم";
            lblUsername.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblUsername.ForeColor = Color.FromArgb(52, 73, 94);
            lblUsername.Location = new Point(50, 170);
            lblUsername.Size = new Size(120, 30);

            txtUsername = new TextBox();
            txtUsername.Location = new Point(50, 200);
            txtUsername.Size = new Size(350, 35);
            txtUsername.Font = new Font("Segoe UI", 12F);
            txtUsername.Text = "admin";
            txtUsername.BorderStyle = BorderStyle.FixedSingle;
            txtUsername.Padding = new Padding(10);

            // كلمة المرور
            var lblPassword = new Label();
            lblPassword.Text = "كلمة المرور";
            lblPassword.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblPassword.ForeColor = Color.FromArgb(52, 73, 94);
            lblPassword.Location = new Point(50, 250);
            lblPassword.Size = new Size(120, 30);

            txtPassword = new TextBox();
            txtPassword.Location = new Point(50, 280);
            txtPassword.Size = new Size(350, 35);
            txtPassword.Font = new Font("Segoe UI", 12F);
            txtPassword.UseSystemPasswordChar = true;
            txtPassword.Text = "admin123";
            txtPassword.BorderStyle = BorderStyle.FixedSingle;
            txtPassword.Padding = new Padding(10);

            // زر الدخول
            btnLogin = new Button();
            btnLogin.Text = "دخول إلى النظام";
            btnLogin.Location = new Point(50, 340);
            btnLogin.Size = new Size(350, 50);
            btnLogin.BackColor = Color.FromArgb(41, 128, 185);
            btnLogin.ForeColor = Color.White;
            btnLogin.FlatStyle = FlatStyle.Flat;
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            btnLogin.Cursor = Cursors.Hand;
            btnLogin.Click += BtnLogin_Click;

            // إضافة التحكمات
            loginPanel.Controls.Add(lblTitle);
            loginPanel.Controls.Add(lblSubtitle);
            loginPanel.Controls.Add(lblUsername);
            loginPanel.Controls.Add(txtUsername);
            loginPanel.Controls.Add(lblPassword);
            loginPanel.Controls.Add(txtPassword);
            loginPanel.Controls.Add(btnLogin);

            mainPanel.Controls.Add(loginPanel);
            this.Controls.Add(mainPanel);

            this.AcceptButton = btnLogin;
        }

        private void MainPanel_Paint(object sender, PaintEventArgs e)
        {
            // تدرج الخلفية
            using (LinearGradientBrush brush = new LinearGradientBrush(
                mainPanel.ClientRectangle,
                Color.FromArgb(74, 144, 226),
                Color.FromArgb(41, 128, 185),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, mainPanel.ClientRectangle);
            }
        }

        private void LoginPanel_Paint(object sender, PaintEventArgs e)
        {
            // ظل للوحة
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, loginPanel.Width, loginPanel.Height), 20);
                loginPanel.Region = new Region(path);
            }
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (AuthService.Login(txtUsername.Text, txtPassword.Text))
            {
                this.Hide();
                var mainForm = new MainDashboardForm();
                mainForm.ShowDialog();
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    // النموذج الرئيسي المحسن مع الداشبورد المدمج
    public class MainDashboardForm : Form
    {
        private Panel sidePanel;
        private Panel topPanel;
        private Panel contentPanel;
        private Label lblWelcome;
        private Label lblDateTime;
        private Timer timeTimer;
        private string currentView = "dashboard";

        public MainDashboardForm()
        {
            InitializeForm();
            SetupSidePanel();
            SetupTopPanel();
            ShowDashboard();
            StartTimer();
        }

        private void InitializeForm()
        {
            this.Text = "نظام الفواتير المحسن - الداشبورد المتكامل";
            this.Size = new Size(1600, 1000);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(236, 240, 241);

            // الشريط الجانبي
            sidePanel = new Panel();
            sidePanel.Width = 320;
            sidePanel.Dock = DockStyle.Right;
            sidePanel.BackColor = Color.FromArgb(44, 62, 80);
            sidePanel.Paint += SidePanel_Paint;

            // الشريط العلوي
            topPanel = new Panel();
            topPanel.Height = 90;
            topPanel.Dock = DockStyle.Top;
            topPanel.BackColor = Color.White;
            topPanel.Paint += TopPanel_Paint;

            // منطقة المحتوى
            contentPanel = new Panel();
            contentPanel.Dock = DockStyle.Fill;
            contentPanel.BackColor = Color.FromArgb(236, 240, 241);
            contentPanel.Padding = new Padding(25);

            this.Controls.Add(contentPanel);
            this.Controls.Add(topPanel);
            this.Controls.Add(sidePanel);
        }

        private void SetupSidePanel()
        {
            // شعار النظام
            var logoPanel = new Panel();
            logoPanel.Height = 120;
            logoPanel.Dock = DockStyle.Top;
            logoPanel.BackColor = Color.FromArgb(52, 73, 94);

            var lblLogo = new Label();
            lblLogo.Text = "💼\nنظام الفواتير\nالمحسن";
            lblLogo.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            lblLogo.ForeColor = Color.White;
            lblLogo.TextAlign = ContentAlignment.MiddleCenter;
            lblLogo.Dock = DockStyle.Fill;

            logoPanel.Controls.Add(lblLogo);
            sidePanel.Controls.Add(logoPanel);

            // قائمة التنقل المحسنة
            var menuItems = new[]
            {
                new { Text = "🏠 الداشبورد الرئيسي", Action = new Action(() => ShowDashboard()), Key = "dashboard" },
                new { Text = "📊 إدارة الفواتير", Action = new Action(() => ShowInvoicesManagement()), Key = "invoices" },
                new { Text = "➕ إضافة فاتورة جديدة", Action = new Action(() => ShowAddInvoice()), Key = "add_invoice" },
                new { Text = "💰 السندات والمدفوعات", Action = new Action(() => ShowReceipts()), Key = "receipts" },
                new { Text = "👥 إدارة العملاء", Action = new Action(() => ShowCustomers()), Key = "customers" },
                new { Text = "📦 إدارة المنتجات", Action = new Action(() => ShowProducts()), Key = "products" },
                new { Text = "📈 التقارير والإحصائيات", Action = new Action(() => ShowReports()), Key = "reports" },
                new { Text = "⚙️ الإعدادات", Action = new Action(() => ShowSettings()), Key = "settings" },
                new { Text = "🚪 تسجيل خروج", Action = new Action(() => Logout()), Key = "logout" }
            };

            int yPos = 140;
            foreach (var item in menuItems)
            {
                var menuButton = CreateMenuButton(item.Text, item.Action, item.Key);
                menuButton.Location = new Point(15, yPos);
                sidePanel.Controls.Add(menuButton);
                yPos += 65;
            }
        }

        private Button CreateMenuButton(string text, Action action, string key)
        {
            var button = new Button();
            button.Text = text;
            button.Size = new Size(290, 55);
            button.BackColor = currentView == key ? Color.FromArgb(52, 152, 219) : Color.Transparent;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 13F, FontStyle.Bold);
            button.TextAlign = ContentAlignment.MiddleLeft;
            button.Padding = new Padding(25, 0, 0, 0);
            button.Cursor = Cursors.Hand;
            button.Tag = key;
            button.Click += (s, e) =>
            {
                currentView = key;
                UpdateMenuButtons();
                action();
            };

            // تأثيرات التفاعل
            button.MouseEnter += (s, e) =>
            {
                if (currentView != key)
                    button.BackColor = Color.FromArgb(52, 152, 219);
            };
            button.MouseLeave += (s, e) =>
            {
                if (currentView != key)
                    button.BackColor = Color.Transparent;
            };

            return button;
        }

        private void UpdateMenuButtons()
        {
            foreach (Control control in sidePanel.Controls)
            {
                if (control is Button button && button.Tag != null)
                {
                    string key = button.Tag.ToString();
                    button.BackColor = currentView == key ? Color.FromArgb(52, 152, 219) : Color.Transparent;
                }
            }
        }

        private void SetupTopPanel()
        {
            // رسالة الترحيب
            lblWelcome = new Label();
            lblWelcome.Text = $"مرحباً، {AuthService.CurrentUser} 👋";
            lblWelcome.Font = new Font("Segoe UI", 20F, FontStyle.Bold);
            lblWelcome.ForeColor = Color.FromArgb(44, 62, 80);
            lblWelcome.Location = new Point(40, 20);
            lblWelcome.Size = new Size(400, 45);

            // التاريخ والوقت
            lblDateTime = new Label();
            lblDateTime.Font = new Font("Segoe UI", 14F);
            lblDateTime.ForeColor = Color.FromArgb(127, 140, 141);
            lblDateTime.Location = new Point(40, 60);
            lblDateTime.Size = new Size(500, 30);

            // زر الإشعارات
            var btnNotifications = new Button();
            btnNotifications.Text = "🔔 الإشعارات";
            btnNotifications.Size = new Size(120, 40);
            btnNotifications.Location = new Point(topPanel.Width - 200, 25);
            btnNotifications.BackColor = Color.FromArgb(231, 76, 60);
            btnNotifications.ForeColor = Color.White;
            btnNotifications.FlatStyle = FlatStyle.Flat;
            btnNotifications.FlatAppearance.BorderSize = 0;
            btnNotifications.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnNotifications.Cursor = Cursors.Hand;
            btnNotifications.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnNotifications.Click += (s, e) => ShowNotifications();

            topPanel.Controls.Add(lblWelcome);
            topPanel.Controls.Add(lblDateTime);
            topPanel.Controls.Add(btnNotifications);
        }

        private void SidePanel_Paint(object sender, PaintEventArgs e)
        {
            // تدرج للشريط الجانبي
            using (LinearGradientBrush brush = new LinearGradientBrush(
                sidePanel.ClientRectangle,
                Color.FromArgb(44, 62, 80),
                Color.FromArgb(52, 73, 94),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, sidePanel.ClientRectangle);
            }
        }

        private void TopPanel_Paint(object sender, PaintEventArgs e)
        {
            // ظل للشريط العلوي
            using (Pen pen = new Pen(Color.FromArgb(189, 195, 199), 2))
            {
                e.Graphics.DrawLine(pen, 0, topPanel.Height - 2, topPanel.Width, topPanel.Height - 2);
            }
        }

        private void StartTimer()
        {
            timeTimer = new Timer();
            timeTimer.Interval = 1000;
            timeTimer.Tick += (s, e) =>
            {
                lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy - HH:mm:ss",
                    new System.Globalization.CultureInfo("ar-SA"));
            };
            timeTimer.Start();

            // تحديث فوري
            lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy - HH:mm:ss",
                new System.Globalization.CultureInfo("ar-SA"));
        }

        // عرض الداشبورد الرئيسي
        private void ShowDashboard()
        {
            contentPanel.Controls.Clear();
            currentView = "dashboard";
            UpdateMenuButtons();

            // لوحة الإحصائيات
            var statsContainer = new Panel();
            statsContainer.Height = 160;
            statsContainer.Dock = DockStyle.Top;
            statsContainer.BackColor = Color.Transparent;
            statsContainer.Padding = new Padding(0, 0, 0, 25);

            // إحصائيات سريعة محسنة
            var stats = new[]
            {
                new { Title = "إجمالي الفواتير", Value = DataService.Invoices.Count.ToString(), SubValue = $"{DataService.Invoices.Sum(i => i.TotalAmount):N0} ر.س", Color = Color.FromArgb(52, 152, 219), Icon = "📊" },
                new { Title = "الفواتير المدفوعة", Value = DataService.Invoices.Count(i => i.Status == "مدفوعة").ToString(), SubValue = $"{DataService.Invoices.Where(i => i.Status == "مدفوعة").Sum(i => i.TotalAmount):N0} ر.س", Color = Color.FromArgb(46, 204, 113), Icon = "✅" },
                new { Title = "الفواتير المتأخرة", Value = DataService.Invoices.Count(i => i.IsOverdue).ToString(), SubValue = $"{DataService.Invoices.Where(i => i.IsOverdue).Sum(i => i.RemainingAmount):N0} ر.س", Color = Color.FromArgb(231, 76, 60), Icon = "⚠️" },
                new { Title = "إجمالي العملاء", Value = DataService.Customers.Count(c => c.IsActive).ToString(), SubValue = $"{DataService.Customers.Sum(c => c.CurrentBalance):N0} ر.س رصيد", Color = Color.FromArgb(155, 89, 182), Icon = "👥" }
            };

            int cardWidth = 320;
            int cardSpacing = 25;
            int startX = 25;

            for (int i = 0; i < stats.Length; i++)
            {
                var statCard = CreateStatCard(stats[i].Title, stats[i].Value, stats[i].SubValue, stats[i].Color, stats[i].Icon);
                statCard.Location = new Point(startX + (i * (cardWidth + cardSpacing)), 15);
                statsContainer.Controls.Add(statCard);
            }

            contentPanel.Controls.Add(statsContainer);

            // منطقة الرسوم البيانية والجداول
            var chartsContainer = new Panel();
            chartsContainer.Dock = DockStyle.Fill;
            chartsContainer.BackColor = Color.Transparent;

            // الرسم البياني للمبيعات
            var salesChart = CreateSalesChart();
            salesChart.Location = new Point(25, 25);
            chartsContainer.Controls.Add(salesChart);

            // جدول الفواتير الأخيرة
            var recentInvoicesPanel = CreateRecentInvoicesPanel();
            recentInvoicesPanel.Location = new Point(750, 25);
            chartsContainer.Controls.Add(recentInvoicesPanel);

            // جدول العملاء الأعلى رصيداً
            var topCustomersPanel = CreateTopCustomersPanel();
            topCustomersPanel.Location = new Point(25, 380);
            chartsContainer.Controls.Add(topCustomersPanel);

            // إحصائيات سريعة إضافية
            var quickStatsPanel = CreateQuickStatsPanel();
            quickStatsPanel.Location = new Point(750, 380);
            chartsContainer.Controls.Add(quickStatsPanel);

            contentPanel.Controls.Add(chartsContainer);
        }

        private Panel CreateStatCard(string title, string value, string subValue, Color color, string icon)
        {
            var card = new Panel();
            card.Size = new Size(320, 130);
            card.BackColor = Color.White;
            card.Paint += (s, e) => DrawCardShadow(e, card, color);

            // الأيقونة
            var iconLabel = new Label();
            iconLabel.Text = icon;
            iconLabel.Font = new Font("Segoe UI Emoji", 28F);
            iconLabel.Location = new Point(250, 25);
            iconLabel.Size = new Size(60, 60);
            iconLabel.TextAlign = ContentAlignment.MiddleCenter;

            // العنوان
            var titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("Segoe UI", 13F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(25, 25);
            titleLabel.Size = new Size(200, 30);

            // القيمة الرئيسية
            var valueLabel = new Label();
            valueLabel.Text = value;
            valueLabel.Font = new Font("Segoe UI", 24F, FontStyle.Bold);
            valueLabel.ForeColor = color;
            valueLabel.Location = new Point(25, 55);
            valueLabel.Size = new Size(120, 40);

            // القيمة الفرعية
            var subValueLabel = new Label();
            subValueLabel.Text = subValue;
            subValueLabel.Font = new Font("Segoe UI", 11F);
            subValueLabel.ForeColor = Color.FromArgb(127, 140, 141);
            subValueLabel.Location = new Point(25, 100);
            subValueLabel.Size = new Size(200, 25);

            card.Controls.Add(iconLabel);
            card.Controls.Add(titleLabel);
            card.Controls.Add(valueLabel);
            card.Controls.Add(subValueLabel);

            return card;
        }

        private void DrawCardShadow(PaintEventArgs e, Panel card, Color accentColor)
        {
            // خلفية بيضاء مع حواف مدورة
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, card.Width, card.Height), 12);
                card.Region = new Region(path);

                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }

            // خط ملون في الأعلى
            using (SolidBrush brush = new SolidBrush(accentColor))
            {
                e.Graphics.FillRectangle(brush, 0, 0, card.Width, 5);
            }
        }

        private Panel CreateSalesChart()
        {
            var chartPanel = new Panel();
            chartPanel.Size = new Size(700, 330);
            chartPanel.BackColor = Color.White;
            chartPanel.Paint += (s, e) => DrawSalesChart(e, chartPanel);

            var titleLabel = new Label();
            titleLabel.Text = "📈 مبيعات آخر 7 أيام";
            titleLabel.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(25, 20);
            titleLabel.Size = new Size(250, 35);

            chartPanel.Controls.Add(titleLabel);
            return chartPanel;
        }

        private void DrawSalesChart(PaintEventArgs e, Panel panel)
        {
            // خلفية بيضاء مع حواف مدورة
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, panel.Width, panel.Height), 12);
                panel.Region = new Region(path);

                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }

            // رسم بياني محسن للمبيعات
            var salesData = new[] { 18000, 25000, 22000, 28000, 35000, 32000, 40000 };
            var days = new[] { "السبت", "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة" };

            int chartX = 60;
            int chartY = 70;
            int chartWidth = 580;
            int chartHeight = 200;
            int maxValue = salesData.Max();

            // رسم الخطوط الإرشادية
            using (Pen gridPen = new Pen(Color.FromArgb(236, 240, 241), 2))
            {
                for (int i = 0; i <= 5; i++)
                {
                    int y = chartY + (chartHeight * i / 5);
                    e.Graphics.DrawLine(gridPen, chartX, y, chartX + chartWidth, y);
                }
            }

            // رسم البيانات
            using (Pen linePen = new Pen(Color.FromArgb(52, 152, 219), 4))
            using (SolidBrush pointBrush = new SolidBrush(Color.FromArgb(52, 152, 219)))
            using (SolidBrush areaBrush = new SolidBrush(Color.FromArgb(50, 52, 152, 219)))
            {
                var points = new Point[salesData.Length];
                var areaPoints = new Point[salesData.Length + 2];

                for (int i = 0; i < salesData.Length; i++)
                {
                    int x = chartX + (chartWidth * i / (salesData.Length - 1));
                    int y = chartY + chartHeight - (int)((double)salesData[i] / maxValue * chartHeight);
                    points[i] = new Point(x, y);
                    areaPoints[i] = new Point(x, y);

                    // رسم النقطة
                    e.Graphics.FillEllipse(pointBrush, x - 6, y - 6, 12, 12);

                    // رسم القيمة
                    using (Font font = new Font("Segoe UI", 10F, FontStyle.Bold))
                    using (SolidBrush textBrush = new SolidBrush(Color.FromArgb(52, 73, 94)))
                    {
                        string valueText = (salesData[i] / 1000).ToString() + "ك";
                        var textSize = e.Graphics.MeasureString(valueText, font);
                        e.Graphics.DrawString(valueText, font, textBrush, x - textSize.Width / 2, y - 30);
                    }

                    // رسم اسم اليوم
                    using (Font font = new Font("Segoe UI", 11F))
                    using (SolidBrush textBrush = new SolidBrush(Color.FromArgb(127, 140, 141)))
                    {
                        var textSize = e.Graphics.MeasureString(days[i], font);
                        e.Graphics.DrawString(days[i], font, textBrush, x - textSize.Width / 2, chartY + chartHeight + 15);
                    }
                }

                // رسم المنطقة تحت الخط
                areaPoints[salesData.Length] = new Point(chartX + chartWidth, chartY + chartHeight);
                areaPoints[salesData.Length + 1] = new Point(chartX, chartY + chartHeight);
                e.Graphics.FillPolygon(areaBrush, areaPoints);

                // رسم الخط
                if (points.Length > 1)
                {
                    e.Graphics.DrawLines(linePen, points);
                }
            }
        }

        private Panel CreateRecentInvoicesPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(700, 330);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "📋 الفواتير الأخيرة";
            titleLabel.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(25, 20);
            titleLabel.Size = new Size(250, 35);

            var listView = new ListView();
            listView.Location = new Point(25, 65);
            listView.Size = new Size(650, 245);
            listView.View = View.Details;
            listView.FullRowSelect = true;
            listView.GridLines = true;
            listView.HeaderStyle = ColumnHeaderStyle.Nonclickable;
            listView.Font = new Font("Segoe UI", 11F);

            listView.Columns.Add("رقم الفاتورة", 130);
            listView.Columns.Add("العميل", 180);
            listView.Columns.Add("المبلغ", 120);
            listView.Columns.Add("الحالة", 120);
            listView.Columns.Add("التاريخ", 100);

            var recentInvoices = DataService.Invoices.OrderByDescending(i => i.InvoiceDate).Take(10);
            foreach (var invoice in recentInvoices)
            {
                var customer = DataService.Customers.FirstOrDefault(c => c.Id == invoice.CustomerId);
                var item = new ListViewItem(invoice.InvoiceNumber);
                item.SubItems.Add(customer?.CustomerName ?? "غير محدد");
                item.SubItems.Add(invoice.TotalAmount.ToString("N0"));
                item.SubItems.Add(invoice.Status);
                item.SubItems.Add(invoice.InvoiceDate.ToString("MM/dd"));

                // تلوين حسب الحالة
                if (invoice.Status == "مدفوعة")
                    item.BackColor = Color.FromArgb(212, 237, 218);
                else if (invoice.Status == "متأخرة")
                    item.BackColor = Color.FromArgb(248, 215, 218);
                else if (invoice.Status == "مدفوعة جزئياً")
                    item.BackColor = Color.FromArgb(255, 243, 205);

                listView.Items.Add(item);
            }

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(listView);
            return panel;
        }

        private Panel CreateTopCustomersPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(700, 280);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "🏆 أعلى العملاء رصيداً";
            titleLabel.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(25, 20);
            titleLabel.Size = new Size(250, 35);

            var topCustomers = DataService.Customers.OrderByDescending(c => c.CurrentBalance).Take(6);
            int yPos = 70;
            int rank = 1;

            foreach (var customer in topCustomers)
            {
                var customerPanel = new Panel();
                customerPanel.Location = new Point(25, yPos);
                customerPanel.Size = new Size(650, 35);
                customerPanel.BackColor = Color.FromArgb(248, 249, 250);

                var rankLabel = new Label();
                rankLabel.Text = rank.ToString();
                rankLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
                rankLabel.ForeColor = Color.FromArgb(52, 152, 219);
                rankLabel.Location = new Point(15, 7);
                rankLabel.Size = new Size(35, 25);
                rankLabel.TextAlign = ContentAlignment.MiddleCenter;

                var nameLabel = new Label();
                nameLabel.Text = customer.CustomerName;
                nameLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                nameLabel.ForeColor = Color.FromArgb(52, 73, 94);
                nameLabel.Location = new Point(60, 7);
                nameLabel.Size = new Size(220, 25);

                var codeLabel = new Label();
                codeLabel.Text = customer.CustomerCode;
                codeLabel.Font = new Font("Segoe UI", 10F);
                codeLabel.ForeColor = Color.FromArgb(127, 140, 141);
                codeLabel.Location = new Point(290, 7);
                codeLabel.Size = new Size(100, 25);

                var balanceLabel = new Label();
                balanceLabel.Text = customer.CurrentBalance.ToString("N0") + " ر.س";
                balanceLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                balanceLabel.ForeColor = customer.CurrentBalance >= 0 ? Color.FromArgb(46, 204, 113) : Color.FromArgb(231, 76, 60);
                balanceLabel.Location = new Point(400, 7);
                balanceLabel.Size = new Size(180, 25);
                balanceLabel.TextAlign = ContentAlignment.MiddleRight;

                customerPanel.Controls.Add(rankLabel);
                customerPanel.Controls.Add(nameLabel);
                customerPanel.Controls.Add(codeLabel);
                customerPanel.Controls.Add(balanceLabel);

                panel.Controls.Add(customerPanel);
                yPos += 40;
                rank++;
            }

            panel.Controls.Add(titleLabel);
            return panel;
        }

        private Panel CreateQuickStatsPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(700, 280);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "⚡ إحصائيات سريعة";
            titleLabel.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(25, 20);
            titleLabel.Size = new Size(250, 35);

            var quickStats = new[]
            {
                new { Label = "متوسط قيمة الفاتورة", Value = DataService.Invoices.Average(i => i.TotalAmount).ToString("N0") + " ر.س", Icon = "💰" },
                new { Label = "إجمالي المدفوعات اليوم", Value = DataService.Receipts.Where(r => r.ReceiptDate.Date == DateTime.Today).Sum(r => r.Amount).ToString("N0") + " ر.س", Icon = "📈" },
                new { Label = "عدد الفواتير المستحقة", Value = DataService.Invoices.Count(i => i.DueDate <= DateTime.Now && i.Status != "مدفوعة").ToString(), Icon = "⏰" },
                new { Label = "نسبة التحصيل", Value = ((DataService.Invoices.Sum(i => i.PaidAmount) / DataService.Invoices.Sum(i => i.TotalAmount)) * 100).ToString("F1") + "%", Icon = "📊" }
            };

            int yPos = 70;
            foreach (var stat in quickStats)
            {
                var statPanel = new Panel();
                statPanel.Location = new Point(25, yPos);
                statPanel.Size = new Size(650, 45);
                statPanel.BackColor = Color.FromArgb(248, 249, 250);

                var iconLabel = new Label();
                iconLabel.Text = stat.Icon;
                iconLabel.Font = new Font("Segoe UI Emoji", 18F);
                iconLabel.Location = new Point(20, 10);
                iconLabel.Size = new Size(35, 30);

                var labelText = new Label();
                labelText.Text = stat.Label;
                labelText.Font = new Font("Segoe UI", 12F);
                labelText.ForeColor = Color.FromArgb(52, 73, 94);
                labelText.Location = new Point(70, 12);
                labelText.Size = new Size(350, 25);

                var valueText = new Label();
                valueText.Text = stat.Value;
                valueText.Font = new Font("Segoe UI", 13F, FontStyle.Bold);
                valueText.ForeColor = Color.FromArgb(52, 152, 219);
                valueText.Location = new Point(430, 10);
                valueText.Size = new Size(200, 30);
                valueText.TextAlign = ContentAlignment.MiddleRight;

                statPanel.Controls.Add(iconLabel);
                statPanel.Controls.Add(labelText);
                statPanel.Controls.Add(valueText);

                panel.Controls.Add(statPanel);
                yPos += 50;
            }

            panel.Controls.Add(titleLabel);
            return panel;
        }

        private void DrawPanelBackground(PaintEventArgs e, Panel panel)
        {
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, panel.Width, panel.Height), 12);
                panel.Region = new Region(path);

                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }
        }

        // عرض إدارة الفواتير المحسنة
        private void ShowInvoicesManagement()
        {
            contentPanel.Controls.Clear();
            currentView = "invoices";
            UpdateMenuButtons();

            // عنوان الصفحة
            var titlePanel = new Panel();
            titlePanel.Height = 60;
            titlePanel.Dock = DockStyle.Top;
            titlePanel.BackColor = Color.Transparent;

            var titleLabel = new Label();
            titleLabel.Text = "📊 إدارة الفواتير";
            titleLabel.Font = new Font("Segoe UI", 24F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(25, 15);
            titleLabel.Size = new Size(300, 40);

            titlePanel.Controls.Add(titleLabel);
            contentPanel.Controls.Add(titlePanel);

            // شريط البحث والفلترة
            var searchPanel = new Panel();
            searchPanel.Height = 90;
            searchPanel.Dock = DockStyle.Top;
            searchPanel.BackColor = Color.White;
            searchPanel.Paint += (s, e) => DrawPanelBackground(e, searchPanel);

            var lblSearch = new Label();
            lblSearch.Text = "البحث:";
            lblSearch.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblSearch.Location = new Point(1200, 20);
            lblSearch.Size = new Size(70, 25);

            var txtSearch = new TextBox();
            txtSearch.Location = new Point(1000, 20);
            txtSearch.Size = new Size(180, 30);
            txtSearch.Font = new Font("Segoe UI", 11F);
            txtSearch.PlaceholderText = "رقم الفاتورة أو اسم العميل...";

            var lblStatus = new Label();
            lblStatus.Text = "الحالة:";
            lblStatus.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblStatus.Location = new Point(900, 20);
            lblStatus.Size = new Size(70, 25);

            var cmbStatus = new ComboBox();
            cmbStatus.Location = new Point(720, 20);
            cmbStatus.Size = new Size(160, 30);
            cmbStatus.Font = new Font("Segoe UI", 11F);
            cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbStatus.Items.AddRange(new string[] { "جميع الحالات", "مسودة", "مؤكدة", "مدفوعة", "مدفوعة جزئياً", "متأخرة", "ملغية" });
            cmbStatus.SelectedIndex = 0;

            var lblFromDate = new Label();
            lblFromDate.Text = "من تاريخ:";
            lblFromDate.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblFromDate.Location = new Point(630, 20);
            lblFromDate.Size = new Size(70, 25);

            var dtpFromDate = new DateTimePicker();
            dtpFromDate.Location = new Point(480, 20);
            dtpFromDate.Size = new Size(140, 30);
            dtpFromDate.Font = new Font("Segoe UI", 11F);
            dtpFromDate.Format = DateTimePickerFormat.Short;
            dtpFromDate.Value = DateTime.Now.AddMonths(-3);

            var lblToDate = new Label();
            lblToDate.Text = "إلى تاريخ:";
            lblToDate.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblToDate.Location = new Point(400, 20);
            lblToDate.Size = new Size(70, 25);

            var dtpToDate = new DateTimePicker();
            dtpToDate.Location = new Point(250, 20);
            dtpToDate.Size = new Size(140, 30);
            dtpToDate.Font = new Font("Segoe UI", 11F);
            dtpToDate.Format = DateTimePickerFormat.Short;
            dtpToDate.Value = DateTime.Now;

            searchPanel.Controls.AddRange(new Control[] {
                lblSearch, txtSearch, lblStatus, cmbStatus, lblFromDate, dtpFromDate, lblToDate, dtpToDate
            });

            contentPanel.Controls.Add(searchPanel);

            // شريط الأزرار
            var buttonPanel = new Panel();
            buttonPanel.Height = 70;
            buttonPanel.Dock = DockStyle.Top;
            buttonPanel.BackColor = Color.Transparent;

            var btnNew = CreateActionButton("➕ فاتورة جديدة", Color.FromArgb(46, 204, 113), () => ShowAddInvoice());
            btnNew.Location = new Point(1200, 15);

            var btnEdit = CreateActionButton("✏️ تعديل", Color.FromArgb(255, 152, 0), () => EditSelectedInvoice());
            btnEdit.Location = new Point(1050, 15);

            var btnDelete = CreateActionButton("🗑️ حذف", Color.FromArgb(231, 76, 60), () => DeleteSelectedInvoice());
            btnDelete.Location = new Point(900, 15);

            var btnPrint = CreateActionButton("🖨️ طباعة", Color.FromArgb(155, 89, 182), () => PrintSelectedInvoice());
            btnPrint.Location = new Point(750, 15);

            var btnPayment = CreateActionButton("💰 تسجيل دفعة", Color.FromArgb(52, 152, 219), () => AddPaymentToInvoice());
            btnPayment.Location = new Point(600, 15);

            buttonPanel.Controls.AddRange(new Control[] { btnNew, btnEdit, btnDelete, btnPrint, btnPayment });
            contentPanel.Controls.Add(buttonPanel);

            // جدول الفواتير
            var invoicesGrid = CreateInvoicesDataGridView();
            invoicesGrid.Dock = DockStyle.Fill;
            contentPanel.Controls.Add(invoicesGrid);

            // شريط الملخص
            var summaryPanel = new Panel();
            summaryPanel.Height = 50;
            summaryPanel.Dock = DockStyle.Bottom;
            summaryPanel.BackColor = Color.FromArgb(52, 73, 94);

            var lblTotalCount = new Label();
            lblTotalCount.Text = $"إجمالي عدد الفواتير: {DataService.Invoices.Count}";
            lblTotalCount.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblTotalCount.ForeColor = Color.White;
            lblTotalCount.Location = new Point(1000, 15);
            lblTotalCount.Size = new Size(200, 25);

            var lblTotalAmount = new Label();
            lblTotalAmount.Text = $"إجمالي المبلغ: {DataService.Invoices.Sum(i => i.TotalAmount):N0} ر.س";
            lblTotalAmount.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblTotalAmount.ForeColor = Color.White;
            lblTotalAmount.Location = new Point(700, 15);
            lblTotalAmount.Size = new Size(250, 25);

            summaryPanel.Controls.Add(lblTotalCount);
            summaryPanel.Controls.Add(lblTotalAmount);
            contentPanel.Controls.Add(summaryPanel);
        }

        private Button CreateActionButton(string text, Color color, Action action)
        {
            var button = new Button();
            button.Text = text;
            button.Size = new Size(140, 40);
            button.BackColor = color;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            button.Cursor = Cursors.Hand;
            button.Click += (s, e) => action();

            // تأثيرات التفاعل
            button.MouseEnter += (s, e) => button.BackColor = ControlPaint.Dark(color, 0.1f);
            button.MouseLeave += (s, e) => button.BackColor = color;

            return button;
        }

        private DataGridView CreateInvoicesDataGridView()
        {
            var dgv = new DataGridView();
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.RightToLeft = RightToLeft.Yes;
            dgv.Font = new Font("Segoe UI", 11F);
            dgv.BackgroundColor = Color.White;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            dgv.ColumnHeadersHeight = 40;
            dgv.RowTemplate.Height = 35;

            // إعداد الأعمدة
            dgv.Columns.Add("Id", "المعرف");
            dgv.Columns["Id"].Width = 60;
            dgv.Columns["Id"].Visible = false;

            dgv.Columns.Add("InvoiceNumber", "رقم الفاتورة");
            dgv.Columns["InvoiceNumber"].Width = 140;

            dgv.Columns.Add("InvoiceDate", "تاريخ الفاتورة");
            dgv.Columns["InvoiceDate"].Width = 120;
            dgv.Columns["InvoiceDate"].DefaultCellStyle.Format = "yyyy/MM/dd";

            dgv.Columns.Add("DueDate", "تاريخ الاستحقاق");
            dgv.Columns["DueDate"].Width = 120;
            dgv.Columns["DueDate"].DefaultCellStyle.Format = "yyyy/MM/dd";

            dgv.Columns.Add("CustomerName", "العميل");
            dgv.Columns["CustomerName"].Width = 200;

            dgv.Columns.Add("TotalAmount", "إجمالي الفاتورة");
            dgv.Columns["TotalAmount"].Width = 140;
            dgv.Columns["TotalAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgv.Columns["TotalAmount"].DefaultCellStyle.Format = "N2";

            dgv.Columns.Add("PaidAmount", "المبلغ المدفوع");
            dgv.Columns["PaidAmount"].Width = 140;
            dgv.Columns["PaidAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgv.Columns["PaidAmount"].DefaultCellStyle.Format = "N2";

            dgv.Columns.Add("RemainingAmount", "المبلغ المتبقي");
            dgv.Columns["RemainingAmount"].Width = 140;
            dgv.Columns["RemainingAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgv.Columns["RemainingAmount"].DefaultCellStyle.Format = "N2";

            dgv.Columns.Add("Status", "الحالة");
            dgv.Columns["Status"].Width = 120;

            dgv.Columns.Add("DaysOverdue", "أيام التأخير");
            dgv.Columns["DaysOverdue"].Width = 120;
            dgv.Columns["DaysOverdue"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            // تحميل البيانات
            RefreshInvoicesGrid(dgv);

            return dgv;
        }

        private void RefreshInvoicesGrid(DataGridView dgv)
        {
            dgv.Rows.Clear();

            foreach (var invoice in DataService.Invoices.OrderByDescending(i => i.InvoiceDate))
            {
                var customer = DataService.Customers.FirstOrDefault(c => c.Id == invoice.CustomerId);
                var customerName = customer?.CustomerName ?? "غير محدد";

                var rowIndex = dgv.Rows.Add();
                var row = dgv.Rows[rowIndex];

                row.Cells["Id"].Value = invoice.Id;
                row.Cells["InvoiceNumber"].Value = invoice.InvoiceNumber;
                row.Cells["InvoiceDate"].Value = invoice.InvoiceDate;
                row.Cells["DueDate"].Value = invoice.DueDate;
                row.Cells["CustomerName"].Value = customerName;
                row.Cells["TotalAmount"].Value = invoice.TotalAmount;
                row.Cells["PaidAmount"].Value = invoice.PaidAmount;
                row.Cells["RemainingAmount"].Value = invoice.RemainingAmount;
                row.Cells["Status"].Value = invoice.Status;
                row.Cells["DaysOverdue"].Value = invoice.DaysOverdue > 0 ? invoice.DaysOverdue.ToString() : "";

                // تلوين الصفوف حسب الحالة
                if (invoice.Status == "مدفوعة")
                {
                    row.DefaultCellStyle.BackColor = Color.FromArgb(212, 237, 218);
                }
                else if (invoice.Status == "مدفوعة جزئياً")
                {
                    row.DefaultCellStyle.BackColor = Color.FromArgb(255, 243, 205);
                }
                else if (invoice.Status == "متأخرة")
                {
                    row.DefaultCellStyle.BackColor = Color.FromArgb(248, 215, 218);
                }
                else if (invoice.Status == "مؤكدة")
                {
                    row.DefaultCellStyle.BackColor = Color.FromArgb(209, 236, 241);
                }
                else if (invoice.Status == "مسودة")
                {
                    row.DefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
                }
            }
        }

        // عرض نموذج إضافة فاتورة جديدة
        private void ShowAddInvoice()
        {
            var addInvoiceForm = new AddEditInvoiceForm();
            if (addInvoiceForm.ShowDialog() == DialogResult.OK)
            {
                // تحديث الداشبورد
                if (currentView == "invoices")
                {
                    ShowInvoicesManagement();
                }
                else
                {
                    ShowDashboard();
                }
            }
        }

        // دوال الأزرار
        private void EditSelectedInvoice()
        {
            MessageBox.Show("🔧 سيتم فتح نموذج تعديل الفاتورة المحددة", "تعديل الفاتورة",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteSelectedInvoice()
        {
            var result = MessageBox.Show("هل أنت متأكد من حذف الفاتورة المحددة؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح الحذف",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void PrintSelectedInvoice()
        {
            MessageBox.Show("🖨️ سيتم طباعة الفاتورة المحددة", "طباعة الفاتورة",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AddPaymentToInvoice()
        {
            MessageBox.Show("💰 سيتم فتح نموذج تسجيل دفعة للفاتورة المحددة", "تسجيل دفعة",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // دوال التنقل الأخرى
        private void ShowReceipts()
        {
            MessageBox.Show("💰 سيتم فتح شاشة إدارة السندات والمدفوعات\n\nالميزات المتوفرة:\n• عرض جميع السندات\n• إضافة سند جديد\n• ربط السندات بالفواتير\n• تقارير المدفوعات",
                "إدارة السندات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCustomers()
        {
            MessageBox.Show("👥 سيتم فتح شاشة إدارة العملاء\n\nالميزات المتوفرة:\n• عرض جميع العملاء\n• إضافة عميل جديد\n• تعديل بيانات العملاء\n• كشف حساب العميل",
                "إدارة العملاء", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowProducts()
        {
            MessageBox.Show("📦 سيتم فتح شاشة إدارة المنتجات\n\nالميزات المتوفرة:\n• عرض جميع المنتجات\n• إضافة منتج جديد\n• تعديل أسعار المنتجات\n• إدارة المخزون",
                "إدارة المنتجات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReports()
        {
            MessageBox.Show("📈 سيتم فتح شاشة التقارير والإحصائيات\n\nالتقارير المتوفرة:\n• تقرير المبيعات\n• تقرير المدفوعات\n• تقرير العملاء\n• تقرير الأرباح",
                "التقارير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowSettings()
        {
            MessageBox.Show("⚙️ سيتم فتح شاشة الإعدادات\n\nالإعدادات المتوفرة:\n• إعدادات الشركة\n• إعدادات الضرائب\n• إعدادات الطباعة\n• إعدادات النسخ الاحتياطي",
                "الإعدادات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowNotifications()
        {
            var overdueCount = DataService.Invoices.Count(i => i.IsOverdue);
            var dueToday = DataService.Invoices.Count(i => i.DueDate.Date == DateTime.Today && i.Status != "مدفوعة");

            MessageBox.Show($"🔔 الإشعارات:\n\n• {overdueCount} فاتورة متأخرة\n• {dueToday} فاتورة مستحقة اليوم\n• آخر تحديث: {DateTime.Now:HH:mm}",
                "الإشعارات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Logout()
        {
            var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                AuthService.Logout();
                this.Close();
            }
        }
    }

    // نموذج إضافة/تعديل الفاتورة المحسن
    public class AddEditInvoiceForm : Form
    {
        private Invoice currentInvoice;
        private bool isEditMode;

        // التحكمات الرئيسية
        private ComboBox cmbCustomer;
        private DateTimePicker dtpInvoiceDate;
        private DateTimePicker dtpDueDate;
        private TextBox txtNotes;
        private DataGridView dgvItems;
        private Label lblSubTotal;
        private Label lblTaxAmount;
        private Label lblDiscountAmount;
        private Label lblTotalAmount;
        private ComboBox cmbStatus;

        public AddEditInvoiceForm(Invoice invoice = null)
        {
            currentInvoice = invoice ?? new Invoice();
            isEditMode = invoice != null;
            InitializeForm();
            LoadData();
        }

        private void InitializeForm()
        {
            this.Text = isEditMode ? "تعديل الفاتورة" : "إضافة فاتورة جديدة";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(236, 240, 241);

            CreateControls();
        }

        private void CreateControls()
        {
            // العنوان الرئيسي
            var titlePanel = new Panel();
            titlePanel.Height = 80;
            titlePanel.Dock = DockStyle.Top;
            titlePanel.BackColor = Color.FromArgb(52, 73, 94);

            var titleLabel = new Label();
            titleLabel.Text = isEditMode ? "✏️ تعديل الفاتورة" : "➕ إضافة فاتورة جديدة";
            titleLabel.Font = new Font("Segoe UI", 20F, FontStyle.Bold);
            titleLabel.ForeColor = Color.White;
            titleLabel.Location = new Point(30, 25);
            titleLabel.Size = new Size(400, 40);

            titlePanel.Controls.Add(titleLabel);
            this.Controls.Add(titlePanel);

            // لوحة المعلومات الأساسية
            var infoPanel = new Panel();
            infoPanel.Height = 200;
            infoPanel.Dock = DockStyle.Top;
            infoPanel.BackColor = Color.White;
            infoPanel.Paint += (s, e) => DrawPanelBackground(e, infoPanel);
            infoPanel.Padding = new Padding(30);

            // الصف الأول
            var lblCustomer = new Label();
            lblCustomer.Text = "العميل:";
            lblCustomer.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblCustomer.Location = new Point(950, 30);
            lblCustomer.Size = new Size(80, 30);

            cmbCustomer = new ComboBox();
            cmbCustomer.Location = new Point(700, 30);
            cmbCustomer.Size = new Size(240, 35);
            cmbCustomer.Font = new Font("Segoe UI", 11F);
            cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList;

            var lblInvoiceDate = new Label();
            lblInvoiceDate.Text = "تاريخ الفاتورة:";
            lblInvoiceDate.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblInvoiceDate.Location = new Point(550, 30);
            lblInvoiceDate.Size = new Size(120, 30);

            dtpInvoiceDate = new DateTimePicker();
            dtpInvoiceDate.Location = new Point(350, 30);
            dtpInvoiceDate.Size = new Size(180, 35);
            dtpInvoiceDate.Font = new Font("Segoe UI", 11F);
            dtpInvoiceDate.Format = DateTimePickerFormat.Short;

            var lblDueDate = new Label();
            lblDueDate.Text = "تاريخ الاستحقاق:";
            lblDueDate.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblDueDate.Location = new Point(200, 30);
            lblDueDate.Size = new Size(130, 30);

            dtpDueDate = new DateTimePicker();
            dtpDueDate.Location = new Point(30, 30);
            dtpDueDate.Size = new Size(160, 35);
            dtpDueDate.Font = new Font("Segoe UI", 11F);
            dtpDueDate.Format = DateTimePickerFormat.Short;

            // الصف الثاني
            var lblStatus = new Label();
            lblStatus.Text = "الحالة:";
            lblStatus.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblStatus.Location = new Point(950, 80);
            lblStatus.Size = new Size(80, 30);

            cmbStatus = new ComboBox();
            cmbStatus.Location = new Point(700, 80);
            cmbStatus.Size = new Size(240, 35);
            cmbStatus.Font = new Font("Segoe UI", 11F);
            cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbStatus.Items.AddRange(new string[] { "مسودة", "مؤكدة", "مدفوعة", "مدفوعة جزئياً", "متأخرة", "ملغية" });

            var lblNotes = new Label();
            lblNotes.Text = "ملاحظات:";
            lblNotes.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblNotes.Location = new Point(550, 80);
            lblNotes.Size = new Size(80, 30);

            txtNotes = new TextBox();
            txtNotes.Location = new Point(30, 80);
            txtNotes.Size = new Size(500, 35);
            txtNotes.Font = new Font("Segoe UI", 11F);
            txtNotes.Multiline = true;
            txtNotes.Height = 60;

            infoPanel.Controls.AddRange(new Control[] {
                lblCustomer, cmbCustomer, lblInvoiceDate, dtpInvoiceDate, lblDueDate, dtpDueDate,
                lblStatus, cmbStatus, lblNotes, txtNotes
            });

            this.Controls.Add(infoPanel);

            // لوحة الأصناف
            var itemsPanel = new Panel();
            itemsPanel.Dock = DockStyle.Fill;
            itemsPanel.BackColor = Color.Transparent;
            itemsPanel.Padding = new Padding(30);

            var itemsTitle = new Label();
            itemsTitle.Text = "📦 أصناف الفاتورة";
            itemsTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            itemsTitle.ForeColor = Color.FromArgb(52, 73, 94);
            itemsTitle.Location = new Point(30, 10);
            itemsTitle.Size = new Size(200, 35);

            // شريط أزرار الأصناف
            var itemButtonsPanel = new Panel();
            itemButtonsPanel.Height = 50;
            itemButtonsPanel.Dock = DockStyle.Top;
            itemButtonsPanel.BackColor = Color.Transparent;

            var btnAddItem = new Button();
            btnAddItem.Text = "➕ إضافة صنف";
            btnAddItem.Size = new Size(120, 35);
            btnAddItem.Location = new Point(1000, 10);
            btnAddItem.BackColor = Color.FromArgb(46, 204, 113);
            btnAddItem.ForeColor = Color.White;
            btnAddItem.FlatStyle = FlatStyle.Flat;
            btnAddItem.FlatAppearance.BorderSize = 0;
            btnAddItem.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnAddItem.Click += BtnAddItem_Click;

            var btnEditItem = new Button();
            btnEditItem.Text = "✏️ تعديل";
            btnEditItem.Size = new Size(100, 35);
            btnEditItem.Location = new Point(890, 10);
            btnEditItem.BackColor = Color.FromArgb(255, 152, 0);
            btnEditItem.ForeColor = Color.White;
            btnEditItem.FlatStyle = FlatStyle.Flat;
            btnEditItem.FlatAppearance.BorderSize = 0;
            btnEditItem.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnEditItem.Click += BtnEditItem_Click;

            var btnDeleteItem = new Button();
            btnDeleteItem.Text = "🗑️ حذف";
            btnDeleteItem.Size = new Size(100, 35);
            btnDeleteItem.Location = new Point(780, 10);
            btnDeleteItem.BackColor = Color.FromArgb(231, 76, 60);
            btnDeleteItem.ForeColor = Color.White;
            btnDeleteItem.FlatStyle = FlatStyle.Flat;
            btnDeleteItem.FlatAppearance.BorderSize = 0;
            btnDeleteItem.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnDeleteItem.Click += BtnDeleteItem_Click;

            itemButtonsPanel.Controls.Add(itemsTitle);
            itemButtonsPanel.Controls.Add(btnAddItem);
            itemButtonsPanel.Controls.Add(btnEditItem);
            itemButtonsPanel.Controls.Add(btnDeleteItem);

            itemsPanel.Controls.Add(itemButtonsPanel);

            // جدول الأصناف
            dgvItems = CreateItemsDataGridView();
            dgvItems.Dock = DockStyle.Fill;
            itemsPanel.Controls.Add(dgvItems);

            this.Controls.Add(itemsPanel);

            // لوحة الملخص والأزرار
            var summaryPanel = new Panel();
            summaryPanel.Height = 150;
            summaryPanel.Dock = DockStyle.Bottom;
            summaryPanel.BackColor = Color.White;
            summaryPanel.Paint += (s, e) => DrawPanelBackground(e, summaryPanel);

            CreateSummaryControls(summaryPanel);
            this.Controls.Add(summaryPanel);
        }

        private void CreateSummaryControls(Panel summaryPanel)
        {
            // ملخص المبالغ
            var lblSubTotalText = new Label();
            lblSubTotalText.Text = "المجموع الفرعي:";
            lblSubTotalText.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblSubTotalText.Location = new Point(950, 20);
            lblSubTotalText.Size = new Size(120, 25);

            lblSubTotal = new Label();
            lblSubTotal.Text = "0.00 ر.س";
            lblSubTotal.Font = new Font("Segoe UI", 12F);
            lblSubTotal.ForeColor = Color.FromArgb(52, 152, 219);
            lblSubTotal.Location = new Point(800, 20);
            lblSubTotal.Size = new Size(140, 25);
            lblSubTotal.TextAlign = ContentAlignment.MiddleRight;

            var lblTaxText = new Label();
            lblTaxText.Text = "الضريبة (15%):";
            lblTaxText.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblTaxText.Location = new Point(950, 50);
            lblTaxText.Size = new Size(120, 25);

            lblTaxAmount = new Label();
            lblTaxAmount.Text = "0.00 ر.س";
            lblTaxAmount.Font = new Font("Segoe UI", 12F);
            lblTaxAmount.ForeColor = Color.FromArgb(52, 152, 219);
            lblTaxAmount.Location = new Point(800, 50);
            lblTaxAmount.Size = new Size(140, 25);
            lblTaxAmount.TextAlign = ContentAlignment.MiddleRight;

            var lblDiscountText = new Label();
            lblDiscountText.Text = "الخصم:";
            lblDiscountText.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblDiscountText.Location = new Point(950, 80);
            lblDiscountText.Size = new Size(120, 25);

            lblDiscountAmount = new Label();
            lblDiscountAmount.Text = "0.00 ر.س";
            lblDiscountAmount.Font = new Font("Segoe UI", 12F);
            lblDiscountAmount.ForeColor = Color.FromArgb(231, 76, 60);
            lblDiscountAmount.Location = new Point(800, 80);
            lblDiscountAmount.Size = new Size(140, 25);
            lblDiscountAmount.TextAlign = ContentAlignment.MiddleRight;

            var lblTotalText = new Label();
            lblTotalText.Text = "الإجمالي:";
            lblTotalText.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            lblTotalText.Location = new Point(950, 110);
            lblTotalText.Size = new Size(120, 30);

            lblTotalAmount = new Label();
            lblTotalAmount.Text = "0.00 ر.س";
            lblTotalAmount.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            lblTotalAmount.ForeColor = Color.FromArgb(46, 204, 113);
            lblTotalAmount.Location = new Point(800, 110);
            lblTotalAmount.Size = new Size(140, 30);
            lblTotalAmount.TextAlign = ContentAlignment.MiddleRight;

            // أزرار الحفظ والإلغاء
            var btnSave = new Button();
            btnSave.Text = "💾 حفظ الفاتورة";
            btnSave.Size = new Size(150, 45);
            btnSave.Location = new Point(500, 80);
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnSave.Cursor = Cursors.Hand;
            btnSave.Click += BtnSave_Click;

            var btnCancel = new Button();
            btnCancel.Text = "❌ إلغاء";
            btnCancel.Size = new Size(120, 45);
            btnCancel.Location = new Point(360, 80);
            btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnCancel.Cursor = Cursors.Hand;
            btnCancel.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            summaryPanel.Controls.AddRange(new Control[] {
                lblSubTotalText, lblSubTotal, lblTaxText, lblTaxAmount,
                lblDiscountText, lblDiscountAmount, lblTotalText, lblTotalAmount,
                btnSave, btnCancel
            });
        }

        private DataGridView CreateItemsDataGridView()
        {
            var dgv = new DataGridView();
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.RightToLeft = RightToLeft.Yes;
            dgv.Font = new Font("Segoe UI", 11F);
            dgv.BackgroundColor = Color.White;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            dgv.ColumnHeadersHeight = 40;
            dgv.RowTemplate.Height = 35;

            // إعداد الأعمدة
            dgv.Columns.Add("ProductName", "اسم المنتج");
            dgv.Columns["ProductName"].Width = 250;

            dgv.Columns.Add("Quantity", "الكمية");
            dgv.Columns["Quantity"].Width = 100;
            dgv.Columns["Quantity"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            dgv.Columns.Add("UnitPrice", "سعر الوحدة");
            dgv.Columns["UnitPrice"].Width = 120;
            dgv.Columns["UnitPrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgv.Columns["UnitPrice"].DefaultCellStyle.Format = "N2";

            dgv.Columns.Add("DiscountPercent", "نسبة الخصم %");
            dgv.Columns["DiscountPercent"].Width = 120;
            dgv.Columns["DiscountPercent"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            dgv.Columns.Add("TotalPrice", "الإجمالي");
            dgv.Columns["TotalPrice"].Width = 140;
            dgv.Columns["TotalPrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgv.Columns["TotalPrice"].DefaultCellStyle.Format = "N2";

            return dgv;
        }

        private void LoadData()
        {
            // تحميل العملاء
            cmbCustomer.Items.Clear();
            foreach (var customer in DataService.Customers.Where(c => c.IsActive))
            {
                cmbCustomer.Items.Add(new { Text = $"{customer.CustomerName} ({customer.CustomerCode})", Value = customer.Id });
            }
            cmbCustomer.DisplayMember = "Text";
            cmbCustomer.ValueMember = "Value";

            if (isEditMode)
            {
                // تحميل بيانات الفاتورة للتعديل
                cmbCustomer.SelectedValue = currentInvoice.CustomerId;
                dtpInvoiceDate.Value = currentInvoice.InvoiceDate;
                dtpDueDate.Value = currentInvoice.DueDate;
                cmbStatus.Text = currentInvoice.Status;
                txtNotes.Text = currentInvoice.Notes;

                // تحميل أصناف الفاتورة
                RefreshItemsGrid();
            }
            else
            {
                // قيم افتراضية للفاتورة الجديدة
                dtpInvoiceDate.Value = DateTime.Now;
                dtpDueDate.Value = DateTime.Now.AddDays(30);
                cmbStatus.SelectedIndex = 0; // مسودة
                currentInvoice.Items = new List<InvoiceItem>();
            }

            CalculateTotals();
        }

        private void RefreshItemsGrid()
        {
            dgvItems.Rows.Clear();

            foreach (var item in currentInvoice.Items)
            {
                var rowIndex = dgvItems.Rows.Add();
                var row = dgvItems.Rows[rowIndex];

                row.Cells["ProductName"].Value = item.ProductName;
                row.Cells["Quantity"].Value = item.Quantity;
                row.Cells["UnitPrice"].Value = item.UnitPrice;
                row.Cells["DiscountPercent"].Value = item.DiscountPercent;
                row.Cells["TotalPrice"].Value = item.TotalPrice;
                row.Tag = item;
            }

            CalculateTotals();
        }

        private void CalculateTotals()
        {
            decimal subTotal = currentInvoice.Items.Sum(i => i.TotalPrice);
            decimal discountAmount = currentInvoice.DiscountAmount;
            decimal taxAmount = (subTotal - discountAmount) * 0.15m;
            decimal totalAmount = subTotal - discountAmount + taxAmount;

            lblSubTotal.Text = subTotal.ToString("N2") + " ر.س";
            lblDiscountAmount.Text = discountAmount.ToString("N2") + " ر.س";
            lblTaxAmount.Text = taxAmount.ToString("N2") + " ر.س";
            lblTotalAmount.Text = totalAmount.ToString("N2") + " ر.س";

            currentInvoice.SubTotal = subTotal;
            currentInvoice.DiscountAmount = discountAmount;
            currentInvoice.TaxAmount = taxAmount;
            currentInvoice.TotalAmount = totalAmount;
        }

        private void DrawPanelBackground(PaintEventArgs e, Panel panel)
        {
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, panel.Width, panel.Height), 12);
                panel.Region = new Region(path);

                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }
        }

        // أحداث الأزرار
        private void BtnAddItem_Click(object sender, EventArgs e)
        {
            var addItemForm = new AddItemForm();
            if (addItemForm.ShowDialog() == DialogResult.OK)
            {
                currentInvoice.Items.Add(addItemForm.InvoiceItem);
                RefreshItemsGrid();
            }
        }

        private void BtnEditItem_Click(object sender, EventArgs e)
        {
            if (dgvItems.SelectedRows.Count > 0)
            {
                var selectedItem = dgvItems.SelectedRows[0].Tag as InvoiceItem;
                var editItemForm = new AddItemForm(selectedItem);
                if (editItemForm.ShowDialog() == DialogResult.OK)
                {
                    var index = currentInvoice.Items.IndexOf(selectedItem);
                    currentInvoice.Items[index] = editItemForm.InvoiceItem;
                    RefreshItemsGrid();
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار صنف للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnDeleteItem_Click(object sender, EventArgs e)
        {
            if (dgvItems.SelectedRows.Count > 0)
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف هذا الصنف؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var selectedItem = dgvItems.SelectedRows[0].Tag as InvoiceItem;
                    currentInvoice.Items.Remove(selectedItem);
                    RefreshItemsGrid();
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار صنف للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                // حفظ بيانات الفاتورة
                currentInvoice.CustomerId = (int)cmbCustomer.SelectedValue;
                currentInvoice.InvoiceDate = dtpInvoiceDate.Value;
                currentInvoice.DueDate = dtpDueDate.Value;
                currentInvoice.Status = cmbStatus.Text;
                currentInvoice.Notes = txtNotes.Text;

                if (isEditMode)
                {
                    DataService.UpdateInvoice(currentInvoice);
                    MessageBox.Show("تم تحديث الفاتورة بنجاح", "نجح التحديث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    DataService.AddInvoice(currentInvoice);
                    MessageBox.Show("تم إضافة الفاتورة بنجاح", "نجحت الإضافة",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
            }
        }

        private bool ValidateForm()
        {
            if (cmbCustomer.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار العميل", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            if (currentInvoice.Items.Count == 0)
            {
                MessageBox.Show("يرجى إضافة صنف واحد على الأقل", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            if (string.IsNullOrWhiteSpace(cmbStatus.Text))
            {
                MessageBox.Show("يرجى اختيار حالة الفاتورة", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            return true;
        }
    }

    // نموذج إضافة صنف للفاتورة
    public class AddItemForm : Form
    {
        public InvoiceItem InvoiceItem { get; private set; }

        private ComboBox cmbProduct;
        private NumericUpDown nudQuantity;
        private NumericUpDown nudUnitPrice;
        private NumericUpDown nudDiscountPercent;
        private Label lblTotalPrice;
        private bool isEditMode;

        public AddItemForm(InvoiceItem item = null)
        {
            InvoiceItem = item ?? new InvoiceItem();
            isEditMode = item != null;
            InitializeForm();
            LoadData();
        }

        private void InitializeForm()
        {
            this.Text = isEditMode ? "تعديل الصنف" : "إضافة صنف";
            this.Size = new Size(600, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            // العنوان
            var titleLabel = new Label();
            titleLabel.Text = isEditMode ? "✏️ تعديل الصنف" : "➕ إضافة صنف جديد";
            titleLabel.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(30, 20);
            titleLabel.Size = new Size(300, 35);

            // المنتج
            var lblProduct = new Label();
            lblProduct.Text = "المنتج:";
            lblProduct.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblProduct.Location = new Point(480, 80);
            lblProduct.Size = new Size(80, 25);

            cmbProduct = new ComboBox();
            cmbProduct.Location = new Point(200, 80);
            cmbProduct.Size = new Size(270, 30);
            cmbProduct.Font = new Font("Segoe UI", 11F);
            cmbProduct.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbProduct.SelectedIndexChanged += CmbProduct_SelectedIndexChanged;

            // الكمية
            var lblQuantity = new Label();
            lblQuantity.Text = "الكمية:";
            lblQuantity.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblQuantity.Location = new Point(480, 130);
            lblQuantity.Size = new Size(80, 25);

            nudQuantity = new NumericUpDown();
            nudQuantity.Location = new Point(350, 130);
            nudQuantity.Size = new Size(120, 30);
            nudQuantity.Font = new Font("Segoe UI", 11F);
            nudQuantity.Minimum = 0.01m;
            nudQuantity.Maximum = 999999;
            nudQuantity.DecimalPlaces = 2;
            nudQuantity.Value = 1;
            nudQuantity.ValueChanged += CalculateTotal;

            // سعر الوحدة
            var lblUnitPrice = new Label();
            lblUnitPrice.Text = "سعر الوحدة:";
            lblUnitPrice.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblUnitPrice.Location = new Point(250, 130);
            lblUnitPrice.Size = new Size(100, 25);

            nudUnitPrice = new NumericUpDown();
            nudUnitPrice.Location = new Point(80, 130);
            nudUnitPrice.Size = new Size(160, 30);
            nudUnitPrice.Font = new Font("Segoe UI", 11F);
            nudUnitPrice.Minimum = 0;
            nudUnitPrice.Maximum = 999999;
            nudUnitPrice.DecimalPlaces = 2;
            nudUnitPrice.ValueChanged += CalculateTotal;

            // نسبة الخصم
            var lblDiscountPercent = new Label();
            lblDiscountPercent.Text = "نسبة الخصم %:";
            lblDiscountPercent.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblDiscountPercent.Location = new Point(480, 180);
            lblDiscountPercent.Size = new Size(120, 25);

            nudDiscountPercent = new NumericUpDown();
            nudDiscountPercent.Location = new Point(350, 180);
            nudDiscountPercent.Size = new Size(120, 30);
            nudDiscountPercent.Font = new Font("Segoe UI", 11F);
            nudDiscountPercent.Minimum = 0;
            nudDiscountPercent.Maximum = 100;
            nudDiscountPercent.DecimalPlaces = 2;
            nudDiscountPercent.ValueChanged += CalculateTotal;

            // الإجمالي
            var lblTotalText = new Label();
            lblTotalText.Text = "الإجمالي:";
            lblTotalText.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            lblTotalText.Location = new Point(250, 180);
            lblTotalText.Size = new Size(80, 30);

            lblTotalPrice = new Label();
            lblTotalPrice.Text = "0.00 ر.س";
            lblTotalPrice.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            lblTotalPrice.ForeColor = Color.FromArgb(46, 204, 113);
            lblTotalPrice.Location = new Point(80, 180);
            lblTotalPrice.Size = new Size(160, 30);
            lblTotalPrice.TextAlign = ContentAlignment.MiddleRight;

            // أزرار الحفظ والإلغاء
            var btnSave = new Button();
            btnSave.Text = "💾 حفظ";
            btnSave.Size = new Size(120, 40);
            btnSave.Location = new Point(350, 280);
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnSave.Click += BtnSave_Click;

            var btnCancel = new Button();
            btnCancel.Text = "❌ إلغاء";
            btnCancel.Size = new Size(120, 40);
            btnCancel.Location = new Point(220, 280);
            btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnCancel.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            this.Controls.AddRange(new Control[] {
                titleLabel, lblProduct, cmbProduct, lblQuantity, nudQuantity,
                lblUnitPrice, nudUnitPrice, lblDiscountPercent, nudDiscountPercent,
                lblTotalText, lblTotalPrice, btnSave, btnCancel
            });
        }

        private void LoadData()
        {
            // تحميل المنتجات
            cmbProduct.Items.Clear();
            foreach (var product in DataService.Products.Where(p => p.IsActive))
            {
                cmbProduct.Items.Add(new { Text = $"{product.ProductName} - {product.UnitPrice:N2} ر.س", Value = product });
            }
            cmbProduct.DisplayMember = "Text";
            cmbProduct.ValueMember = "Value";

            if (isEditMode)
            {
                // تحميل بيانات الصنف للتعديل
                var product = DataService.Products.FirstOrDefault(p => p.Id == InvoiceItem.ProductId);
                if (product != null)
                {
                    cmbProduct.SelectedValue = product;
                }
                nudQuantity.Value = InvoiceItem.Quantity;
                nudUnitPrice.Value = InvoiceItem.UnitPrice;
                nudDiscountPercent.Value = InvoiceItem.DiscountPercent;
            }

            CalculateTotal(null, null);
        }

        private void CmbProduct_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbProduct.SelectedValue is Product product)
            {
                nudUnitPrice.Value = product.UnitPrice;
                CalculateTotal(null, null);
            }
        }

        private void CalculateTotal(object sender, EventArgs e)
        {
            decimal quantity = nudQuantity.Value;
            decimal unitPrice = nudUnitPrice.Value;
            decimal discountPercent = nudDiscountPercent.Value;
            decimal total = (quantity * unitPrice) * (1 - discountPercent / 100);

            lblTotalPrice.Text = total.ToString("N2") + " ر.س";
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                var product = cmbProduct.SelectedValue as Product;

                InvoiceItem.ProductId = product.Id;
                InvoiceItem.ProductName = product.ProductName;
                InvoiceItem.Quantity = nudQuantity.Value;
                InvoiceItem.UnitPrice = nudUnitPrice.Value;
                InvoiceItem.DiscountPercent = nudDiscountPercent.Value;

                this.DialogResult = DialogResult.OK;
            }
        }

        private bool ValidateForm()
        {
            if (cmbProduct.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار المنتج", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            if (nudQuantity.Value <= 0)
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            if (nudUnitPrice.Value <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر صحيح", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            return true;
        }
    }

    // إضافة دالة مساعدة للحواف المدورة
    public static class GraphicsExtensions
    {
        public static void AddRoundedRectangle(this GraphicsPath path, Rectangle rect, int radius)
        {
            int diameter = radius * 2;
            Size size = new Size(diameter, diameter);
            Rectangle arc = new Rectangle(rect.Location, size);

            // الزاوية اليسرى العلوية
            path.AddArc(arc, 180, 90);

            // الزاوية اليمنى العلوية
            arc.X = rect.Right - diameter;
            path.AddArc(arc, 270, 90);

            // الزاوية اليمنى السفلى
            arc.Y = rect.Bottom - diameter;
            path.AddArc(arc, 0, 90);

            // الزاوية اليسرى السفلى
            arc.X = rect.Left;
            path.AddArc(arc, 90, 90);

            path.CloseFigure();
        }
    }

    // نقطة دخول التطبيق
    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new LoginForm());
        }
    }
}