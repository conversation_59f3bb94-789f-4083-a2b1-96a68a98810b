<?xml version="1.0"?>
<doc>
    <members>
        <member name="T:System.Data.SQLite.SQLiteMetaDataCollectionNames">
            <summary>
            MetaDataCollections specific to SQLite
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteMetaDataCollectionNames.Catalogs">
            <summary>
            Returns a list of databases attached to the connection
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteMetaDataCollectionNames.Columns">
            <summary>
            Returns column information for the specified table
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteMetaDataCollectionNames.Indexes">
            <summary>
            Returns index information for the optionally-specified table
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteMetaDataCollectionNames.IndexColumns">
            <summary>
            Returns base columns for the given index
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteMetaDataCollectionNames.Tables">
            <summary>
            Returns the tables in the given catalog
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteMetaDataCollectionNames.Views">
            <summary>
            Returns user-defined views in the given catalog
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteMetaDataCollectionNames.ViewColumns">
            <summary>
            Returns underlying column information on the given view
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteMetaDataCollectionNames.ForeignKeys">
            <summary>
            Returns foreign key information for the given catalog
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteMetaDataCollectionNames.Triggers">
            <summary>
            Returns the triggers on the database
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteConnectionStringBuilder">
            <summary>
            SQLite implementation of DbConnectionStringBuilder.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnectionStringBuilder._properties">
            <summary>
            Properties of this class
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnectionStringBuilder.#ctor">
            <overloads>
            Constructs a new instance of the class
            </overloads>
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnectionStringBuilder.#ctor(System.String)">
            <summary>
            Constructs a new instance of the class using the specified connection string.
            </summary>
            <param name="connectionString">The connection string to parse</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnectionStringBuilder.Initialize(System.String)">
            <summary>
            Private initializer, which assigns the connection string and resets the builder
            </summary>
            <param name="cnnString">The connection string to assign</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnectionStringBuilder.TryGetValue(System.String,System.Object@)">
            <summary>
            Helper function for retrieving values from the connectionstring
            </summary>
            <param name="keyword">The keyword to retrieve settings for</param>
            <param name="value">The resulting parameter value</param>
            <returns>Returns true if the value was found and returned</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnectionStringBuilder.FallbackGetProperties(System.Collections.Hashtable)">
            <summary>
            Fallback method for MONO, which doesn't implement DbConnectionStringBuilder.GetProperties()
            </summary>
            <param name="propertyList">The hashtable to fill with property descriptors</param>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.Version">
            <summary>
            Gets/Sets the default version of the SQLite engine to instantiate.  Currently the only valid value is 3, indicating version 3 of the sqlite library.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.SyncMode">
            <summary>
            Gets/Sets the synchronization mode (file flushing) of the connection string.  Default is "Normal".
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.UseUTF16Encoding">
            <summary>
            Gets/Sets the encoding for the connection string.  The default is "False" which indicates UTF-8 encoding.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.Pooling">
            <summary>
            Gets/Sets whether or not to use connection pooling.  The default is "False"
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.BinaryGUID">
            <summary>
            Gets/Sets whethor not to store GUID's in binary format.  The default is True
            which saves space in the database.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.DataSource">
            <summary>
            Gets/Sets the filename to open on the connection string.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.Uri">
            <summary>
            An alternate to the data source property
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.DefaultTimeout">
            <summary>
            Gets/sets the default command timeout for newly-created commands.  This is especially useful for 
            commands used internally such as inside a SQLiteTransaction, where setting the timeout is not possible.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.Enlist">
            <summary>
            Determines whether or not the connection will automatically participate
            in the current distributed transaction (if one exists)
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.FailIfMissing">
            <summary>
            If set to true, will throw an exception if the database specified in the connection
            string does not exist.  If false, the database will be created automatically.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.LegacyFormat">
            <summary>
            If enabled, uses the legacy 3.xx format for maximum compatibility, but results in larger
            database sizes.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.ReadOnly">
            <summary>
            When enabled, the database will be opened for read-only access and writing will be disabled.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.Password">
            <summary>
            Gets/sets the database encryption password
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.PageSize">
            <summary>
            Gets/Sets the page size for the connection.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.MaxPageCount">
            <summary>
            Gets/Sets the maximum number of pages the database may hold
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.CacheSize">
            <summary>
            Gets/Sets the cache size for the connection.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.DateTimeFormat">
            <summary>
            Gets/Sets the datetime format for the connection.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.JournalMode">
            <summary>
            Determines how SQLite handles the transaction journal file.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnectionStringBuilder.DefaultIsolationLevel">
            <summary>
            Sets the default isolation level for transactions on the connection.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteCommandBuilder">
            <summary>
            SQLite implementation of DbCommandBuilder.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.#ctor(System.Data.SQLite.SQLiteDataAdapter)">
            <summary>
            Initializes the command builder and associates it with the specified data adapter.
            </summary>
            <param name="adp"></param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.ApplyParameterInfo(System.Data.Common.DbParameter,System.Data.DataRow,System.Data.StatementType,System.Boolean)">
            <summary>
            Minimal amount of parameter processing.  Primarily sets the DbType for the parameter equal to the provider type in the schema
            </summary>
            <param name="parameter">The parameter to use in applying custom behaviors to a row</param>
            <param name="row">The row to apply the parameter to</param>
            <param name="statementType">The type of statement</param>
            <param name="whereClause">Whether the application of the parameter is part of a WHERE clause</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.GetParameterName(System.String)">
            <summary>
            Returns a valid named parameter
            </summary>
            <param name="parameterName">The name of the parameter</param>
            <returns>Error</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.GetParameterName(System.Int32)">
            <summary>
            Returns a named parameter for the given ordinal
            </summary>
            <param name="parameterOrdinal">The i of the parameter</param>
            <returns>Error</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.GetParameterPlaceholder(System.Int32)">
            <summary>
            Returns a placeholder character for the specified parameter i.
            </summary>
            <param name="parameterOrdinal">The index of the parameter to provide a placeholder for</param>
            <returns>Returns a named parameter</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.SetRowUpdatingHandler(System.Data.Common.DbDataAdapter)">
            <summary>
            Sets the handler for receiving row updating events.  Used by the DbCommandBuilder to autogenerate SQL
            statements that may not have previously been generated.
            </summary>
            <param name="adapter">A data adapter to receive events on.</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.GetDeleteCommand">
            <summary>
            Returns the automatically-generated SQLite command to delete rows from the database
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.GetDeleteCommand(System.Boolean)">
            <summary>
            Returns the automatically-generated SQLite command to delete rows from the database
            </summary>
            <param name="useColumnsForParameterNames"></param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.GetUpdateCommand">
            <summary>
            Returns the automatically-generated SQLite command to update rows in the database
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.GetUpdateCommand(System.Boolean)">
            <summary>
            Returns the automatically-generated SQLite command to update rows in the database
            </summary>
            <param name="useColumnsForParameterNames"></param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.GetInsertCommand">
            <summary>
            Returns the automatically-generated SQLite command to insert rows into the database
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.GetInsertCommand(System.Boolean)">
            <summary>
            Returns the automatically-generated SQLite command to insert rows into the database
            </summary>
            <param name="useColumnsForParameterNames"></param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.QuoteIdentifier(System.String)">
            <summary>
            Places brackets around an identifier
            </summary>
            <param name="unquotedIdentifier">The identifier to quote</param>
            <returns>The bracketed identifier</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.UnquoteIdentifier(System.String)">
            <summary>
            Removes brackets around an identifier
            </summary>
            <param name="quotedIdentifier">The quoted (bracketed) identifier</param>
            <returns>The undecorated identifier</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommandBuilder.GetSchemaTable(System.Data.Common.DbCommand)">
            <summary>
            Override helper, which can help the base command builder choose the right keys for the given query
            </summary>
            <param name="sourceCommand"></param>
            <returns></returns>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommandBuilder.DataAdapter">
            <summary>
            Gets/sets the DataAdapter for this CommandBuilder
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommandBuilder.CatalogLocation">
            <summary>
            Overridden to hide its property from the designer
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommandBuilder.CatalogSeparator">
            <summary>
            Overridden to hide its property from the designer
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommandBuilder.QuotePrefix">
            <summary>
            Overridden to hide its property from the designer
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommandBuilder.QuoteSuffix">
            <summary>
            Overridden to hide its property from the designer
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommandBuilder.SchemaSeparator">
            <summary>
            Overridden to hide its property from the designer
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SR">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SR.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SR.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SR.DataTypes">
             <summary>
               Looks up a localized string similar to &lt;?xml version=&quot;1.0&quot; standalone=&quot;yes&quot;?&gt;
            &lt;DocumentElement&gt;
              &lt;DataTypes&gt;
                &lt;TypeName&gt;smallint&lt;/TypeName&gt;
                &lt;ProviderDbType&gt;10&lt;/ProviderDbType&gt;
                &lt;ColumnSize&gt;5&lt;/ColumnSize&gt;
                &lt;DataType&gt;System.Int16&lt;/DataType&gt;
                &lt;CreateFormat&gt;smallint&lt;/CreateFormat&gt;
                &lt;IsAutoIncrementable&gt;false&lt;/IsAutoIncrementable&gt;
                &lt;IsCaseSensitive&gt;false&lt;/IsCaseSensitive&gt;
                &lt;IsFixedLength&gt;true&lt;/IsFixedLength&gt;
                &lt;IsFixedPrecisionScale&gt;true&lt;/IsFixedPrecisionScale&gt;
                &lt;IsLong&gt;false&lt;/IsLong&gt;
                &lt;IsNullable&gt;true&lt;/ [rest of string was truncated]&quot;;.
             </summary>
        </member>
        <member name="P:System.Data.SQLite.SR.Keywords">
            <summary>
              Looks up a localized string similar to ALL,ALTER,AND,AS,AUTOINCREMENT,BETWEEN,BY,CASE,CHECK,COLLATE,COMMIT,CONSTRAINT,CREATE,CROSS,DEFAULT,DEFERRABLE,DELETE,DISTINCT,DROP,ELSE,ESCAPE,EXCEPT,FOREIGN,FROM,FULL,GROUP,HAVING,IN,INDEX,INNER,INSERT,INTERSECT,INTO,IS,ISNULL,JOIN,LEFT,LIMIT,NATURAL,NOT,NOTNULL,NULL,ON,OR,ORDER,OUTER,PRIMARY,REFERENCES,RIGHT,ROLLBACK,SELECT,SET,TABLE,THEN,TO,TRANSACTION,UNION,UNIQUE,UPDATE,USING,VALUES,WHEN,WHERE.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SR.MetaDataCollections">
             <summary>
               Looks up a localized string similar to &lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot; ?&gt;
            &lt;DocumentElement&gt;
              &lt;MetaDataCollections&gt;
                &lt;CollectionName&gt;MetaDataCollections&lt;/CollectionName&gt;
                &lt;NumberOfRestrictions&gt;0&lt;/NumberOfRestrictions&gt;
                &lt;NumberOfIdentifierParts&gt;0&lt;/NumberOfIdentifierParts&gt;
              &lt;/MetaDataCollections&gt;
              &lt;MetaDataCollections&gt;
                &lt;CollectionName&gt;DataSourceInformation&lt;/CollectionName&gt;
                &lt;NumberOfRestrictions&gt;0&lt;/NumberOfRestrictions&gt;
                &lt;NumberOfIdentifierParts&gt;0&lt;/NumberOfIdentifierParts&gt;
              &lt;/MetaDataCollections&gt;
              &lt;MetaDataC [rest of string was truncated]&quot;;.
             </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnectionPool._connections">
            <summary>
            The connection pool object
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnectionPool._poolVersion">
            <summary>
            The default version number new pools will get
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnectionPool.Remove(System.String,System.Int32,System.Int32@)">
            <summary>
            Attempt to pull a pooled connection out of the queue for active duty
            </summary>
            <param name="fileName">The filename for a desired connection</param>
            <param name="maxPoolSize">The maximum size the connection pool for the filename can be</param>
            <param name="version">The pool version the returned connection will belong to</param>
            <returns>Returns NULL if no connections were available.  Even if none are, the poolversion will still be a valid pool version</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnectionPool.ClearAllPools">
            <summary>
            Clears out all pooled connections and rev's up the default pool version to force all old active objects
            not in the pool to get discarded rather than returned to their pools.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnectionPool.ClearPool(System.String)">
            <summary>
            Clear a given pool for a given filename.  Discards anything in the pool for the given file, and revs the pool
            version so current active objects on the old version of the pool will get discarded rather than be returned to the pool.
            </summary>
            <param name="fileName">The filename of the pool to clear</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnectionPool.Add(System.String,System.Data.SQLite.SQLiteConnectionHandle,System.Int32)">
            <summary>
            Return a connection to the pool for someone else to use.
            </summary>
            <param name="fileName">The filename of the pool to use</param>
            <param name="hdl">The connection handle to pool</param>
            <param name="version">The pool version the handle was created under</param>
            <remarks>
            If the version numbers don't match between the connection and the pool, then the handle is discarded.
            </remarks>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnectionPool.ResizePool(System.Data.SQLite.SQLiteConnectionPool.Pool,System.Boolean)">
            <summary>
            We don't have to thread-lock anything in this function, because it's only called by other functions above
            which already have a thread-safe lock.
            </summary>
            <param name="queue">The queue to resize</param>
            <param name="forAdding">If a function intends to add to the pool, this is true, which forces the resize
            to take one more than it needs from the pool</param>
        </member>
        <member name="T:System.Data.SQLite.SQLiteConnectionPool.Pool">
            <summary>
            Keeps track of connections made on a specified file.  The PoolVersion dictates whether old objects get
            returned to the pool or discarded when no longer in use.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteConnection">
            <summary>
            SQLite implentation of DbConnection.
            </summary>
            <remarks>
            The <see cref="P:System.Data.SQLite.SQLiteConnection.ConnectionString">ConnectionString</see> property of the SQLiteConnection class can contain the following parameter(s), delimited with a semi-colon:
            <list type="table">
            <listheader>
            <term>Parameter</term>
            <term>Values</term>
            <term>Required</term>
            <term>Default</term>
            </listheader>
            <item>
            <description>Data Source</description>
            <description>{filename}</description>
            <description>Y</description>
            <description></description>
            </item>
            <item>
            <description>Version</description>
            <description>3</description>
            <description>N</description>
            <description>3</description>
            </item>
            <item>
            <description>UseUTF16Encoding</description>
            <description><b>True</b><br/><b>False</b></description>
            <description>N</description>
            <description>False</description>
            </item>
            <item>
            <description>DateTimeFormat</description>
            <description><b>Ticks</b> - Use DateTime.Ticks<br/><b>ISO8601</b> - Use ISO8601 DateTime format</description>
            <description>N</description>
            <description>ISO8601</description>
            </item>
            <item>
            <description>BinaryGUID</description>
            <description><b>True</b> - Store GUID columns in binary form<br/><b>False</b> - Store GUID columns as text</description>
            <description>N</description>
            <description>True</description>
            </item>
            <item>
            <description>Cache Size</description>
            <description>{size in bytes}</description>
            <description>N</description>
            <description>2000</description>
            </item>
            <item>
            <description>Synchronous</description>
            <description><b>Normal</b> - Normal file flushing behavior<br/><b>Full</b> - Full flushing after all writes<br/><b>Off</b> - Underlying OS flushes I/O's</description>
            <description>N</description>
            <description>Normal</description>
            </item>
            <item>
            <description>Page Size</description>
            <description>{size in bytes}</description>
            <description>N</description>
            <description>1024</description>
            </item>
            <item>
            <description>Password</description>
            <description>{password}</description>
            <description>N</description>
            <description></description>
            </item>
            <item>
            <description>Enlist</description>
            <description><b>Y</b> - Automatically enlist in distributed transactions<br/><b>N</b> - No automatic enlistment</description>
            <description>N</description>
            <description>Y</description>
            </item>
            <item>
            <description>Pooling</description>
            <description><b>True</b> - Use connection pooling<br/><b>False</b> - Do not use connection pooling</description>
            <description>N</description>
            <description>False</description>
            </item>
            <item>
            <description>FailIfMissing</description>
            <description><b>True</b> - Don't create the database if it does not exist, throw an error instead<br/><b>False</b> - Automatically create the database if it does not exist</description>
            <description>N</description>
            <description>False</description>
            </item>
            <item>
            <description>Max Page Count</description>
            <description>{size in pages} - Limits the maximum number of pages (limits the size) of the database</description>
            <description>N</description>
            <description>0</description>
            </item>
            <item>
            <description>Legacy Format</description>
            <description><b>True</b> - Use the more compatible legacy 3.x database format<br/><b>False</b> - Use the newer 3.3x database format which compresses numbers more effectively</description>
            <description>N</description>
            <description>False</description>
            </item>
            <item>
            <description>Default Timeout</description>
            <description>{time in seconds}<br/>The default command timeout</description>
            <description>N</description>
            <description>30</description>
            </item>
            <item>
            <description>Journal Mode</description>
            <description><b>Delete</b> - Delete the journal file after a commit<br/><b>Persist</b> - Zero out and leave the journal file on disk after a commit<br/><b>Off</b> - Disable the rollback journal entirely</description>
            <description>N</description>
            <description>Delete</description>
            </item>
            <item>
            <description>Read Only</description>
            <description><b>True</b> - Open the database for read only access<br/><b>False</b> - Open the database for normal read/write access</description>
            <description>N</description>
            <description>False</description>
            </item>
            <item>
            <description>Max Pool Size</description>
            <description>The maximum number of connections for the given connection string that can be in the connection pool</description>
            <description>N</description>
            <description>100</description>
            </item>
            <item>
            <description>Default IsolationLevel</description>
            <description>The default transaciton isolation level</description>
            <description>N</description>
            <description>Serializable</description>
            </item>
            </list>
            </remarks>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnection._connectionState">
            <summary>
            State of the current connection
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnection._connectionString">
            <summary>
            The connection string
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnection._transactionLevel">
            <summary>
            Nesting level of the transactions open on the connection
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnection._defaultIsolation">
            <summary>
            The default isolation level for new transactions
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnection._enlistment">
            <summary>
            Whether or not the connection is enlisted in a distrubuted transaction
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnection._sql">
            <summary>
            The base SQLite object to interop with
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnection._dataSource">
            <summary>
            The database filename minus path and extension
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnection._password">
            <summary>
            Temporary password storage, emptied after the database has been opened
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConnection._defaultTimeout">
            <summary>
            Default command timeout
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.#ctor">
            <overloads>
             Constructs a new SQLiteConnection object
             </overloads>
             <summary>
             Default constructor
             </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.#ctor(System.String)">
            <summary>
            Initializes the connection with the specified connection string
            </summary>
            <param name="connectionString">The connection string to use on the connection</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.#ctor(System.Data.SQLite.SQLiteConnection)">
            <summary>
            Clones the settings and connection string from an existing connection.  If the existing connection is already open, this
            function will open its own connection, enumerate any attached databases of the original connection, and automatically
            attach to them.
            </summary>
            <param name="connection"></param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Clone">
            <summary>
            Creates a clone of the connection.  All attached databases and user-defined functions are cloned.  If the existing connection is open, the cloned connection 
            will also be opened.
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Dispose(System.Boolean)">
            <summary>
            Disposes of the SQLiteConnection, closing it if it is active.
            </summary>
            <param name="disposing">True if the connection is being explicitly closed.</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.CreateFile(System.String)">
            <summary>
            Creates a database file.  This just creates a zero-byte file which SQLite
            will turn into a database when the file is opened properly.
            </summary>
            <param name="databaseFileName">The file to create</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.OnStateChange(System.Data.ConnectionState)">
            <summary>
            Raises the state change event when the state of the connection changes
            </summary>
            <param name="newState">The new state.  If it is different from the previous state, an event is raised.</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.BeginTransaction(System.Data.IsolationLevel,System.Boolean)">
            <summary>
            OBSOLETE.  Creates a new SQLiteTransaction if one isn't already active on the connection.
            </summary>
            <param name="isolationLevel">This parameter is ignored.</param>
            <param name="deferredLock">When TRUE, SQLite defers obtaining a write lock until a write operation is requested.
            When FALSE, a writelock is obtained immediately.  The default is TRUE, but in a multi-threaded multi-writer 
            environment, one may instead choose to lock the database immediately to avoid any possible writer deadlock.</param>
            <returns>Returns a SQLiteTransaction object.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.BeginTransaction(System.Boolean)">
            <summary>
            OBSOLETE.  Creates a new SQLiteTransaction if one isn't already active on the connection.
            </summary>
            <param name="deferredLock">When TRUE, SQLite defers obtaining a write lock until a write operation is requested.
            When FALSE, a writelock is obtained immediately.  The default is false, but in a multi-threaded multi-writer 
            environment, one may instead choose to lock the database immediately to avoid any possible writer deadlock.</param>
            <returns>Returns a SQLiteTransaction object.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.BeginTransaction(System.Data.IsolationLevel)">
            <summary>
            Creates a new SQLiteTransaction if one isn't already active on the connection.
            </summary>
            <param name="isolationLevel">Supported isolation levels are Serializable, ReadCommitted and Unspecified.</param>
            <remarks>
            Unspecified will use the default isolation level specified in the connection string.  If no isolation level is specified in the 
            connection string, Serializable is used.
            Serializable transactions are the default.  In this mode, the engine gets an immediate lock on the database, and no other threads
            may begin a transaction.  Other threads may read from the database, but not write.
            With a ReadCommitted isolation level, locks are deferred and elevated as needed.  It is possible for multiple threads to start
            a transaction in ReadCommitted mode, but if a thread attempts to commit a transaction while another thread
            has a ReadCommitted lock, it may timeout or cause a deadlock on both threads until both threads' CommandTimeout's are reached.
            </remarks>
            <returns>Returns a SQLiteTransaction object.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.BeginTransaction">
            <summary>
            Creates a new SQLiteTransaction if one isn't already active on the connection.
            </summary>
            <returns>Returns a SQLiteTransaction object.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.BeginDbTransaction(System.Data.IsolationLevel)">
            <summary>
            Forwards to the local BeginTransaction() function
            </summary>
            <param name="isolationLevel">Supported isolation levels are Unspecified, Serializable, and ReadCommitted</param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.ChangeDatabase(System.String)">
            <summary>
            Not implemented
            </summary>
            <param name="databaseName"></param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Close">
            <summary>
            When the database connection is closed, all commands linked to this connection are automatically reset.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.ClearPool(System.Data.SQLite.SQLiteConnection)">
            <summary>
            Clears the connection pool associated with the connection.  Any other active connections using the same database file
            will be discarded instead of returned to the pool when they are closed.
            </summary>
            <param name="connection"></param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.ClearAllPools">
            <summary>
            Clears all connection pools.  Any active connections will be discarded instead of sent to the pool when they are closed.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.CreateCommand">
            <summary>
            Create a new SQLiteCommand and associate it with this connection.
            </summary>
            <returns>Returns an instantiated SQLiteCommand object already assigned to this connection.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.CreateDbCommand">
            <summary>
            Forwards to the local CreateCommand() function
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.ParseConnectionString(System.String)">
            <summary>
            Parses the connection string into component parts
            </summary>
            <param name="connectionString">The connection string to parse</param>
            <returns>An array of key-value pairs representing each parameter of the connection string</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.EnlistTransaction(System.Transactions.Transaction)">
            <summary>
            Manual distributed transaction enlistment support
            </summary>
            <param name="transaction">The distributed transaction to enlist in</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.FindKey(System.Collections.Generic.SortedList{System.String,System.String},System.String,System.String)">
            <summary>
            Looks for a key in the array of key/values of the parameter string.  If not found, return the specified default value
            </summary>
            <param name="items">The list to look in</param>
            <param name="key">The key to find</param>
            <param name="defValue">The default value to return if the key is not found</param>
            <returns>The value corresponding to the specified key, or the default value if not found.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Open">
            <summary>
            Opens the connection using the parameters found in the <see cref="P:System.Data.SQLite.SQLiteConnection.ConnectionString">ConnectionString</see>
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.ChangePassword(System.String)">
            <summary>
            Change the password (or assign a password) to an open database.
            </summary>
            <remarks>
            No readers or writers may be active for this process.  The database must already be open
            and if it already was password protected, the existing password must already have been supplied.
            </remarks>
            <param name="newPassword">The new password to assign to the database</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.ChangePassword(System.Byte[])">
            <summary>
            Change the password (or assign a password) to an open database.
            </summary>
            <remarks>
            No readers or writers may be active for this process.  The database must already be open
            and if it already was password protected, the existing password must already have been supplied.
            </remarks>
            <param name="newPassword">The new password to assign to the database</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.SetPassword(System.String)">
            <summary>
            Sets the password for a password-protected database.  A password-protected database is
            unusable for any operation until the password has been set.
            </summary>
            <param name="databasePassword">The password for the database</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.SetPassword(System.Byte[])">
            <summary>
            Sets the password for a password-protected database.  A password-protected database is
            unusable for any operation until the password has been set.
            </summary>
            <param name="databasePassword">The password for the database</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.ExpandFileName(System.String)">
            <summary>
            Expand the filename of the data source, resolving the |DataDirectory| macro as appropriate.
            </summary>
            <param name="sourceFile">The database filename to expand</param>
            <returns>The expanded path and filename of the filename</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.GetSchema">
            <overloads>
             The following commands are used to extract schema information out of the database.  Valid schema types are:
             <list type="bullet">
             <item>
             <description>MetaDataCollections</description>
             </item>
             <item>
             <description>DataSourceInformation</description>
             </item>
             <item>
             <description>Catalogs</description>
             </item>
             <item>
             <description>Columns</description>
             </item>
             <item>
             <description>ForeignKeys</description>
             </item>
             <item>
             <description>Indexes</description>
             </item>
             <item>
             <description>IndexColumns</description>
             </item>
             <item>
             <description>Tables</description>
             </item>
             <item>
             <description>Views</description>
             </item>
             <item>
             <description>ViewColumns</description>
             </item>
             </list>
             </overloads>
             <summary>
             Returns the MetaDataCollections schema
             </summary>
             <returns>A DataTable of the MetaDataCollections schema</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.GetSchema(System.String)">
            <summary>
            Returns schema information of the specified collection
            </summary>
            <param name="collectionName">The schema collection to retrieve</param>
            <returns>A DataTable of the specified collection</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.GetSchema(System.String,System.String[])">
            <summary>
            Retrieves schema information using the specified constraint(s) for the specified collection
            </summary>
            <param name="collectionName">The collection to retrieve</param>
            <param name="restrictionValues">The restrictions to impose</param>
            <returns>A DataTable of the specified collection</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Schema_MetaDataCollections">
            <summary>
            Builds a MetaDataCollections schema datatable
            </summary>
            <returns>DataTable</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Schema_DataSourceInformation">
            <summary>
            Builds a DataSourceInformation datatable
            </summary>
            <returns>DataTable</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Schema_Columns(System.String,System.String,System.String)">
            <summary>
            Build a Columns schema
            </summary>
            <param name="strCatalog">The catalog (attached database) to query, can be null</param>
            <param name="strTable">The table to retrieve schema information for, must not be null</param>
            <param name="strColumn">The column to retrieve schema information for, can be null</param>
            <returns>DataTable</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Schema_Indexes(System.String,System.String,System.String)">
            <summary>
            Returns index information for the given database and catalog
            </summary>
            <param name="strCatalog">The catalog (attached database) to query, can be null</param>
            <param name="strIndex">The name of the index to retrieve information for, can be null</param>
            <param name="strTable">The table to retrieve index information for, can be null</param>
            <returns>DataTable</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Schema_Tables(System.String,System.String,System.String)">
            <summary>
            Retrieves table schema information for the database and catalog
            </summary>
            <param name="strCatalog">The catalog (attached database) to retrieve tables on</param>
            <param name="strTable">The table to retrieve, can be null</param>
            <param name="strType">The table type, can be null</param>
            <returns>DataTable</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Schema_Views(System.String,System.String)">
            <summary>
            Retrieves view schema information for the database
            </summary>
            <param name="strCatalog">The catalog (attached database) to retrieve views on</param>
            <param name="strView">The view name, can be null</param>
            <returns>DataTable</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Schema_Catalogs(System.String)">
            <summary>
            Retrieves catalog (attached databases) schema information for the database
            </summary>
            <param name="strCatalog">The catalog to retrieve, can be null</param>
            <returns>DataTable</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Schema_IndexColumns(System.String,System.String,System.String,System.String)">
            <summary>
            Returns the base column information for indexes in a database
            </summary>
            <param name="strCatalog">The catalog to retrieve indexes for (can be null)</param>
            <param name="strTable">The table to restrict index information by (can be null)</param>
            <param name="strIndex">The index to restrict index information by (can be null)</param>
            <param name="strColumn">The source column to restrict index information by (can be null)</param>
            <returns>A DataTable containing the results</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Schema_ViewColumns(System.String,System.String,System.String)">
            <summary>
            Returns detailed column information for a specified view
            </summary>
            <param name="strCatalog">The catalog to retrieve columns for (can be null)</param>
            <param name="strView">The view to restrict column information by (can be null)</param>
            <param name="strColumn">The source column to restrict column information by (can be null)</param>
            <returns>A DataTable containing the results</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConnection.Schema_ForeignKeys(System.String,System.String,System.String)">
            <summary>
            Retrieves foreign key information from the specified set of filters
            </summary>
            <param name="strCatalog">An optional catalog to restrict results on</param>
            <param name="strTable">An optional table to restrict results on</param>
            <param name="strKeyName">An optional foreign key name to restrict results on</param>
            <returns>A DataTable with the results of the query</returns>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnection.DbProviderFactory">
            <summary>
            Returns a SQLiteProviderFactory object.
            </summary>
        </member>
        <member name="E:System.Data.SQLite.SQLiteConnection.StateChange">
            <summary>
            This event is raised whenever the database is opened or closed.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnection.ConnectionString">
            <summary>
            The connection string containing the parameters for the connection
            </summary>
            <remarks>
            <list type="table">
            <listheader>
            <term>Parameter</term>
            <term>Values</term>
            <term>Required</term>
            <term>Default</term>
            </listheader>
            <item>
            <description>Data Source</description>
            <description>{filename}</description>
            <description>Y</description>
            <description></description>
            </item>
            <item>
            <description>Version</description>
            <description>3</description>
            <description>N</description>
            <description>3</description>
            </item>
            <item>
            <description>UseUTF16Encoding</description>
            <description><b>True</b><br/><b>False</b></description>
            <description>N</description>
            <description>False</description>
            </item>
            <item>
            <description>DateTimeFormat</description>
            <description><b>Ticks</b> - Use DateTime.Ticks<br/><b>ISO8601</b> - Use ISO8601 DateTime format<br/><b>JulianDay</b> - Use JulianDay format</description>
            <description>N</description>
            <description>ISO8601</description>
            </item>
            <item>
            <description>BinaryGUID</description>
            <description><b>Yes/On/1</b> - Store GUID columns in binary form<br/><b>No/Off/0</b> - Store GUID columns as text</description>
            <description>N</description>
            <description>On</description>
            </item>
            <item>
            <description>Cache Size</description>
            <description>{size in bytes}</description>
            <description>N</description>
            <description>2000</description>
            </item>
            <item>
            <description>Synchronous</description>
            <description><b>Normal</b> - Normal file flushing behavior<br/><b>Full</b> - Full flushing after all writes<br/><b>Off</b> - Underlying OS flushes I/O's</description>
            <description>N</description>
            <description>Normal</description>
            </item>
            <item>
            <description>Page Size</description>
            <description>{size in bytes}</description>
            <description>N</description>
            <description>1024</description>
            </item>
            <item>
            <description>Password</description>
            <description>{password}</description>
            <description>N</description>
            <description></description>
            </item>
            <item>
            <description>Enlist</description>
            <description><B>Y</B> - Automatically enlist in distributed transactions<br/><b>N</b> - No automatic enlistment</description>
            <description>N</description>
            <description>Y</description>
            </item>
            <item>
            <description>Pooling</description>
            <description><b>True</b> - Use connection pooling<br/><b>False</b> - Do not use connection pooling</description>
            <description>N</description>
            <description>False</description>
            </item>
            <item>
            <description>FailIfMissing</description>
            <description><b>True</b> - Don't create the database if it does not exist, throw an error instead<br/><b>False</b> - Automatically create the database if it does not exist</description>
            <description>N</description>
            <description>False</description>
            </item>
            <item>
            <description>Max Page Count</description>
            <description>{size in pages} - Limits the maximum number of pages (limits the size) of the database</description>
            <description>N</description>
            <description>0</description>
            </item>
            <item>
            <description>Legacy Format</description>
            <description><b>True</b> - Use the more compatible legacy 3.x database format<br/><b>False</b> - Use the newer 3.3x database format which compresses numbers more effectively</description>
            <description>N</description>
            <description>False</description>
            </item>
            <item>
            <description>Default Timeout</description>
            <description>{time in seconds}<br/>The default command timeout</description>
            <description>N</description>
            <description>30</description>
            </item>
            <item>
            <description>Journal Mode</description>
            <description><b>Delete</b> - Delete the journal file after a commit<br/><b>Persist</b> - Zero out and leave the journal file on disk after a commit<br/><b>Off</b> - Disable the rollback journal entirely</description>
            <description>N</description>
            <description>Delete</description>
            </item>
            <item>
            <description>Read Only</description>
            <description><b>True</b> - Open the database for read only access<br/><b>False</b> - Open the database for normal read/write access</description>
            <description>N</description>
            <description>False</description>
            </item>
            <item>
            <description>Max Pool Size</description>
            <description>The maximum number of connections for the given connection string that can be in the connection pool</description>
            <description>N</description>
            <description>100</description>
            </item>
            <item>
            <description>Default IsolationLevel</description>
            <description>The default transaciton isolation level</description>
            <description>N</description>
            <description>Serializable</description>
            </item>
            </list>
            </remarks>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnection.DataSource">
            <summary>
            Returns the filename without extension or path
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnection.Database">
            <summary>
            Returns an empty string
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnection.DefaultTimeout">
            <summary>
            Gets/sets the default command timeout for newly-created commands.  This is especially useful for 
            commands used internally such as inside a SQLiteTransaction, where setting the timeout is not possible.
            This can also be set in the ConnectionString with "Default Timeout"
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnection.ServerVersion">
            <summary>
            Returns the version of the underlying SQLite database engine
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnection.SQLiteVersion">
            <summary>
            Returns the version of the underlying SQLite database engine
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteConnection.State">
            <summary>
            Returns the state of the connection.
            </summary>
        </member>
        <member name="E:System.Data.SQLite.SQLiteConnection.Update">
            <summary>
            This event is raised whenever SQLite makes an update/delete/insert into the database on
            this connection.  It only applies to the given connection.
            </summary>
        </member>
        <member name="E:System.Data.SQLite.SQLiteConnection.Commit">
            <summary>
            This event is raised whenever SQLite is committing a transaction.
            Return non-zero to trigger a rollback
            </summary>
        </member>
        <member name="E:System.Data.SQLite.SQLiteConnection.RollBack">
            <summary>
            This event is raised whenever SQLite is committing a transaction.
            Return non-zero to trigger a rollback
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SynchronizationModes">
            <summary>
            The I/O file cache flushing behavior for the connection
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SynchronizationModes.Normal">
            <summary>
            Normal file flushing at critical sections of the code
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SynchronizationModes.Full">
            <summary>
            Full file flushing after every write operation
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SynchronizationModes.Off">
            <summary>
            Use the default operating system's file flushing, SQLite does not explicitly flush the file buffers after writing
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteCommitHandler">
            <summary>
            Raised when a transaction is about to be committed.  To roll back a transaction, set the 
            rollbackTrans boolean value to true.
            </summary>
            <param name="sender">The connection committing the transaction</param>
            <param name="e">Event arguments on the transaction</param>
        </member>
        <member name="T:System.Data.SQLite.SQLiteUpdateEventHandler">
            <summary>
            Raised when data is inserted, updated and deleted on a given connection
            </summary>
            <param name="sender">The connection committing the transaction</param>
            <param name="e">The event parameters which triggered the event</param>
        </member>
        <member name="T:System.Data.SQLite.UpdateEventType">
            <summary>
            Whenever an update event is triggered on a connection, this enum will indicate
            exactly what type of operation is being performed.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.UpdateEventType.Delete">
            <summary>
            A row is being deleted from the given database and table
            </summary>
        </member>
        <member name="F:System.Data.SQLite.UpdateEventType.Insert">
            <summary>
            A row is being inserted into the table.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.UpdateEventType.Update">
            <summary>
            A row is being updated in the table.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.UpdateEventArgs">
            <summary>
            Passed during an Update callback, these event arguments detail the type of update operation being performed
            on the given connection.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.UpdateEventArgs.Database">
            <summary>
            The name of the database being updated (usually "main" but can be any attached or temporary database)
            </summary>
        </member>
        <member name="F:System.Data.SQLite.UpdateEventArgs.Table">
            <summary>
            The name of the table being updated
            </summary>
        </member>
        <member name="F:System.Data.SQLite.UpdateEventArgs.Event">
            <summary>
            The type of update being performed (insert/update/delete)
            </summary>
        </member>
        <member name="F:System.Data.SQLite.UpdateEventArgs.RowId">
            <summary>
            The RowId affected by this update.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.CommitEventArgs">
            <summary>
            Event arguments raised when a transaction is being committed
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CommitEventArgs.AbortTransaction">
            <summary>
            Set to true to abort the transaction and trigger a rollback
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteFunction">
            <summary>
            This abstract class is designed to handle user-defined functions easily.  An instance of the derived class is made for each
            connection to the database.
            </summary>
            <remarks>
            Although there is one instance of a class derived from SQLiteFunction per database connection, the derived class has no access
            to the underlying connection.  This is necessary to deter implementers from thinking it would be a good idea to make database
            calls during processing.
            
            It is important to distinguish between a per-connection instance, and a per-SQL statement context.  One instance of this class
            services all SQL statements being stepped through on that connection, and there can be many.  One should never store per-statement
            information in member variables of user-defined function classes.
            
            For aggregate functions, always create and store your per-statement data in the contextData object on the 1st step.  This data will
            be automatically freed for you (and Dispose() called if the item supports IDisposable) when the statement completes.
            </remarks>
        </member>
        <member name="F:System.Data.SQLite.SQLiteFunction._base">
            <summary>
            The base connection this function is attached to
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteFunction._contextDataList">
            <summary>
            Internal array used to keep track of aggregate function context data
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteFunction._InvokeFunc">
            <summary>
            Holds a reference to the callback function for user functions
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteFunction._StepFunc">
            <summary>
            Holds a reference to the callbakc function for stepping in an aggregate function
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteFunction._FinalFunc">
            <summary>
            Holds a reference to the callback function for finalizing an aggregate function
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteFunction._CompareFunc">
            <summary>
            Holds a reference to the callback function for collation sequences
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteFunction._context">
            <summary>
            Current context of the current callback.  Only valid during a callback
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteFunction._registeredFunctions">
            <summary>
            This static list contains all the user-defined functions declared using the proper attributes.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.#ctor">
            <summary>
            Internal constructor, initializes the function's internal variables.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.Invoke(System.Object[])">
            <summary>
            Scalar functions override this method to do their magic.
            </summary>
            <remarks>
            Parameters passed to functions have only an affinity for a certain data type, there is no underlying schema available
            to force them into a certain type.  Therefore the only types you will ever see as parameters are
            DBNull.Value, Int64, Double, String or byte[] array.
            </remarks>
            <param name="args">The arguments for the command to process</param>
            <returns>You may return most simple types as a return value, null or DBNull.Value to return null, DateTime, or
            you may return an Exception-derived class if you wish to return an error to SQLite.  Do not actually throw the error,
            just return it!</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.Step(System.Object[],System.Int32,System.Object@)">
            <summary>
            Aggregate functions override this method to do their magic.
            </summary>
            <remarks>
            Typically you'll be updating whatever you've placed in the contextData field and returning as quickly as possible.
            </remarks>
            <param name="args">The arguments for the command to process</param>
            <param name="stepNumber">The 1-based step number.  This is incrememted each time the step method is called.</param>
            <param name="contextData">A placeholder for implementers to store contextual data pertaining to the current context.</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.Final(System.Object)">
            <summary>
            Aggregate functions override this method to finish their aggregate processing.
            </summary>
            <remarks>
            If you implemented your aggregate function properly,
            you've been recording and keeping track of your data in the contextData object provided, and now at this stage you should have
            all the information you need in there to figure out what to return.
            NOTE:  It is possible to arrive here without receiving a previous call to Step(), in which case the contextData will
            be null.  This can happen when no rows were returned.  You can either return null, or 0 or some other custom return value
            if that is the case.
            </remarks>
            <param name="contextData">Your own assigned contextData, provided for you so you can return your final results.</param>
            <returns>You may return most simple types as a return value, null or DBNull.Value to return null, DateTime, or
            you may return an Exception-derived class if you wish to return an error to SQLite.  Do not actually throw the error,
            just return it!
            </returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.Compare(System.String,System.String)">
            <summary>
            User-defined collation sequences override this method to provide a custom string sorting algorithm.
            </summary>
            <param name="param1">The first string to compare</param>
            <param name="param2">The second strnig to compare</param>
            <returns>1 if param1 is greater than param2, 0 if they are equal, or -1 if param1 is less than param2</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.ConvertParams(System.Int32,System.IntPtr)">
            <summary>
            Converts an IntPtr array of context arguments to an object array containing the resolved parameters the pointers point to.
            </summary>
            <remarks>
            Parameters passed to functions have only an affinity for a certain data type, there is no underlying schema available
            to force them into a certain type.  Therefore the only types you will ever see as parameters are
            DBNull.Value, Int64, Double, String or byte[] array.
            </remarks>
            <param name="nArgs">The number of arguments</param>
            <param name="argsptr">A pointer to the array of arguments</param>
            <returns>An object array of the arguments once they've been converted to .NET values</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.SetReturnValue(System.IntPtr,System.Object)">
            <summary>
            Takes the return value from Invoke() and Final() and figures out how to return it to SQLite's context.
            </summary>
            <param name="context">The context the return value applies to</param>
            <param name="returnValue">The parameter to return to SQLite</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.ScalarCallback(System.IntPtr,System.Int32,System.IntPtr)">
            <summary>
            Internal scalar callback function, which wraps the raw context pointer and calls the virtual Invoke() method.
            </summary>
            <param name="context">A raw context pointer</param>
            <param name="nArgs">Number of arguments passed in</param>
            <param name="argsptr">A pointer to the array of arguments</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.CompareCallback(System.IntPtr,System.Int32,System.IntPtr,System.Int32,System.IntPtr)">
            <summary>
            Internal collation sequence function, which wraps up the raw string pointers and executes the Compare() virtual function.
            </summary>
            <param name="ptr">Not used</param>
            <param name="len1">Length of the string pv1</param>
            <param name="ptr1">Pointer to the first string to compare</param>
            <param name="len2">Length of the string pv2</param>
            <param name="ptr2">Pointer to the second string to compare</param>
            <returns>Returns -1 if the first string is less than the second.  0 if they are equal, or 1 if the first string is greater
            than the second.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.StepCallback(System.IntPtr,System.Int32,System.IntPtr)">
            <summary>
            The internal aggregate Step function callback, which wraps the raw context pointer and calls the virtual Step() method.
            </summary>
            <remarks>
            This function takes care of doing the lookups and getting the important information put together to call the Step() function.
            That includes pulling out the user's contextData and updating it after the call is made.  We use a sorted list for this so
            binary searches can be done to find the data.
            </remarks>
            <param name="context">A raw context pointer</param>
            <param name="nArgs">Number of arguments passed in</param>
            <param name="argsptr">A pointer to the array of arguments</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.FinalCallback(System.IntPtr)">
            <summary>
            An internal aggregate Final function callback, which wraps the context pointer and calls the virtual Final() method.
            </summary>
            <param name="context">A raw context pointer</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.Dispose(System.Boolean)">
            <summary>
            Placeholder for a user-defined disposal routine
            </summary>
            <param name="disposing">True if the object is being disposed explicitly</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.Dispose">
            <summary>
            Disposes of any active contextData variables that were not automatically cleaned up.  Sometimes this can happen if
            someone closes the connection while a DataReader is open.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.#cctor">
            <summary>
            Using reflection, enumerate all assemblies in the current appdomain looking for classes that
            have a SQLiteFunctionAttribute attribute, and registering them accordingly.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.RegisterFunction(System.Type)">
            <summary>
            Manual method of registering a function.  The type must still have the SQLiteFunctionAttributes in order to work
            properly, but this is a workaround for the Compact Framework where enumerating assemblies is not currently supported.
            </summary>
            <param name="typ">The type of the function to register</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunction.BindFunctions(System.Data.SQLite.SQLiteBase)">
            <summary>
            Called by SQLiteBase derived classes, this function binds all user-defined functions to a connection.
            It is done this way so that all user-defined functions will access the database using the same encoding scheme
            as the connection (UTF-8 or UTF-16).
            </summary>
            <remarks>
            The wrapper functions that interop with SQLite will create a unique cookie value, which internally is a pointer to
            all the wrapped callback functions.  The interop function uses it to map CDecl callbacks to StdCall callbacks.
            </remarks>
            <param name="sqlbase">The base object on which the functions are to bind</param>
            <returns>Returns an array of functions which the connection object should retain until the connection is closed.</returns>
        </member>
        <member name="P:System.Data.SQLite.SQLiteFunction.SQLiteConvert">
            <summary>
            Returns a reference to the underlying connection's SQLiteConvert class, which can be used to convert
            strings and DateTime's into the current connection's encoding schema.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteFunctionEx">
            <summary>
            Extends SQLiteFunction and allows an inherited class to obtain the collating sequence associated with a function call.
            </summary>
            <remarks>
            User-defined functions can call the GetCollationSequence() method in this class and use it to compare strings and char arrays.
            </remarks>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunctionEx.GetCollationSequence">
            <summary>
            Obtains the collating sequence in effect for the given function.
            </summary>
            <returns></returns>
        </member>
        <member name="T:System.Data.SQLite.FunctionType">
            <summary>
            The type of user-defined function to declare
            </summary>
        </member>
        <member name="F:System.Data.SQLite.FunctionType.Scalar">
            <summary>
            Scalar functions are designed to be called and return a result immediately.  Examples include ABS(), Upper(), Lower(), etc.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.FunctionType.Aggregate">
            <summary>
            Aggregate functions are designed to accumulate data until the end of a call and then return a result gleaned from the accumulated data.
            Examples include SUM(), COUNT(), AVG(), etc.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.FunctionType.Collation">
            <summary>
            Collation sequences are used to sort textual data in a custom manner, and appear in an ORDER BY clause.  Typically text in an ORDER BY is
            sorted using a straight case-insensitive comparison function.  Custom collating sequences can be used to alter the behavior of text sorting
            in a user-defined manner.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteCallback">
            <summary>
            An internal callback delegate declaration.
            </summary>
            <param name="context">Raw context pointer for the user function</param>
            <param name="nArgs">Count of arguments to the function</param>
            <param name="argsptr">A pointer to the array of argument pointers</param>
        </member>
        <member name="T:System.Data.SQLite.SQLiteFinalCallback">
            <summary>
            An internal final callback delegate declaration.
            </summary>
            <param name="context">Raw context pointer for the user function</param>
        </member>
        <member name="T:System.Data.SQLite.SQLiteCollation">
            <summary>
            Internal callback delegate for implementing collation sequences
            </summary>
            <param name="puser">Not used</param>
            <param name="len1">Length of the string pv1</param>
            <param name="pv1">Pointer to the first string to compare</param>
            <param name="len2">Length of the string pv2</param>
            <param name="pv2">Pointer to the second string to compare</param>
            <returns>Returns -1 if the first string is less than the second.  0 if they are equal, or 1 if the first string is greater
            than the second.</returns>
        </member>
        <member name="T:System.Data.SQLite.CollationTypeEnum">
            <summary>
            The type of collating sequence
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CollationTypeEnum.Binary">
            <summary>
            The built-in BINARY collating sequence
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CollationTypeEnum.NoCase">
            <summary>
            The built-in NOCASE collating sequence
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CollationTypeEnum.Reverse">
            <summary>
            The built-in REVERSE collating sequence
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CollationTypeEnum.Custom">
            <summary>
            A custom user-defined collating sequence
            </summary>
        </member>
        <member name="T:System.Data.SQLite.CollationEncodingEnum">
            <summary>
            The encoding type the collation sequence uses
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CollationEncodingEnum.UTF8">
            <summary>
            The collation sequence is UTF8
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CollationEncodingEnum.UTF16LE">
            <summary>
            The collation sequence is UTF16 little-endian
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CollationEncodingEnum.UTF16BE">
            <summary>
            The collation sequence is UTF16 big-endian
            </summary>
        </member>
        <member name="T:System.Data.SQLite.CollationSequence">
            <summary>
            A struct describing the collating sequence a function is executing in
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CollationSequence.Name">
            <summary>
            The name of the collating sequence
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CollationSequence.Type">
            <summary>
            The type of collating sequence
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CollationSequence.Encoding">
            <summary>
            The text encoding of the collation sequence
            </summary>
        </member>
        <member name="F:System.Data.SQLite.CollationSequence._func">
            <summary>
            Context of the function that requested the collating sequence
            </summary>
        </member>
        <member name="M:System.Data.SQLite.CollationSequence.Compare(System.String,System.String)">
            <summary>
            Calls the base collating sequence to compare two strings
            </summary>
            <param name="s1">The first string to compare</param>
            <param name="s2">The second string to compare</param>
            <returns>-1 if s1 is less than s2, 0 if s1 is equal to s2, and 1 if s1 is greater than s2</returns>
        </member>
        <member name="M:System.Data.SQLite.CollationSequence.Compare(System.Char[],System.Char[])">
            <summary>
            Calls the base collating sequence to compare two character arrays
            </summary>
            <param name="c1">The first array to compare</param>
            <param name="c2">The second array to compare</param>
            <returns>-1 if c1 is less than c2, 0 if c1 is equal to c2, and 1 if c1 is greater than c2</returns>
        </member>
        <member name="T:System.Data.SQLite.SQLiteFactory">
            <summary>
            SQLite implementation of DbProviderFactory.
            </summary>
            <summary>
            SQLite implementation of DbProviderFactory.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteFactory.Instance">
            <summary>
            Static instance member which returns an instanced SQLiteFactory class.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFactory.CreateCommand">
            <summary>
            Returns a new SQLiteCommand object.
            </summary>
            <returns>A SQLiteCommand object.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFactory.CreateCommandBuilder">
            <summary>
            Returns a new SQLiteCommandBuilder object.
            </summary>
            <returns>A SQLiteCommandBuilder object.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFactory.CreateConnection">
            <summary>
            Creates a new SQLiteConnection.
            </summary>
            <returns>A SQLiteConnection object.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFactory.CreateConnectionStringBuilder">
            <summary>
            Creates a new SQLiteConnectionStringBuilder.
            </summary>
            <returns>A SQLiteConnectionStringBuilder object.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFactory.CreateDataAdapter">
            <summary>
            Creates a new SQLiteDataAdapter.
            </summary>
            <returns>A SQLiteDataAdapter object.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFactory.CreateParameter">
            <summary>
            Creates a new SQLiteParameter.
            </summary>
            <returns>A SQLiteParameter object.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFactory.System#IServiceProvider#GetService(System.Type)">
            <summary>
            Will provide a DbProviderServices object in .NET 3.5
            </summary>
            <param name="serviceType">The class or interface type to query for</param>
            <returns></returns>
        </member>
        <member name="T:System.Data.SQLite.SQLiteException">
            <summary>
            SQLite exception class.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteException.#ctor(System.Int32,System.String)">
            <summary>
            Public constructor for generating a SQLite error given the base error code
            </summary>
            <param name="errorCode">The SQLite error code to report</param>
            <param name="extendedInformation">Extra text to go along with the error message text</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteException.#ctor(System.String)">
            <summary>
            Various public constructors that just pass along to the base Exception
            </summary>
            <param name="message">Passed verbatim to Exception</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteException.#ctor">
            <summary>
            Various public constructors that just pass along to the base Exception
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteException.#ctor(System.String,System.Exception)">
            <summary>
            Various public constructors that just pass along to the base Exception
            <param name="message">Passed to Exception</param>
            <param name="innerException">Passed to Exception</param>
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteException.GetStockErrorMessage(System.Int32,System.String)">
            <summary>
            Initializes the exception class with the SQLite error code.
            </summary>
            <param name="errorCode">The SQLite error code</param>
            <param name="errorMessage">A detailed error message</param>
            <returns>An error message string</returns>
        </member>
        <member name="P:System.Data.SQLite.SQLiteException.ErrorCode">
            <summary>
            Retrieves the underlying SQLite error code for this exception
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteErrorCode">
            <summary>
            SQLite error codes
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Ok">
            <summary>
            Success
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Error">
            <summary>
            SQL error or missing database
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Internal">
            <summary>
            Internal logic error in SQLite
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Perm">
            <summary>
            Access permission denied
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Abort">
            <summary>
            Callback routine requested an abort
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Busy">
            <summary>
            The database file is locked
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Locked">
            <summary>
            A table in the database is locked
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.NoMem">
            <summary>
            malloc() failed
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.ReadOnly">
            <summary>
            Attempt to write a read-only database
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Interrupt">
            <summary>
            Operation terminated by sqlite3_interrupt()
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.IOErr">
            <summary>
            Some kind of disk I/O error occurred
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Corrupt">
            <summary>
            The database disk image is malformed
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.NotFound">
            <summary>
            Table or record not found
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Full">
            <summary>
            Insertion failed because database is full
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.CantOpen">
            <summary>
            Unable to open the database file
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Protocol">
            <summary>
            Database lock protocol error
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Empty">
            <summary>
            Database is empty
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Schema">
            <summary>
            The database schema changed
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.TooBig">
            <summary>
            Too much data for one row of a table
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Constraint">
            <summary>
            Abort due to constraint violation
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Mismatch">
            <summary>
            Data type mismatch
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Misuse">
            <summary>
            Library used incorrectly
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.NOLFS">
            <summary>
            Uses OS features not supported on host
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Auth">
            <summary>
            Authorization denied
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Format">
            <summary>
            Auxiliary database format error
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Range">
            <summary>
            2nd parameter to sqlite3_bind out of range
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.NotADatabase">
            <summary>
            File opened that is not a database file
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Row">
            <summary>
            sqlite3_step() has another row ready
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteErrorCode.Done">
            <summary>
            sqlite3_step() has finished executing
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteBase">
            <summary>
            This internal class provides the foundation of SQLite support.  It defines all the abstract members needed to implement
            a SQLite data provider, and inherits from SQLiteConvert which allows for simple translations of string to and from SQLite.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteConvert">
            <summary>
            This base class provides datatype conversion services for the SQLite provider.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConvert._datetimeFormats">
            <summary>
            An array of ISO8601 datetime formats we support conversion from
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConvert._utf8">
            <summary>
            An UTF-8 Encoding instance, so we can convert strings to and from UTF-8
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteConvert._datetimeFormat">
            <summary>
            The default DateTime format for this instance
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.#ctor(System.Data.SQLite.SQLiteDateFormats)">
            <summary>
            Initializes the conversion class
            </summary>
            <param name="fmt">The default date/time format to use for this instance</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.ToUTF8(System.String)">
            <summary>
            Converts a string to a UTF-8 encoded byte array sized to include a null-terminating character.
            </summary>
            <param name="sourceText">The string to convert to UTF-8</param>
            <returns>A byte array containing the converted string plus an extra 0 terminating byte at the end of the array.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.ToUTF8(System.DateTime)">
            <summary>
            Convert a DateTime to a UTF-8 encoded, zero-terminated byte array.
            </summary>
            <remarks>
            This function is a convenience function, which first calls ToString() on the DateTime, and then calls ToUTF8() with the
            string result.
            </remarks>
            <param name="dateTimeValue">The DateTime to convert.</param>
            <returns>The UTF-8 encoded string, including a 0 terminating byte at the end of the array.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.ToString(System.IntPtr,System.Int32)">
            <summary>
            Converts a UTF-8 encoded IntPtr of the specified length into a .NET string
            </summary>
            <param name="nativestring">The pointer to the memory where the UTF-8 string is encoded</param>
            <param name="nativestringlen">The number of bytes to decode</param>
            <returns>A string containing the translated character(s)</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.UTF8ToString(System.IntPtr,System.Int32)">
            <summary>
            Converts a UTF-8 encoded IntPtr of the specified length into a .NET string
            </summary>
            <param name="nativestring">The pointer to the memory where the UTF-8 string is encoded</param>
            <param name="nativestringlen">The number of bytes to decode</param>
            <returns>A string containing the translated character(s)</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.ToDateTime(System.String)">
            <summary>
            Converts a string into a DateTime, using the current DateTimeFormat specified for the connection when it was opened.
            </summary>
            <remarks>
            Acceptable ISO8601 DateTime formats are:
              yyyy-MM-dd HH:mm:ss
              yyyyMMddHHmmss
              yyyyMMddTHHmmssfffffff
              yyyy-MM-dd
              yy-MM-dd
              yyyyMMdd
              HH:mm:ss
              THHmmss
            </remarks>
            <param name="dateText">The string containing either a Tick value, a JulianDay double, or an ISO8601-format string</param>
            <returns>A DateTime value</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.ToDateTime(System.Double)">
            <summary>
            Converts a julianday value into a DateTime
            </summary>
            <param name="julianDay">The value to convert</param>
            <returns>A .NET DateTime</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.ToJulianDay(System.DateTime)">
            <summary>
            Converts a DateTime struct to a JulianDay double
            </summary>
            <param name="value">The DateTime to convert</param>
            <returns>The JulianDay value the Datetime represents</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.ToString(System.DateTime)">
            <summary>
            Converts a DateTime to a string value, using the current DateTimeFormat specified for the connection when it was opened.
            </summary>
            <param name="dateValue">The DateTime value to convert</param>
            <returns>Either a string consisting of the tick count for DateTimeFormat.Ticks, a JulianDay double, or a date/time in ISO8601 format.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.ToDateTime(System.IntPtr,System.Int32)">
            <summary>
            Internal function to convert a UTF-8 encoded IntPtr of the specified length to a DateTime.
            </summary>
            <remarks>
            This is a convenience function, which first calls ToString() on the IntPtr to convert it to a string, then calls
            ToDateTime() on the string to return a DateTime.
            </remarks>
            <param name="ptr">A pointer to the UTF-8 encoded string</param>
            <param name="len">The length in bytes of the string</param>
            <returns>The parsed DateTime value</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.Split(System.String,System.Char)">
            <summary>
            Smart method of splitting a string.  Skips quoted elements, removes the quotes.
            </summary>
            <remarks>
            This split function works somewhat like the String.Split() function in that it breaks apart a string into
            pieces and returns the pieces as an array.  The primary differences are:
            <list type="bullet">
            <item><description>Only one character can be provided as a separator character</description></item>
            <item><description>Quoted text inside the string is skipped over when searching for the separator, and the quotes are removed.</description></item>
            </list>
            Thus, if splitting the following string looking for a comma:<br/>
            One,Two, "Three, Four", Five<br/>
            <br/>
            The resulting array would contain<br/>
            [0] One<br/>
            [1] Two<br/>
            [2] Three, Four<br/>
            [3] Five<br/>
            <br/>
            Note that the leading and trailing spaces were removed from each item during the split.
            </remarks>
            <param name="source">Source string to split apart</param>
            <param name="separator">Separator character</param>
            <returns>A string array of the split up elements</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.ToBoolean(System.Object)">
            <summary>
            Convert a value to true or false.
            </summary>
            <param name="source">A string or number representing true or false</param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.ToBoolean(System.String)">
            <summary>
            Convert a string to true or false.
            </summary>
            <param name="source">A string representing true or false</param>
            <returns></returns>
            <remarks>
            "yes", "no", "y", "n", "0", "1", "on", "off" as well as Boolean.FalseString and Boolean.TrueString will all be
            converted to a proper boolean value.
            </remarks>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.ColumnToType(System.Data.SQLite.SQLiteStatement,System.Int32,System.Data.SQLite.SQLiteType)">
            <summary>
            Determines the data type of a column in a statement
            </summary>
            <param name="stmt">The statement to retrieve information for</param>
            <param name="i">The column to retrieve type information on</param>
            <param name="typ">The SQLiteType to receive the affinity for the given column</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.SQLiteTypeToType(System.Data.SQLite.SQLiteType)">
            <summary>
            Converts a SQLiteType to a .NET Type object
            </summary>
            <param name="t">The SQLiteType to convert</param>
            <returns>Returns a .NET Type object</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.TypeToDbType(System.Type)">
            <summary>
            For a given intrinsic type, return a DbType
            </summary>
            <param name="typ">The native type to convert</param>
            <returns>The corresponding (closest match) DbType</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.DbTypeToColumnSize(System.Data.DbType)">
            <summary>
            Returns the ColumnSize for the given DbType
            </summary>
            <param name="typ">The DbType to get the size of</param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.DbTypeToType(System.Data.DbType)">
            <summary>
            Convert a DbType to a Type
            </summary>
            <param name="typ">The DbType to convert from</param>
            <returns>The closest-match .NET type</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.TypeToAffinity(System.Type)">
            <summary>
            For a given type, return the closest-match SQLite TypeAffinity, which only understands a very limited subset of types.
            </summary>
            <param name="typ">The type to evaluate</param>
            <returns>The SQLite type affinity for that type.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteConvert.TypeNameToDbType(System.String)">
            <summary>
            For a given type name, return a closest-match .NET type
            </summary>
            <param name="Name">The name of the type to match</param>
            <returns>The .NET DBType the text evaluates to.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteBase.Open(System.String,System.Data.SQLite.SQLiteOpenFlagsEnum,System.Int32,System.Boolean)">
            <summary>
            Opens a database.
            </summary>
            <remarks>
            Implementers should call SQLiteFunction.BindFunctions() and save the array after opening a connection
            to bind all attributed user-defined functions and collating sequences to the new connection.
            </remarks>
            <param name="strFilename">The filename of the database to open.  SQLite automatically creates it if it doesn't exist.</param>
            <param name="flags">The open flags to use when creating the connection</param>
            <param name="maxPoolSize">The maximum size of the pool for the given filename</param>
            <param name="usePool">If true, the connection can be pulled from the connection pool</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteBase.Close">
            <summary>
            Closes the currently-open database.
            </summary>
            <remarks>
            After the database has been closed implemeters should call SQLiteFunction.UnbindFunctions() to deallocate all interop allocated
            memory associated with the user-defined functions and collating sequences tied to the closed connection.
            </remarks>
        </member>
        <member name="M:System.Data.SQLite.SQLiteBase.SetTimeout(System.Int32)">
            <summary>
            Sets the busy timeout on the connection.  SQLiteCommand will call this before executing any command.
            </summary>
            <param name="nTimeoutMS">The number of milliseconds to wait before returning SQLITE_BUSY</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteBase.SQLiteLastError">
            <summary>
            Returns the text of the last error issued by SQLite
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteBase.ClearPool">
            <summary>
            When pooling is enabled, force this connection to be disposed rather than returned to the pool
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteBase.Prepare(System.Data.SQLite.SQLiteConnection,System.String,System.Data.SQLite.SQLiteStatement,System.UInt32,System.String@)">
            <summary>
            Prepares a SQL statement for execution.
            </summary>
            <param name="cnn">The source connection preparing the command.  Can be null for any caller except LINQ</param>
            <param name="strSql">The SQL command text to prepare</param>
            <param name="previous">The previous statement in a multi-statement command, or null if no previous statement exists</param>
            <param name="timeoutMS">The timeout to wait before aborting the prepare</param>
            <param name="strRemain">The remainder of the statement that was not processed.  Each call to prepare parses the
            SQL up to to either the end of the text or to the first semi-colon delimiter.  The remaining text is returned
            here for a subsequent call to Prepare() until all the text has been processed.</param>
            <returns>Returns an initialized SQLiteStatement.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteBase.Step(System.Data.SQLite.SQLiteStatement)">
            <summary>
            Steps through a prepared statement.
            </summary>
            <param name="stmt">The SQLiteStatement to step through</param>
            <returns>True if a row was returned, False if not.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteBase.Reset(System.Data.SQLite.SQLiteStatement)">
            <summary>
            Resets a prepared statement so it can be executed again.  If the error returned is SQLITE_SCHEMA, 
            transparently attempt to rebuild the SQL statement and throw an error if that was not possible.
            </summary>
            <param name="stmt">The statement to reset</param>
            <returns>Returns -1 if the schema changed while resetting, 0 if the reset was sucessful or 6 (SQLITE_LOCKED) if the reset failed due to a lock</returns>
        </member>
        <member name="P:System.Data.SQLite.SQLiteBase.Version">
            <summary>
            Returns a string representing the active version of SQLite
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteBase.Changes">
            <summary>
            Returns the number of changes the last executing insert/update caused.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.TypeAffinity">
            <summary>
            SQLite has very limited types, and is inherently text-based.  The first 5 types below represent the sum of all types SQLite
            understands.  The DateTime extension to the spec is for internal use only.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.TypeAffinity.Uninitialized">
            <summary>
            Not used
            </summary>
        </member>
        <member name="F:System.Data.SQLite.TypeAffinity.Int64">
            <summary>
            All integers in SQLite default to Int64
            </summary>
        </member>
        <member name="F:System.Data.SQLite.TypeAffinity.Double">
            <summary>
            All floating point numbers in SQLite default to double
            </summary>
        </member>
        <member name="F:System.Data.SQLite.TypeAffinity.Text">
            <summary>
            The default data type of SQLite is text
            </summary>
        </member>
        <member name="F:System.Data.SQLite.TypeAffinity.Blob">
            <summary>
            Typically blob types are only seen when returned from a function
            </summary>
        </member>
        <member name="F:System.Data.SQLite.TypeAffinity.Null">
            <summary>
            Null types can be returned from functions
            </summary>
        </member>
        <member name="F:System.Data.SQLite.TypeAffinity.DateTime">
            <summary>
            Used internally by this provider
            </summary>
        </member>
        <member name="F:System.Data.SQLite.TypeAffinity.None">
            <summary>
            Used internally
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteDateFormats">
            <summary>
            This implementation of SQLite for ADO.NET can process date/time fields in databases in only one of three formats.  Ticks, ISO8601
            and JulianDay.
            </summary>
            <remarks>
            ISO8601 is more compatible, readable, fully-processable, but less accurate as it doesn't provide time down to fractions of a second.
            JulianDay is the numeric format the SQLite uses internally and is arguably the most compatible with 3rd party tools.  It is
            not readable as text without post-processing.
            Ticks less compatible with 3rd party tools that query the database, and renders the DateTime field unreadable as text without post-processing.
            
            The preferred order of choosing a datetime format is JulianDay, ISO8601, and then Ticks.  Ticks is mainly present for legacy 
            code support.
            </remarks>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDateFormats.Ticks">
            <summary>
            Using ticks is not recommended and is not well supported with LINQ.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDateFormats.ISO8601">
            <summary>
            The default format for this provider.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDateFormats.JulianDay">
            <summary>
            JulianDay format, which is what SQLite uses internally
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteJournalModeEnum">
            <summary>
            This enum determines how SQLite treats its journal file.
            </summary>
            <remarks>
            By default SQLite will create and delete the journal file when needed during a transaction.
            However, for some computers running certain filesystem monitoring tools, the rapid
            creation and deletion of the journal file can cause those programs to fail, or to interfere with SQLite.
            
            If a program or virus scanner is interfering with SQLite's journal file, you may receive errors like "unable to open database file"
            when starting a transaction.  If this is happening, you may want to change the default journal mode to Persist.
            </remarks>
        </member>
        <member name="F:System.Data.SQLite.SQLiteJournalModeEnum.Delete">
            <summary>
            The default mode, this causes SQLite to create and destroy the journal file as-needed.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteJournalModeEnum.Persist">
            <summary>
            When this is set, SQLite will keep the journal file even after a transaction has completed.  It's contents will be erased,
            and the journal re-used as often as needed.  If it is deleted, it will be recreated the next time it is needed.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteJournalModeEnum.Off">
            <summary>
            This option disables the rollback journal entirely.  Interrupted transactions or a program crash can cause database
            corruption in this mode!
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteType">
            <summary>
            Struct used internally to determine the datatype of a column in a resultset
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteType.Type">
            <summary>
            The DbType of the column, or DbType.Object if it cannot be determined
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteType.Affinity">
            <summary>
            The affinity of a column, used for expressions or when Type is DbType.Object
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteStatement">
            <summary>
            Represents a single SQL statement in SQLite.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteStatement._sql">
            <summary>
            The underlying SQLite object this statement is bound to
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteStatement._sqlStatement">
            <summary>
            The command text of this SQL statement
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteStatement._sqlite_stmt">
            <summary>
            The actual statement pointer
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteStatement._unnamedParameters">
            <summary>
            An index from which unnamed parameters begin
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteStatement._paramNames">
            <summary>
            Names of the parameters as SQLite understands them to be
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteStatement._paramValues">
            <summary>
            Parameters for this statement
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteStatement._command">
            <summary>
            Command this statement belongs to (if any)
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteStatement.#ctor(System.Data.SQLite.SQLiteBase,System.Data.SQLite.SQLiteStatementHandle,System.String,System.Data.SQLite.SQLiteStatement)">
            <summary>
            Initializes the statement and attempts to get all information about parameters in the statement
            </summary>
            <param name="sqlbase">The base SQLite object</param>
            <param name="stmt">The statement</param>
            <param name="strCommand">The command text for this statement</param>
            <param name="previous">The previous command in a multi-statement command</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteStatement.MapParameter(System.String,System.Data.SQLite.SQLiteParameter)">
            <summary>
            Called by SQLiteParameterCollection, this function determines if the specified parameter name belongs to
            this statement, and if so, keeps a reference to the parameter so it can be bound later.
            </summary>
            <param name="s">The parameter name to map</param>
            <param name="p">The parameter to assign it</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteStatement.Dispose">
            <summary>
            Disposes and finalizes the statement
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteStatement.BindParameters">
            <summary>
             Bind all parameters, making sure the caller didn't miss any
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteStatement.BindParameter(System.Int32,System.Data.SQLite.SQLiteParameter)">
            <summary>
            Perform the bind operation for an individual parameter
            </summary>
            <param name="index">The index of the parameter to bind</param>
            <param name="param">The parameter we're binding</param>
        </member>
        <member name="T:System.Data.SQLite.SQLiteDataReader">
            <summary>
            SQLite implementation of DbDataReader.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDataReader._command">
            <summary>
            Underlying command this reader is attached to
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDataReader._activeStatementIndex">
            <summary>
            Index of the current statement in the command being processed
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDataReader._activeStatement">
            <summary>
            Current statement being Read()
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDataReader._readingState">
            <summary>
            State of the current statement being processed.
            -1 = First Step() executed, so the first Read() will be ignored
             0 = Actively reading
             1 = Finished reading
             2 = Non-row-returning statement, no records
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDataReader._rowsAffected">
            <summary>
            Number of records affected by the insert/update statements executed on the command
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDataReader._fieldCount">
            <summary>
            Count of fields (columns) in the row-returning statement currently being processed
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDataReader._fieldTypeArray">
            <summary>
            Datatypes of active fields (columns) in the current statement, used for type-restricting data
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDataReader._commandBehavior">
            <summary>
            The behavior of the datareader
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDataReader._disposeCommand">
            <summary>
            If set, then dispose of the command object when the reader is finished
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteDataReader._keyInfo">
            <summary>
            An array of rowid's for the active statement if CommandBehavior.KeyInfo is specified
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.#ctor(System.Data.SQLite.SQLiteCommand,System.Data.CommandBehavior)">
            <summary>
            Internal constructor, initializes the datareader and sets up to begin executing statements
            </summary>
            <param name="cmd">The SQLiteCommand this data reader is for</param>
            <param name="behave">The expected behavior of the data reader</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.Close">
            <summary>
            Closes the datareader, potentially closing the connection as well if CommandBehavior.CloseConnection was specified.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.CheckClosed">
            <summary>
            Throw an error if the datareader is closed
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.CheckValidRow">
            <summary>
            Throw an error if a row is not loaded
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetEnumerator">
            <summary>
            Enumerator support
            </summary>
            <returns>Returns a DbEnumerator object.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.VerifyType(System.Int32,System.Data.DbType)">
            <summary>
            SQLite is inherently un-typed.  All datatypes in SQLite are natively strings.  The definition of the columns of a table
            and the affinity of returned types are all we have to go on to type-restrict data in the reader.
            
            This function attempts to verify that the type of data being requested of a column matches the datatype of the column.  In
            the case of columns that are not backed into a table definition, we attempt to match up the affinity of a column (int, double, string or blob)
            to a set of known types that closely match that affinity.  It's not an exact science, but its the best we can do.
            </summary>
            <returns>
            This function throws an InvalidTypeCast() exception if the requested type doesn't match the column's definition or affinity.
            </returns>
            <param name="i">The index of the column to type-check</param>
            <param name="typ">The type we want to get out of the column</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetBoolean(System.Int32)">
            <summary>
            Retrieves the column as a boolean value
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>bool</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetByte(System.Int32)">
            <summary>
            Retrieves the column as a single byte value
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>byte</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Retrieves a column as an array of bytes (blob)
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <param name="fieldOffset">The zero-based index of where to begin reading the data</param>
            <param name="buffer">The buffer to write the bytes into</param>
            <param name="bufferoffset">The zero-based index of where to begin writing into the array</param>
            <param name="length">The number of bytes to retrieve</param>
            <returns>The actual number of bytes written into the array</returns>
            <remarks>
            To determine the number of bytes in the column, pass a null value for the buffer.  The total length will be returned.
            </remarks>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetChar(System.Int32)">
            <summary>
            Returns the column as a single character
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>char</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
            <summary>
            Retrieves a column as an array of chars (blob)
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <param name="fieldoffset">The zero-based index of where to begin reading the data</param>
            <param name="buffer">The buffer to write the characters into</param>
            <param name="bufferoffset">The zero-based index of where to begin writing into the array</param>
            <param name="length">The number of bytes to retrieve</param>
            <returns>The actual number of characters written into the array</returns>
            <remarks>
            To determine the number of characters in the column, pass a null value for the buffer.  The total length will be returned.
            </remarks>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetDataTypeName(System.Int32)">
            <summary>
            Retrieves the name of the back-end datatype of the column
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>string</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetDateTime(System.Int32)">
            <summary>
            Retrieve the column as a date/time value
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>DateTime</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetDecimal(System.Int32)">
            <summary>
            Retrieve the column as a decimal value
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>decimal</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetDouble(System.Int32)">
            <summary>
            Returns the column as a double
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>double</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetFieldType(System.Int32)">
            <summary>
            Returns the .NET type of a given column
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>Type</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetFloat(System.Int32)">
            <summary>
            Returns a column as a float value
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>float</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetGuid(System.Int32)">
            <summary>
            Returns the column as a Guid
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>Guid</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetInt16(System.Int32)">
            <summary>
            Returns the column as a short
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>Int16</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetInt32(System.Int32)">
            <summary>
            Retrieves the column as an int
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>Int32</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetInt64(System.Int32)">
            <summary>
            Retrieves the column as a long
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>Int64</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetName(System.Int32)">
            <summary>
            Retrieves the name of the column
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>string</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetOrdinal(System.String)">
            <summary>
            Retrieves the i of a column, given its name
            </summary>
            <param name="name">The name of the column to retrieve</param>
            <returns>The int i of the column</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetSchemaTable">
            <summary>
            Schema information in SQLite is difficult to map into .NET conventions, so a lot of work must be done
            to gather the necessary information so it can be represented in an ADO.NET manner.
            </summary>
            <returns>Returns a DataTable containing the schema information for the active SELECT statement being processed.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetString(System.Int32)">
            <summary>
            Retrieves the column as a string
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>string</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetValue(System.Int32)">
            <summary>
            Retrieves the column as an object corresponding to the underlying datatype of the column
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>object</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetValues(System.Object[])">
            <summary>
            Retreives the values of multiple columns, up to the size of the supplied array
            </summary>
            <param name="values">The array to fill with values from the columns in the current resultset</param>
            <returns>The number of columns retrieved</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.IsDBNull(System.Int32)">
            <summary>
            Returns True if the specified column is null
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>True or False</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.NextResult">
            <summary>
            Moves to the next resultset in multiple row-returning SQL command.
            </summary>
            <returns>True if the command was successful and a new resultset is available, False otherwise.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.GetSQLiteType(System.Int32)">
            <summary>
            Retrieves the SQLiteType for a given column, and caches it to avoid repetetive interop calls.
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>A SQLiteType structure</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataReader.Read">
            <summary>
            Reads the next row from the resultset
            </summary>
            <returns>True if a new row was successfully loaded and is ready for processing</returns>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataReader.Depth">
            <summary>
            Not implemented.  Returns 0
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataReader.FieldCount">
            <summary>
            Returns the number of columns in the current resultset
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataReader.VisibleFieldCount">
            <summary>
            Returns the number of visible fielsd in the current resultset
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataReader.HasRows">
            <summary>
            Returns True if the resultset has rows that can be fetched
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataReader.IsClosed">
            <summary>
            Returns True if the data reader is closed
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataReader.RecordsAffected">
            <summary>
            Retrieve the count of records affected by an update/insert command.  Only valid once the data reader is closed!
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataReader.Item(System.String)">
            <summary>
            Indexer to retrieve data from a column given its name
            </summary>
            <param name="name">The name of the column to retrieve data for</param>
            <returns>The value contained in the column</returns>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataReader.Item(System.Int32)">
            <summary>
            Indexer to retrieve data from a column given its i
            </summary>
            <param name="i">The index of the column to retrieve</param>
            <returns>The value contained in the column</returns>
        </member>
        <member name="T:System.Data.SQLite.SQLiteDataAdapter">
            <summary>
            SQLite implementation of DbDataAdapter.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataAdapter.#ctor">
            <overloads>
            This class is just a shell around the DbDataAdapter.  Nothing from DbDataAdapter is overridden here, just a few constructors are defined.
            </overloads>
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataAdapter.#ctor(System.Data.SQLite.SQLiteCommand)">
            <summary>
            Constructs a data adapter using the specified select command.
            </summary>
            <param name="cmd">The select command to associate with the adapter.</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataAdapter.#ctor(System.String,System.Data.SQLite.SQLiteConnection)">
            <summary>
            Constructs a data adapter with the supplied select command text and associated with the specified connection.
            </summary>
            <param name="commandText">The select command text to associate with the data adapter.</param>
            <param name="connection">The connection to associate with the select command.</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataAdapter.#ctor(System.String,System.String)">
            <summary>
            Constructs a data adapter with the specified select command text, and using the specified database connection string.
            </summary>
            <param name="commandText">The select command text to use to construct a select command.</param>
            <param name="connectionString">A connection string suitable for passing to a new SQLiteConnection, which is associated with the select command.</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataAdapter.OnRowUpdating(System.Data.Common.RowUpdatingEventArgs)">
            <summary>
            Raised by the underlying DbDataAdapter when a row is being updated
            </summary>
            <param name="value">The event's specifics</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteDataAdapter.OnRowUpdated(System.Data.Common.RowUpdatedEventArgs)">
            <summary>
            Raised by DbDataAdapter after a row is updated
            </summary>
            <param name="value">The event's specifics</param>
        </member>
        <member name="E:System.Data.SQLite.SQLiteDataAdapter.RowUpdating">
            <summary>
            Row updating event handler
            </summary>
        </member>
        <member name="E:System.Data.SQLite.SQLiteDataAdapter.RowUpdated">
            <summary>
            Row updated event handler
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataAdapter.SelectCommand">
            <summary>
            Gets/sets the select command for this DataAdapter
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataAdapter.InsertCommand">
            <summary>
            Gets/sets the insert command for this DataAdapter
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataAdapter.UpdateCommand">
            <summary>
            Gets/sets the update command for this DataAdapter
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteDataAdapter.DeleteCommand">
            <summary>
            Gets/sets the delete command for this DataAdapter
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteTransaction">
            <summary>
            SQLite implementation of DbTransaction.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteTransaction._cnn">
            <summary>
            The connection to which this transaction is bound
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteTransaction.#ctor(System.Data.SQLite.SQLiteConnection,System.Boolean)">
            <summary>
            Constructs the transaction object, binding it to the supplied connection
            </summary>
            <param name="connection">The connection to open a transaction on</param>
            <param name="deferredLock">TRUE to defer the writelock, or FALSE to lock immediately</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteTransaction.Commit">
            <summary>
            Commits the current transaction.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteTransaction.Dispose(System.Boolean)">
            <summary>
            Disposes the transaction.  If it is currently active, any changes are rolled back.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteTransaction.Rollback">
            <summary>
            Rolls back the active transaction.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteTransaction.Connection">
            <summary>
            Returns the underlying connection to which this transaction applies.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteTransaction.DbConnection">
            <summary>
            Forwards to the local Connection property
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteTransaction.IsolationLevel">
            <summary>
            Gets the isolation level of the transaction.  SQLite only supports Serializable transactions.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteParameterCollection">
            <summary>
            SQLite implementation of DbParameterCollection.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteParameterCollection._command">
            <summary>
            The underlying command to which this collection belongs
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteParameterCollection._parameterList">
            <summary>
            The internal array of parameters in this collection
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteParameterCollection._unboundFlag">
            <summary>
            Determines whether or not all parameters have been bound to their statement(s)
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.#ctor(System.Data.SQLite.SQLiteCommand)">
            <summary>
            Initializes the collection
            </summary>
            <param name="cmd">The command to which the collection belongs</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.GetEnumerator">
            <summary>
            Retrieves an enumerator for the collection
            </summary>
            <returns>An enumerator for the underlying array</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.Add(System.String,System.Data.DbType,System.Int32,System.String)">
            <summary>
            Adds a parameter to the collection
            </summary>
            <param name="parameterName">The parameter name</param>
            <param name="parameterType">The data type</param>
            <param name="parameterSize">The size of the value</param>
            <param name="sourceColumn">The source column</param>
            <returns>A SQLiteParameter object</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.Add(System.String,System.Data.DbType,System.Int32)">
            <summary>
            Adds a parameter to the collection
            </summary>
            <param name="parameterName">The parameter name</param>
            <param name="parameterType">The data type</param>
            <param name="parameterSize">The size of the value</param>
            <returns>A SQLiteParameter object</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.Add(System.String,System.Data.DbType)">
            <summary>
            Adds a parameter to the collection
            </summary>
            <param name="parameterName">The parameter name</param>
            <param name="parameterType">The data type</param>
            <returns>A SQLiteParameter object</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.Add(System.Data.SQLite.SQLiteParameter)">
            <summary>
            Adds a parameter to the collection
            </summary>
            <param name="parameter">The parameter to add</param>
            <returns>A zero-based index of where the parameter is located in the array</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.Add(System.Object)">
            <summary>
            Adds a parameter to the collection
            </summary>
            <param name="value">The parameter to add</param>
            <returns>A zero-based index of where the parameter is located in the array</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.AddWithValue(System.String,System.Object)">
            <summary>
            Adds a named/unnamed parameter and its value to the parameter collection.
            </summary>
            <param name="parameterName">Name of the parameter, or null to indicate an unnamed parameter</param>
            <param name="value">The initial value of the parameter</param>
            <returns>Returns the SQLiteParameter object created during the call.</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.AddRange(System.Data.SQLite.SQLiteParameter[])">
            <summary>
            Adds an array of parameters to the collection
            </summary>
            <param name="values">The array of parameters to add</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.AddRange(System.Array)">
            <summary>
            Adds an array of parameters to the collection
            </summary>
            <param name="values">The array of parameters to add</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.Clear">
            <summary>
            Clears the array and resets the collection
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.Contains(System.String)">
            <summary>
            Determines if the named parameter exists in the collection
            </summary>
            <param name="parameterName">The name of the parameter to check</param>
            <returns>True if the parameter is in the collection</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.Contains(System.Object)">
            <summary>
            Determines if the parameter exists in the collection
            </summary>
            <param name="value">The SQLiteParameter to check</param>
            <returns>True if the parameter is in the collection</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            Not implemented
            </summary>
            <param name="array"></param>
            <param name="index"></param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.GetParameter(System.String)">
            <summary>
            Retrieve a parameter by name from the collection
            </summary>
            <param name="parameterName">The name of the parameter to fetch</param>
            <returns>A DbParameter object</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.GetParameter(System.Int32)">
            <summary>
            Retrieves a parameter by its index in the collection
            </summary>
            <param name="index">The index of the parameter to retrieve</param>
            <returns>A DbParameter object</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.IndexOf(System.String)">
            <summary>
            Returns the index of a parameter given its name
            </summary>
            <param name="parameterName">The name of the parameter to find</param>
            <returns>-1 if not found, otherwise a zero-based index of the parameter</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.IndexOf(System.Object)">
            <summary>
            Returns the index of a parameter
            </summary>
            <param name="value">The parameter to find</param>
            <returns>-1 if not found, otherwise a zero-based index of the parameter</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.Insert(System.Int32,System.Object)">
            <summary>
            Inserts a parameter into the array at the specified location
            </summary>
            <param name="index">The zero-based index to insert the parameter at</param>
            <param name="value">The parameter to insert</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.Remove(System.Object)">
            <summary>
            Removes a parameter from the collection
            </summary>
            <param name="value">The parameter to remove</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.RemoveAt(System.String)">
            <summary>
            Removes a parameter from the collection given its name
            </summary>
            <param name="parameterName">The name of the parameter to remove</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.RemoveAt(System.Int32)">
            <summary>
            Removes a parameter from the collection given its index
            </summary>
            <param name="index">The zero-based parameter index to remove</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.SetParameter(System.String,System.Data.Common.DbParameter)">
            <summary>
            Re-assign the named parameter to a new parameter object
            </summary>
            <param name="parameterName">The name of the parameter to replace</param>
            <param name="value">The new parameter</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.SetParameter(System.Int32,System.Data.Common.DbParameter)">
            <summary>
            Re-assign a parameter at the specified index
            </summary>
            <param name="index">The zero-based index of the parameter to replace</param>
            <param name="value">The new parameter</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.Unbind">
            <summary>
            Un-binds all parameters from their statements
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameterCollection.MapParameters(System.Data.SQLite.SQLiteStatement)">
            <summary>
            This function attempts to map all parameters in the collection to all statements in a Command.
            Since named parameters may span multiple statements, this function makes sure all statements are bound
            to the same named parameter.  Unnamed parameters are bound in sequence.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameterCollection.IsSynchronized">
            <summary>
            Returns true
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameterCollection.IsFixedSize">
            <summary>
            Returns false
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameterCollection.IsReadOnly">
            <summary>
            Returns false
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameterCollection.SyncRoot">
            <summary>
            Returns null
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameterCollection.Count">
            <summary>
            Returns a count of parameters in the collection
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameterCollection.Item(System.String)">
            <summary>
            Overloaded to specialize the return value of the default indexer
            </summary>
            <param name="parameterName">Name of the parameter to get/set</param>
            <returns>The specified named SQLite parameter</returns>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameterCollection.Item(System.Int32)">
            <summary>
            Overloaded to specialize the return value of the default indexer
            </summary>
            <param name="index">The index of the parameter to get/set</param>
            <returns>The specified SQLite parameter</returns>
        </member>
        <member name="T:System.Data.SQLite.SQLiteKeyReader">
            <summary>
            This class provides key info for a given SQLite statement.
            <remarks>
            Providing key information for a given statement is non-trivial :(
            </remarks>
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteKeyReader.#ctor(System.Data.SQLite.SQLiteConnection,System.Data.SQLite.SQLiteDataReader,System.Data.SQLite.SQLiteStatement)">
            <summary>
            This function does all the nasty work at determining what keys need to be returned for
            a given statement.
            </summary>
            <param name="cnn"></param>
            <param name="reader"></param>
            <param name="stmt"></param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteKeyReader.Sync">
            <summary>
            Make sure all the subqueries are open and ready and sync'd with the current rowid
            of the table they're supporting
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteKeyReader.Reset">
            <summary>
            Release any readers on any subqueries
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteKeyReader.AppendSchemaTable(System.Data.DataTable)">
            <summary>
            Append all the columns we've added to the original query to the schema
            </summary>
            <param name="tbl"></param>
        </member>
        <member name="P:System.Data.SQLite.SQLiteKeyReader.Count">
            <summary>
            How many additional columns of keyinfo we're holding
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteKeyReader.KeyInfo">
            <summary>
            Used to support CommandBehavior.KeyInfo
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteKeyReader.KeyQuery">
            <summary>
            A single sub-query for a given table/database.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteCommand">
            <summary>
            SQLite implementation of DbCommand.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteCommand._commandText">
            <summary>
            The command text this command is based on
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteCommand._cnn">
            <summary>
            The connection the command is associated with
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteCommand._version">
            <summary>
            The version of the connection the command is associated with
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteCommand._activeReader">
            <summary>
            Indicates whether or not a DataReader is active on the command.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteCommand._commandTimeout">
            <summary>
            The timeout for the command, kludged because SQLite doesn't support per-command timeout values
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteCommand._designTimeVisible">
            <summary>
            Designer support
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteCommand._updateRowSource">
            <summary>
            Used by DbDataAdapter to determine updating behavior
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteCommand._parameterCollection">
            <summary>
            The collection of parameters for the command
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteCommand._statementList">
            <summary>
            The SQL command text, broken into individual SQL statements as they are executed
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteCommand._remainingText">
            <summary>
            Unprocessed SQL text that has not been executed
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteCommand._transaction">
            <summary>
            Transaction associated with this command
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.#ctor">
            <overloads>
             Constructs a new SQLiteCommand
             </overloads>
             <summary>
             Default constructor
             </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.#ctor(System.String)">
            <summary>
            Initializes the command with the given command text
            </summary>
            <param name="commandText">The SQL command text</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.#ctor(System.String,System.Data.SQLite.SQLiteConnection)">
            <summary>
            Initializes the command with the given SQL command text and attach the command to the specified
            connection.
            </summary>
            <param name="commandText">The SQL command text</param>
            <param name="connection">The connection to associate with the command</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.#ctor(System.Data.SQLite.SQLiteConnection)">
            <summary>
            Initializes the command and associates it with the specified connection.
            </summary>
            <param name="connection">The connection to associate with the command</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.#ctor(System.String,System.Data.SQLite.SQLiteConnection,System.Data.SQLite.SQLiteTransaction)">
            <summary>
            Initializes a command with the given SQL, connection and transaction
            </summary>
            <param name="commandText">The SQL command text</param>
            <param name="connection">The connection to associate with the command</param>
            <param name="transaction">The transaction the command should be associated with</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.Dispose(System.Boolean)">
            <summary>
            Disposes of the command and clears all member variables
            </summary>
            <param name="disposing">Whether or not the class is being explicitly or implicitly disposed</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.ClearCommands">
            <summary>
            Clears and destroys all statements currently prepared
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.BuildNextCommand">
            <summary>
            Builds an array of prepared statements for each complete SQL statement in the command text
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.Cancel">
            <summary>
            Not implemented
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.CreateDbParameter">
            <summary>
            Forwards to the local CreateParameter() function
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.CreateParameter">
            <summary>
            Create a new parameter
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.InitializeForReader">
            <summary>
            This function ensures there are no active readers, that we have a valid connection,
            that the connection is open, that all statements are prepared and all parameters are assigned
            in preparation for allocating a data reader.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
            <summary>
            Creates a new SQLiteDataReader to execute/iterate the array of SQLite prepared statements
            </summary>
            <param name="behavior">The behavior the data reader should adopt</param>
            <returns>Returns a SQLiteDataReader object</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.ExecuteReader(System.Data.CommandBehavior)">
            <summary>
            Overrides the default behavior to return a SQLiteDataReader specialization class
            </summary>
            <param name="behavior">The flags to be associated with the reader</param>
            <returns>A SQLiteDataReader</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.ExecuteReader">
            <summary>
            Overrides the default behavior of DbDataReader to return a specialized SQLiteDataReader class
            </summary>
            <returns>A SQLiteDataReader</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.ClearDataReader">
            <summary>
            Called by the SQLiteDataReader when the data reader is closed.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.ExecuteNonQuery">
            <summary>
            Execute the command and return the number of rows inserted/updated affected by it.
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.ExecuteScalar">
            <summary>
            Execute the command and return the first column of the first row of the resultset
            (if present), or null if no resultset was returned.
            </summary>
            <returns>The first column of the first row of the first resultset from the query</returns>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.Prepare">
            <summary>
            Does nothing.  Commands are prepared as they are executed the first time, and kept in prepared state afterwards.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteCommand.Clone">
            <summary>
            Clones a command, including all its parameters
            </summary>
            <returns>A new SQLiteCommand with the same commandtext, connection and parameters</returns>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommand.CommandText">
            <summary>
            The SQL command text associated with the command
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommand.CommandTimeout">
            <summary>
            The amount of time to wait for the connection to become available before erroring out
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommand.CommandType">
            <summary>
            The type of the command.  SQLite only supports CommandType.Text
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommand.Connection">
            <summary>
            The connection associated with this command
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommand.DbConnection">
            <summary>
            Forwards to the local Connection property
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommand.Parameters">
            <summary>
            Returns the SQLiteParameterCollection for the given command
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommand.DbParameterCollection">
            <summary>
            Forwards to the local Parameters property
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommand.Transaction">
            <summary>
            The transaction associated with this command.  SQLite only supports one transaction per connection, so this property forwards to the
            command's underlying connection.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommand.DbTransaction">
            <summary>
            Forwards to the local Transaction property
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommand.UpdatedRowSource">
            <summary>
            Sets the method the SQLiteCommandBuilder uses to determine how to update inserted or updated rows in a DataTable.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteCommand.DesignTimeVisible">
            <summary>
            Determines if the command is visible at design time.  Defaults to True.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLiteFunctionAttribute">
            <summary>
            A simple custom attribute to enable us to easily find user-defined functions in
            the loaded assemblies and initialize them in SQLite as connections are made.
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteFunctionAttribute.#ctor">
            <summary>
            Default constructor, initializes the internal variables for the function.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteFunctionAttribute.Name">
            <summary>
            The function's name as it will be used in SQLite command text.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteFunctionAttribute.Arguments">
            <summary>
            The number of arguments this function expects.  -1 if the number of arguments is variable.
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteFunctionAttribute.FuncType">
            <summary>
            The type of function this implementation will be.
            </summary>
        </member>
        <member name="T:System.Data.SQLite.SQLite3">
            <summary>
            This class implements SQLiteBase completely, and is the guts of the code that interop's SQLite with .NET
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLite3._sql">
            <summary>
            The opaque pointer returned to us by the sqlite provider
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLite3._functionsArray">
            <summary>
            The user-defined functions registered on this connection
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLite3.GetValue(System.Data.SQLite.SQLiteStatement,System.Int32,System.Data.SQLite.SQLiteType)">
            <summary>
            Helper function to retrieve a column of data from an active statement.
            </summary>
            <param name="stmt">The statement being step()'d through</param>
            <param name="index">The column index to retrieve</param>
            <param name="typ">The type of data contained in the column.  If Uninitialized, this function will retrieve the datatype information.</param>
            <returns>Returns the data in the column</returns>
        </member>
        <member name="T:System.Data.SQLite.SQLite3_UTF16">
            <summary>
            Alternate SQLite3 object, overriding many text behaviors to support UTF-16 (Unicode)
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLite3_UTF16.ToString(System.IntPtr,System.Int32)">
            <summary>
            Overrides SQLiteConvert.ToString() to marshal UTF-16 strings instead of UTF-8
            </summary>
            <param name="b">A pointer to a UTF-16 string</param>
            <param name="nbytelen">The length (IN BYTES) of the string</param>
            <returns>A .NET string</returns>
        </member>
        <member name="T:System.Data.SQLite.SQLiteParameter">
            <summary>
            SQLite implementation of DbParameter.
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteParameter._dbType">
            <summary>
            The data type of the parameter
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteParameter._rowVersion">
            <summary>
            The version information for mapping the parameter
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteParameter._objValue">
            <summary>
            The value of the data in the parameter
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteParameter._sourceColumn">
            <summary>
            The source column for the parameter
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteParameter._parameterName">
            <summary>
            The column name
            </summary>
        </member>
        <member name="F:System.Data.SQLite.SQLiteParameter._dataSize">
            <summary>
            The data size, unused by SQLite
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.String)">
            <summary>
            Constructs a named parameter given the specified parameter name
            </summary>
            <param name="parameterName">The parameter name</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.String,System.Object)">
            <summary>
            Constructs a named parameter given the specified parameter name and initial value
            </summary>
            <param name="parameterName">The parameter name</param>
            <param name="value">The initial value of the parameter</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.String,System.Data.DbType)">
            <summary>
            Constructs a named parameter of the specified type
            </summary>
            <param name="parameterName">The parameter name</param>
            <param name="dbType">The datatype of the parameter</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.String,System.Data.DbType,System.String)">
            <summary>
            Constructs a named parameter of the specified type and source column reference
            </summary>
            <param name="parameterName">The parameter name</param>
            <param name="dbType">The data type</param>
            <param name="sourceColumn">The source column</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.String,System.Data.DbType,System.String,System.Data.DataRowVersion)">
            <summary>
            Constructs a named parameter of the specified type, source column and row version
            </summary>
            <param name="parameterName">The parameter name</param>
            <param name="dbType">The data type</param>
            <param name="sourceColumn">The source column</param>
            <param name="rowVersion">The row version information</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.Data.DbType)">
            <summary>
            Constructs an unnamed parameter of the specified data type
            </summary>
            <param name="dbType">The datatype of the parameter</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.Data.DbType,System.Object)">
            <summary>
            Constructs an unnamed parameter of the specified data type and sets the initial value
            </summary>
            <param name="dbType">The datatype of the parameter</param>
            <param name="value">The initial value of the parameter</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.Data.DbType,System.String)">
            <summary>
            Constructs an unnamed parameter of the specified data type and source column
            </summary>
            <param name="dbType">The datatype of the parameter</param>
            <param name="sourceColumn">The source column</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.Data.DbType,System.String,System.Data.DataRowVersion)">
            <summary>
            Constructs an unnamed parameter of the specified data type, source column and row version
            </summary>
            <param name="dbType">The data type</param>
            <param name="sourceColumn">The source column</param>
            <param name="rowVersion">The row version information</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.String,System.Data.DbType,System.Int32)">
            <summary>
            Constructs a named parameter of the specified type and size
            </summary>
            <param name="parameterName">The parameter name</param>
            <param name="parameterType">The data type</param>
            <param name="parameterSize">The size of the parameter</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.String,System.Data.DbType,System.Int32,System.String)">
            <summary>
            Constructs a named parameter of the specified type, size and source column
            </summary>
            <param name="parameterName">The name of the parameter</param>
            <param name="parameterType">The data type</param>
            <param name="parameterSize">The size of the parameter</param>
            <param name="sourceColumn">The source column</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.String,System.Data.DbType,System.Int32,System.String,System.Data.DataRowVersion)">
            <summary>
            Constructs a named parameter of the specified type, size, source column and row version
            </summary>
            <param name="parameterName">The name of the parameter</param>
            <param name="parameterType">The data type</param>
            <param name="parameterSize">The size of the parameter</param>
            <param name="sourceColumn">The source column</param>
            <param name="rowVersion">The row version information</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.String,System.Data.DbType,System.Int32,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Object)">
            <summary>
            Constructs a named parameter of the specified type, size, source column and row version
            </summary>
            <param name="parameterName">The name of the parameter</param>
            <param name="parameterType">The data type</param>
            <param name="parameterSize">The size of the parameter</param>
            <param name="direction">Only input parameters are supported in SQLite</param>
            <param name="isNullable">Ignored</param>
            <param name="precision">Ignored</param>
            <param name="scale">Ignored</param>
            <param name="sourceColumn">The source column</param>
            <param name="rowVersion">The row version information</param>
            <param name="value">The initial value to assign the parameter</param>   
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.String,System.Data.DbType,System.Int32,System.Data.ParameterDirection,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Boolean,System.Object)">
            <summary>
            Constructs a named parameter, yet another flavor
            </summary>
            <param name="parameterName">The name of the parameter</param>
            <param name="parameterType">The data type</param>
            <param name="parameterSize">The size of the parameter</param>
            <param name="direction">Only input parameters are supported in SQLite</param>
            <param name="precision">Ignored</param>
            <param name="scale">Ignored</param>
            <param name="sourceColumn">The source column</param>
            <param name="rowVersion">The row version information</param>
            <param name="sourceColumnNullMapping">Whether or not this parameter is for comparing NULL's</param>
            <param name="value">The intial value to assign the parameter</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.Data.DbType,System.Int32)">
            <summary>
            Constructs an unnamed parameter of the specified type and size
            </summary>
            <param name="parameterType">The data type</param>
            <param name="parameterSize">The size of the parameter</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.Data.DbType,System.Int32,System.String)">
            <summary>
            Constructs an unnamed parameter of the specified type, size, and source column
            </summary>
            <param name="parameterType">The data type</param>
            <param name="parameterSize">The size of the parameter</param>
            <param name="sourceColumn">The source column</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.#ctor(System.Data.DbType,System.Int32,System.String,System.Data.DataRowVersion)">
            <summary>
            Constructs an unnamed parameter of the specified type, size, source column and row version
            </summary>
            <param name="parameterType">The data type</param>
            <param name="parameterSize">The size of the parameter</param>
            <param name="sourceColumn">The source column</param>
            <param name="rowVersion">The row version information</param>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.ResetDbType">
            <summary>
            Resets the DbType of the parameter so it can be inferred from the value
            </summary>
        </member>
        <member name="M:System.Data.SQLite.SQLiteParameter.Clone">
            <summary>
            Clones a parameter
            </summary>
            <returns>A new, unassociated SQLiteParameter</returns>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameter.IsNullable">
            <summary>
            Whether or not the parameter can contain a null value
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameter.DbType">
            <summary>
            Returns the datatype of the parameter
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameter.Direction">
            <summary>
            Supports only input parameters
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameter.ParameterName">
            <summary>
            Returns the parameter name
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameter.Size">
            <summary>
            Returns the size of the parameter
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameter.SourceColumn">
            <summary>
            Gets/sets the source column
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameter.SourceColumnNullMapping">
            <summary>
            Used by DbCommandBuilder to determine the mapping for nullable fields
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameter.SourceVersion">
            <summary>
            Gets and sets the row version
            </summary>
        </member>
        <member name="P:System.Data.SQLite.SQLiteParameter.Value">
            <summary>
            Gets and sets the parameter value.  If no datatype was specified, the datatype will assume the type from the value given.
            </summary>
        </member>
    </members>
</doc>
