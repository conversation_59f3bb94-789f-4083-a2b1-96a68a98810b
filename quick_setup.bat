@echo off
chcp 65001 > nul
echo ========================================
echo إعداد سريع لنظام المحاسبة الذكي
echo ========================================
echo.

echo هذا الإعداد السريع سيقوم بما يلي:
echo 1. التحقق من متطلبات النظام
echo 2. إنشاء المجلدات المطلوبة
echo 3. إعداد قاعدة البيانات الأولية
echo 4. إنشاء ملفات الإعداد
echo.

set /p confirm="هل تريد المتابعة؟ (Y/N): "
if /i "%confirm%" NEQ "Y" (
    echo تم إلغاء الإعداد
    pause
    exit /b 0
)

echo.
echo بدء الإعداد...
echo.

echo 1. إنشاء المجلدات المطلوبة...
if not exist "Backups" mkdir "Backups"
if not exist "Backups\Auto" mkdir "Backups\Auto"
if not exist "Reports" mkdir "Reports"
if not exist "Logs" mkdir "Logs"
if not exist "bin" mkdir "bin"
if not exist "bin\Debug" mkdir "bin\Debug"
echo ✓ تم إنشاء المجلدات

echo.
echo 2. نسخ ملفات الإعداد...
if not exist "system_config.json" (
    echo تم إنشاء ملف الإعداد الافتراضي
) else (
    echo ملف الإعداد موجود مسبقاً
)

echo.
echo 3. التحقق من ملفات النظام...
set missing_files=0

if not exist "Program.cs" (
    echo ✗ ملف Program.cs مفقود
    set /a missing_files+=1
) else (
    echo ✓ Program.cs موجود
)

if not exist "App.config" (
    echo ✗ ملف App.config مفقود
    set /a missing_files+=1
) else (
    echo ✓ App.config موجود
)

if not exist "AccountingSystem.csproj" (
    echo ✗ ملف المشروع مفقود
    set /a missing_files+=1
) else (
    echo ✓ ملف المشروع موجود
)

if %missing_files% GTR 0 (
    echo.
    echo تحذير: يوجد %missing_files% ملف مفقود
    echo قد لا يعمل النظام بشكل صحيح
    echo.
)

echo.
echo 4. إنشاء ملفات مساعدة...

echo @echo off > run_system.bat
echo echo تشغيل نظام المحاسبة الذكي... >> run_system.bat
echo if exist "bin\Debug\AccountingSystem.exe" ( >> run_system.bat
echo     start "" "bin\Debug\AccountingSystem.exe" >> run_system.bat
echo ) else if exist "AccountingSystem.exe" ( >> run_system.bat
echo     start "" "AccountingSystem.exe" >> run_system.bat
echo ) else ( >> run_system.bat
echo     echo لم يتم العثور على ملف التطبيق >> run_system.bat
echo     echo يرجى بناء المشروع أولاً >> run_system.bat
echo     pause >> run_system.bat
echo ) >> run_system.bat

echo تم إنشاء ملف run_system.bat

echo.
echo 5. إنشاء ملف معلومات النظام...

echo نظام المحاسبة الذكي > system_info.txt
echo ==================== >> system_info.txt
echo الإصدار: 1.0.0 >> system_info.txt
echo تاريخ الإعداد: %date% %time% >> system_info.txt
echo المجلد: %cd% >> system_info.txt
echo. >> system_info.txt
echo معلومات تسجيل الدخول الافتراضية: >> system_info.txt
echo اسم المستخدم: admin >> system_info.txt
echo كلمة المرور: admin123 >> system_info.txt
echo. >> system_info.txt
echo الملفات المهمة: >> system_info.txt
echo - دليل_المستخدم.md: دليل الاستخدام >> system_info.txt
echo - README.md: معلومات تقنية >> system_info.txt
echo - start_system.bat: تشغيل النظام >> system_info.txt
echo - build_and_run.bat: بناء وتشغيل النظام >> system_info.txt

echo تم إنشاء ملف system_info.txt

echo.
echo ========================================
echo تم إكمال الإعداد السريع بنجاح!
echo ========================================
echo.

if %missing_files% EQU 0 (
    echo ✓ جميع الملفات موجودة
    echo ✓ تم إنشاء المجلدات المطلوبة
    echo ✓ تم إعداد ملفات التشغيل
    echo.
    echo الخطوات التالية:
    echo 1. تشغيل build_and_run.bat لبناء النظام
    echo 2. أو تشغيل start_system.bat إذا كان النظام مبني مسبقاً
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
) else (
    echo تحذير: يوجد ملفات مفقودة
    echo يرجى التأكد من وجود جميع ملفات المشروع
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
