# البدء السريع - نظام المحاسبة الذكي

## خيارات التشغيل

يمكنك تشغيل النظام بطرق مختلفة حسب احتياجاتك:

### 1. الإصدار المبسط (للاختبار السريع)

**الأسرع والأسهل - لا يتطلب مكتبات إضافية**

```bash
# تشغيل ملف البناء المبسط
build_simple.bat
```

**المميزات:**
- ✅ تشغيل فوري بدون تثبيت مكتبات
- ✅ واجهة تسجيل دخول كاملة
- ✅ القوائم الأساسية
- ✅ تصميم عربي متكامل
- ✅ إدارة العملاء والمنتجات (جديد!)
- ❌ بدون قاعدة بيانات حقيقية
- ❌ بدون ميزات الذكاء الاصطناعي

### 2. الإصدار الكامل (للاستخدام الفعلي)

**يتطلب تثبيت مكتبات إضافية**

```bash
# الإعداد السريع
quick_setup.bat

# تثبيت المتطلبات
install_requirements.bat

# البناء والتشغيل
build_and_run.bat
```

**المميزات:**
- ✅ جميع ميزات النظام
- ✅ قاعدة بيانات SQLite
- ✅ ميزات الذكاء الاصطناعي
- ✅ النسخ الاحتياطي التلقائي
- ✅ التقارير المالية
- ✅ إدارة العملاء والمنتجات الكاملة
- ✅ إدارة الحسابات والقيود اليومية

## معلومات تسجيل الدخول

**لجميع الإصدارات:**
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

⚠️ **مهم:** غيّر كلمة المرور بعد تسجيل الدخول الأول

## استكشاف الأخطاء

### مشكلة: "لم يتم العثور على مترجم C#"

**الحل:**
1. تشغيل **Developer Command Prompt for Visual Studio**
2. أو إضافة مسار مترجم C# إلى PATH:
   ```
   C:\Windows\Microsoft.NET\Framework64\v4.0.30319\
   ```

### مشكلة: "ConfigurationManager does not exist"

**الحل:**
- استخدم الإصدار المبسط أولاً: `build_simple.bat`
- أو تأكد من تثبيت .NET Framework SDK كاملاً

### مشكلة: "ملفات مكتبات مفقودة"

**الحل:**
1. تحميل المكتبات المطلوبة:
   - System.Data.SQLite.dll
   - Dapper.dll
   - Newtonsoft.Json.dll
2. وضعها في مجلد المشروع
3. تشغيل `build_and_run.bat`

## الملفات المهمة

| الملف | الوصف |
|-------|--------|
| `build_simple.bat` | بناء الإصدار المبسط |
| `build_and_run.bat` | بناء الإصدار الكامل |
| `quick_setup.bat` | إعداد سريع للنظام |
| `start_system.bat` | تشغيل النظام |
| `test_system.bat` | اختبار النظام |
| `دليل_المستخدم.md` | دليل الاستخدام الكامل |
| `README.md` | معلومات تقنية مفصلة |

## خطوات البدء الموصى بها

### للمطورين والمختبرين:
1. `build_simple.bat` - للاختبار السريع
2. `quick_setup.bat` - للإعداد
3. `build_and_run.bat` - للنسخة الكاملة

### للمستخدمين النهائيين:
1. `quick_setup.bat` - إعداد أولي
2. `install_requirements.bat` - تثبيت المتطلبات
3. `start_system.bat` - تشغيل النظام

## الدعم

### الحصول على المساعدة:
1. راجع `دليل_المستخدم.md` للاستخدام
2. راجع `README.md` للمعلومات التقنية
3. تشغيل `test_system.bat` لفحص النظام

### الإبلاغ عن المشاكل:
- وصف مفصل للمشكلة
- خطوات إعادة إنتاج المشكلة
- رسائل الخطأ (إن وجدت)
- نوع الإصدار المستخدم (مبسط/كامل)

---

**نصيحة:** ابدأ بالإصدار المبسط للتأكد من عمل النظام، ثم انتقل للإصدار الكامل للحصول على جميع الميزات.
