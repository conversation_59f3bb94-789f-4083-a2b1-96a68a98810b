@echo off
chcp 65001 > nul
echo ========================================
echo بناء الإصدار المبسط من نظام المحاسبة
echo ========================================
echo.

echo هذا الإصدار المبسط يعمل بدون مكتبات خارجية
echo ويستخدم فقط مكتبات .NET Framework الأساسية
echo.

echo التحقق من وجود مترجم C#...
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo لم يتم العثور على مترجم C#
    echo.
    echo الحلول المقترحة:
    echo 1. تشغيل Developer Command Prompt for Visual Studio
    echo 2. تثبيت .NET Framework SDK
    echo 3. إضافة مسار مترجم C# إلى متغير PATH
    echo.
    echo مسارات مترجم C# الشائعة:
    echo C:\Windows\Microsoft.NET\Framework64\v4.0.30319\
    echo C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\Roslyn\
    echo.
    pause
    exit /b 1
)

echo ✓ تم العثور على مترجم C#

echo.
echo التحقق من وجود الملفات...

if not exist "SimpleProgram.cs" (
    echo ✗ SimpleProgram.cs مفقود
    echo يرجى التأكد من وجود الملف في نفس المجلد
    pause
    exit /b 1
)

echo ✓ SimpleProgram.cs موجود

echo.
echo بناء الإصدار المبسط...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /out:SimpleAccountingSystem.exe ^
    SimpleProgram.cs ^
    Utils\SecurityHelper.cs ^
    Data\SimpleDatabaseHelper.cs ^
    Services\SimpleAuthenticationService.cs ^
    Models\User.cs ^
    Models\Account.cs ^
    Models\Customer.cs ^
    Models\Product.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء الإصدار المبسط بنجاح!
    echo.
    echo تم إنشاء الملف: SimpleAccountingSystem.exe
    echo.
    echo ========================================
    echo تشغيل الإصدار المبسط
    echo ========================================
    echo.

    set /p run="هل تريد تشغيل النظام الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام...
        start "" "SimpleAccountingSystem.exe"
        echo.
        echo تم تشغيل النظام بنجاح!
        echo.
        echo معلومات تسجيل الدخول:
        echo اسم المستخدم: admin
        echo كلمة المرور: admin123
    )

    echo.
    echo ملاحظات مهمة:
    echo • هذا إصدار مبسط للاختبار فقط
    echo • لا يحتوي على قاعدة بيانات حقيقية
    echo • لا يحتوي على ميزات الذكاء الاصطناعي
    echo • للحصول على النسخة الكاملة، استخدم build_and_run.bat

) else (
    echo.
    echo ✗ فشل في بناء الإصدار المبسط
    echo.
    echo الأخطاء المحتملة:
    echo 1. أخطاء في الكود
    echo 2. مراجع مفقودة
    echo 3. مشاكل في مترجم C#
    echo.
    echo يرجى مراجعة رسائل الخطأ أعلاه
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
