using System;
using System.IO;
using System.Text.Json;

namespace IntegratedInvoiceSystem.Services
{
    /// <summary>
    /// خدمة إدارة الإعدادات
    /// </summary>
    public static class SettingsService
    {
        private static readonly string SettingsFilePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "IntegratedInvoiceSystem",
            "settings.json"
        );

        private static AppSettings _settings;

        static SettingsService()
        {
            LoadSettings();
        }

        public static AppSettings Settings => _settings ?? new AppSettings();

        public static void LoadSettings()
        {
            try
            {
                if (File.Exists(SettingsFilePath))
                {
                    var json = File.ReadAllText(SettingsFilePath);
                    _settings = JsonSerializer.Deserialize<AppSettings>(json) ?? new AppSettings();
                }
                else
                {
                    _settings = new AppSettings();
                }
            }
            catch (Exception)
            {
                _settings = new AppSettings();
            }
        }

        public static void SaveSettings()
        {
            try
            {
                var directory = Path.GetDirectoryName(SettingsFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonSerializer.Serialize(_settings, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                File.WriteAllText(SettingsFilePath, json);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// كلاس إعدادات التطبيق
    /// </summary>
    public class AppSettings
    {
        // إعدادات الشركة
        public string CompanyName { get; set; } = "شركة المثال للتجارة";
        public string CompanyAddress { get; set; } = "الرياض، المملكة العربية السعودية";
        public string CompanyPhone { get; set; } = "+966 11 123 4567";
        public string CompanyEmail { get; set; } = "<EMAIL>";
        public string TaxNumber { get; set; } = "*********";
        public string CommercialRegister { get; set; } = "1010123456";

        // إعدادات عامة
        public string Currency { get; set; } = "ريال سعودي";
        public string Language { get; set; } = "العربية";
        public string DateFormat { get; set; } = "dd/MM/yyyy";
        public bool AutoSave { get; set; } = true;
        public int AutoSaveInterval { get; set; } = 5;

        // إعدادات الضرائب
        public double VATRate { get; set; } = 15.0;
        public bool IncludeVAT { get; set; } = true;

        // إعدادات الطباعة
        public string DefaultPrinter { get; set; } = "";
        public string PaperSize { get; set; } = "A4";
        public bool PrintLogo { get; set; } = true;
        public bool PrintFooter { get; set; } = true;

        // إعدادات النسخ الاحتياطي
        public string BackupPath { get; set; } = @"C:\Backup\Accounting";
        public bool AutoBackup { get; set; } = false;
        public int BackupInterval { get; set; } = 7;
        public string BackupTime { get; set; } = "02:00";

        // إعدادات التنبيهات
        public bool OverdueNotifications { get; set; } = true;
        public bool LowStockNotifications { get; set; } = true;
        public bool PaymentReminders { get; set; } = true;
        public int ReminderDays { get; set; } = 3;
    }
}
