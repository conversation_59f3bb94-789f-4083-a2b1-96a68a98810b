using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Models;
using IntegratedInvoiceSystem.Services;

namespace IntegratedInvoiceSystem.Forms
{
    /// <summary>
    /// نموذج إدارة الفواتير المتكامل
    /// </summary>
    public partial class InvoicesManagementForm : Form
    {
        #region المتغيرات
        private List<Invoice> invoices;
        private List<Invoice> filteredInvoices;
        private Invoice selectedInvoice;
        #endregion

        #region البناء والتهيئة
        public InvoicesManagementForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // تهيئة الإعدادات الأساسية
            this.WindowState = FormWindowState.Maximized;
            this.KeyPreview = true;

            // تهيئة التواريخ
            dtpDateFrom.Value = DateTime.Now.AddMonths(-1);
            dtpDateTo.Value = DateTime.Now;
            cmbStatus.SelectedIndex = 0;

            // تهيئة DataGridView
            SetupDataGridView();
        }

        private void SetupDataGridView()
        {
            dataGridView.AutoGenerateColumns = false;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.ReadOnly = true;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;

            // تنسيق الألوان
            dataGridView.BackgroundColor = Color.White;
            dataGridView.GridColor = Color.FromArgb(189, 195, 199);
            dataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dataGridView.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dataGridView.RowHeadersVisible = false;
            dataGridView.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            AddDataGridViewColumns();
        }

        private void AddDataGridViewColumns()
        {
            dataGridView.Columns.Clear();

            // رقم الفاتورة
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "InvoiceNumber",
                HeaderText = "رقم الفاتورة",
                DataPropertyName = "InvoiceNumber",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // تاريخ الفاتورة
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "InvoiceDate",
                HeaderText = "تاريخ الفاتورة",
                DataPropertyName = "InvoiceDate",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "dd/MM/yyyy",
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            });

            // العميل
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerName",
                HeaderText = "العميل",
                Width = 200
            });

            // المبلغ الإجمالي
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalAmount",
                HeaderText = "المبلغ الإجمالي",
                DataPropertyName = "TotalAmount",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "C2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // المبلغ المدفوع
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PaidAmount",
                HeaderText = "المبلغ المدفوع",
                DataPropertyName = "PaidAmount",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "C2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // المبلغ المتبقي
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RemainingAmount",
                HeaderText = "المبلغ المتبقي",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "C2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // الحالة
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                DataPropertyName = "Status",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // تاريخ الاستحقاق
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DueDate",
                HeaderText = "تاريخ الاستحقاق",
                DataPropertyName = "DueDate",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "dd/MM/yyyy",
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            });

            // الملاحظات
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Notes",
                HeaderText = "الملاحظات",
                DataPropertyName = "Notes",
                Width = 200
            });
        }
        #endregion

        #region تحميل البيانات
        private void InvoicesManagementForm_Load(object sender, EventArgs e)
        {
            LoadInvoices();
        }

        private void LoadInvoices()
        {
            try
            {
                lblStatusText.Text = "جاري تحميل الفواتير...";

                // تحميل الفواتير من الخدمة
                invoices = DataService.GetInvoices();

                // تطبيق الفلترة
                ApplyFilters();

                lblStatusText.Text = "تم تحميل البيانات بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفواتير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatusText.Text = "خطأ في تحميل البيانات";
            }
        }

        private void ApplyFilters()
        {
            try
            {
                filteredInvoices = invoices.Where(invoice =>
                {
                    // فلتر النص
                    bool textMatch = string.IsNullOrEmpty(txtSearch.Text) ||
                        invoice.InvoiceNumber.Contains(txtSearch.Text) ||
                        GetCustomerName(invoice.CustomerId).Contains(txtSearch.Text);

                    // فلتر الحالة
                    bool statusMatch = cmbStatus.SelectedIndex == 0 ||
                        invoice.Status == cmbStatus.Text;

                    // فلتر التاريخ
                    bool dateMatch = invoice.InvoiceDate.Date >= dtpDateFrom.Value.Date &&
                        invoice.InvoiceDate.Date <= dtpDateTo.Value.Date;

                    return textMatch && statusMatch && dateMatch;
                }).ToList();

                // ربط البيانات
                BindData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BindData()
        {
            try
            {
                var bindingList = filteredInvoices.Select(invoice => new
                {
                    Id = invoice.Id,
                    InvoiceNumber = invoice.InvoiceNumber,
                    InvoiceDate = invoice.InvoiceDate,
                    CustomerName = GetCustomerName(invoice.CustomerId),
                    TotalAmount = invoice.TotalAmount,
                    PaidAmount = invoice.PaidAmount,
                    RemainingAmount = invoice.TotalAmount - invoice.PaidAmount,
                    Status = invoice.Status,
                    DueDate = invoice.DueDate,
                    Notes = invoice.Notes
                }).ToList();

                dataGridView.DataSource = bindingList;

                // تحديث عداد السجلات
                lblRecordCount.Text = $"عدد السجلات: {filteredInvoices.Count}";

                // تلوين الصفوف حسب الحالة
                ColorizeRows();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ربط البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ColorizeRows()
        {
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                if (row.Cells["Status"].Value != null)
                {
                    string status = row.Cells["Status"].Value.ToString();
                    switch (status)
                    {
                        case "مسودة":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
                            break;
                        case "مؤكدة":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(220, 248, 255);
                            break;
                        case "مدفوعة":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(220, 255, 220);
                            break;
                        case "ملغاة":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 220, 220);
                            break;
                        case "متأخرة":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 200, 200);
                            break;
                    }
                }
            }
        }

        private string GetCustomerName(int customerId)
        {
            var customer = DataService.GetCustomers().FirstOrDefault(c => c.Id == customerId);
            return customer?.CustomerName ?? "غير محدد";
        }
        #endregion

        #region معالجات الأحداث - الأزرار
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نموذج إضافة فاتورة جديدة", "إضافة فاتورة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // var addForm = new AddEditInvoiceForm();
                // if (addForm.ShowDialog() == DialogResult.OK)
                // {
                //     LoadInvoices();
                // }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج إضافة الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            EditSelectedInvoice();
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DeleteSelectedInvoice();
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            PrintSelectedInvoice();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadInvoices();
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        #endregion

        #region معالجات الأحداث - البحث والفلترة
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CmbStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void DtpDateFrom_ValueChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void DtpDateTo_ValueChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void BtnClearSearch_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbStatus.SelectedIndex = 0;
            dtpDateFrom.Value = DateTime.Now.AddMonths(-1);
            dtpDateTo.Value = DateTime.Now;
            ApplyFilters();
        }
        #endregion

        #region معالجات الأحداث - DataGridView
        private void DataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                var selectedRow = dataGridView.SelectedRows[0];
                if (selectedRow.Cells["Id"].Value != null)
                {
                    int invoiceId = Convert.ToInt32(selectedRow.Cells["Id"].Value);
                    selectedInvoice = invoices.FirstOrDefault(i => i.Id == invoiceId);

                    // تحديث حالة الأزرار
                    UpdateButtonStates();
                }
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedInvoice();
            }
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = selectedInvoice != null;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection && selectedInvoice.Status == "مسودة";
            btnPrint.Enabled = hasSelection;

            // تحديث القائمة المنسدلة
            menuEdit.Enabled = hasSelection;
            menuDelete.Enabled = hasSelection && selectedInvoice.Status == "مسودة";
            menuPrint.Enabled = hasSelection;
            menuDuplicate.Enabled = hasSelection;
            menuMarkPaid.Enabled = hasSelection && selectedInvoice.Status != "مدفوعة" && selectedInvoice.Status != "ملغاة";
        }
        #endregion

        #region معالجات الأحداث - القائمة المنسدلة
        private void MenuEdit_Click(object sender, EventArgs e)
        {
            EditSelectedInvoice();
        }

        private void MenuDelete_Click(object sender, EventArgs e)
        {
            DeleteSelectedInvoice();
        }

        private void MenuPrint_Click(object sender, EventArgs e)
        {
            PrintSelectedInvoice();
        }

        private void MenuDuplicate_Click(object sender, EventArgs e)
        {
            DuplicateSelectedInvoice();
        }

        private void MenuMarkPaid_Click(object sender, EventArgs e)
        {
            MarkInvoiceAsPaid();
        }
        #endregion

        #region العمليات الأساسية
        private void EditSelectedInvoice()
        {
            if (selectedInvoice == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                MessageBox.Show($"سيتم فتح نموذج تعديل الفاتورة {selectedInvoice.InvoiceNumber}", "تعديل فاتورة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // var editForm = new AddEditInvoiceForm(selectedInvoice);
                // if (editForm.ShowDialog() == DialogResult.OK)
                // {
                //     LoadInvoices();
                // }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج تعديل الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteSelectedInvoice()
        {
            if (selectedInvoice == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (selectedInvoice.Status != "مسودة")
            {
                MessageBox.Show("لا يمكن حذف الفاتورة إلا إذا كانت في حالة مسودة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show($"هل أنت متأكد من حذف الفاتورة رقم {selectedInvoice.InvoiceNumber}؟",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    DataService.DeleteInvoice(selectedInvoice.Id);
                    LoadInvoices();
                    MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح الحذف",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void PrintSelectedInvoice()
        {
            if (selectedInvoice == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للطباعة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // هنا يمكن إضافة كود الطباعة الفعلي
                MessageBox.Show($"سيتم طباعة الفاتورة رقم {selectedInvoice.InvoiceNumber}", "طباعة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DuplicateSelectedInvoice()
        {
            if (selectedInvoice == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة للنسخ", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                MessageBox.Show($"سيتم نسخ الفاتورة {selectedInvoice.InvoiceNumber}", "نسخ فاتورة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                // var duplicateForm = new AddEditInvoiceForm(selectedInvoice, true);
                // if (duplicateForm.ShowDialog() == DialogResult.OK)
                // {
                //     LoadInvoices();
                // }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نسخ الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MarkInvoiceAsPaid()
        {
            if (selectedInvoice == null)
            {
                MessageBox.Show("يرجى اختيار فاتورة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (selectedInvoice.Status == "مدفوعة")
            {
                MessageBox.Show("الفاتورة مدفوعة بالفعل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var result = MessageBox.Show($"هل تريد تسجيل الفاتورة رقم {selectedInvoice.InvoiceNumber} كمدفوعة؟",
                "تأكيد الدفع", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    selectedInvoice.Status = "مدفوعة";
                    selectedInvoice.PaidAmount = selectedInvoice.TotalAmount;
                    DataService.UpdateInvoice(selectedInvoice);
                    LoadInvoices();
                    MessageBox.Show("تم تسجيل الفاتورة كمدفوعة بنجاح", "نجح التحديث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث حالة الفاتورة: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        #endregion

        #region معالجات إضافية
        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            switch (keyData)
            {
                case Keys.F1:
                    BtnAdd_Click(null, null);
                    return true;
                case Keys.F2:
                    BtnEdit_Click(null, null);
                    return true;
                case Keys.Delete:
                    BtnDelete_Click(null, null);
                    return true;
                case Keys.F5:
                    BtnRefresh_Click(null, null);
                    return true;
                case Keys.Escape:
                    BtnClose_Click(null, null);
                    return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }
        #endregion
    }
}
