@echo off
chcp 65001 > nul
echo ========================================
echo فحص نهائي لنظام المحاسبة الذكي
echo ========================================
echo.

echo هذا الفحص سيتأكد من جاهزية النظام للتشغيل
echo.

set total_files=0
set found_files=0
set errors=0

echo ========================================
echo 1. فحص الملفات الأساسية
echo ========================================

set core_files=Program.cs SimpleProgram.cs App.config AccountingSystem.csproj packages.config

for %%f in (%core_files%) do (
    set /a total_files+=1
    if exist "%%f" (
        echo ✓ %%f
        set /a found_files+=1
    ) else (
        echo ✗ %%f مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 2. فحص ملفات التشغيل
echo ========================================

set run_files=build_simple.bat build_and_run.bat quick_setup.bat start_system.bat

for %%f in (%run_files%) do (
    set /a total_files+=1
    if exist "%%f" (
        echo ✓ %%f
        set /a found_files+=1
    ) else (
        echo ✗ %%f مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 3. فحص ملفات التوثيق
echo ========================================

set doc_files=README.md دليل_المستخدم.md البدء_السريع.md

for %%f in (%doc_files%) do (
    set /a total_files+=1
    if exist "%%f" (
        echo ✓ %%f
        set /a found_files+=1
    ) else (
        echo ⚠ %%f مفقود (اختياري)
    )
)

echo.
echo ========================================
echo 4. فحص مجلدات المشروع
echo ========================================

set folders=Models Data Services Utils Forms Properties

for %%d in (%folders%) do (
    if exist "%%d" (
        echo ✓ مجلد %%d موجود
        
        rem عد الملفات في كل مجلد
        for /f %%i in ('dir /b "%%d\*.cs" 2^>nul ^| find /c /v ""') do (
            if %%i GTR 0 (
                echo   └─ يحتوي على %%i ملف
            ) else (
                echo   └─ فارغ
            )
        )
    ) else (
        echo ✗ مجلد %%d مفقود
        set /a errors+=1
    )
)

echo.
echo ========================================
echo 5. اختبار بناء الإصدار المبسط
echo ========================================

if exist "SimpleProgram.cs" (
    echo محاولة بناء الإصدار المبسط...
    
    where csc >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ مترجم C# متوفر
        
        csc /target:winexe /reference:System.dll /reference:System.Core.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /out:TestBuild.exe SimpleProgram.cs >nul 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo ✓ نجح بناء الإصدار المبسط
            if exist "TestBuild.exe" del "TestBuild.exe" >nul 2>&1
        ) else (
            echo ✗ فشل بناء الإصدار المبسط
            set /a errors+=1
        )
    ) else (
        echo ⚠ مترجم C# غير متوفر (استخدم Developer Command Prompt)
    )
) else (
    echo ✗ SimpleProgram.cs مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo 6. فحص إعدادات النظام
echo ========================================

if exist "system_config.json" (
    echo ✓ ملف الإعدادات موجود
) else (
    echo ⚠ ملف الإعدادات مفقود (سيتم إنشاؤه تلقائياً)
)

if exist "App.config" (
    echo ✓ ملف إعدادات التطبيق موجود
) else (
    echo ✗ ملف إعدادات التطبيق مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo النتائج النهائية
echo ========================================

echo إجمالي الملفات المفحوصة: %total_files%
echo الملفات الموجودة: %found_files%
echo الأخطاء: %errors%

if %errors% EQU 0 (
    echo.
    echo 🎉 ممتاز! النظام جاهز للتشغيل
    echo.
    echo خيارات التشغيل:
    echo.
    echo 1. للاختبار السريع:
    echo    build_simple.bat
    echo.
    echo 2. للنسخة الكاملة:
    echo    quick_setup.bat
    echo    install_requirements.bat  
    echo    build_and_run.bat
    echo.
    echo 3. إذا كان النظام مبني مسبقاً:
    echo    start_system.bat
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    
) else (
    echo.
    echo ⚠ يوجد %errors% خطأ يجب إصلاحه
    echo.
    echo الإجراءات المقترحة:
    echo 1. تأكد من وجود جميع ملفات المشروع
    echo 2. راجع ملف README.md للمتطلبات
    echo 3. استخدم quick_setup.bat للإعداد التلقائي
    echo.
    echo يمكنك تجربة الإصدار المبسط إذا كان متوفراً:
    echo build_simple.bat
)

echo.
echo ========================================
echo معلومات إضافية
echo ========================================
echo التاريخ: %date%
echo الوقت: %time%
echo المجلد: %cd%
echo نظام التشغيل: %OS%

echo.
echo للحصول على مساعدة مفصلة، راجع:
echo - البدء_السريع.md
echo - دليل_المستخدم.md  
echo - README.md

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
