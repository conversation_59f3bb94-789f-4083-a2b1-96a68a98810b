using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AccountingSystem.Models;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج إدارة المنتجات
    /// </summary>
    public partial class ProductsForm : Form
    {
        private DataGridView dgvProducts;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnClose;
        private TextBox txtSearch;
        private Label lblSearch;
        private ComboBox cmbCategory;
        private Label lblCategory;
        private List<Product> products;

        public ProductsForm()
        {
            InitializeComponent();
            LoadProducts();
        }

        private void InitializeComponent()
        {
            this.dgvProducts = new DataGridView();
            this.btnAdd = new Button();
            this.btnEdit = new Button();
            this.btnDelete = new Button();
            this.btnClose = new Button();
            this.txtSearch = new TextBox();
            this.lblSearch = new Label();
            this.cmbCategory = new ComboBox();
            this.lblCategory = new Label();

            this.SuspendLayout();

            // Form
            this.Text = "إدارة المنتجات";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Search Label
            this.lblSearch.Text = "البحث:";
            this.lblSearch.Location = new Point(900, 20);
            this.lblSearch.Size = new Size(60, 23);
            this.lblSearch.TextAlign = ContentAlignment.MiddleRight;

            // Search TextBox
            this.txtSearch.Location = new Point(720, 20);
            this.txtSearch.Size = new Size(160, 23);
            this.txtSearch.TextChanged += TxtSearch_TextChanged;

            // Category Label
            this.lblCategory.Text = "الفئة:";
            this.lblCategory.Location = new Point(660, 20);
            this.lblCategory.Size = new Size(50, 23);
            this.lblCategory.TextAlign = ContentAlignment.MiddleRight;

            // Category ComboBox
            this.cmbCategory.Location = new Point(500, 20);
            this.cmbCategory.Size = new Size(140, 23);
            this.cmbCategory.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbCategory.Items.AddRange(new string[] { "الكل", "إلكترونيات", "ملابس", "أغذية", "أدوات منزلية", "كتب", "أخرى" });
            this.cmbCategory.SelectedIndex = 0;
            this.cmbCategory.SelectedIndexChanged += CmbCategory_SelectedIndexChanged;

            // Add Button
            this.btnAdd.Text = "إضافة منتج";
            this.btnAdd.Location = new Point(380, 18);
            this.btnAdd.Size = new Size(100, 27);
            this.btnAdd.BackColor = Color.FromArgb(76, 175, 80);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.FlatStyle = FlatStyle.Flat;
            this.btnAdd.Click += BtnAdd_Click;

            // Edit Button
            this.btnEdit.Text = "تعديل";
            this.btnEdit.Location = new Point(260, 18);
            this.btnEdit.Size = new Size(100, 27);
            this.btnEdit.BackColor = Color.FromArgb(255, 152, 0);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.Click += BtnEdit_Click;

            // Delete Button
            this.btnDelete.Text = "حذف";
            this.btnDelete.Location = new Point(140, 18);
            this.btnDelete.Size = new Size(100, 27);
            this.btnDelete.BackColor = Color.FromArgb(244, 67, 54);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.Click += BtnDelete_Click;

            // Close Button
            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(20, 18);
            this.btnClose.Size = new Size(100, 27);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Click += BtnClose_Click;

            // DataGridView
            this.dgvProducts.Location = new Point(20, 60);
            this.dgvProducts.Size = new Size(960, 480);
            this.dgvProducts.AllowUserToAddRows = false;
            this.dgvProducts.AllowUserToDeleteRows = false;
            this.dgvProducts.ReadOnly = true;
            this.dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvProducts.RightToLeft = RightToLeft.Yes;
            this.dgvProducts.Font = new Font("Tahoma", 10F);
            this.dgvProducts.DoubleClick += DgvProducts_DoubleClick;

            SetupDataGridView();

            // Add controls to form
            this.Controls.Add(this.lblSearch);
            this.Controls.Add(this.txtSearch);
            this.Controls.Add(this.lblCategory);
            this.Controls.Add(this.cmbCategory);
            this.Controls.Add(this.btnAdd);
            this.Controls.Add(this.btnEdit);
            this.Controls.Add(this.btnDelete);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.dgvProducts);

            this.ResumeLayout(false);
        }

        private void SetupDataGridView()
        {
            dgvProducts.Columns.Clear();

            dgvProducts.Columns.Add("Id", "المعرف");
            dgvProducts.Columns["Id"].Width = 60;
            dgvProducts.Columns["Id"].Visible = false;

            dgvProducts.Columns.Add("ProductCode", "كود المنتج");
            dgvProducts.Columns["ProductCode"].Width = 100;

            dgvProducts.Columns.Add("ProductName", "اسم المنتج");
            dgvProducts.Columns["ProductName"].Width = 200;

            dgvProducts.Columns.Add("Category", "الفئة");
            dgvProducts.Columns["Category"].Width = 120;

            dgvProducts.Columns.Add("SalePrice", "سعر البيع");
            dgvProducts.Columns["SalePrice"].Width = 100;
            dgvProducts.Columns["SalePrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvProducts.Columns["SalePrice"].DefaultCellStyle.Format = "N2";

            dgvProducts.Columns.Add("PurchasePrice", "سعر الشراء");
            dgvProducts.Columns["PurchasePrice"].Width = 100;
            dgvProducts.Columns["PurchasePrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvProducts.Columns["PurchasePrice"].DefaultCellStyle.Format = "N2";

            dgvProducts.Columns.Add("CurrentStock", "المخزون الحالي");
            dgvProducts.Columns["CurrentStock"].Width = 120;
            dgvProducts.Columns["CurrentStock"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            dgvProducts.Columns.Add("Unit", "الوحدة");
            dgvProducts.Columns["Unit"].Width = 80;

            dgvProducts.Columns.Add("Description", "الوصف");
            dgvProducts.Columns["Description"].Width = 150;
        }

        private void LoadProducts()
        {
            try
            {
                // إنشاء بيانات تجريبية للمنتجات
                products = new List<Product>
                {
                    new Product { Id = 1, ProductCode = "P001", ProductName = "لابتوب ديل", Category = "إلكترونيات", SalePrice = 2500.00m, PurchasePrice = 2000.00m, CurrentStock = 10, Unit = "قطعة", Description = "لابتوب ديل انسبايرون 15", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                    new Product { Id = 2, ProductCode = "P002", ProductName = "قميص قطني", Category = "ملابس", SalePrice = 85.00m, PurchasePrice = 60.00m, CurrentStock = 50, Unit = "قطعة", Description = "قميص قطني عالي الجودة", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                    new Product { Id = 3, ProductCode = "P003", ProductName = "أرز بسمتي", Category = "أغذية", SalePrice = 25.00m, PurchasePrice = 18.00m, CurrentStock = 100, Unit = "كيس", Description = "أرز بسمتي هندي 5 كيلو", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                    new Product { Id = 4, ProductCode = "P004", ProductName = "مكنسة كهربائية", Category = "أدوات منزلية", SalePrice = 450.00m, PurchasePrice = 350.00m, CurrentStock = 15, Unit = "قطعة", Description = "مكنسة كهربائية قوة 2000 واط", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                    new Product { Id = 5, ProductCode = "P005", ProductName = "كتاب البرمجة", Category = "كتب", SalePrice = 120.00m, PurchasePrice = 80.00m, CurrentStock = 25, Unit = "قطعة", Description = "كتاب تعلم البرمجة للمبتدئين", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                    new Product { Id = 6, ProductCode = "P006", ProductName = "هاتف ذكي", Category = "إلكترونيات", SalePrice = 1200.00m, PurchasePrice = 900.00m, CurrentStock = 8, Unit = "قطعة", Description = "هاتف ذكي بشاشة 6.5 بوصة", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                    new Product { Id = 7, ProductCode = "P007", ProductName = "فستان صيفي", Category = "ملابس", SalePrice = 150.00m, PurchasePrice = 100.00m, CurrentStock = 30, Unit = "قطعة", Description = "فستان صيفي أنيق", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                    new Product { Id = 8, ProductCode = "P008", ProductName = "زيت زيتون", Category = "أغذية", SalePrice = 45.00m, PurchasePrice = 30.00m, CurrentStock = 60, Unit = "زجاجة", Description = "زيت زيتون بكر ممتاز 500 مل", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" }
                };

                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            dgvProducts.Rows.Clear();

            var filteredProducts = products.Where(p => p.IsActive);

            // تطبيق فلتر البحث
            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                string searchText = txtSearch.Text.ToLower();
                filteredProducts = filteredProducts.Where(p =>
                    p.ProductName.ToLower().Contains(searchText) ||
                    p.ProductCode.ToLower().Contains(searchText) ||
                    (p.Description != null && p.Description.ToLower().Contains(searchText))
                );
            }

            // تطبيق فلتر الفئة
            if (cmbCategory.SelectedIndex > 0)
            {
                string selectedCategory = cmbCategory.SelectedItem.ToString();
                filteredProducts = filteredProducts.Where(p => p.Category == selectedCategory);
            }

            foreach (var product in filteredProducts)
            {
                var rowIndex = dgvProducts.Rows.Add();
                var row = dgvProducts.Rows[rowIndex];

                row.Cells["Id"].Value = product.Id;
                row.Cells["ProductCode"].Value = product.ProductCode;
                row.Cells["ProductName"].Value = product.ProductName;
                row.Cells["Category"].Value = product.Category;
                row.Cells["SalePrice"].Value = product.SalePrice;
                row.Cells["PurchasePrice"].Value = product.PurchasePrice;
                row.Cells["CurrentStock"].Value = product.CurrentStock;
                row.Cells["Unit"].Value = product.Unit;
                row.Cells["Description"].Value = product.Description;

                // تلوين الصفوف حسب الكمية المتوفرة
                if (product.CurrentStock <= 5)
                {
                    row.DefaultCellStyle.BackColor = Color.LightCoral; // كمية قليلة
                }
                else if (product.CurrentStock <= 20)
                {
                    row.DefaultCellStyle.BackColor = Color.LightYellow; // كمية متوسطة
                }
                else
                {
                    row.DefaultCellStyle.BackColor = Color.LightGreen; // كمية جيدة
                }
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            RefreshDataGridView();
        }

        private void CmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            RefreshDataGridView();
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new AddEditProductForm();
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    var newProduct = addForm.Product;
                    newProduct.Id = products.Count > 0 ? products.Max(p => p.Id) + 1 : 1;
                    newProduct.ProductCode = $"P{newProduct.Id:000}";
                    products.Add(newProduct);
                    RefreshDataGridView();

                    MessageBox.Show("تم إضافة المنتج بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            EditSelectedProduct();
        }

        private void DgvProducts_DoubleClick(object sender, EventArgs e)
        {
            EditSelectedProduct();
        }

        private void EditSelectedProduct()
        {
            try
            {
                if (dgvProducts.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار منتج للتعديل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int productId = (int)dgvProducts.SelectedRows[0].Cells["Id"].Value;
                var product = products.FirstOrDefault(p => p.Id == productId);

                if (product != null)
                {
                    var editForm = new AddEditProductForm(product);
                    if (editForm.ShowDialog() == DialogResult.OK)
                    {
                        var updatedProduct = editForm.Product;
                        var index = products.FindIndex(p => p.Id == productId);
                        if (index >= 0)
                        {
                            products[index] = updatedProduct;
                            RefreshDataGridView();

                            MessageBox.Show("تم تعديل المنتج بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvProducts.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار منتج للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int productId = (int)dgvProducts.SelectedRows[0].Cells["Id"].Value;
                var product = products.FirstOrDefault(p => p.Id == productId);

                if (product != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف المنتج '{product.ProductName}'؟",
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        product.IsActive = false; // حذف منطقي
                        RefreshDataGridView();

                        MessageBox.Show("تم حذف المنتج بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
