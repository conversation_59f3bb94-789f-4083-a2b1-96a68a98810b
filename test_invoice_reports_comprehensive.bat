@echo off
chcp 65001 > nul
echo.
echo ========================================
echo اختبار نظام تقارير الفواتير الشامل
echo ========================================
echo.

echo 🔍 فحص الملفات المطلوبة...
echo.

set "missing_files="

if not exist "Forms\InvoicesReportForm.cs" (
    echo ❌ ملف InvoicesReportForm.cs مفقود
    set "missing_files=1"
) else (
    echo ✅ Forms\InvoicesReportForm.cs موجود
)

if not exist "Forms\InvoiceAnalyticsForm.cs" (
    echo ❌ ملف InvoiceAnalyticsForm.cs مفقود
    set "missing_files=1"
) else (
    echo ✅ Forms\InvoiceAnalyticsForm.cs موجود
)

if not exist "Services\InvoiceReportService.cs" (
    echo ❌ ملف InvoiceReportService.cs مفقود
    set "missing_files=1"
) else (
    echo ✅ Services\InvoiceReportService.cs موجود
)

if not exist "Models\ReportModels.cs" (
    echo ❌ ملف ReportModels.cs مفقود
    set "missing_files=1"
) else (
    echo ✅ Models\ReportModels.cs موجود
)

if not exist "Utils\ExportHelper.cs" (
    echo ❌ ملف ExportHelper.cs مفقود
    set "missing_files=1"
) else (
    echo ✅ Utils\ExportHelper.cs موجود
)

if not exist "Utils\PrintHelper.cs" (
    echo ❌ ملف PrintHelper.cs مفقود
    set "missing_files=1"
) else (
    echo ✅ Utils\PrintHelper.cs موجود
)

if defined missing_files (
    echo.
    echo ❌ بعض الملفات مفقودة. يرجى التأكد من وجود جميع الملفات المطلوبة.
    pause
    exit /b 1
)

echo.
echo ✅ جميع الملفات المطلوبة موجودة!
echo.

echo 🔧 بناء نظام التقارير الشامل...
echo.

:: البحث عن مترجم C#
set "csc_path="
for %%i in (csc.exe) do set "csc_path=%%~$PATH:i"

if not defined csc_path (
    echo 🔍 البحث عن مترجم C# في مواقع Visual Studio...
    
    for /d %%d in ("C:\Program Files*\Microsoft Visual Studio\*\*\MSBuild\*\Bin\Roslyn") do (
        if exist "%%d\csc.exe" (
            set "csc_path=%%d\csc.exe"
            goto found_csc
        )
    )
    
    for /d %%d in ("C:\Program Files*\dotnet\sdk\*") do (
        if exist "%%d\Roslyn\bincore\csc.exe" (
            set "csc_path=%%d\Roslyn\bincore\csc.exe"
            goto found_csc
        )
    )
    
    :found_csc
)

if not defined csc_path (
    echo ❌ لم يتم العثور على مترجم C#
    echo.
    echo الحلول المقترحة:
    echo 1. تثبيت Visual Studio أو Visual Studio Build Tools
    echo 2. تثبيت .NET Framework SDK
    echo 3. إضافة مسار المترجم إلى متغير PATH
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على مترجم C#: %csc_path%
echo.

echo 📦 تجميع نظام التقارير...

"%csc_path%" /target:exe ^
    /reference:System.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Data.dll ^
    /reference:System.Linq.dll ^
    /out:InvoiceReportsSystem.exe ^
    Forms\InvoicesReportForm.cs ^
    Forms\InvoiceAnalyticsForm.cs ^
    Services\InvoiceReportService.cs ^
    Models\ReportModels.cs ^
    Utils\ExportHelper.cs ^
    Utils\PrintHelper.cs ^
    InvoicesSystemComplete.cs

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تجميع النظام
    echo.
    echo الأخطاء المحتملة:
    echo • مراجع مفقودة
    echo • أخطاء في الكود
    echo • تضارب في أسماء الفئات
    echo.
    echo 🔧 محاولة البناء مع النظام المتكامل...
    
    "%csc_path%" /target:exe ^
        /reference:System.dll ^
        /reference:System.Windows.Forms.dll ^
        /reference:System.Drawing.dll ^
        /reference:System.Data.dll ^
        /reference:System.Linq.dll ^
        /out:InvoiceReportsIntegrated.exe ^
        IntegratedInvoiceSystem.cs ^
        Forms\InvoicesReportForm.cs ^
        Utils\ExportHelper.cs ^
        Utils\PrintHelper.cs
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في البناء المتكامل أيضاً
        pause
        exit /b 1
    ) else (
        echo ✅ تم البناء المتكامل بنجاح!
        set "exe_name=InvoiceReportsIntegrated.exe"
    )
) else (
    echo ✅ تم تجميع النظام بنجاح!
    set "exe_name=InvoiceReportsSystem.exe"
)

echo.
echo 🎉 نظام تقارير الفواتير الشامل جاهز!
echo.
echo ========================================
echo ميزات النظام الجديد:
echo ========================================
echo.
echo 📊 تقارير شاملة:
echo • التقرير الإجمالي مع مؤشرات الأداء
echo • التقرير المفصل لجميع الفواتير
echo • تحليل أعمار الفواتير المستحقة
echo • رسوم بيانية وتحليلات متقدمة
echo.
echo 🔍 فلترة متقدمة:
echo • فلترة حسب التاريخ والعميل والحالة
echo • بحث ذكي في البيانات
echo • تجميع وتصنيف البيانات
echo.
echo 📈 تحليلات متقدمة:
echo • معدلات التحصيل
echo • اتجاهات المبيعات الشهرية
echo • تحليل أداء العملاء
echo • مؤشرات الأداء الرئيسية
echo.
echo 📤 تصدير وطباعة:
echo • تصدير إلى Excel/CSV
echo • طباعة احترافية مع معاينة
echo • تقارير مخصصة
echo.
echo 🎨 واجهة محسنة:
echo • تصميم عصري وجذاب
echo • ألوان تفاعلية حسب الحالة
echo • تخطيط متجاوب
echo • دعم كامل للغة العربية
echo.
echo ========================================
echo كيفية الاستخدام:
echo ========================================
echo.
echo 1. شغل البرنامج: %exe_name%
echo 2. سجل دخول بـ admin/admin123
echo 3. من قائمة "التقارير" اختر "تقرير الفواتير"
echo 4. استخدم الفلاتر لتخصيص التقرير
echo 5. اضغط "إنشاء التقرير" لعرض البيانات
echo 6. استخدم أزرار التصدير والطباعة
echo 7. تصفح التبويبات المختلفة للتحليلات
echo.
echo ========================================
echo التحسينات المضافة:
echo ========================================
echo.
echo ✅ إصلاح شامل لتقارير الفواتير
echo ✅ واجهات تقارير متعددة التبويبات
echo ✅ مؤشرات أداء تفاعلية
echo ✅ تحليل أعمار الفواتير
echo ✅ رسوم بيانية وإحصائيات
echo ✅ تصدير متقدم للبيانات
echo ✅ طباعة احترافية
echo ✅ فلترة وبحث متقدم
echo ✅ تلوين تفاعلي للبيانات
echo ✅ دعم كامل للغة العربية
echo.

if exist "%exe_name%" (
    echo 🚀 هل تريد تشغيل النظام الآن؟ (Y/N)
    set /p "run_choice=الاختيار: "
    
    if /i "%run_choice%"=="Y" (
        echo.
        echo 🎉 تشغيل نظام تقارير الفواتير الشامل...
        start "" "%exe_name%"
    )
)

echo.
echo ✅ اكتمل الإعداد بنجاح!
echo.
pause
