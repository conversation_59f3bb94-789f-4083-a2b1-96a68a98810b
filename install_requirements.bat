@echo off
echo ========================================
echo تثبيت متطلبات نظام المحاسبة الذكي
echo ========================================
echo.

echo التحقق من وجود .NET Framework 4.8...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release 2>nul | find "528040" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ .NET Framework 4.8 مثبت
) else (
    echo ✗ .NET Framework 4.8 غير مثبت
    echo يرجى تحميل وتثبيت .NET Framework 4.8 من موقع Microsoft
    echo https://dotnet.microsoft.com/download/dotnet-framework/net48
    pause
    exit /b 1
)

echo.
echo استعادة حزم NuGet...
nuget restore AccountingSystem.sln

if %ERRORLEVEL% NEQ 0 (
    echo فشل في استعادة حزم NuGet
    echo تأكد من تثبيت NuGet CLI
    pause
    exit /b 1
)

echo.
echo بناء المشروع...
msbuild AccountingSystem.sln /p:Configuration=Debug

if %ERRORLEVEL% NEQ 0 (
    echo فشل في بناء المشروع
    pause
    exit /b 1
)

echo.
echo ========================================
echo تم تثبيت المتطلبات بنجاح!
echo ========================================
echo.
echo يمكنك الآن تشغيل التطبيق باستخدام:
echo run_tests.bat
echo.
echo أو تشغيل الملف التنفيذي مباشرة:
echo bin\Debug\AccountingSystem.exe
echo.
pause
