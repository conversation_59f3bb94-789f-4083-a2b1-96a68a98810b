using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Models;
using IntegratedInvoiceSystem.Services;
using AccountingSystem.Utils;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج تقارير الفواتير الشامل المحدث
    /// </summary>
    public partial class ComprehensiveInvoicesReportForm : Form
    {
        #region Fields
        private TabControl tabControl;
        private TabPage tabSummary, tabDetailed, tabAnalytics, tabAging;

        // Summary Tab Controls
        private DateTimePicker dtpFromDate, dtpToDate;
        private ComboBox cmbCustomer, cmbStatus;
        private Button btnGenerate, btnExport, btnPrint;
        private DataGridView dgvSummary;
        private Panel pnlKPIs;
        private Label lblTotalInvoices, lblTotalAmount, lblPaidAmount, lblOutstandingAmount;
        private Label lblTotalInvoicesValue, lblTotalAmountValue, lblPaidAmountValue, lblOutstandingAmountValue;

        // Detailed Tab Controls
        private DataGridView dgvDetailed;
        private Button btnExportDetailed, btnPrintDetailed;

        // Analytics Tab Controls
        private Panel pnlCharts;
        private Button btnRefreshCharts;

        // Aging Tab Controls
        private DataGridView dgvAging;
        private Button btnExportAging, btnPrintAging;

        // Data
        private List<Invoice> invoices;
        private List<Customer> customers;
        private List<Receipt> receipts;

        #endregion

        #region Constructor
        public ComprehensiveInvoicesReportForm()
        {
            InitializeComponent();
            LoadData();
            SetupForm();
            LoadDefaultData();
        }
        #endregion

        #region Form Setup
        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "تقارير الفواتير الشامل";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.Font = new Font("Tahoma", 9F);
            this.WindowState = FormWindowState.Maximized;

            // Create tab control
            CreateTabControl();
            CreateSummaryTab();
            CreateDetailedTab();
            CreateAnalyticsTab();
            CreateAgingTab();

            this.ResumeLayout(false);
        }

        private void CreateTabControl()
        {
            tabControl = new TabControl();
            tabControl.Dock = DockStyle.Fill;
            tabControl.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            tabControl.RightToLeftLayout = true;

            // Create tabs
            tabSummary = new TabPage("التقرير الإجمالي");
            tabDetailed = new TabPage("التقرير المفصل");
            tabAnalytics = new TabPage("التحليلات والرسوم البيانية");
            tabAging = new TabPage("تحليل الأعمار");

            tabControl.TabPages.AddRange(new TabPage[] { tabSummary, tabDetailed, tabAnalytics, tabAging });
            this.Controls.Add(tabControl);
        }

        private void CreateSummaryTab()
        {
            // Filter Panel
            Panel pnlFilter = new Panel();
            pnlFilter.Height = 80;
            pnlFilter.Dock = DockStyle.Top;
            pnlFilter.BackColor = Color.FromArgb(240, 248, 255);
            pnlFilter.Padding = new Padding(10);

            // Date filters
            Label lblFromDate = new Label();
            lblFromDate.Text = "من تاريخ:";
            lblFromDate.Location = new Point(950, 15);
            lblFromDate.Size = new Size(60, 23);
            lblFromDate.TextAlign = ContentAlignment.MiddleRight;

            dtpFromDate = new DateTimePicker();
            dtpFromDate.Location = new Point(780, 15);
            dtpFromDate.Size = new Size(160, 23);
            dtpFromDate.Value = DateTime.Today.AddMonths(-1);

            Label lblToDate = new Label();
            lblToDate.Text = "إلى تاريخ:";
            lblToDate.Location = new Point(700, 15);
            lblToDate.Size = new Size(60, 23);
            lblToDate.TextAlign = ContentAlignment.MiddleRight;

            dtpToDate = new DateTimePicker();
            dtpToDate.Location = new Point(530, 15);
            dtpToDate.Size = new Size(160, 23);
            dtpToDate.Value = DateTime.Today;

            // Customer filter
            Label lblCustomer = new Label();
            lblCustomer.Text = "العميل:";
            lblCustomer.Location = new Point(450, 15);
            lblCustomer.Size = new Size(60, 23);
            lblCustomer.TextAlign = ContentAlignment.MiddleRight;

            cmbCustomer = new ComboBox();
            cmbCustomer.Location = new Point(280, 15);
            cmbCustomer.Size = new Size(160, 23);
            cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList;

            // Status filter
            Label lblStatus = new Label();
            lblStatus.Text = "الحالة:";
            lblStatus.Location = new Point(200, 15);
            lblStatus.Size = new Size(60, 23);
            lblStatus.TextAlign = ContentAlignment.MiddleRight;

            cmbStatus = new ComboBox();
            cmbStatus.Location = new Point(30, 15);
            cmbStatus.Size = new Size(160, 23);
            cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;

            // Buttons
            btnGenerate = new Button();
            btnGenerate.Text = "إنشاء التقرير";
            btnGenerate.Location = new Point(950, 45);
            btnGenerate.Size = new Size(100, 30);
            btnGenerate.BackColor = Color.FromArgb(0, 123, 255);
            btnGenerate.ForeColor = Color.White;
            btnGenerate.FlatStyle = FlatStyle.Flat;
            btnGenerate.Click += BtnGenerate_Click;

            btnExport = new Button();
            btnExport.Text = "تصدير Excel";
            btnExport.Location = new Point(840, 45);
            btnExport.Size = new Size(100, 30);
            btnExport.BackColor = Color.FromArgb(40, 167, 69);
            btnExport.ForeColor = Color.White;
            btnExport.FlatStyle = FlatStyle.Flat;
            btnExport.Click += BtnExport_Click;

            btnPrint = new Button();
            btnPrint.Text = "طباعة";
            btnPrint.Location = new Point(730, 45);
            btnPrint.Size = new Size(100, 30);
            btnPrint.BackColor = Color.FromArgb(108, 117, 125);
            btnPrint.ForeColor = Color.White;
            btnPrint.FlatStyle = FlatStyle.Flat;
            btnPrint.Click += BtnPrint_Click;

            pnlFilter.Controls.AddRange(new Control[] {
                lblFromDate, dtpFromDate, lblToDate, dtpToDate,
                lblCustomer, cmbCustomer, lblStatus, cmbStatus,
                btnGenerate, btnExport, btnPrint
            });

            // KPIs Panel
            CreateKPIsPanel();

            // Summary Grid
            CreateSummaryGrid();

            tabSummary.Controls.AddRange(new Control[] { pnlFilter, pnlKPIs, dgvSummary });
        }

        private void CreateKPIsPanel()
        {
            pnlKPIs = new Panel();
            pnlKPIs.Height = 100;
            pnlKPIs.Dock = DockStyle.Top;
            pnlKPIs.BackColor = Color.White;
            pnlKPIs.Padding = new Padding(10);

            // KPI Cards
            CreateKPICard("إجمالي الفواتير", "0", Color.FromArgb(0, 123, 255), 10, out lblTotalInvoices, out lblTotalInvoicesValue);
            CreateKPICard("إجمالي المبلغ", "0.00 ريال", Color.FromArgb(40, 167, 69), 280, out lblTotalAmount, out lblTotalAmountValue);
            CreateKPICard("المبلغ المدفوع", "0.00 ريال", Color.FromArgb(23, 162, 184), 550, out lblPaidAmount, out lblPaidAmountValue);
            CreateKPICard("المبلغ المستحق", "0.00 ريال", Color.FromArgb(220, 53, 69), 820, out lblOutstandingAmount, out lblOutstandingAmountValue);
        }

        private void CreateKPICard(string title, string value, Color color, int x, out Label lblTitle, out Label lblValue)
        {
            Panel card = new Panel();
            card.Size = new Size(250, 80);
            card.Location = new Point(x, 10);
            card.BackColor = color;
            card.ForeColor = Color.White;

            lblTitle = new Label();
            lblTitle.Text = title;
            lblTitle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblTitle.Location = new Point(10, 10);
            lblTitle.Size = new Size(230, 25);
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;

            lblValue = new Label();
            lblValue.Text = value;
            lblValue.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            lblValue.Location = new Point(10, 35);
            lblValue.Size = new Size(230, 35);
            lblValue.TextAlign = ContentAlignment.MiddleCenter;

            card.Controls.AddRange(new Control[] { lblTitle, lblValue });
            pnlKPIs.Controls.Add(card);
        }

        private void CreateSummaryGrid()
        {
            dgvSummary = new DataGridView();
            dgvSummary.Dock = DockStyle.Fill;
            dgvSummary.BackgroundColor = Color.White;
            dgvSummary.BorderStyle = BorderStyle.None;
            dgvSummary.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvSummary.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvSummary.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvSummary.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            dgvSummary.ColumnHeadersHeight = 40;
            dgvSummary.DefaultCellStyle.Font = new Font("Tahoma", 9F);
            dgvSummary.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvSummary.RowHeadersVisible = false;
            dgvSummary.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvSummary.MultiSelect = false;
            dgvSummary.ReadOnly = true;
            dgvSummary.AllowUserToAddRows = false;
            dgvSummary.AllowUserToDeleteRows = false;
            dgvSummary.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
        }

        private void CreateDetailedTab()
        {
            // Filter Panel for Detailed Tab
            Panel pnlDetailedFilter = new Panel();
            pnlDetailedFilter.Height = 50;
            pnlDetailedFilter.Dock = DockStyle.Top;
            pnlDetailedFilter.BackColor = Color.FromArgb(248, 249, 250);
            pnlDetailedFilter.Padding = new Padding(10);

            btnExportDetailed = new Button();
            btnExportDetailed.Text = "تصدير Excel";
            btnExportDetailed.Location = new Point(950, 10);
            btnExportDetailed.Size = new Size(100, 30);
            btnExportDetailed.BackColor = Color.FromArgb(40, 167, 69);
            btnExportDetailed.ForeColor = Color.White;
            btnExportDetailed.FlatStyle = FlatStyle.Flat;
            btnExportDetailed.Click += BtnExportDetailed_Click;

            btnPrintDetailed = new Button();
            btnPrintDetailed.Text = "طباعة";
            btnPrintDetailed.Location = new Point(840, 10);
            btnPrintDetailed.Size = new Size(100, 30);
            btnPrintDetailed.BackColor = Color.FromArgb(108, 117, 125);
            btnPrintDetailed.ForeColor = Color.White;
            btnPrintDetailed.FlatStyle = FlatStyle.Flat;
            btnPrintDetailed.Click += BtnPrintDetailed_Click;

            pnlDetailedFilter.Controls.AddRange(new Control[] { btnExportDetailed, btnPrintDetailed });

            // Detailed Grid
            dgvDetailed = new DataGridView();
            dgvDetailed.Dock = DockStyle.Fill;
            dgvDetailed.BackgroundColor = Color.White;
            dgvDetailed.BorderStyle = BorderStyle.None;
            dgvDetailed.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvDetailed.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvDetailed.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvDetailed.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            dgvDetailed.ColumnHeadersHeight = 40;
            dgvDetailed.DefaultCellStyle.Font = new Font("Tahoma", 9F);
            dgvDetailed.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvDetailed.RowHeadersVisible = false;
            dgvDetailed.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvDetailed.MultiSelect = false;
            dgvDetailed.ReadOnly = true;
            dgvDetailed.AllowUserToAddRows = false;
            dgvDetailed.AllowUserToDeleteRows = false;
            dgvDetailed.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            tabDetailed.Controls.AddRange(new Control[] { pnlDetailedFilter, dgvDetailed });
        }

        private void CreateAnalyticsTab()
        {
            // Analytics Panel
            Panel pnlAnalyticsFilter = new Panel();
            pnlAnalyticsFilter.Height = 50;
            pnlAnalyticsFilter.Dock = DockStyle.Top;
            pnlAnalyticsFilter.BackColor = Color.FromArgb(248, 249, 250);
            pnlAnalyticsFilter.Padding = new Padding(10);

            btnRefreshCharts = new Button();
            btnRefreshCharts.Text = "تحديث الرسوم البيانية";
            btnRefreshCharts.Location = new Point(850, 10);
            btnRefreshCharts.Size = new Size(150, 30);
            btnRefreshCharts.BackColor = Color.FromArgb(0, 123, 255);
            btnRefreshCharts.ForeColor = Color.White;
            btnRefreshCharts.FlatStyle = FlatStyle.Flat;
            btnRefreshCharts.Click += BtnRefreshCharts_Click;

            pnlAnalyticsFilter.Controls.Add(btnRefreshCharts);

            // Charts Panel
            pnlCharts = new Panel();
            pnlCharts.Dock = DockStyle.Fill;
            pnlCharts.BackColor = Color.White;
            pnlCharts.AutoScroll = true;

            tabAnalytics.Controls.AddRange(new Control[] { pnlAnalyticsFilter, pnlCharts });
        }

        private void CreateAgingTab()
        {
            // Aging Filter Panel
            Panel pnlAgingFilter = new Panel();
            pnlAgingFilter.Height = 50;
            pnlAgingFilter.Dock = DockStyle.Top;
            pnlAgingFilter.BackColor = Color.FromArgb(248, 249, 250);
            pnlAgingFilter.Padding = new Padding(10);

            btnExportAging = new Button();
            btnExportAging.Text = "تصدير Excel";
            btnExportAging.Location = new Point(950, 10);
            btnExportAging.Size = new Size(100, 30);
            btnExportAging.BackColor = Color.FromArgb(40, 167, 69);
            btnExportAging.ForeColor = Color.White;
            btnExportAging.FlatStyle = FlatStyle.Flat;
            btnExportAging.Click += BtnExportAging_Click;

            btnPrintAging = new Button();
            btnPrintAging.Text = "طباعة";
            btnPrintAging.Location = new Point(840, 10);
            btnPrintAging.Size = new Size(100, 30);
            btnPrintAging.BackColor = Color.FromArgb(108, 117, 125);
            btnPrintAging.ForeColor = Color.White;
            btnPrintAging.FlatStyle = FlatStyle.Flat;
            btnPrintAging.Click += BtnPrintAging_Click;

            pnlAgingFilter.Controls.AddRange(new Control[] { btnExportAging, btnPrintAging });

            // Aging Grid
            dgvAging = new DataGridView();
            dgvAging.Dock = DockStyle.Fill;
            dgvAging.BackgroundColor = Color.White;
            dgvAging.BorderStyle = BorderStyle.None;
            dgvAging.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvAging.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvAging.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvAging.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            dgvAging.ColumnHeadersHeight = 40;
            dgvAging.DefaultCellStyle.Font = new Font("Tahoma", 9F);
            dgvAging.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvAging.RowHeadersVisible = false;
            dgvAging.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvAging.MultiSelect = false;
            dgvAging.ReadOnly = true;
            dgvAging.AllowUserToAddRows = false;
            dgvAging.AllowUserToDeleteRows = false;
            dgvAging.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            tabAging.Controls.AddRange(new Control[] { pnlAgingFilter, dgvAging });
        }

        private void SetupForm()
        {
            // Setup summary grid columns
            SetupSummaryGridColumns();
            SetupDetailedGridColumns();
            SetupAgingGridColumns();

            // Load combo box data
            LoadComboBoxData();
        }

        private void SetupSummaryGridColumns()
        {
            dgvSummary.Columns.Clear();
            dgvSummary.Columns.Add("CustomerName", "اسم العميل");
            dgvSummary.Columns.Add("InvoiceCount", "عدد الفواتير");
            dgvSummary.Columns.Add("TotalAmount", "إجمالي المبلغ");
            dgvSummary.Columns.Add("PaidAmount", "المبلغ المدفوع");
            dgvSummary.Columns.Add("OutstandingAmount", "المبلغ المستحق");
            dgvSummary.Columns.Add("CollectionRate", "معدل التحصيل %");

            // Format currency columns
            dgvSummary.Columns["TotalAmount"].DefaultCellStyle.Format = "N2";
            dgvSummary.Columns["PaidAmount"].DefaultCellStyle.Format = "N2";
            dgvSummary.Columns["OutstandingAmount"].DefaultCellStyle.Format = "N2";
            dgvSummary.Columns["CollectionRate"].DefaultCellStyle.Format = "N1";

            // Set column widths
            dgvSummary.Columns["CustomerName"].FillWeight = 25;
            dgvSummary.Columns["InvoiceCount"].FillWeight = 15;
            dgvSummary.Columns["TotalAmount"].FillWeight = 20;
            dgvSummary.Columns["PaidAmount"].FillWeight = 20;
            dgvSummary.Columns["OutstandingAmount"].FillWeight = 20;
        }

        private void SetupDetailedGridColumns()
        {
            dgvDetailed.Columns.Clear();
            dgvDetailed.Columns.Add("InvoiceNumber", "رقم الفاتورة");
            dgvDetailed.Columns.Add("InvoiceDate", "تاريخ الفاتورة");
            dgvDetailed.Columns.Add("DueDate", "تاريخ الاستحقاق");
            dgvDetailed.Columns.Add("CustomerName", "اسم العميل");
            dgvDetailed.Columns.Add("TotalAmount", "إجمالي المبلغ");
            dgvDetailed.Columns.Add("PaidAmount", "المبلغ المدفوع");
            dgvDetailed.Columns.Add("RemainingAmount", "المبلغ المتبقي");
            dgvDetailed.Columns.Add("Status", "الحالة");
            dgvDetailed.Columns.Add("DaysOverdue", "أيام التأخير");

            // Format columns
            dgvDetailed.Columns["InvoiceDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            dgvDetailed.Columns["DueDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            dgvDetailed.Columns["TotalAmount"].DefaultCellStyle.Format = "N2";
            dgvDetailed.Columns["PaidAmount"].DefaultCellStyle.Format = "N2";
            dgvDetailed.Columns["RemainingAmount"].DefaultCellStyle.Format = "N2";
        }

        private void SetupAgingGridColumns()
        {
            dgvAging.Columns.Clear();
            dgvAging.Columns.Add("CustomerName", "اسم العميل");
            dgvAging.Columns.Add("Current", "الحالي (0-30)");
            dgvAging.Columns.Add("Days31to60", "31-60 يوم");
            dgvAging.Columns.Add("Days61to90", "61-90 يوم");
            dgvAging.Columns.Add("Days91to120", "91-120 يوم");
            dgvAging.Columns.Add("Over120", "أكثر من 120 يوم");
            dgvAging.Columns.Add("Total", "الإجمالي");

            // Format currency columns
            for (int i = 1; i < dgvAging.Columns.Count; i++)
            {
                dgvAging.Columns[i].DefaultCellStyle.Format = "N2";
            }
        }
        #endregion

        #region Data Loading
        private void LoadData()
        {
            try
            {
                // Load data from DataService or database
                invoices = DataService.Invoices ?? new List<Invoice>();
                customers = DataService.Customers ?? new List<Customer>();
                receipts = DataService.Receipts ?? new List<Receipt>();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Initialize empty lists if loading fails
                invoices = new List<Invoice>();
                customers = new List<Customer>();
                receipts = new List<Receipt>();
            }
        }

        private void LoadComboBoxData()
        {
            // Load customers
            cmbCustomer.Items.Clear();
            cmbCustomer.Items.Add(new { Text = "جميع العملاء", Value = -1 });
            foreach (var customer in customers.OrderBy(c => c.CustomerName))
            {
                cmbCustomer.Items.Add(new { Text = customer.CustomerName, Value = customer.Id });
            }
            cmbCustomer.DisplayMember = "Text";
            cmbCustomer.ValueMember = "Value";
            cmbCustomer.SelectedIndex = 0;

            // Load status options
            cmbStatus.Items.Clear();
            cmbStatus.Items.Add(new { Text = "جميع الحالات", Value = "" });
            cmbStatus.Items.Add(new { Text = "مدفوعة", Value = "مدفوعة" });
            cmbStatus.Items.Add(new { Text = "مدفوعة جزئياً", Value = "مدفوعة جزئياً" });
            cmbStatus.Items.Add(new { Text = "غير مدفوعة", Value = "غير مدفوعة" });
            cmbStatus.Items.Add(new { Text = "متأخرة", Value = "متأخرة" });
            cmbStatus.Items.Add(new { Text = "مؤكدة", Value = "مؤكدة" });
            cmbStatus.Items.Add(new { Text = "مسودة", Value = "مسودة" });
            cmbStatus.DisplayMember = "Text";
            cmbStatus.ValueMember = "Value";
            cmbStatus.SelectedIndex = 0;
        }

        private void LoadDefaultData()
        {
            // Load initial report data
            GenerateReport();
        }
        #endregion

        #region Event Handlers
        private void BtnGenerate_Click(object sender, EventArgs e)
        {
            GenerateReport();
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            ExportSummaryToExcel();
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            PrintSummaryReport();
        }

        private void BtnExportDetailed_Click(object sender, EventArgs e)
        {
            ExportDetailedToExcel();
        }

        private void BtnPrintDetailed_Click(object sender, EventArgs e)
        {
            PrintDetailedReport();
        }

        private void BtnRefreshCharts_Click(object sender, EventArgs e)
        {
            RefreshAnalytics();
        }

        private void BtnExportAging_Click(object sender, EventArgs e)
        {
            ExportAgingToExcel();
        }

        private void BtnPrintAging_Click(object sender, EventArgs e)
        {
            PrintAgingReport();
        }
        #endregion

        #region Report Generation
        private void GenerateReport()
        {
            try
            {
                // Get filtered invoices
                var filteredInvoices = GetFilteredInvoices();

                // Update KPIs
                UpdateKPIs(filteredInvoices);

                // Load summary data
                LoadSummaryData(filteredInvoices);

                // Load detailed data
                LoadDetailedData(filteredInvoices);

                // Load aging data
                LoadAgingData(filteredInvoices);

                // Refresh analytics
                RefreshAnalytics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private List<Invoice> GetFilteredInvoices()
        {
            var filtered = invoices.AsQueryable();

            // Filter by date range
            filtered = filtered.Where(i => i.InvoiceDate >= dtpFromDate.Value.Date &&
                                         i.InvoiceDate <= dtpToDate.Value.Date);

            // Filter by customer
            var selectedCustomer = cmbCustomer.SelectedItem as dynamic;
            if (selectedCustomer != null && selectedCustomer.Value != -1)
            {
                int customerId = selectedCustomer.Value;
                filtered = filtered.Where(i => i.CustomerId == customerId);
            }

            // Filter by status
            var selectedStatus = cmbStatus.SelectedItem as dynamic;
            if (selectedStatus != null && !string.IsNullOrEmpty(selectedStatus.Value))
            {
                string statusValue = selectedStatus.Value;
                filtered = filtered.Where(i => i.Status == statusValue);
            }

            return filtered.ToList();
        }

        private void UpdateKPIs(List<Invoice> filteredInvoices)
        {
            var totalInvoices = filteredInvoices.Count;
            var totalAmount = filteredInvoices.Sum(i => i.TotalAmount);
            var paidAmount = filteredInvoices.Sum(i => i.PaidAmount);
            var outstandingAmount = totalAmount - paidAmount;

            lblTotalInvoicesValue.Text = totalInvoices.ToString("N0");
            lblTotalAmountValue.Text = totalAmount.ToString("N2") + " ريال";
            lblPaidAmountValue.Text = paidAmount.ToString("N2") + " ريال";
            lblOutstandingAmountValue.Text = outstandingAmount.ToString("N2") + " ريال";
        }

        private void LoadSummaryData(List<Invoice> filteredInvoices)
        {
            dgvSummary.Rows.Clear();

            var customerSummary = filteredInvoices
                .GroupBy(i => i.CustomerId)
                .Select(g => new
                {
                    CustomerId = g.Key,
                    CustomerName = customers.FirstOrDefault(c => c.Id == g.Key)?.CustomerName ?? "غير محدد",
                    InvoiceCount = g.Count(),
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    PaidAmount = g.Sum(i => i.PaidAmount),
                    OutstandingAmount = g.Sum(i => i.TotalAmount - i.PaidAmount),
                    CollectionRate = g.Sum(i => i.TotalAmount) > 0 ?
                        (g.Sum(i => i.PaidAmount) / g.Sum(i => i.TotalAmount)) * 100 : 0
                })
                .OrderByDescending(s => s.TotalAmount)
                .ToList();

            foreach (var summary in customerSummary)
            {
                var row = dgvSummary.Rows.Add();
                dgvSummary.Rows[row].Cells["CustomerName"].Value = summary.CustomerName;
                dgvSummary.Rows[row].Cells["InvoiceCount"].Value = summary.InvoiceCount;
                dgvSummary.Rows[row].Cells["TotalAmount"].Value = summary.TotalAmount;
                dgvSummary.Rows[row].Cells["PaidAmount"].Value = summary.PaidAmount;
                dgvSummary.Rows[row].Cells["OutstandingAmount"].Value = summary.OutstandingAmount;
                dgvSummary.Rows[row].Cells["CollectionRate"].Value = summary.CollectionRate;

                // Color coding based on collection rate
                if (summary.CollectionRate >= 90)
                    dgvSummary.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(212, 237, 218);
                else if (summary.CollectionRate >= 70)
                    dgvSummary.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(255, 243, 205);
                else if (summary.CollectionRate < 50)
                    dgvSummary.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(248, 215, 218);
            }
        }

        private void LoadDetailedData(List<Invoice> filteredInvoices)
        {
            dgvDetailed.Rows.Clear();

            foreach (var invoice in filteredInvoices.OrderByDescending(i => i.InvoiceDate))
            {
                var customer = customers.FirstOrDefault(c => c.Id == invoice.CustomerId);
                var remainingAmount = invoice.TotalAmount - invoice.PaidAmount;
                var daysOverdue = invoice.DueDate < DateTime.Today ? (DateTime.Today - invoice.DueDate).Days : 0;

                var row = dgvDetailed.Rows.Add();
                dgvDetailed.Rows[row].Cells["InvoiceNumber"].Value = invoice.InvoiceNumber;
                dgvDetailed.Rows[row].Cells["InvoiceDate"].Value = invoice.InvoiceDate;
                dgvDetailed.Rows[row].Cells["DueDate"].Value = invoice.DueDate;
                dgvDetailed.Rows[row].Cells["CustomerName"].Value = customer?.CustomerName ?? "غير محدد";
                dgvDetailed.Rows[row].Cells["TotalAmount"].Value = invoice.TotalAmount;
                dgvDetailed.Rows[row].Cells["PaidAmount"].Value = invoice.PaidAmount;
                dgvDetailed.Rows[row].Cells["RemainingAmount"].Value = remainingAmount;
                dgvDetailed.Rows[row].Cells["Status"].Value = invoice.Status;
                dgvDetailed.Rows[row].Cells["DaysOverdue"].Value = daysOverdue > 0 ? daysOverdue.ToString() : "-";

                // Color coding based on status
                switch (invoice.Status)
                {
                    case "مدفوعة":
                        dgvDetailed.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(212, 237, 218);
                        break;
                    case "مدفوعة جزئياً":
                        dgvDetailed.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(255, 243, 205);
                        break;
                    case "متأخرة":
                        dgvDetailed.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(248, 215, 218);
                        break;
                    case "مؤكدة":
                        dgvDetailed.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(217, 237, 247);
                        break;
                    case "مسودة":
                        dgvDetailed.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
                        break;
                }
            }
        }

        private void LoadAgingData(List<Invoice> filteredInvoices)
        {
            dgvAging.Rows.Clear();

            var outstandingInvoices = filteredInvoices.Where(i => i.TotalAmount > i.PaidAmount).ToList();

            var agingData = outstandingInvoices
                .GroupBy(i => i.CustomerId)
                .Select(g => new
                {
                    CustomerId = g.Key,
                    CustomerName = customers.FirstOrDefault(c => c.Id == g.Key)?.CustomerName ?? "غير محدد",
                    Current = g.Where(i => (DateTime.Today - i.DueDate).Days <= 30)
                            .Sum(i => i.TotalAmount - i.PaidAmount),
                    Days31to60 = g.Where(i => (DateTime.Today - i.DueDate).Days > 30 &&
                                            (DateTime.Today - i.DueDate).Days <= 60)
                               .Sum(i => i.TotalAmount - i.PaidAmount),
                    Days61to90 = g.Where(i => (DateTime.Today - i.DueDate).Days > 60 &&
                                            (DateTime.Today - i.DueDate).Days <= 90)
                               .Sum(i => i.TotalAmount - i.PaidAmount),
                    Days91to120 = g.Where(i => (DateTime.Today - i.DueDate).Days > 90 &&
                                             (DateTime.Today - i.DueDate).Days <= 120)
                                .Sum(i => i.TotalAmount - i.PaidAmount),
                    Over120 = g.Where(i => (DateTime.Today - i.DueDate).Days > 120)
                            .Sum(i => i.TotalAmount - i.PaidAmount),
                    Total = g.Sum(i => i.TotalAmount - i.PaidAmount)
                })
                .Where(a => a.Total > 0)
                .OrderByDescending(a => a.Total)
                .ToList();

            foreach (var aging in agingData)
            {
                var row = dgvAging.Rows.Add();
                dgvAging.Rows[row].Cells["CustomerName"].Value = aging.CustomerName;
                dgvAging.Rows[row].Cells["Current"].Value = aging.Current;
                dgvAging.Rows[row].Cells["Days31to60"].Value = aging.Days31to60;
                dgvAging.Rows[row].Cells["Days61to90"].Value = aging.Days61to90;
                dgvAging.Rows[row].Cells["Days91to120"].Value = aging.Days91to120;
                dgvAging.Rows[row].Cells["Over120"].Value = aging.Over120;
                dgvAging.Rows[row].Cells["Total"].Value = aging.Total;

                // Color coding based on aging
                if (aging.Over120 > 0)
                    dgvAging.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(248, 215, 218);
                else if (aging.Days91to120 > 0)
                    dgvAging.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(255, 243, 205);
                else if (aging.Days61to90 > 0)
                    dgvAging.Rows[row].DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 205);
            }
        }

        private void RefreshAnalytics()
        {
            try
            {
                pnlCharts.Controls.Clear();

                var filteredInvoices = GetFilteredInvoices();

                // Create analytics panels
                CreateStatusChart(filteredInvoices);
                CreateMonthlyTrendChart(filteredInvoices);
                CreateTopCustomersChart(filteredInvoices);
                CreateCollectionRateChart(filteredInvoices);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث التحليلات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CreateStatusChart(List<Invoice> invoices)
        {
            var statusPanel = new Panel();
            statusPanel.Size = new Size(500, 300);
            statusPanel.Location = new Point(10, 10);
            statusPanel.BorderStyle = BorderStyle.FixedSingle;
            statusPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "توزيع الفواتير حسب الحالة";
            titleLabel.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(480, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var statusGroups = invoices.GroupBy(i => i.Status)
                .Select(g => new { Status = g.Key, Count = g.Count(), Amount = g.Sum(i => i.TotalAmount) })
                .OrderByDescending(g => g.Amount)
                .ToList();

            int y = 50;
            foreach (var group in statusGroups)
            {
                var statusLabel = new Label();
                statusLabel.Text = $"{group.Status}: {group.Count} فاتورة - {group.Amount:N2} ريال";
                statusLabel.Location = new Point(20, y);
                statusLabel.Size = new Size(460, 25);
                statusLabel.Font = new Font("Tahoma", 10F);
                statusPanel.Controls.Add(statusLabel);
                y += 30;
            }

            statusPanel.Controls.Add(titleLabel);
            pnlCharts.Controls.Add(statusPanel);
        }

        private void CreateMonthlyTrendChart(List<Invoice> invoices)
        {
            var trendPanel = new Panel();
            trendPanel.Size = new Size(500, 300);
            trendPanel.Location = new Point(520, 10);
            trendPanel.BorderStyle = BorderStyle.FixedSingle;
            trendPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "الاتجاه الشهري للفواتير";
            titleLabel.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(480, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var monthlyData = invoices
                .GroupBy(i => new { Year = i.InvoiceDate.Year, Month = i.InvoiceDate.Month })
                .Select(g => new
                {
                    Period = $"{g.Key.Year}/{g.Key.Month:00}",
                    Count = g.Count(),
                    Amount = g.Sum(i => i.TotalAmount)
                })
                .OrderBy(g => g.Period)
                .Take(12)
                .ToList();

            int y = 50;
            foreach (var data in monthlyData)
            {
                var dataLabel = new Label();
                dataLabel.Text = $"{data.Period}: {data.Count} فاتورة - {data.Amount:N2} ريال";
                dataLabel.Location = new Point(20, y);
                dataLabel.Size = new Size(460, 25);
                dataLabel.Font = new Font("Tahoma", 10F);
                trendPanel.Controls.Add(dataLabel);
                y += 30;
            }

            trendPanel.Controls.Add(titleLabel);
            pnlCharts.Controls.Add(trendPanel);
        }

        private void CreateTopCustomersChart(List<Invoice> invoices)
        {
            var customersPanel = new Panel();
            customersPanel.Size = new Size(500, 300);
            customersPanel.Location = new Point(10, 320);
            customersPanel.BorderStyle = BorderStyle.FixedSingle;
            customersPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "أفضل العملاء (حسب قيمة الفواتير)";
            titleLabel.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(480, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var topCustomers = invoices
                .GroupBy(i => i.CustomerId)
                .Select(g => new
                {
                    CustomerName = customers.FirstOrDefault(c => c.Id == g.Key)?.CustomerName ?? "غير محدد",
                    Count = g.Count(),
                    Amount = g.Sum(i => i.TotalAmount)
                })
                .OrderByDescending(g => g.Amount)
                .Take(8)
                .ToList();

            int y = 50;
            foreach (var customer in topCustomers)
            {
                var customerLabel = new Label();
                customerLabel.Text = $"{customer.CustomerName}: {customer.Count} فاتورة - {customer.Amount:N2} ريال";
                customerLabel.Location = new Point(20, y);
                customerLabel.Size = new Size(460, 25);
                customerLabel.Font = new Font("Tahoma", 10F);
                customersPanel.Controls.Add(customerLabel);
                y += 30;
            }

            customersPanel.Controls.Add(titleLabel);
            pnlCharts.Controls.Add(customersPanel);
        }

        private void CreateCollectionRateChart(List<Invoice> invoices)
        {
            var collectionPanel = new Panel();
            collectionPanel.Size = new Size(500, 300);
            collectionPanel.Location = new Point(520, 320);
            collectionPanel.BorderStyle = BorderStyle.FixedSingle;
            collectionPanel.BackColor = Color.White;

            var titleLabel = new Label();
            titleLabel.Text = "معدلات التحصيل";
            titleLabel.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(480, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            var totalAmount = invoices.Sum(i => i.TotalAmount);
            var paidAmount = invoices.Sum(i => i.PaidAmount);
            var collectionRate = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

            var rateLabel = new Label();
            rateLabel.Text = $"معدل التحصيل الإجمالي: {collectionRate:N1}%";
            rateLabel.Location = new Point(20, 50);
            rateLabel.Size = new Size(460, 30);
            rateLabel.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            rateLabel.ForeColor = collectionRate >= 80 ? Color.Green : collectionRate >= 60 ? Color.Orange : Color.Red;

            var amountLabel = new Label();
            amountLabel.Text = $"إجمالي الفواتير: {totalAmount:N2} ريال";
            amountLabel.Location = new Point(20, 90);
            amountLabel.Size = new Size(460, 25);
            amountLabel.Font = new Font("Tahoma", 10F);

            var paidLabel = new Label();
            paidLabel.Text = $"المبلغ المحصل: {paidAmount:N2} ريال";
            paidLabel.Location = new Point(20, 120);
            paidLabel.Size = new Size(460, 25);
            paidLabel.Font = new Font("Tahoma", 10F);

            var outstandingLabel = new Label();
            outstandingLabel.Text = $"المبلغ المستحق: {(totalAmount - paidAmount):N2} ريال";
            outstandingLabel.Location = new Point(20, 150);
            outstandingLabel.Size = new Size(460, 25);
            outstandingLabel.Font = new Font("Tahoma", 10F);

            collectionPanel.Controls.AddRange(new Control[] { titleLabel, rateLabel, amountLabel, paidLabel, outstandingLabel });
            pnlCharts.Controls.Add(collectionPanel);
        }
        #endregion

        #region Export and Print Methods
        private void ExportSummaryToExcel()
        {
            try
            {
                var saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Excel Files|*.xlsx";
                saveDialog.Title = "حفظ تقرير الفواتير الإجمالي";
                saveDialog.FileName = $"تقرير_الفواتير_الإجمالي_{DateTime.Now:yyyyMMdd}.xlsx";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportHelper.ExportDataGridViewToExcel(dgvSummary, saveDialog.FileName, "تقرير الفواتير الإجمالي");
                    MessageBox.Show("تم تصدير التقرير بنجاح!", "تصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportDetailedToExcel()
        {
            try
            {
                var saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Excel Files|*.xlsx";
                saveDialog.Title = "حفظ التقرير المفصل";
                saveDialog.FileName = $"التقرير_المفصل_{DateTime.Now:yyyyMMdd}.xlsx";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportHelper.ExportDataGridViewToExcel(dgvDetailed, saveDialog.FileName, "التقرير المفصل");
                    MessageBox.Show("تم تصدير التقرير بنجاح!", "تصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportAgingToExcel()
        {
            try
            {
                var saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Excel Files|*.xlsx";
                saveDialog.Title = "حفظ تقرير تحليل الأعمار";
                saveDialog.FileName = $"تقرير_تحليل_الأعمار_{DateTime.Now:yyyyMMdd}.xlsx";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportHelper.ExportDataGridViewToExcel(dgvAging, saveDialog.FileName, "تقرير تحليل الأعمار");
                    MessageBox.Show("تم تصدير التقرير بنجاح!", "تصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintSummaryReport()
        {
            try
            {
                var reportGenerator = new ReportGenerator();
                var filteredInvoices = GetFilteredInvoices();

                var reportLines = new List<string>();
                reportLines.Add("تقرير الفواتير الإجمالي");
                reportLines.Add("===================");
                reportLines.Add($"الفترة: من {dtpFromDate.Value:yyyy/MM/dd} إلى {dtpToDate.Value:yyyy/MM/dd}");
                reportLines.Add($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
                reportLines.Add("");

                // KPIs
                reportLines.Add("المؤشرات الرئيسية:");
                reportLines.Add($"إجمالي الفواتير: {lblTotalInvoicesValue.Text}");
                reportLines.Add($"إجمالي المبلغ: {lblTotalAmountValue.Text}");
                reportLines.Add($"المبلغ المدفوع: {lblPaidAmountValue.Text}");
                reportLines.Add($"المبلغ المستحق: {lblOutstandingAmountValue.Text}");
                reportLines.Add("");

                // Summary data
                reportLines.Add("تفاصيل العملاء:");
                reportLines.Add("اسم العميل".PadRight(25) + "عدد الفواتير".PadLeft(12) + "إجمالي المبلغ".PadLeft(15) +
                              "المبلغ المدفوع".PadLeft(15) + "المبلغ المستحق".PadLeft(15) + "معدل التحصيل".PadLeft(12));
                reportLines.Add(new string('=', 100));

                foreach (DataGridViewRow row in dgvSummary.Rows)
                {
                    if (row.IsNewRow) continue;

                    var line = row.Cells["CustomerName"].Value?.ToString().PadRight(25) +
                              row.Cells["InvoiceCount"].Value?.ToString().PadLeft(12) +
                              Convert.ToDecimal(row.Cells["TotalAmount"].Value).ToString("N2").PadLeft(15) +
                              Convert.ToDecimal(row.Cells["PaidAmount"].Value).ToString("N2").PadLeft(15) +
                              Convert.ToDecimal(row.Cells["OutstandingAmount"].Value).ToString("N2").PadLeft(15) +
                              Convert.ToDecimal(row.Cells["CollectionRate"].Value).ToString("N1").PadLeft(12) + "%";
                    reportLines.Add(line);
                }

                PrintHelper.PrintReport(reportLines, "تقرير الفواتير الإجمالي");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintDetailedReport()
        {
            try
            {
                var reportLines = new List<string>();
                reportLines.Add("التقرير المفصل للفواتير");
                reportLines.Add("====================");
                reportLines.Add($"الفترة: من {dtpFromDate.Value:yyyy/MM/dd} إلى {dtpToDate.Value:yyyy/MM/dd}");
                reportLines.Add($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
                reportLines.Add("");

                reportLines.Add("رقم الفاتورة".PadRight(15) + "التاريخ".PadLeft(12) + "الاستحقاق".PadLeft(12) +
                              "العميل".PadRight(20) + "الإجمالي".PadLeft(12) + "المدفوع".PadLeft(12) +
                              "المتبقي".PadLeft(12) + "الحالة".PadRight(15));
                reportLines.Add(new string('=', 120));

                foreach (DataGridViewRow row in dgvDetailed.Rows)
                {
                    if (row.IsNewRow) continue;

                    var line = row.Cells["InvoiceNumber"].Value?.ToString().PadRight(15) +
                              Convert.ToDateTime(row.Cells["InvoiceDate"].Value).ToString("yyyy/MM/dd").PadLeft(12) +
                              Convert.ToDateTime(row.Cells["DueDate"].Value).ToString("yyyy/MM/dd").PadLeft(12) +
                              row.Cells["CustomerName"].Value?.ToString().PadRight(20) +
                              Convert.ToDecimal(row.Cells["TotalAmount"].Value).ToString("N0").PadLeft(12) +
                              Convert.ToDecimal(row.Cells["PaidAmount"].Value).ToString("N0").PadLeft(12) +
                              Convert.ToDecimal(row.Cells["RemainingAmount"].Value).ToString("N0").PadLeft(12) +
                              row.Cells["Status"].Value?.ToString().PadRight(15);
                    reportLines.Add(line);
                }

                PrintHelper.PrintReport(reportLines, "التقرير المفصل للفواتير");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintAgingReport()
        {
            try
            {
                var reportLines = new List<string>();
                reportLines.Add("تقرير تحليل أعمار الفواتير");
                reportLines.Add("=======================");
                reportLines.Add($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
                reportLines.Add("");

                reportLines.Add("العميل".PadRight(20) + "الحالي".PadLeft(12) + "31-60".PadLeft(12) +
                              "61-90".PadLeft(12) + "91-120".PadLeft(12) + "+120".PadLeft(12) + "الإجمالي".PadLeft(12));
                reportLines.Add(new string('=', 100));

                foreach (DataGridViewRow row in dgvAging.Rows)
                {
                    if (row.IsNewRow) continue;

                    var line = row.Cells["CustomerName"].Value?.ToString().PadRight(20) +
                              Convert.ToDecimal(row.Cells["Current"].Value).ToString("N0").PadLeft(12) +
                              Convert.ToDecimal(row.Cells["Days31to60"].Value).ToString("N0").PadLeft(12) +
                              Convert.ToDecimal(row.Cells["Days61to90"].Value).ToString("N0").PadLeft(12) +
                              Convert.ToDecimal(row.Cells["Days91to120"].Value).ToString("N0").PadLeft(12) +
                              Convert.ToDecimal(row.Cells["Over120"].Value).ToString("N0").PadLeft(12) +
                              Convert.ToDecimal(row.Cells["Total"].Value).ToString("N0").PadLeft(12);
                    reportLines.Add(line);
                }

                PrintHelper.PrintReport(reportLines, "تقرير تحليل أعمار الفواتير");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion
    }
}