using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Models;
using IntegratedInvoiceSystem.Services;

namespace IntegratedInvoiceSystem.Forms
{
    /// <summary>
    /// عنصر الداشبورد المتكامل
    /// </summary>
    public class DashboardControl : UserControl
    {
        #region المتغيرات الخاصة
        private Panel statsContainer;
        private Panel chartsContainer;
        private DashboardStats stats;
        #endregion

        #region البناء والتهيئة
        public DashboardControl()
        {
            InitializeControl();
            LoadData();
            CreateStatsCards();
            CreateChartsAndTables();
        }

        private void InitializeControl()
        {
            this.BackColor = Color.FromArgb(236, 240, 241);
            this.Dock = DockStyle.Fill;
            this.Padding = new Padding(0);

            // لوحة الإحصائيات
            statsContainer = new Panel();
            statsContainer.Height = 180;
            statsContainer.Dock = DockStyle.Top;
            statsContainer.BackColor = Color.Transparent;
            statsContainer.Padding = new Padding(0, 0, 0, 30);

            // منطقة الرسوم البيانية والجداول
            chartsContainer = new Panel();
            chartsContainer.Dock = DockStyle.Fill;
            chartsContainer.BackColor = Color.Transparent;

            this.Controls.Add(chartsContainer);
            this.Controls.Add(statsContainer);
        }

        private void LoadData()
        {
            stats = DataService.GetDashboardStats();
        }
        #endregion

        #region إنشاء بطاقات الإحصائيات
        private void CreateStatsCards()
        {
            var statsData = new[]
            {
                new { Title = "إجمالي الفواتير", Value = stats.TotalInvoices.ToString(), SubValue = $"{stats.TotalSales:N0} ر.س", Color = Color.FromArgb(52, 152, 219), Icon = "📊" },
                new { Title = "الفواتير المدفوعة", Value = stats.PaidInvoices.ToString(), SubValue = $"{stats.PaidAmount:N0} ر.س", Color = Color.FromArgb(46, 204, 113), Icon = "✅" },
                new { Title = "الفواتير المتأخرة", Value = stats.OverdueInvoices.ToString(), SubValue = $"{stats.OverdueAmount:N0} ر.س", Color = Color.FromArgb(231, 76, 60), Icon = "⚠️" },
                new { Title = "إجمالي العملاء", Value = stats.TotalCustomers.ToString(), SubValue = $"{stats.TotalBalance:N0} ر.س رصيد", Color = Color.FromArgb(155, 89, 182), Icon = "👥" },
                new { Title = "متوسط قيمة الفاتورة", Value = $"{stats.AverageInvoiceValue:N0} ر.س", SubValue = "هذا الشهر", Color = Color.FromArgb(255, 152, 0), Icon = "💰" }
            };

            int cardWidth = 350;
            int cardSpacing = 30;
            int startX = 30;

            for (int i = 0; i < statsData.Length; i++)
            {
                var statCard = CreateStatCard(statsData[i].Title, statsData[i].Value, statsData[i].SubValue, statsData[i].Color, statsData[i].Icon);
                int row = i / 3;
                int col = i % 3;
                statCard.Location = new Point(startX + (col * (cardWidth + cardSpacing)), 20 + (row * 90));
                statsContainer.Controls.Add(statCard);
            }
        }

        private Panel CreateStatCard(string title, string value, string subValue, Color color, string icon)
        {
            var card = new Panel();
            card.Size = new Size(350, 140);
            card.BackColor = Color.White;
            card.Paint += (s, e) => DrawCardShadow(e, card, color);

            // الأيقونة
            var iconLabel = new Label();
            iconLabel.Text = icon;
            iconLabel.Font = new Font("Segoe UI Emoji", 32F);
            iconLabel.Location = new Point(270, 30);
            iconLabel.Size = new Size(70, 70);
            iconLabel.TextAlign = ContentAlignment.MiddleCenter;

            // العنوان
            var titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(30, 30);
            titleLabel.Size = new Size(220, 35);

            // القيمة الرئيسية
            var valueLabel = new Label();
            valueLabel.Text = value;
            valueLabel.Font = new Font("Segoe UI", 26F, FontStyle.Bold);
            valueLabel.ForeColor = color;
            valueLabel.Location = new Point(30, 65);
            valueLabel.Size = new Size(200, 45);

            // القيمة الفرعية
            var subValueLabel = new Label();
            subValueLabel.Text = subValue;
            subValueLabel.Font = new Font("Segoe UI", 12F);
            subValueLabel.ForeColor = Color.FromArgb(127, 140, 141);
            subValueLabel.Location = new Point(30, 115);
            subValueLabel.Size = new Size(220, 25);

            card.Controls.Add(iconLabel);
            card.Controls.Add(titleLabel);
            card.Controls.Add(valueLabel);
            card.Controls.Add(subValueLabel);

            return card;
        }

        private void DrawCardShadow(PaintEventArgs e, Panel card, Color accentColor)
        {
            // خلفية بيضاء مع حواف مدورة
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, card.Width, card.Height), 15);
                card.Region = new Region(path);
                
                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }

            // خط ملون في الأعلى
            using (SolidBrush brush = new SolidBrush(accentColor))
            {
                e.Graphics.FillRectangle(brush, 0, 0, card.Width, 6);
            }
        }
        #endregion

        #region إنشاء الرسوم البيانية والجداول
        private void CreateChartsAndTables()
        {
            // الرسم البياني للمبيعات
            var salesChart = CreateSalesChart();
            salesChart.Location = new Point(30, 30);
            chartsContainer.Controls.Add(salesChart);

            // جدول الفواتير الأخيرة
            var recentInvoicesPanel = CreateRecentInvoicesPanel();
            recentInvoicesPanel.Location = new Point(800, 30);
            chartsContainer.Controls.Add(recentInvoicesPanel);

            // جدول العملاء الأعلى رصيداً
            var topCustomersPanel = CreateTopCustomersPanel();
            topCustomersPanel.Location = new Point(30, 420);
            chartsContainer.Controls.Add(topCustomersPanel);

            // إحصائيات سريعة إضافية
            var quickStatsPanel = CreateQuickStatsPanel();
            quickStatsPanel.Location = new Point(800, 420);
            chartsContainer.Controls.Add(quickStatsPanel);
        }

        private Panel CreateSalesChart()
        {
            var chartPanel = new Panel();
            chartPanel.Size = new Size(750, 360);
            chartPanel.BackColor = Color.White;
            chartPanel.Paint += (s, e) => DrawSalesChart(e, chartPanel);

            var titleLabel = new Label();
            titleLabel.Text = "📈 مبيعات آخر 7 أيام";
            titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(30, 25);
            titleLabel.Size = new Size(300, 40);

            chartPanel.Controls.Add(titleLabel);
            return chartPanel;
        }

        private void DrawSalesChart(PaintEventArgs e, Panel panel)
        {
            // خلفية بيضاء مع حواف مدورة
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, panel.Width, panel.Height), 15);
                panel.Region = new Region(path);
                
                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }

            // رسم بياني محسن للمبيعات
            var salesData = new[] { 22000, 28000, 25000, 32000, 38000, 35000, 45000 };
            var days = new[] { "السبت", "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة" };
            
            int chartX = 70;
            int chartY = 80;
            int chartWidth = 620;
            int chartHeight = 220;
            int maxValue = salesData.Max();

            // رسم الخطوط الإرشادية
            using (Pen gridPen = new Pen(Color.FromArgb(236, 240, 241), 2))
            {
                for (int i = 0; i <= 5; i++)
                {
                    int y = chartY + (chartHeight * i / 5);
                    e.Graphics.DrawLine(gridPen, chartX, y, chartX + chartWidth, y);
                }
            }

            // رسم البيانات
            using (Pen linePen = new Pen(Color.FromArgb(52, 152, 219), 5))
            using (SolidBrush pointBrush = new SolidBrush(Color.FromArgb(52, 152, 219)))
            using (SolidBrush areaBrush = new SolidBrush(Color.FromArgb(50, 52, 152, 219)))
            {
                var points = new Point[salesData.Length];
                var areaPoints = new Point[salesData.Length + 2];
                
                for (int i = 0; i < salesData.Length; i++)
                {
                    int x = chartX + (chartWidth * i / (salesData.Length - 1));
                    int y = chartY + chartHeight - (int)((double)salesData[i] / maxValue * chartHeight);
                    points[i] = new Point(x, y);
                    areaPoints[i] = new Point(x, y);
                    
                    // رسم النقطة
                    e.Graphics.FillEllipse(pointBrush, x - 8, y - 8, 16, 16);
                    
                    // رسم القيمة
                    using (Font font = new Font("Segoe UI", 11F, FontStyle.Bold))
                    using (SolidBrush textBrush = new SolidBrush(Color.FromArgb(52, 73, 94)))
                    {
                        string valueText = (salesData[i] / 1000).ToString() + "ك";
                        var textSize = e.Graphics.MeasureString(valueText, font);
                        e.Graphics.DrawString(valueText, font, textBrush, x - textSize.Width / 2, y - 35);
                    }
                    
                    // رسم اسم اليوم
                    using (Font font = new Font("Segoe UI", 12F))
                    using (SolidBrush textBrush = new SolidBrush(Color.FromArgb(127, 140, 141)))
                    {
                        var textSize = e.Graphics.MeasureString(days[i], font);
                        e.Graphics.DrawString(days[i], font, textBrush, x - textSize.Width / 2, chartY + chartHeight + 20);
                    }
                }
                
                // رسم المنطقة تحت الخط
                areaPoints[salesData.Length] = new Point(chartX + chartWidth, chartY + chartHeight);
                areaPoints[salesData.Length + 1] = new Point(chartX, chartY + chartHeight);
                e.Graphics.FillPolygon(areaBrush, areaPoints);
                
                // رسم الخط
                if (points.Length > 1)
                {
                    e.Graphics.DrawLines(linePen, points);
                }
            }
        }

        private Panel CreateRecentInvoicesPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(750, 360);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "📋 الفواتير الأخيرة";
            titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(30, 25);
            titleLabel.Size = new Size(300, 40);

            var listView = new ListView();
            listView.Location = new Point(30, 75);
            listView.Size = new Size(690, 265);
            listView.View = View.Details;
            listView.FullRowSelect = true;
            listView.GridLines = true;
            listView.HeaderStyle = ColumnHeaderStyle.Nonclickable;
            listView.Font = new Font("Segoe UI", 11F);

            listView.Columns.Add("رقم الفاتورة", 140);
            listView.Columns.Add("العميل", 200);
            listView.Columns.Add("المبلغ", 130);
            listView.Columns.Add("الحالة", 130);
            listView.Columns.Add("التاريخ", 90);

            var recentInvoices = DataService.Invoices.OrderByDescending(i => i.InvoiceDate).Take(12);
            foreach (var invoice in recentInvoices)
            {
                var customer = DataService.Customers.FirstOrDefault(c => c.Id == invoice.CustomerId);
                var item = new ListViewItem(invoice.InvoiceNumber);
                item.SubItems.Add(customer?.CustomerName ?? "غير محدد");
                item.SubItems.Add(invoice.TotalAmount.ToString("N0"));
                item.SubItems.Add(invoice.Status);
                item.SubItems.Add(invoice.InvoiceDate.ToString("MM/dd"));

                // تلوين حسب الحالة
                if (invoice.Status == "مدفوعة")
                    item.BackColor = Color.FromArgb(212, 237, 218);
                else if (invoice.Status == "متأخرة")
                    item.BackColor = Color.FromArgb(248, 215, 218);
                else if (invoice.Status == "مدفوعة جزئياً")
                    item.BackColor = Color.FromArgb(255, 243, 205);

                listView.Items.Add(item);
            }

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(listView);
            return panel;
        }

        private Panel CreateTopCustomersPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(750, 300);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "🏆 أعلى العملاء رصيداً";
            titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(30, 25);
            titleLabel.Size = new Size(300, 40);

            var topCustomers = DataService.Customers.OrderByDescending(c => c.CurrentBalance).Take(7);
            int yPos = 80;
            int rank = 1;

            foreach (var customer in topCustomers)
            {
                var customerPanel = new Panel();
                customerPanel.Location = new Point(30, yPos);
                customerPanel.Size = new Size(690, 30);
                customerPanel.BackColor = Color.FromArgb(248, 249, 250);

                var rankLabel = new Label();
                rankLabel.Text = rank.ToString();
                rankLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
                rankLabel.ForeColor = Color.FromArgb(52, 152, 219);
                rankLabel.Location = new Point(15, 5);
                rankLabel.Size = new Size(40, 25);
                rankLabel.TextAlign = ContentAlignment.MiddleCenter;

                var nameLabel = new Label();
                nameLabel.Text = customer.CustomerName;
                nameLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                nameLabel.ForeColor = Color.FromArgb(52, 73, 94);
                nameLabel.Location = new Point(70, 5);
                nameLabel.Size = new Size(250, 25);

                var codeLabel = new Label();
                codeLabel.Text = customer.CustomerCode;
                codeLabel.Font = new Font("Segoe UI", 10F);
                codeLabel.ForeColor = Color.FromArgb(127, 140, 141);
                codeLabel.Location = new Point(330, 5);
                codeLabel.Size = new Size(100, 25);

                var phoneLabel = new Label();
                phoneLabel.Text = customer.Phone;
                phoneLabel.Font = new Font("Segoe UI", 10F);
                phoneLabel.ForeColor = Color.FromArgb(127, 140, 141);
                phoneLabel.Location = new Point(440, 5);
                phoneLabel.Size = new Size(120, 25);

                var balanceLabel = new Label();
                balanceLabel.Text = customer.CurrentBalance.ToString("N0") + " ر.س";
                balanceLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                balanceLabel.ForeColor = customer.CurrentBalance >= 0 ? Color.FromArgb(46, 204, 113) : Color.FromArgb(231, 76, 60);
                balanceLabel.Location = new Point(570, 5);
                balanceLabel.Size = new Size(120, 25);
                balanceLabel.TextAlign = ContentAlignment.MiddleRight;

                customerPanel.Controls.Add(rankLabel);
                customerPanel.Controls.Add(nameLabel);
                customerPanel.Controls.Add(codeLabel);
                customerPanel.Controls.Add(phoneLabel);
                customerPanel.Controls.Add(balanceLabel);

                panel.Controls.Add(customerPanel);
                yPos += 35;
                rank++;
            }

            panel.Controls.Add(titleLabel);
            return panel;
        }

        private Panel CreateQuickStatsPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(750, 300);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "⚡ إحصائيات سريعة";
            titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(30, 25);
            titleLabel.Size = new Size(300, 40);

            var quickStats = new[]
            {
                new { Label = "نسبة التحصيل", Value = stats.CollectionRate.ToString("F1") + "%", Icon = "📊" },
                new { Label = "إجمالي المدفوعات اليوم", Value = stats.TodayPayments.ToString("N0") + " ر.س", Icon = "📈" },
                new { Label = "عدد الفواتير المستحقة", Value = stats.DueToday.ToString(), Icon = "⏰" },
                new { Label = "عدد العملاء الجدد", Value = stats.NewCustomersThisMonth.ToString(), Icon = "👤" },
                new { Label = "منتجات بمخزون منخفض", Value = stats.LowStockProducts.ToString(), Icon = "📦" }
            };

            int yPos = 80;
            foreach (var stat in quickStats)
            {
                var statPanel = new Panel();
                statPanel.Location = new Point(30, yPos);
                statPanel.Size = new Size(690, 40);
                statPanel.BackColor = Color.FromArgb(248, 249, 250);

                var iconLabel = new Label();
                iconLabel.Text = stat.Icon;
                iconLabel.Font = new Font("Segoe UI Emoji", 20F);
                iconLabel.Location = new Point(20, 8);
                iconLabel.Size = new Size(40, 30);

                var labelText = new Label();
                labelText.Text = stat.Label;
                labelText.Font = new Font("Segoe UI", 12F);
                labelText.ForeColor = Color.FromArgb(52, 73, 94);
                labelText.Location = new Point(80, 10);
                labelText.Size = new Size(400, 25);

                var valueText = new Label();
                valueText.Text = stat.Value;
                valueText.Font = new Font("Segoe UI", 13F, FontStyle.Bold);
                valueText.ForeColor = Color.FromArgb(52, 152, 219);
                valueText.Location = new Point(490, 8);
                valueText.Size = new Size(180, 30);
                valueText.TextAlign = ContentAlignment.MiddleRight;

                statPanel.Controls.Add(iconLabel);
                statPanel.Controls.Add(labelText);
                statPanel.Controls.Add(valueText);

                panel.Controls.Add(statPanel);
                yPos += 45;
            }

            panel.Controls.Add(titleLabel);
            return panel;
        }

        private void DrawPanelBackground(PaintEventArgs e, Panel panel)
        {
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, panel.Width, panel.Height), 15);
                panel.Region = new Region(path);
                
                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }
        }
        #endregion
    }

    /// <summary>
    /// إضافة دالة مساعدة للحواف المدورة
    /// </summary>
    public static class GraphicsPathExtensions
    {
        public static void AddRoundedRectangle(this GraphicsPath path, Rectangle rect, int radius)
        {
            int diameter = radius * 2;
            Size size = new Size(diameter, diameter);
            Rectangle arc = new Rectangle(rect.Location, size);

            // الزاوية اليسرى العلوية
            path.AddArc(arc, 180, 90);

            // الزاوية اليمنى العلوية
            arc.X = rect.Right - diameter;
            path.AddArc(arc, 270, 90);

            // الزاوية اليمنى السفلى
            arc.Y = rect.Bottom - diameter;
            path.AddArc(arc, 0, 90);

            // الزاوية اليسرى السفلى
            arc.X = rect.Left;
            path.AddArc(arc, 90, 90);

            path.CloseFigure();
        }
    }
}
