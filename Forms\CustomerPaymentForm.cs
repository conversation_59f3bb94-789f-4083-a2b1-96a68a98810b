using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AccountingSystem.Models;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج تسجيل دفعة العميل
    /// </summary>
    public partial class CustomerPaymentForm : Form
    {
        private Label lblCustomerInfo;
        private TextBox txtCustomerInfo;
        private DateTimePicker dtpPaymentDate;
        private TextBox txtAmount;
        private ComboBox cmbPaymentMethod;
        private TextBox txtReferenceNumber;
        private ComboBox cmbInvoice;
        private TextBox txtNotes;
        private CheckBox chkPartialPayment;
        private Button btnSave;
        private Button btnCancel;
        
        private Label lblPaymentDate;
        private Label lblAmount;
        private Label lblPaymentMethod;
        private Label lblReferenceNumber;
        private Label lblInvoice;
        private Label lblNotes;
        private Label lblInvoiceDetails;
        private TextBox txtInvoiceDetails;
        
        public Receipt Receipt { get; private set; }
        public int SelectedInvoiceId { get; private set; }
        
        private Customer customer;
        private List<Invoice> customerInvoices;
        
        public CustomerPaymentForm(Customer selectedCustomer, List<Invoice> invoices)
        {
            customer = selectedCustomer;
            customerInvoices = invoices.Where(i => i.Status != "مدفوع").ToList();
            Receipt = new Receipt();
            InitializeComponent();
            LoadData();
        }
        
        private void InitializeComponent()
        {
            this.lblCustomerInfo = new Label();
            this.txtCustomerInfo = new TextBox();
            this.dtpPaymentDate = new DateTimePicker();
            this.txtAmount = new TextBox();
            this.cmbPaymentMethod = new ComboBox();
            this.txtReferenceNumber = new TextBox();
            this.cmbInvoice = new ComboBox();
            this.txtNotes = new TextBox();
            this.chkPartialPayment = new CheckBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            
            this.lblPaymentDate = new Label();
            this.lblAmount = new Label();
            this.lblPaymentMethod = new Label();
            this.lblReferenceNumber = new Label();
            this.lblInvoice = new Label();
            this.lblNotes = new Label();
            this.lblInvoiceDetails = new Label();
            this.txtInvoiceDetails = new TextBox();
            
            this.SuspendLayout();
            
            // Form
            this.Text = "تسجيل دفعة العميل";
            this.Size = new Size(600, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Customer Info
            this.lblCustomerInfo.Text = "معلومات العميل:";
            this.lblCustomerInfo.Location = new Point(500, 20);
            this.lblCustomerInfo.Size = new Size(80, 23);
            this.lblCustomerInfo.TextAlign = ContentAlignment.MiddleRight;
            this.lblCustomerInfo.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            this.txtCustomerInfo.Location = new Point(50, 20);
            this.txtCustomerInfo.Size = new Size(430, 50);
            this.txtCustomerInfo.Multiline = true;
            this.txtCustomerInfo.ReadOnly = true;
            this.txtCustomerInfo.BackColor = Color.LightBlue;
            this.txtCustomerInfo.Font = new Font("Tahoma", 10F);
            
            // Payment Date
            this.lblPaymentDate.Text = "تاريخ الدفع:";
            this.lblPaymentDate.Location = new Point(500, 90);
            this.lblPaymentDate.Size = new Size(80, 23);
            this.lblPaymentDate.TextAlign = ContentAlignment.MiddleRight;
            
            this.dtpPaymentDate.Location = new Point(50, 90);
            this.dtpPaymentDate.Size = new Size(430, 23);
            this.dtpPaymentDate.Format = DateTimePickerFormat.Short;
            this.dtpPaymentDate.Value = DateTime.Now;
            
            // Invoice Selection
            this.lblInvoice.Text = "الفاتورة:";
            this.lblInvoice.Location = new Point(500, 130);
            this.lblInvoice.Size = new Size(80, 23);
            this.lblInvoice.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbInvoice.Location = new Point(50, 130);
            this.cmbInvoice.Size = new Size(430, 23);
            this.cmbInvoice.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbInvoice.SelectedIndexChanged += CmbInvoice_SelectedIndexChanged;
            
            // Invoice Details
            this.lblInvoiceDetails.Text = "تفاصيل الفاتورة:";
            this.lblInvoiceDetails.Location = new Point(500, 170);
            this.lblInvoiceDetails.Size = new Size(80, 23);
            this.lblInvoiceDetails.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtInvoiceDetails.Location = new Point(50, 170);
            this.txtInvoiceDetails.Size = new Size(430, 60);
            this.txtInvoiceDetails.Multiline = true;
            this.txtInvoiceDetails.ReadOnly = true;
            this.txtInvoiceDetails.BackColor = Color.LightYellow;
            this.txtInvoiceDetails.Font = new Font("Tahoma", 10F);
            
            // Amount
            this.lblAmount.Text = "المبلغ:";
            this.lblAmount.Location = new Point(500, 250);
            this.lblAmount.Size = new Size(80, 23);
            this.lblAmount.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtAmount.Location = new Point(50, 250);
            this.txtAmount.Size = new Size(200, 23);
            this.txtAmount.TextAlign = HorizontalAlignment.Right;
            this.txtAmount.Font = new Font("Tahoma", 10F);
            this.txtAmount.TextChanged += TxtAmount_TextChanged;
            
            // Partial Payment Checkbox
            this.chkPartialPayment.Text = "دفعة جزئية";
            this.chkPartialPayment.Location = new Point(300, 250);
            this.chkPartialPayment.Size = new Size(100, 23);
            this.chkPartialPayment.Font = new Font("Tahoma", 10F);
            this.chkPartialPayment.CheckedChanged += ChkPartialPayment_CheckedChanged;
            
            // Payment Method
            this.lblPaymentMethod.Text = "طريقة الدفع:";
            this.lblPaymentMethod.Location = new Point(500, 290);
            this.lblPaymentMethod.Size = new Size(80, 23);
            this.lblPaymentMethod.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbPaymentMethod.Location = new Point(50, 290);
            this.cmbPaymentMethod.Size = new Size(200, 23);
            this.cmbPaymentMethod.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbPaymentMethod.Items.AddRange(new string[] { "نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان", "بطاقة مدين" });
            this.cmbPaymentMethod.SelectedIndex = 0;
            
            // Reference Number
            this.lblReferenceNumber.Text = "رقم المرجع:";
            this.lblReferenceNumber.Location = new Point(500, 330);
            this.lblReferenceNumber.Size = new Size(80, 23);
            this.lblReferenceNumber.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtReferenceNumber.Location = new Point(50, 330);
            this.txtReferenceNumber.Size = new Size(200, 23);
            this.txtReferenceNumber.Font = new Font("Tahoma", 10F);
            
            // Notes
            this.lblNotes.Text = "ملاحظات:";
            this.lblNotes.Location = new Point(500, 370);
            this.lblNotes.Size = new Size(80, 23);
            this.lblNotes.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtNotes.Location = new Point(50, 370);
            this.txtNotes.Size = new Size(430, 80);
            this.txtNotes.Multiline = true;
            this.txtNotes.ScrollBars = ScrollBars.Vertical;
            this.txtNotes.Font = new Font("Tahoma", 10F);
            
            // Save Button
            this.btnSave.Text = "حفظ الدفعة";
            this.btnSave.Location = new Point(380, 480);
            this.btnSave.Size = new Size(120, 35);
            this.btnSave.BackColor = Color.FromArgb(76, 175, 80);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            this.btnSave.Click += BtnSave_Click;
            
            // Cancel Button
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.Location = new Point(230, 480);
            this.btnCancel.Size = new Size(120, 35);
            this.btnCancel.BackColor = Color.FromArgb(244, 67, 54);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Tahoma", 10F);
            this.btnCancel.Click += BtnCancel_Click;
            
            // Add controls to form
            this.Controls.Add(this.lblCustomerInfo);
            this.Controls.Add(this.txtCustomerInfo);
            this.Controls.Add(this.lblPaymentDate);
            this.Controls.Add(this.dtpPaymentDate);
            this.Controls.Add(this.lblInvoice);
            this.Controls.Add(this.cmbInvoice);
            this.Controls.Add(this.lblInvoiceDetails);
            this.Controls.Add(this.txtInvoiceDetails);
            this.Controls.Add(this.lblAmount);
            this.Controls.Add(this.txtAmount);
            this.Controls.Add(this.chkPartialPayment);
            this.Controls.Add(this.lblPaymentMethod);
            this.Controls.Add(this.cmbPaymentMethod);
            this.Controls.Add(this.lblReferenceNumber);
            this.Controls.Add(this.txtReferenceNumber);
            this.Controls.Add(this.lblNotes);
            this.Controls.Add(this.txtNotes);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnCancel);
            
            this.ResumeLayout(false);
            
            // Set default button
            this.AcceptButton = this.btnSave;
            this.CancelButton = this.btnCancel;
        }
        
        private void LoadData()
        {
            // عرض معلومات العميل
            txtCustomerInfo.Text = $"كود العميل: {customer.CustomerCode} - اسم العميل: {customer.CustomerName}\r\n" +
                                  $"الرصيد الحالي: {customer.CurrentBalance:N2} ريال";
            
            // تحميل الفواتير غير المدفوعة
            cmbInvoice.Items.Clear();
            cmbInvoice.Items.Add("دفعة عامة (غير مرتبطة بفاتورة)");
            
            foreach (var invoice in customerInvoices.OrderByDescending(i => i.InvoiceDate))
            {
                var remainingAmount = invoice.TotalAmount - invoice.PaidAmount;
                cmbInvoice.Items.Add($"{invoice.InvoiceNumber} - {remainingAmount:N2} ريال ({invoice.Status})");
            }
            
            if (cmbInvoice.Items.Count > 0)
                cmbInvoice.SelectedIndex = 0;
        }
        
        private void CmbInvoice_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbInvoice.SelectedIndex > 0)
            {
                var selectedInvoice = customerInvoices[cmbInvoice.SelectedIndex - 1];
                var remainingAmount = selectedInvoice.TotalAmount - selectedInvoice.PaidAmount;
                
                txtInvoiceDetails.Text = $"رقم الفاتورة: {selectedInvoice.InvoiceNumber}\r\n" +
                                        $"تاريخ الفاتورة: {selectedInvoice.InvoiceDate:yyyy/MM/dd}\r\n" +
                                        $"تاريخ الاستحقاق: {selectedInvoice.DueDate:yyyy/MM/dd}\r\n" +
                                        $"إجمالي الفاتورة: {selectedInvoice.TotalAmount:N2} ريال\r\n" +
                                        $"المبلغ المدفوع: {selectedInvoice.PaidAmount:N2} ريال\r\n" +
                                        $"المبلغ المتبقي: {remainingAmount:N2} ريال";
                
                // تعيين المبلغ المتبقي كقيمة افتراضية
                if (!chkPartialPayment.Checked)
                {
                    txtAmount.Text = remainingAmount.ToString("F2");
                }
                
                SelectedInvoiceId = selectedInvoice.Id;
            }
            else
            {
                txtInvoiceDetails.Text = "دفعة عامة غير مرتبطة بفاتورة محددة";
                txtAmount.Text = "";
                SelectedInvoiceId = 0;
            }
        }
        
        private void ChkPartialPayment_CheckedChanged(object sender, EventArgs e)
        {
            if (chkPartialPayment.Checked)
            {
                txtAmount.Text = "";
                txtAmount.Focus();
            }
            else if (cmbInvoice.SelectedIndex > 0)
            {
                var selectedInvoice = customerInvoices[cmbInvoice.SelectedIndex - 1];
                var remainingAmount = selectedInvoice.TotalAmount - selectedInvoice.PaidAmount;
                txtAmount.Text = remainingAmount.ToString("F2");
            }
        }
        
        private void TxtAmount_TextChanged(object sender, EventArgs e)
        {
            // التحقق من صحة المبلغ المدخل
            if (decimal.TryParse(txtAmount.Text, out decimal amount) && cmbInvoice.SelectedIndex > 0)
            {
                var selectedInvoice = customerInvoices[cmbInvoice.SelectedIndex - 1];
                var remainingAmount = selectedInvoice.TotalAmount - selectedInvoice.PaidAmount;
                
                if (amount > remainingAmount)
                {
                    txtAmount.BackColor = Color.LightCoral;
                }
                else
                {
                    txtAmount.BackColor = Color.White;
                }
            }
            else
            {
                txtAmount.BackColor = Color.White;
            }
        }
        
        private bool ValidateInput()
        {
            if (!decimal.TryParse(txtAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAmount.Focus();
                return false;
            }
            
            if (cmbInvoice.SelectedIndex > 0)
            {
                var selectedInvoice = customerInvoices[cmbInvoice.SelectedIndex - 1];
                var remainingAmount = selectedInvoice.TotalAmount - selectedInvoice.PaidAmount;
                
                if (amount > remainingAmount)
                {
                    MessageBox.Show($"المبلغ المدخل أكبر من المبلغ المتبقي ({remainingAmount:N2} ريال)", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtAmount.Focus();
                    return false;
                }
            }
            
            if (cmbPaymentMethod.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار طريقة الدفع", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbPaymentMethod.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(txtReferenceNumber.Text))
            {
                MessageBox.Show("يرجى إدخال رقم المرجع", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtReferenceNumber.Focus();
                return false;
            }
            
            return true;
        }
        
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;
                
                Receipt.ReceiptDate = dtpPaymentDate.Value;
                Receipt.CustomerId = customer.Id;
                Receipt.Amount = decimal.Parse(txtAmount.Text);
                Receipt.PaymentMethod = cmbPaymentMethod.SelectedItem.ToString();
                Receipt.ReferenceNumber = txtReferenceNumber.Text.Trim();
                Receipt.Status = "مؤكد";
                Receipt.CreatedDate = DateTime.Now;
                Receipt.CreatedBy = Services.AuthenticationService.CurrentUser?.Username ?? "System";
                
                // إعداد الملاحظات
                string notes = txtNotes.Text.Trim();
                if (cmbInvoice.SelectedIndex > 0)
                {
                    var selectedInvoice = customerInvoices[cmbInvoice.SelectedIndex - 1];
                    notes = $"دفعة من فاتورة {selectedInvoice.InvoiceNumber}";
                    if (!string.IsNullOrEmpty(txtNotes.Text.Trim()))
                    {
                        notes += " - " + txtNotes.Text.Trim();
                    }
                }
                else
                {
                    notes = "دفعة عامة" + (string.IsNullOrEmpty(notes) ? "" : " - " + notes);
                }
                Receipt.Notes = notes;
                
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الدفعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
