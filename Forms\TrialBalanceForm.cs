using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AccountingSystem.Services;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج تقرير ميزان المراجعة
    /// </summary>
    public partial class TrialBalanceForm : Form
    {
        private Panel panelHeader;
        private Label lblAsOfDate;
        private DateTimePicker dtpAsOfDate;
        private Button btnGenerate;
        private Button btnPrint;
        private Button btnExport;
        private Button btnClose;
        
        private DataGridView dgvTrialBalance;
        
        private Panel panelSummary;
        private Label lblTotalDebits;
        private Label lblTotalCredits;
        private Label lblDifference;
        private TextBox txtTotalDebits;
        private TextBox txtTotalCredits;
        private TextBox txtDifference;
        
        private List<TrialBalanceItem> trialBalanceItems;
        
        public TrialBalanceForm()
        {
            InitializeComponent();
            dtpAsOfDate.Value = DateTime.Now;
            GenerateTrialBalance();
        }
        
        private void InitializeComponent()
        {
            this.panelHeader = new Panel();
            this.lblAsOfDate = new Label();
            this.dtpAsOfDate = new DateTimePicker();
            this.btnGenerate = new Button();
            this.btnPrint = new Button();
            this.btnExport = new Button();
            this.btnClose = new Button();
            
            this.dgvTrialBalance = new DataGridView();
            
            this.panelSummary = new Panel();
            this.lblTotalDebits = new Label();
            this.lblTotalCredits = new Label();
            this.lblDifference = new Label();
            this.txtTotalDebits = new TextBox();
            this.txtTotalCredits = new TextBox();
            this.txtDifference = new TextBox();
            
            this.SuspendLayout();
            
            // Form
            this.Text = "ميزان المراجعة";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Header Panel
            this.panelHeader.Location = new Point(12, 12);
            this.panelHeader.Size = new Size(976, 60);
            this.panelHeader.BorderStyle = BorderStyle.FixedSingle;
            
            // Header Controls
            this.lblAsOfDate.Text = "كما في تاريخ:";
            this.lblAsOfDate.Location = new Point(850, 20);
            this.lblAsOfDate.Size = new Size(100, 23);
            this.lblAsOfDate.TextAlign = ContentAlignment.MiddleRight;
            
            this.dtpAsOfDate.Location = new Point(650, 20);
            this.dtpAsOfDate.Size = new Size(180, 23);
            this.dtpAsOfDate.Format = DateTimePickerFormat.Short;
            
            this.btnGenerate.Text = "إنشاء التقرير";
            this.btnGenerate.Location = new Point(520, 18);
            this.btnGenerate.Size = new Size(100, 27);
            this.btnGenerate.BackColor = Color.FromArgb(25, 118, 210);
            this.btnGenerate.ForeColor = Color.White;
            this.btnGenerate.FlatStyle = FlatStyle.Flat;
            this.btnGenerate.Click += BtnGenerate_Click;
            
            this.btnPrint.Text = "طباعة";
            this.btnPrint.Location = new Point(400, 18);
            this.btnPrint.Size = new Size(100, 27);
            this.btnPrint.BackColor = Color.FromArgb(103, 58, 183);
            this.btnPrint.ForeColor = Color.White;
            this.btnPrint.FlatStyle = FlatStyle.Flat;
            this.btnPrint.Click += BtnPrint_Click;
            
            this.btnExport.Text = "تصدير";
            this.btnExport.Location = new Point(280, 18);
            this.btnExport.Size = new Size(100, 27);
            this.btnExport.BackColor = Color.FromArgb(76, 175, 80);
            this.btnExport.ForeColor = Color.White;
            this.btnExport.FlatStyle = FlatStyle.Flat;
            this.btnExport.Click += BtnExport_Click;
            
            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(20, 18);
            this.btnClose.Size = new Size(100, 27);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Click += BtnClose_Click;
            
            // Add controls to header panel
            this.panelHeader.Controls.AddRange(new Control[] {
                this.lblAsOfDate, this.dtpAsOfDate,
                this.btnGenerate, this.btnPrint, this.btnExport, this.btnClose
            });
            
            // DataGridView
            this.dgvTrialBalance.Location = new Point(12, 90);
            this.dgvTrialBalance.Size = new Size(976, 480);
            this.dgvTrialBalance.AllowUserToAddRows = false;
            this.dgvTrialBalance.AllowUserToDeleteRows = false;
            this.dgvTrialBalance.ReadOnly = true;
            this.dgvTrialBalance.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvTrialBalance.RightToLeft = RightToLeft.Yes;
            this.dgvTrialBalance.Font = new Font("Tahoma", 10F);
            
            SetupDataGridView();
            
            // Summary Panel
            this.panelSummary.Location = new Point(12, 590);
            this.panelSummary.Size = new Size(976, 60);
            this.panelSummary.BorderStyle = BorderStyle.FixedSingle;
            
            this.lblTotalDebits.Text = "إجمالي المدين:";
            this.lblTotalDebits.Location = new Point(850, 20);
            this.lblTotalDebits.Size = new Size(100, 23);
            this.lblTotalDebits.TextAlign = ContentAlignment.MiddleRight;
            this.lblTotalDebits.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            this.txtTotalDebits.Location = new Point(720, 20);
            this.txtTotalDebits.Size = new Size(120, 23);
            this.txtTotalDebits.ReadOnly = true;
            this.txtTotalDebits.BackColor = Color.LightGray;
            this.txtTotalDebits.TextAlign = HorizontalAlignment.Right;
            this.txtTotalDebits.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            this.lblTotalCredits.Text = "إجمالي الدائن:";
            this.lblTotalCredits.Location = new Point(580, 20);
            this.lblTotalCredits.Size = new Size(100, 23);
            this.lblTotalCredits.TextAlign = ContentAlignment.MiddleRight;
            this.lblTotalCredits.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            this.txtTotalCredits.Location = new Point(450, 20);
            this.txtTotalCredits.Size = new Size(120, 23);
            this.txtTotalCredits.ReadOnly = true;
            this.txtTotalCredits.BackColor = Color.LightGray;
            this.txtTotalCredits.TextAlign = HorizontalAlignment.Right;
            this.txtTotalCredits.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            this.lblDifference.Text = "الفرق:";
            this.lblDifference.Location = new Point(380, 20);
            this.lblDifference.Size = new Size(60, 23);
            this.lblDifference.TextAlign = ContentAlignment.MiddleRight;
            this.lblDifference.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            this.txtDifference.Location = new Point(250, 20);
            this.txtDifference.Size = new Size(120, 23);
            this.txtDifference.ReadOnly = true;
            this.txtDifference.BackColor = Color.LightYellow;
            this.txtDifference.TextAlign = HorizontalAlignment.Right;
            this.txtDifference.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            
            // Add controls to summary panel
            this.panelSummary.Controls.AddRange(new Control[] {
                this.lblTotalDebits, this.txtTotalDebits,
                this.lblTotalCredits, this.txtTotalCredits,
                this.lblDifference, this.txtDifference
            });
            
            // Add controls to form
            this.Controls.Add(this.panelHeader);
            this.Controls.Add(this.dgvTrialBalance);
            this.Controls.Add(this.panelSummary);
            
            this.ResumeLayout(false);
        }
        
        private void SetupDataGridView()
        {
            dgvTrialBalance.Columns.Clear();
            
            dgvTrialBalance.Columns.Add("AccountCode", "كود الحساب");
            dgvTrialBalance.Columns["AccountCode"].Width = 120;
            
            dgvTrialBalance.Columns.Add("AccountName", "اسم الحساب");
            dgvTrialBalance.Columns["AccountName"].Width = 300;
            
            dgvTrialBalance.Columns.Add("AccountType", "نوع الحساب");
            dgvTrialBalance.Columns["AccountType"].Width = 120;
            
            dgvTrialBalance.Columns.Add("DebitBalance", "رصيد مدين");
            dgvTrialBalance.Columns["DebitBalance"].Width = 150;
            dgvTrialBalance.Columns["DebitBalance"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvTrialBalance.Columns["DebitBalance"].DefaultCellStyle.Format = "N2";
            
            dgvTrialBalance.Columns.Add("CreditBalance", "رصيد دائن");
            dgvTrialBalance.Columns["CreditBalance"].Width = 150;
            dgvTrialBalance.Columns["CreditBalance"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvTrialBalance.Columns["CreditBalance"].DefaultCellStyle.Format = "N2";
        }
        
        private void GenerateTrialBalance()
        {
            try
            {
                trialBalanceItems = AccountService.GetTrialBalance(dtpAsOfDate.Value);
                RefreshDataGridView();
                CalculateTotals();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء ميزان المراجعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                trialBalanceItems = new List<TrialBalanceItem>();
            }
        }
        
        private void RefreshDataGridView()
        {
            dgvTrialBalance.Rows.Clear();
            
            if (trialBalanceItems == null)
                return;
            
            foreach (var item in trialBalanceItems.OrderBy(i => i.AccountCode))
            {
                var rowIndex = dgvTrialBalance.Rows.Add();
                var row = dgvTrialBalance.Rows[rowIndex];
                
                row.Cells["AccountCode"].Value = item.AccountCode;
                row.Cells["AccountName"].Value = item.AccountName;
                row.Cells["AccountType"].Value = item.AccountType;
                row.Cells["DebitBalance"].Value = item.DebitBalance > 0 ? item.DebitBalance : (object)DBNull.Value;
                row.Cells["CreditBalance"].Value = item.CreditBalance > 0 ? item.CreditBalance : (object)DBNull.Value;
                
                // تلوين الصفوف حسب نوع الحساب
                switch (item.AccountType)
                {
                    case "أصول":
                        row.DefaultCellStyle.BackColor = Color.LightBlue;
                        break;
                    case "خصوم":
                        row.DefaultCellStyle.BackColor = Color.LightCoral;
                        break;
                    case "حقوق ملكية":
                        row.DefaultCellStyle.BackColor = Color.LightGreen;
                        break;
                    case "إيرادات":
                        row.DefaultCellStyle.BackColor = Color.LightGoldenrodYellow;
                        break;
                    case "مصروفات":
                        row.DefaultCellStyle.BackColor = Color.LightPink;
                        break;
                }
            }
        }
        
        private void CalculateTotals()
        {
            if (trialBalanceItems == null)
                return;
            
            decimal totalDebits = trialBalanceItems.Sum(i => i.DebitBalance);
            decimal totalCredits = trialBalanceItems.Sum(i => i.CreditBalance);
            decimal difference = totalDebits - totalCredits;
            
            txtTotalDebits.Text = totalDebits.ToString("N2");
            txtTotalCredits.Text = totalCredits.ToString("N2");
            txtDifference.Text = difference.ToString("N2");
            
            // تلوين الفرق
            if (Math.Abs(difference) < 0.01m)
            {
                txtDifference.BackColor = Color.LightGreen;
            }
            else
            {
                txtDifference.BackColor = Color.LightCoral;
            }
        }
        
        private void BtnGenerate_Click(object sender, EventArgs e)
        {
            GenerateTrialBalance();
        }
        
        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (trialBalanceItems == null || !trialBalanceItems.Any())
                {
                    MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                var reportGenerator = new Utils.ReportGenerator();
                PrintTrialBalance(reportGenerator);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void PrintTrialBalance(Utils.ReportGenerator reportGenerator)
        {
            var reportLines = new List<string>();
            
            // عنوان التقرير
            reportLines.Add("ميزان المراجعة");
            reportLines.Add("===============");
            reportLines.Add($"كما في تاريخ: {dtpAsOfDate.Value:yyyy/MM/dd}");
            reportLines.Add($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
            reportLines.Add("");
            
            // رأس الجدول
            reportLines.Add("كود الحساب    اسم الحساب                    نوع الحساب      رصيد مدين      رصيد دائن");
            reportLines.Add("================================================================================");
            
            // البيانات
            foreach (var item in trialBalanceItems.OrderBy(i => i.AccountCode))
            {
                string debitBalance = item.DebitBalance > 0 ? item.DebitBalance.ToString("N2") : "";
                string creditBalance = item.CreditBalance > 0 ? item.CreditBalance.ToString("N2") : "";
                
                string line = $"{item.AccountCode.PadRight(12)} {item.AccountName.PadRight(30)} " +
                             $"{item.AccountType.PadRight(15)} {debitBalance.PadLeft(12)} {creditBalance.PadLeft(12)}";
                reportLines.Add(line);
            }
            
            reportLines.Add("================================================================================");
            
            // الإجماليات
            decimal totalDebits = trialBalanceItems.Sum(i => i.DebitBalance);
            decimal totalCredits = trialBalanceItems.Sum(i => i.CreditBalance);
            
            reportLines.Add($"{"الإجمالي".PadRight(59)} {totalDebits.ToString("N2").PadLeft(12)} {totalCredits.ToString("N2").PadLeft(12)}");
            reportLines.Add($"الفرق: {(totalDebits - totalCredits):N2}");
            
            // حفظ التقرير مؤقتاً وطباعته
            string tempFile = System.IO.Path.GetTempFileName() + ".txt";
            System.IO.File.WriteAllLines(tempFile, reportLines, System.Text.Encoding.UTF8);
            
            try
            {
                System.Diagnostics.Process.Start("notepad.exe", "/p " + tempFile);
            }
            catch
            {
                MessageBox.Show($"تم حفظ التقرير في: {tempFile}", "تقرير ميزان المراجعة", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        
        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                if (trialBalanceItems == null || !trialBalanceItems.Any())
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "ملفات CSV (*.csv)|*.csv|ملفات نصية (*.txt)|*.txt";
                saveDialog.Title = "تصدير ميزان المراجعة";
                saveDialog.FileName = $"ميزان_المراجعة_{dtpAsOfDate.Value:yyyyMMdd}.csv";
                
                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToCSV(saveDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void ExportToCSV(string filePath)
        {
            var lines = new List<string>();
            
            // رأس الملف
            lines.Add("كود الحساب,اسم الحساب,نوع الحساب,رصيد مدين,رصيد دائن");
            
            // البيانات
            foreach (var item in trialBalanceItems.OrderBy(i => i.AccountCode))
            {
                lines.Add($"{item.AccountCode},{item.AccountName},{item.AccountType}," +
                         $"{item.DebitBalance:N2},{item.CreditBalance:N2}");
            }
            
            // الإجماليات
            decimal totalDebits = trialBalanceItems.Sum(i => i.DebitBalance);
            decimal totalCredits = trialBalanceItems.Sum(i => i.CreditBalance);
            lines.Add($"الإجمالي,,,{totalDebits:N2},{totalCredits:N2}");
            
            System.IO.File.WriteAllLines(filePath, lines, System.Text.Encoding.UTF8);
            
            MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{filePath}", "نجح التصدير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
