using System;
using System.Linq;
using IntegratedInvoiceSystem.Models;

namespace IntegratedInvoiceSystem.Services
{
    /// <summary>
    /// خدمة المصادقة وإدارة المستخدمين
    /// </summary>
    public static class AuthService
    {
        /// <summary>
        /// المستخدم الحالي المسجل دخوله
        /// </summary>
        public static User CurrentUser { get; private set; }

        /// <summary>
        /// اسم المستخدم الحالي
        /// </summary>
        public static string CurrentUserName => CurrentUser?.FullName ?? "غير محدد";

        /// <summary>
        /// اسم المستخدم للنظام
        /// </summary>
        public static string CurrentUsername => CurrentUser?.Username ?? "system";

        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>true إذا نجح تسجيل الدخول، false إذا فشل</returns>
        public static bool Login(string username, string password)
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                return false;

            var user = DataService.Users.FirstOrDefault(u => 
                u.Username.Equals(username, StringComparison.OrdinalIgnoreCase) && 
                u.Password == password && 
                u.IsActive);

            if (user != null)
            {
                CurrentUser = user;
                user.LastLogin = DateTime.Now;
                
                // تسجيل عملية تسجيل الدخول
                LogLoginAttempt(username, true, "تسجيل دخول ناجح");
                
                return true;
            }

            // تسجيل محاولة تسجيل دخول فاشلة
            LogLoginAttempt(username, false, "اسم المستخدم أو كلمة المرور غير صحيحة");
            
            return false;
        }

        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public static void Logout()
        {
            if (CurrentUser != null)
            {
                LogLoginAttempt(CurrentUser.Username, true, "تسجيل خروج");
                CurrentUser = null;
            }
        }

        /// <summary>
        /// التحقق من وجود صلاحية معينة للمستخدم الحالي
        /// </summary>
        /// <param name="permission">اسم الصلاحية</param>
        /// <returns>true إذا كان المستخدم يملك الصلاحية، false إذا لم يملكها</returns>
        public static bool HasPermission(string permission)
        {
            if (CurrentUser == null)
                return false;

            if (string.IsNullOrWhiteSpace(permission))
                return true;

            // المدير له جميع الصلاحيات
            if (IsAdmin())
                return true;

            return CurrentUser.Permissions.Contains(permission);
        }

        /// <summary>
        /// التحقق من كون المستخدم الحالي مدير
        /// </summary>
        /// <returns>true إذا كان المستخدم مدير، false إذا لم يكن</returns>
        public static bool IsAdmin()
        {
            return CurrentUser?.Role == "مدير";
        }

        /// <summary>
        /// التحقق من كون المستخدم الحالي موظف
        /// </summary>
        /// <returns>true إذا كان المستخدم موظف، false إذا لم يكن</returns>
        public static bool IsEmployee()
        {
            return CurrentUser?.Role == "موظف";
        }

        /// <summary>
        /// الحصول على قائمة بجميع الصلاحيات المتاحة
        /// </summary>
        /// <returns>قائمة بأسماء الصلاحيات</returns>
        public static string[] GetAllPermissions()
        {
            return new string[]
            {
                "إدارة الفواتير",
                "إدارة العملاء", 
                "إدارة المنتجات",
                "التقارير",
                "الإعدادات",
                "إدارة المستخدمين",
                "النسخ الاحتياطي",
                "عرض العمليات"
            };
        }

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        /// <param name="user">بيانات المستخدم الجديد</param>
        /// <returns>true إذا تم إضافة المستخدم بنجاح، false إذا فشلت العملية</returns>
        public static bool AddUser(User user)
        {
            if (!IsAdmin())
                return false;

            if (string.IsNullOrWhiteSpace(user.Username) || string.IsNullOrWhiteSpace(user.Password))
                return false;

            // التحقق من عدم وجود مستخدم بنفس الاسم
            if (DataService.Users.Any(u => u.Username.Equals(user.Username, StringComparison.OrdinalIgnoreCase)))
                return false;

            user.Id = DataService.Users.Count > 0 ? DataService.Users.Max(u => u.Id) + 1 : 1;
            DataService.Users.Add(user);

            return true;
        }

        /// <summary>
        /// تحديث بيانات مستخدم
        /// </summary>
        /// <param name="user">بيانات المستخدم المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح، false إذا فشلت العملية</returns>
        public static bool UpdateUser(User user)
        {
            if (!IsAdmin())
                return false;

            var existingUser = DataService.Users.FirstOrDefault(u => u.Id == user.Id);
            if (existingUser == null)
                return false;

            // التحقق من عدم وجود مستخدم آخر بنفس الاسم
            if (DataService.Users.Any(u => u.Id != user.Id && u.Username.Equals(user.Username, StringComparison.OrdinalIgnoreCase)))
                return false;

            var index = DataService.Users.IndexOf(existingUser);
            DataService.Users[index] = user;

            return true;
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>true إذا تم الحذف بنجاح، false إذا فشلت العملية</returns>
        public static bool DeleteUser(int userId)
        {
            if (!IsAdmin())
                return false;

            // لا يمكن حذف المستخدم الحالي
            if (CurrentUser?.Id == userId)
                return false;

            var user = DataService.Users.FirstOrDefault(u => u.Id == userId);
            if (user == null)
                return false;

            DataService.Users.Remove(user);
            return true;
        }

        /// <summary>
        /// تغيير كلمة مرور المستخدم الحالي
        /// </summary>
        /// <param name="oldPassword">كلمة المرور القديمة</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>true إذا تم تغيير كلمة المرور بنجاح، false إذا فشلت العملية</returns>
        public static bool ChangePassword(string oldPassword, string newPassword)
        {
            if (CurrentUser == null)
                return false;

            if (CurrentUser.Password != oldPassword)
                return false;

            if (string.IsNullOrWhiteSpace(newPassword))
                return false;

            CurrentUser.Password = newPassword;
            return true;
        }

        /// <summary>
        /// إعادة تعيين كلمة مرور مستخدم (للمدير فقط)
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>true إذا تم إعادة التعيين بنجاح، false إذا فشلت العملية</returns>
        public static bool ResetPassword(int userId, string newPassword)
        {
            if (!IsAdmin())
                return false;

            var user = DataService.Users.FirstOrDefault(u => u.Id == userId);
            if (user == null)
                return false;

            if (string.IsNullOrWhiteSpace(newPassword))
                return false;

            user.Password = newPassword;
            return true;
        }

        /// <summary>
        /// تفعيل أو إلغاء تفعيل مستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="isActive">حالة التفعيل</param>
        /// <returns>true إذا تم تغيير الحالة بنجاح، false إذا فشلت العملية</returns>
        public static bool SetUserActiveStatus(int userId, bool isActive)
        {
            if (!IsAdmin())
                return false;

            // لا يمكن إلغاء تفعيل المستخدم الحالي
            if (CurrentUser?.Id == userId && !isActive)
                return false;

            var user = DataService.Users.FirstOrDefault(u => u.Id == userId);
            if (user == null)
                return false;

            user.IsActive = isActive;
            return true;
        }

        /// <summary>
        /// الحصول على قائمة المستخدمين النشطين
        /// </summary>
        /// <returns>قائمة المستخدمين النشطين</returns>
        public static User[] GetActiveUsers()
        {
            return DataService.Users.Where(u => u.IsActive).ToArray();
        }

        /// <summary>
        /// الحصول على قائمة جميع المستخدمين
        /// </summary>
        /// <returns>قائمة جميع المستخدمين</returns>
        public static User[] GetAllUsers()
        {
            if (!IsAdmin())
                return new User[0];

            return DataService.Users.ToArray();
        }

        /// <summary>
        /// تسجيل محاولة تسجيل الدخول
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="isSuccessful">نجح أم فشل</param>
        /// <param name="details">تفاصيل إضافية</param>
        private static void LogLoginAttempt(string username, bool isSuccessful, string details)
        {
            var operation = new Operation
            {
                Id = DataService.Operations.Count + 1,
                Type = "Login",
                EntityType = "User",
                EntityId = 0,
                Description = $"محاولة تسجيل دخول للمستخدم: {username}",
                Timestamp = DateTime.Now,
                UserId = username,
                UserName = username,
                Details = details,
                IsSuccessful = isSuccessful
            };

            DataService.Operations.Add(operation);
        }

        /// <summary>
        /// التحقق من صحة بيانات المستخدم
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <returns>رسالة الخطأ إن وجدت، أو null إذا كانت البيانات صحيحة</returns>
        public static string ValidateUser(User user)
        {
            if (string.IsNullOrWhiteSpace(user.Username))
                return "اسم المستخدم مطلوب";

            if (user.Username.Length < 3)
                return "اسم المستخدم يجب أن يكون 3 أحرف على الأقل";

            if (string.IsNullOrWhiteSpace(user.Password))
                return "كلمة المرور مطلوبة";

            if (user.Password.Length < 6)
                return "كلمة المرور يجب أن تكون 6 أحرف على الأقل";

            if (string.IsNullOrWhiteSpace(user.FullName))
                return "الاسم الكامل مطلوب";

            if (string.IsNullOrWhiteSpace(user.Role))
                return "الدور مطلوب";

            return null;
        }
    }
}
