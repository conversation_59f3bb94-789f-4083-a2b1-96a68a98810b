using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using AccountingSystem.Data;
using AccountingSystem.Models;

namespace AccountingSystem.Services
{
    /// <summary>
    /// خدمة التحليل بالذكاء الاصطناعي
    /// </summary>
    public class AIAnalysisService
    {
        /// <summary>
        /// تحليل الأداء المالي
        /// </summary>
        /// <returns>تقرير تحليل الأداء المالي</returns>
        public static FinancialAnalysisReport AnalyzeFinancialPerformance()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    var report = new FinancialAnalysisReport();
                    
                    // تحليل الإيرادات والمصروفات
                    var revenueAccounts = connection.Query<Account>(@"
                        SELECT * FROM Accounts 
                        WHERE AccountType = 'إيرادات' AND IsActive = 1");
                    
                    var expenseAccounts = connection.Query<Account>(@"
                        SELECT * FROM Accounts 
                        WHERE AccountType = 'مصروفات' AND IsActive = 1");
                    
                    report.TotalRevenue = revenueAccounts.Sum(a => a.Balance);
                    report.TotalExpenses = expenseAccounts.Sum(a => a.Balance);
                    report.NetIncome = report.TotalRevenue - report.TotalExpenses;
                    
                    // تحليل الأصول والخصوم
                    var assetAccounts = connection.Query<Account>(@"
                        SELECT * FROM Accounts 
                        WHERE AccountType = 'أصول' AND IsActive = 1");
                    
                    var liabilityAccounts = connection.Query<Account>(@"
                        SELECT * FROM Accounts 
                        WHERE AccountType = 'خصوم' AND IsActive = 1");
                    
                    report.TotalAssets = assetAccounts.Sum(a => a.Balance);
                    report.TotalLiabilities = liabilityAccounts.Sum(a => a.Balance);
                    
                    // حساب النسب المالية
                    if (report.TotalRevenue > 0)
                    {
                        report.ProfitMargin = (report.NetIncome / report.TotalRevenue) * 100;
                    }
                    
                    if (report.TotalAssets > 0)
                    {
                        report.ROA = (report.NetIncome / report.TotalAssets) * 100;
                    }
                    
                    if (report.TotalLiabilities > 0)
                    {
                        report.DebtRatio = (report.TotalLiabilities / report.TotalAssets) * 100;
                    }
                    
                    // توليد التوصيات
                    report.Recommendations = GenerateRecommendations(report);
                    
                    report.AnalysisDate = DateTime.Now;
                    
                    return report;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحليل الأداء المالي: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// توليد التوصيات الذكية
        /// </summary>
        /// <param name="report">تقرير التحليل المالي</param>
        /// <returns>قائمة التوصيات</returns>
        private static List<string> GenerateRecommendations(FinancialAnalysisReport report)
        {
            var recommendations = new List<string>();
            
            // تحليل هامش الربح
            if (report.ProfitMargin < 10)
            {
                recommendations.Add("هامش الربح منخفض. يُنصح بمراجعة استراتيجية التسعير أو تقليل التكاليف.");
            }
            else if (report.ProfitMargin > 30)
            {
                recommendations.Add("هامش ربح ممتاز. يمكن الاستثمار في التوسع أو تطوير المنتجات.");
            }
            
            // تحليل نسبة الديون
            if (report.DebtRatio > 60)
            {
                recommendations.Add("نسبة الديون مرتفعة. يُنصح بتقليل الالتزامات أو زيادة الأصول.");
            }
            else if (report.DebtRatio < 30)
            {
                recommendations.Add("نسبة ديون منخفصة. يمكن الاستفادة من التمويل للنمو.");
            }
            
            // تحليل العائد على الأصول
            if (report.ROA < 5)
            {
                recommendations.Add("العائد على الأصول منخفض. يُنصح بتحسين كفاءة استخدام الأصول.");
            }
            else if (report.ROA > 15)
            {
                recommendations.Add("عائد ممتاز على الأصول. استمر في الاستراتيجية الحالية.");
            }
            
            // تحليل صافي الدخل
            if (report.NetIncome < 0)
            {
                recommendations.Add("الشركة تحقق خسائر. يجب مراجعة النموذج التجاري والتكاليف فوراً.");
            }
            
            // تحليل السيولة
            if (report.TotalAssets > 0 && report.TotalLiabilities > 0)
            {
                var currentRatio = report.TotalAssets / report.TotalLiabilities;
                if ( (float)currentRatio < 1.2)
                {
                    recommendations.Add("نسبة السيولة منخفضة. قد تواجه صعوبة في سداد الالتزامات قصيرة الأجل.");
                }
            }
            
            return recommendations;
        }
        
        /// <summary>
        /// تحليل اتجاهات المبيعات
        /// </summary>
        /// <param name="months">عدد الأشهر للتحليل</param>
        /// <returns>تقرير اتجاهات المبيعات</returns>
        public static SalesTrendReport AnalyzeSalesTrends(int months = 12)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    var report = new SalesTrendReport();
                    var startDate = DateTime.Now.AddMonths(-months);
                    
                    // تحليل الفواتير حسب الشهر
                    var monthlySales = connection.Query<MonthlySales>(@"
                        SELECT 
                            strftime('%Y-%m', InvoiceDate) as Month,
                            SUM(TotalAmount) as TotalSales,
                            COUNT(*) as InvoiceCount
                        FROM Invoices 
                        WHERE InvoiceDate >= @StartDate 
                            AND InvoiceType = 'مبيعات' 
                            AND Status != 'ملغية'
                        GROUP BY strftime('%Y-%m', InvoiceDate)
                        ORDER BY Month",
                        new { StartDate = startDate.ToString("yyyy-MM-dd") });
                    
                    report.MonthlySales = monthlySales.ToList();
                    
                    if (report.MonthlySales.Count > 1)
                    {
                        // حساب معدل النمو
                        var lastMonth = report.MonthlySales.Last().TotalSales;
                        var previousMonth = report.MonthlySales[report.MonthlySales.Count - 2].TotalSales;
                        
                        if (previousMonth > 0)
                        {
                            report.GrowthRate = ((lastMonth - previousMonth) / previousMonth) * 100;
                        }
                        
                        // تحديد الاتجاه
                        if (report.GrowthRate > 5)
                        {
                            report.Trend = "نمو";
                            report.TrendDescription = "المبيعات في نمو مستمر";
                        }
                        else if (report.GrowthRate < -5)
                        {
                            report.Trend = "انخفاض";
                            report.TrendDescription = "المبيعات في انخفاض";
                        }
                        else
                        {
                            report.Trend = "مستقر";
                            report.TrendDescription = "المبيعات مستقرة";
                        }
                    }
                    
                    report.AnalysisDate = DateTime.Now;
                    
                    return report;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحليل اتجاهات المبيعات: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// كشف الأخطاء المحاسبية المحتملة
        /// </summary>
        /// <returns>قائمة الأخطاء المحتملة</returns>
        public static List<AccountingError> DetectAccountingErrors()
        {
            var errors = new List<AccountingError>();
            
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // فحص توازن القيود اليومية
                    var unbalancedEntries = connection.Query<JournalEntry>(@"
                        SELECT * FROM JournalEntries 
                        WHERE ABS(TotalDebit - TotalCredit) > 0.01 
                            AND Status = 'مؤكد'");
                    
                    foreach (var entry in unbalancedEntries)
                    {
                        errors.Add(new AccountingError
                        {
                            Type = "قيد غير متوازن",
                            Description = $"القيد رقم {entry.EntryNumber} غير متوازن",
                            Severity = "عالي",
                            Reference = entry.EntryNumber
                        });
                    }
                    
                    // فحص الحسابات بأرصدة سالبة غير منطقية
                    var negativeAssets = connection.Query<Account>(@"
                        SELECT * FROM Accounts 
                        WHERE AccountType = 'أصول' 
                            AND Balance < 0 
                            AND IsActive = 1");
                    
                    foreach (var account in negativeAssets)
                    {
                        errors.Add(new AccountingError
                        {
                            Type = "رصيد سالب غير منطقي",
                            Description = $"حساب الأصول '{account.AccountName}' له رصيد سالب",
                            Severity = "متوسط",
                            Reference = account.AccountCode
                        });
                    }
                    
                    // فحص الفواتير المدفوعة أكثر من قيمتها
                    var overpaidInvoices = connection.Query<Invoice>(@"
                        SELECT * FROM Invoices 
                        WHERE PaidAmount > TotalAmount 
                            AND Status != 'ملغية'");
                    
                    foreach (var invoice in overpaidInvoices)
                    {
                        errors.Add(new AccountingError
                        {
                            Type = "دفع زائد",
                            Description = $"الفاتورة رقم {invoice.InvoiceNumber} مدفوعة أكثر من قيمتها",
                            Severity = "متوسط",
                            Reference = invoice.InvoiceNumber
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                errors.Add(new AccountingError
                {
                    Type = "خطأ في النظام",
                    Description = $"خطأ في فحص الأخطاء المحاسبية: {ex.Message}",
                    Severity = "عالي",
                    Reference = "SYSTEM"
                });
            }
            
            return errors;
        }
    }
    
    /// <summary>
    /// تقرير التحليل المالي
    /// </summary>
    public class FinancialAnalysisReport
    {
        public decimal TotalRevenue { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetIncome { get; set; }
        public decimal TotalAssets { get; set; }
        public decimal TotalLiabilities { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal ROA { get; set; }
        public decimal DebtRatio { get; set; }
        public List<string> Recommendations { get; set; } = new List<string>();
        public DateTime AnalysisDate { get; set; }
    }
    
    /// <summary>
    /// تقرير اتجاهات المبيعات
    /// </summary>
    public class SalesTrendReport
    {
        public List<MonthlySales> MonthlySales { get; set; } = new List<MonthlySales>();
        public decimal GrowthRate { get; set; }
        public string Trend { get; set; }
        public string TrendDescription { get; set; }
        public DateTime AnalysisDate { get; set; }
    }
    
    /// <summary>
    /// مبيعات شهرية
    /// </summary>
    public class MonthlySales
    {
        public string Month { get; set; }
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
    }
    
    /// <summary>
    /// خطأ محاسبي
    /// </summary>
    public class AccountingError
    {
        public string Type { get; set; }
        public string Description { get; set; }
        public string Severity { get; set; }
        public string Reference { get; set; }
    }
}
