using System;

namespace AccountingSystem.Models
{
    /// <summary>
    /// نموذج المنتج
    /// </summary>
    public class Product
    {
        public int Id { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string ProductNameEnglish { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public string Unit { get; set; } // وحدة القياس
        public decimal PurchasePrice { get; set; }
        public decimal SalePrice { get; set; }
        public decimal MinimumStock { get; set; }
        public decimal CurrentStock { get; set; }
        public bool IsActive { get; set; }
        public string Barcode { get; set; }
        public decimal TaxRate { get; set; }
        public int? SalesAccountId { get; set; } // حساب المبيعات
        public int? SalesReturnAccountId { get; set; } // حساب مردود المبيعات
        public int? PurchaseAccountId { get; set; } // حساب المشتريات
        public int? InventoryAccountId { get; set; } // حساب المخزون
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }
        
        // خصائص إضافية للعرض
        public Account SalesAccount { get; set; }
        public Account SalesReturnAccount { get; set; }
        public Account PurchaseAccount { get; set; }
        public Account InventoryAccount { get; set; }
    }
}
