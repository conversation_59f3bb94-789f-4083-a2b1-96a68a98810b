using System;
using System.Drawing;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Services;

namespace IntegratedInvoiceSystem.Forms
{
    /// <summary>
    /// نموذج الإعدادات العامة
    /// </summary>
    public partial class SettingsForm : Form
    {
        #region المتغيرات
        private TabControl tabControl;
        private TabPage tabGeneral, tabTax, tabPrint, tabBackup, tabNotifications;

        // إعدادات عامة
        private TextBox txtCurrency;
        private ComboBox cmbLanguage;
        private ComboBox cmbDateFormat;
        private CheckBox chkAutoSave;
        private NumericUpDown nudAutoSaveInterval;

        // إعدادات الضرائب
        private NumericUpDown nudVATRate;
        private CheckBox chkIncludeVAT;
        private TextBox txtTaxNumber;

        // إعدادات الطباعة
        private ComboBox cmbPrinter;
        private ComboBox cmbPaperSize;
        private CheckBox chkPrintLogo;
        private CheckBox chkPrintFooter;

        // إعدادات النسخ الاحتياطي
        private TextBox txtBackupPath;
        private CheckBox chkAutoBackup;
        private NumericUpDown nudBackupInterval;
        private ComboBox cmbBackupTime;

        // إعدادات التنبيهات
        private CheckBox chkOverdueNotifications;
        private CheckBox chkLowStockNotifications;
        private CheckBox chkPaymentReminders;
        private NumericUpDown nudReminderDays;

        private Button btnSave;
        private Button btnCancel;
        private Button btnReset;
        #endregion

        #region البناء والتهيئة
        public SettingsForm()
        {
            InitializeComponent();
            LoadSettings();
        }

        private void InitializeComponent()
        {
            this.Text = "الإعدادات العامة";
            this.Size = new Size(700, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
            SetupLayout();
        }

        private void CreateControls()
        {
            // إنشاء التبويبات
            tabControl = new TabControl();
            tabControl.Size = new Size(660, 500);
            tabControl.Location = new Point(20, 20);
            tabControl.Font = new Font("Segoe UI", 10F);

            // تبويب الإعدادات العامة
            tabGeneral = new TabPage("إعدادات عامة");
            CreateGeneralTab();

            // تبويب الضرائب
            tabTax = new TabPage("الضرائب");
            CreateTaxTab();

            // تبويب الطباعة
            tabPrint = new TabPage("الطباعة");
            CreatePrintTab();

            // تبويب النسخ الاحتياطي
            tabBackup = new TabPage("النسخ الاحتياطي");
            CreateBackupTab();

            // تبويب التنبيهات
            tabNotifications = new TabPage("التنبيهات");
            CreateNotificationsTab();

            tabControl.TabPages.AddRange(new TabPage[] { tabGeneral, tabTax, tabPrint, tabBackup, tabNotifications });

            // أزرار الحفظ والإلغاء
            btnSave = new Button();
            btnSave.Text = "حفظ";
            btnSave.Location = new Point(580, 540);
            btnSave.Size = new Size(100, 35);
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button();
            btnCancel.Text = "إلغاء";
            btnCancel.Location = new Point(470, 540);
            btnCancel.Size = new Size(100, 35);
            btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.Click += BtnCancel_Click;

            btnReset = new Button();
            btnReset.Text = "إعادة تعيين";
            btnReset.Location = new Point(360, 540);
            btnReset.Size = new Size(100, 35);
            btnReset.BackColor = Color.FromArgb(108, 117, 125);
            btnReset.ForeColor = Color.White;
            btnReset.FlatStyle = FlatStyle.Flat;
            btnReset.Click += BtnReset_Click;

            this.Controls.AddRange(new Control[] { tabControl, btnSave, btnCancel, btnReset });
        }

        private void CreateGeneralTab()
        {
            // العملة
            var lblCurrency = new Label();
            lblCurrency.Text = "العملة:";
            lblCurrency.Location = new Point(550, 30);
            lblCurrency.Size = new Size(80, 23);

            txtCurrency = new TextBox();
            txtCurrency.Location = new Point(400, 30);
            txtCurrency.Size = new Size(120, 23);
            txtCurrency.Text = "ريال سعودي";

            // اللغة
            var lblLanguage = new Label();
            lblLanguage.Text = "اللغة:";
            lblLanguage.Location = new Point(550, 70);
            lblLanguage.Size = new Size(80, 23);

            cmbLanguage = new ComboBox();
            cmbLanguage.Location = new Point(400, 70);
            cmbLanguage.Size = new Size(120, 23);
            cmbLanguage.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbLanguage.Items.AddRange(new string[] { "العربية", "English" });
            cmbLanguage.SelectedIndex = 0;

            // تنسيق التاريخ
            var lblDateFormat = new Label();
            lblDateFormat.Text = "تنسيق التاريخ:";
            lblDateFormat.Location = new Point(550, 110);
            lblDateFormat.Size = new Size(80, 23);

            cmbDateFormat = new ComboBox();
            cmbDateFormat.Location = new Point(400, 110);
            cmbDateFormat.Size = new Size(120, 23);
            cmbDateFormat.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbDateFormat.Items.AddRange(new string[] { "dd/MM/yyyy", "MM/dd/yyyy", "yyyy/MM/dd" });
            cmbDateFormat.SelectedIndex = 0;

            // الحفظ التلقائي
            chkAutoSave = new CheckBox();
            chkAutoSave.Text = "الحفظ التلقائي";
            chkAutoSave.Location = new Point(400, 150);
            chkAutoSave.Size = new Size(120, 23);
            chkAutoSave.Checked = true;

            var lblAutoSaveInterval = new Label();
            lblAutoSaveInterval.Text = "كل (دقيقة):";
            lblAutoSaveInterval.Location = new Point(550, 190);
            lblAutoSaveInterval.Size = new Size(80, 23);

            nudAutoSaveInterval = new NumericUpDown();
            nudAutoSaveInterval.Location = new Point(400, 190);
            nudAutoSaveInterval.Size = new Size(120, 23);
            nudAutoSaveInterval.Minimum = 1;
            nudAutoSaveInterval.Maximum = 60;
            nudAutoSaveInterval.Value = 5;

            tabGeneral.Controls.AddRange(new Control[] {
                lblCurrency, txtCurrency,
                lblLanguage, cmbLanguage,
                lblDateFormat, cmbDateFormat,
                chkAutoSave,
                lblAutoSaveInterval, nudAutoSaveInterval
            });
        }

        private void CreateTaxTab()
        {
            // معدل ضريبة القيمة المضافة
            var lblVATRate = new Label();
            lblVATRate.Text = "معدل ضريبة القيمة المضافة (%):";
            lblVATRate.Location = new Point(450, 30);
            lblVATRate.Size = new Size(180, 23);

            nudVATRate = new NumericUpDown();
            nudVATRate.Location = new Point(300, 30);
            nudVATRate.Size = new Size(120, 23);
            nudVATRate.DecimalPlaces = 2;
            nudVATRate.Minimum = 0;
            nudVATRate.Maximum = 100;
            nudVATRate.Value = 15;

            // تضمين ضريبة القيمة المضافة
            chkIncludeVAT = new CheckBox();
            chkIncludeVAT.Text = "تضمين ضريبة القيمة المضافة في الأسعار";
            chkIncludeVAT.Location = new Point(300, 70);
            chkIncludeVAT.Size = new Size(300, 23);
            chkIncludeVAT.Checked = true;

            // الرقم الضريبي
            var lblTaxNumber = new Label();
            lblTaxNumber.Text = "الرقم الضريبي:";
            lblTaxNumber.Location = new Point(550, 110);
            lblTaxNumber.Size = new Size(80, 23);

            txtTaxNumber = new TextBox();
            txtTaxNumber.Location = new Point(300, 110);
            txtTaxNumber.Size = new Size(200, 23);

            tabTax.Controls.AddRange(new Control[] {
                lblVATRate, nudVATRate,
                chkIncludeVAT,
                lblTaxNumber, txtTaxNumber
            });
        }

        private void CreatePrintTab()
        {
            // الطابعة الافتراضية
            var lblPrinter = new Label();
            lblPrinter.Text = "الطابعة الافتراضية:";
            lblPrinter.Location = new Point(500, 30);
            lblPrinter.Size = new Size(120, 23);

            cmbPrinter = new ComboBox();
            cmbPrinter.Location = new Point(300, 30);
            cmbPrinter.Size = new Size(180, 23);
            cmbPrinter.DropDownStyle = ComboBoxStyle.DropDownList;

            // حجم الورق
            var lblPaperSize = new Label();
            lblPaperSize.Text = "حجم الورق:";
            lblPaperSize.Location = new Point(550, 70);
            lblPaperSize.Size = new Size(80, 23);

            cmbPaperSize = new ComboBox();
            cmbPaperSize.Location = new Point(300, 70);
            cmbPaperSize.Size = new Size(180, 23);
            cmbPaperSize.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbPaperSize.Items.AddRange(new string[] { "A4", "A5", "Letter" });
            cmbPaperSize.SelectedIndex = 0;

            // طباعة الشعار
            chkPrintLogo = new CheckBox();
            chkPrintLogo.Text = "طباعة شعار الشركة";
            chkPrintLogo.Location = new Point(300, 110);
            chkPrintLogo.Size = new Size(180, 23);
            chkPrintLogo.Checked = true;

            // طباعة التذييل
            chkPrintFooter = new CheckBox();
            chkPrintFooter.Text = "طباعة تذييل الفاتورة";
            chkPrintFooter.Location = new Point(300, 150);
            chkPrintFooter.Size = new Size(180, 23);
            chkPrintFooter.Checked = true;

            tabPrint.Controls.AddRange(new Control[] {
                lblPrinter, cmbPrinter,
                lblPaperSize, cmbPaperSize,
                chkPrintLogo,
                chkPrintFooter
            });
        }

        private void CreateBackupTab()
        {
            // مسار النسخ الاحتياطي
            var lblBackupPath = new Label();
            lblBackupPath.Text = "مسار النسخ الاحتياطي:";
            lblBackupPath.Location = new Point(500, 30);
            lblBackupPath.Size = new Size(120, 23);

            txtBackupPath = new TextBox();
            txtBackupPath.Location = new Point(200, 30);
            txtBackupPath.Size = new Size(280, 23);
            txtBackupPath.Text = @"C:\Backup\Accounting";

            var btnBrowseBackup = new Button();
            btnBrowseBackup.Text = "تصفح...";
            btnBrowseBackup.Location = new Point(120, 30);
            btnBrowseBackup.Size = new Size(70, 25);
            btnBrowseBackup.Click += BtnBrowseBackup_Click;

            // النسخ الاحتياطي التلقائي
            chkAutoBackup = new CheckBox();
            chkAutoBackup.Text = "النسخ الاحتياطي التلقائي";
            chkAutoBackup.Location = new Point(400, 70);
            chkAutoBackup.Size = new Size(180, 23);

            // فترة النسخ الاحتياطي
            var lblBackupInterval = new Label();
            lblBackupInterval.Text = "كل (يوم):";
            lblBackupInterval.Location = new Point(550, 110);
            lblBackupInterval.Size = new Size(80, 23);

            nudBackupInterval = new NumericUpDown();
            nudBackupInterval.Location = new Point(400, 110);
            nudBackupInterval.Size = new Size(120, 23);
            nudBackupInterval.Minimum = 1;
            nudBackupInterval.Maximum = 30;
            nudBackupInterval.Value = 7;

            // وقت النسخ الاحتياطي
            var lblBackupTime = new Label();
            lblBackupTime.Text = "الوقت:";
            lblBackupTime.Location = new Point(550, 150);
            lblBackupTime.Size = new Size(80, 23);

            cmbBackupTime = new ComboBox();
            cmbBackupTime.Location = new Point(400, 150);
            cmbBackupTime.Size = new Size(120, 23);
            cmbBackupTime.DropDownStyle = ComboBoxStyle.DropDownList;
            for (int i = 0; i < 24; i++)
            {
                cmbBackupTime.Items.Add($"{i:00}:00");
            }
            cmbBackupTime.SelectedIndex = 2; // 02:00

            tabBackup.Controls.AddRange(new Control[] {
                lblBackupPath, txtBackupPath, btnBrowseBackup,
                chkAutoBackup,
                lblBackupInterval, nudBackupInterval,
                lblBackupTime, cmbBackupTime
            });
        }

        private void CreateNotificationsTab()
        {
            // تنبيهات الفواتير المتأخرة
            chkOverdueNotifications = new CheckBox();
            chkOverdueNotifications.Text = "تنبيهات الفواتير المتأخرة";
            chkOverdueNotifications.Location = new Point(400, 30);
            chkOverdueNotifications.Size = new Size(200, 23);
            chkOverdueNotifications.Checked = true;

            // تنبيهات المخزون المنخفض
            chkLowStockNotifications = new CheckBox();
            chkLowStockNotifications.Text = "تنبيهات المخزون المنخفض";
            chkLowStockNotifications.Location = new Point(400, 70);
            chkLowStockNotifications.Size = new Size(200, 23);
            chkLowStockNotifications.Checked = true;

            // تذكير المدفوعات
            chkPaymentReminders = new CheckBox();
            chkPaymentReminders.Text = "تذكير المدفوعات";
            chkPaymentReminders.Location = new Point(400, 110);
            chkPaymentReminders.Size = new Size(200, 23);
            chkPaymentReminders.Checked = true;

            // أيام التذكير
            var lblReminderDays = new Label();
            lblReminderDays.Text = "التذكير قبل (يوم):";
            lblReminderDays.Location = new Point(500, 150);
            lblReminderDays.Size = new Size(120, 23);

            nudReminderDays = new NumericUpDown();
            nudReminderDays.Location = new Point(400, 150);
            nudReminderDays.Size = new Size(80, 23);
            nudReminderDays.Minimum = 1;
            nudReminderDays.Maximum = 30;
            nudReminderDays.Value = 3;

            tabNotifications.Controls.AddRange(new Control[] {
                chkOverdueNotifications,
                chkLowStockNotifications,
                chkPaymentReminders,
                lblReminderDays, nudReminderDays
            });
        }

        private void SetupLayout()
        {
            this.BackColor = Color.FromArgb(248, 249, 250);
        }
        #endregion

        #region تحميل وحفظ الإعدادات
        private void LoadSettings()
        {
            try
            {
                // تحميل الإعدادات من خدمة الإعدادات
                var settings = SettingsService.Settings;

                txtCurrency.Text = settings.Currency;
                cmbLanguage.Text = settings.Language;
                cmbDateFormat.Text = settings.DateFormat;
                chkAutoSave.Checked = settings.AutoSave;
                nudAutoSaveInterval.Value = settings.AutoSaveInterval;

                nudVATRate.Value = (decimal)settings.VATRate;
                chkIncludeVAT.Checked = settings.IncludeVAT;
                txtTaxNumber.Text = settings.TaxNumber;

                LoadPrinters();
                cmbPaperSize.Text = settings.PaperSize;
                chkPrintLogo.Checked = settings.PrintLogo;
                chkPrintFooter.Checked = settings.PrintFooter;

                txtBackupPath.Text = settings.BackupPath;
                chkAutoBackup.Checked = settings.AutoBackup;
                nudBackupInterval.Value = settings.BackupInterval;
                cmbBackupTime.Text = settings.BackupTime;

                chkOverdueNotifications.Checked = settings.OverdueNotifications;
                chkLowStockNotifications.Checked = settings.LowStockNotifications;
                chkPaymentReminders.Checked = settings.PaymentReminders;
                nudReminderDays.Value = settings.ReminderDays;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void LoadPrinters()
        {
            try
            {
                cmbPrinter.Items.Clear();
                foreach (string printerName in System.Drawing.Printing.PrinterSettings.InstalledPrinters)
                {
                    cmbPrinter.Items.Add(printerName);
                }

                if (cmbPrinter.Items.Count > 0)
                {
                    cmbPrinter.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الطابعات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void SaveSettings()
        {
            try
            {
                // حفظ الإعدادات في خدمة الإعدادات
                var settings = SettingsService.Settings;

                settings.Currency = txtCurrency.Text;
                settings.Language = cmbLanguage.Text;
                settings.DateFormat = cmbDateFormat.Text;
                settings.AutoSave = chkAutoSave.Checked;
                settings.AutoSaveInterval = (int)nudAutoSaveInterval.Value;

                settings.VATRate = (double)nudVATRate.Value;
                settings.IncludeVAT = chkIncludeVAT.Checked;
                settings.TaxNumber = txtTaxNumber.Text;

                settings.DefaultPrinter = cmbPrinter.Text;
                settings.PaperSize = cmbPaperSize.Text;
                settings.PrintLogo = chkPrintLogo.Checked;
                settings.PrintFooter = chkPrintFooter.Checked;

                settings.BackupPath = txtBackupPath.Text;
                settings.AutoBackup = chkAutoBackup.Checked;
                settings.BackupInterval = (int)nudBackupInterval.Value;
                settings.BackupTime = cmbBackupTime.Text;

                settings.OverdueNotifications = chkOverdueNotifications.Checked;
                settings.LowStockNotifications = chkLowStockNotifications.Checked;
                settings.PaymentReminders = chkPaymentReminders.Checked;
                settings.ReminderDays = (int)nudReminderDays.Value;

                SettingsService.SaveSettings();

                MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح الحفظ",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region معالجات الأحداث
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateSettings())
            {
                SaveSettings();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟",
                "تأكيد إعادة التعيين", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                ResetToDefaults();
            }
        }

        private void BtnBrowseBackup_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "اختر مجلد النسخ الاحتياطي";
                folderDialog.SelectedPath = txtBackupPath.Text;

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    txtBackupPath.Text = folderDialog.SelectedPath;
                }
            }
        }

        private bool ValidateSettings()
        {
            if (string.IsNullOrWhiteSpace(txtCurrency.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العملة", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabControl.SelectedTab = tabGeneral;
                txtCurrency.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtBackupPath.Text))
            {
                MessageBox.Show("يرجى تحديد مسار النسخ الاحتياطي", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabControl.SelectedTab = tabBackup;
                txtBackupPath.Focus();
                return false;
            }

            return true;
        }

        private void ResetToDefaults()
        {
            try
            {
                // إعادة تعيين القيم الافتراضية
                txtCurrency.Text = "ريال سعودي";
                cmbLanguage.SelectedIndex = 0;
                cmbDateFormat.SelectedIndex = 0;
                chkAutoSave.Checked = true;
                nudAutoSaveInterval.Value = 5;

                nudVATRate.Value = 15;
                chkIncludeVAT.Checked = true;
                txtTaxNumber.Text = "";

                cmbPaperSize.SelectedIndex = 0;
                chkPrintLogo.Checked = true;
                chkPrintFooter.Checked = true;

                txtBackupPath.Text = @"C:\Backup\Accounting";
                chkAutoBackup.Checked = false;
                nudBackupInterval.Value = 7;
                cmbBackupTime.SelectedIndex = 2;

                chkOverdueNotifications.Checked = true;
                chkLowStockNotifications.Checked = true;
                chkPaymentReminders.Checked = true;
                nudReminderDays.Value = 3;

                MessageBox.Show("تم إعادة تعيين الإعدادات للقيم الافتراضية", "إعادة تعيين",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion
    }
}
