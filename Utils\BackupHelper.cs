using System;
using System.IO;
using System.Windows.Forms;

namespace AccountingSystem.Utils
{
    /// <summary>
    /// مساعد النسخ الاحتياطي
    /// </summary>
    public static class BackupHelper
    {
        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>true إذا تم إنشاء النسخة الاحتياطية بنجاح</returns>
        public static bool CreateBackup(string backupPath = null)
        {
            try
            {
                string databasePath = "database.db";
                
                // التحقق من وجود قاعدة البيانات
                if (!File.Exists(databasePath))
                {
                    MessageBox.Show("ملف قاعدة البيانات غير موجود", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
                
                // تحديد مسار النسخة الاحتياطية إذا لم يتم تحديده
                if (string.IsNullOrEmpty(backupPath))
                {
                    string backupFolder = "Backups";
                    if (!Directory.Exists(backupFolder))
                    {
                        Directory.CreateDirectory(backupFolder);
                    }
                    
                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    backupPath = Path.Combine(backupFolder, $"database_backup_{timestamp}.db");
                }
                
                // نسخ ملف قاعدة البيانات
                File.Copy(databasePath, backupPath, true);
                
                MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح في:\n{backupPath}", 
                    "نجح النسخ الاحتياطي", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
        
        /// <summary>
        /// استعادة قاعدة البيانات من نسخة احتياطية
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>true إذا تم استعادة النسخة الاحتياطية بنجاح</returns>
        public static bool RestoreBackup(string backupPath)
        {
            try
            {
                // التحقق من وجود ملف النسخة الاحتياطية
                if (!File.Exists(backupPath))
                {
                    MessageBox.Show("ملف النسخة الاحتياطية غير موجود", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
                
                // تأكيد الاستعادة
                var result = MessageBox.Show(
                    "هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم استبدال قاعدة البيانات الحالية.", 
                    "تأكيد الاستعادة", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                
                if (result != DialogResult.Yes)
                {
                    return false;
                }
                
                string databasePath = "database.db";
                
                // إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
                if (File.Exists(databasePath))
                {
                    string currentBackupPath = $"database_before_restore_{DateTime.Now:yyyyMMdd_HHmmss}.db";
                    File.Copy(databasePath, currentBackupPath, true);
                }
                
                // استعادة النسخة الاحتياطية
                File.Copy(backupPath, databasePath, true);
                
                MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح", "نجحت الاستعادة", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
        
        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية المتاحة
        /// </summary>
        /// <returns>قائمة مسارات النسخ الاحتياطية</returns>
        public static string[] GetAvailableBackups()
        {
            try
            {
                string backupFolder = "Backups";
                
                if (!Directory.Exists(backupFolder))
                {
                    return new string[0];
                }
                
                return Directory.GetFiles(backupFolder, "*.db");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الحصول على النسخ الاحتياطية: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new string[0];
            }
        }
        
        /// <summary>
        /// حذف النسخ الاحتياطية القديمة
        /// </summary>
        /// <param name="daysToKeep">عدد الأيام للاحتفاظ بالنسخ الاحتياطية</param>
        /// <returns>عدد الملفات المحذوفة</returns>
        public static int CleanupOldBackups(int daysToKeep = 30)
        {
            try
            {
                string backupFolder = "Backups";
                
                if (!Directory.Exists(backupFolder))
                {
                    return 0;
                }
                
                var files = Directory.GetFiles(backupFolder, "*.db");
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                int deletedCount = 0;
                
                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                        deletedCount++;
                    }
                }
                
                if (deletedCount > 0)
                {
                    MessageBox.Show($"تم حذف {deletedCount} نسخة احتياطية قديمة", "تنظيف النسخ الاحتياطية", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                
                return deletedCount;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تنظيف النسخ الاحتياطية: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return 0;
            }
        }
        
        /// <summary>
        /// إنشاء نسخة احتياطية تلقائية
        /// </summary>
        public static void CreateAutoBackup()
        {
            try
            {
                // إنشاء نسخة احتياطية تلقائية يومياً
                string autoBackupFolder = Path.Combine("Backups", "Auto");
                if (!Directory.Exists(autoBackupFolder))
                {
                    Directory.CreateDirectory(autoBackupFolder);
                }
                
                string today = DateTime.Now.ToString("yyyyMMdd");
                string autoBackupPath = Path.Combine(autoBackupFolder, $"auto_backup_{today}.db");
                
                // إنشاء نسخة احتياطية فقط إذا لم تكن موجودة لهذا اليوم
                if (!File.Exists(autoBackupPath))
                {
                    string databasePath = "database.db";
                    if (File.Exists(databasePath))
                    {
                        File.Copy(databasePath, autoBackupPath, true);
                    }
                }
                
                // تنظيف النسخ الاحتياطية التلقائية القديمة (الاحتفاظ بآخر 7 أيام)
                var autoFiles = Directory.GetFiles(autoBackupFolder, "*.db");
                var cutoffDate = DateTime.Now.AddDays(-7);
                
                foreach (var file in autoFiles)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                    }
                }
            }
            catch (Exception)
            {
                // تجاهل الأخطاء في النسخ الاحتياطي التلقائي
                // لتجنب إزعاج المستخدم
            }
        }
        
        /// <summary>
        /// تصدير البيانات إلى ملف CSV
        /// </summary>
        /// <param name="exportPath">مسار ملف التصدير</param>
        /// <returns>true إذا تم التصدير بنجاح</returns>
        public static bool ExportToCSV(string exportPath)
        {
            try
            {
                // هذه وظيفة مبسطة للتصدير
                // يمكن تطويرها لاحقاً لتصدير جداول محددة
                
                using (var writer = new StreamWriter(exportPath, false, System.Text.Encoding.UTF8))
                {
                    writer.WriteLine("تصدير بيانات نظام المحاسبة الذكي");
                    writer.WriteLine($"تاريخ التصدير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    writer.WriteLine("================================");
                    writer.WriteLine();
                    
                    // يمكن إضافة المزيد من البيانات هنا
                    writer.WriteLine("ملاحظة: هذه نسخة مبسطة من التصدير");
                    writer.WriteLine("للحصول على تصدير كامل، استخدم النسخ الاحتياطي لقاعدة البيانات");
                }
                
                MessageBox.Show($"تم تصدير البيانات بنجاح إلى:\n{exportPath}", "نجح التصدير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
    }
}
