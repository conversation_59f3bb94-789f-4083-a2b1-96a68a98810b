@echo off
chcp 65001 > nul
echo ========================================
echo بناء النظام المتكامل للفواتير - الإصدار المجزأ
echo ========================================
echo.

echo 🎯 النظام المدمج والشامل - مجزأ لسهولة التطوير
echo ✨ دمج جميع الميزات في نظام واحد مع تجزئة الملفات
echo 🏠 واجهة رئيسية محسنة مع داشبورد متكامل
echo 📊 إدارة شاملة للفواتير والحسابات
echo 👥 إدارة المستخدمين والصلاحيات
echo 🔧 نظام متطور وقابل للتطوير
echo.

rem التحقق من وجود مترجم C#
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ مترجم C# غير متوفر
    echo.
    echo الحلول:
    echo 1. افتح Developer Command Prompt for Visual Studio
    echo 2. أو ثبت .NET Framework SDK
    echo 3. أو استخدم Visual Studio لبناء المشروع
    echo.
    goto :end
)

echo ✓ مترجم C# متوفر
echo.

echo فحص الملفات المطلوبة...

set FILES_MISSING=0

if not exist "Models\DataModels.cs" (
    echo ✗ ملف Models\DataModels.cs مفقود
    set FILES_MISSING=1
)

if not exist "Services\DataService.cs" (
    echo ✗ ملف Services\DataService.cs مفقود
    set FILES_MISSING=1
)

if not exist "Services\AuthService.cs" (
    echo ✗ ملف Services\AuthService.cs مفقود
    set FILES_MISSING=1
)

if not exist "Forms\MainDashboardForm.cs" (
    echo ✗ ملف Forms\MainDashboardForm.cs مفقود
    set FILES_MISSING=1
)

if not exist "Controls\DashboardControl.cs" (
    echo ✗ ملف Controls\DashboardControl.cs مفقود
    set FILES_MISSING=1
)

if not exist "IntegratedProgram.cs" (
    echo ✗ ملف IntegratedProgram.cs مفقود
    set FILES_MISSING=1
)

if %FILES_MISSING%==1 (
    echo.
    echo ✗ بعض الملفات مفقودة. يرجى التأكد من وجود جميع الملفات المطلوبة.
    goto :end
)

echo ✓ جميع الملفات موجودة
echo.

echo بناء النظام المتكامل...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /main:IntegratedInvoiceSystem.IntegratedProgram ^
    /out:IntegratedInvoiceSystem.exe ^
    Models\DataModels.cs ^
    Services\DataService.cs ^
    Services\AuthService.cs ^
    Forms\MainDashboardForm.cs ^
    Controls\DashboardControl.cs ^
    IntegratedProgram.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء النظام المتكامل بنجاح!
    echo.
    echo تم إنشاء: IntegratedInvoiceSystem.exe
    echo.

    echo إنشاء ملف الإعداد...
    echo ^<?xml version="1.0" encoding="utf-8"?^> > IntegratedInvoiceSystem.exe.config
    echo ^<configuration^> >> IntegratedInvoiceSystem.exe.config
    echo   ^<startup^> >> IntegratedInvoiceSystem.exe.config
    echo     ^<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/^> >> IntegratedInvoiceSystem.exe.config
    echo   ^</startup^> >> IntegratedInvoiceSystem.exe.config
    echo ^</configuration^> >> IntegratedInvoiceSystem.exe.config

    echo ✓ تم إنشاء ملف الإعداد
    echo.

    echo إنشاء مجلد البيانات...
    if not exist "Data" mkdir Data
    echo ✓ تم إنشاء مجلد البيانات
    echo.

    set /p run="هل تريد تشغيل النظام المتكامل الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام المتكامل...
        start "" "IntegratedInvoiceSystem.exe"
        echo.
        echo ✓ تم تشغيل النظام المتكامل بنجاح!
    )

    echo.
    echo ========================================
    echo النظام المتكامل للفواتير - الإصدار 3.0
    echo ========================================
    echo.
    echo ملف التشغيل: IntegratedInvoiceSystem.exe
    echo.
    echo 👤 معلومات تسجيل الدخول:
    echo.
    echo المدير:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo الصلاحيات: جميع الصلاحيات
    echo.
    echo الموظف:
    echo اسم المستخدم: user
    echo كلمة المرور: user123
    echo الصلاحيات: الفواتير والعملاء فقط
    echo.
    echo 🎯 الميزات الجديدة في النظام المدمج:
    echo ✅ دمج جميع الأنظمة السابقة في نظام واحد شامل
    echo ✅ واجهة رئيسية محسنة مع داشبورد تفاعلي
    echo ✅ إدارة المستخدمين والصلاحيات
    echo ✅ نظام مصادقة محسن
    echo ✅ بيانات تجريبية أكثر ثراءً وتنوعاً
    echo ✅ حفظ واستعادة البيانات
    echo ✅ نسخ احتياطية تلقائية
    echo ✅ تصميم متناسق وأنيق في جميع الشاشات
    echo.
    echo 🏠 الواجهة الرئيسية المتكاملة:
    echo • شريط جانبي بـ 12 قسم رئيسي
    echo • شريط علوي مع معلومات المستخدم والوقت
    echo • أزرار سريعة للعمليات الشائعة
    echo • داشبورد تفاعلي مع إحصائيات شاملة
    echo • تنقل سلس بين الأقسام
    echo • تحديث تلقائي للبيانات
    echo.
    echo 📊 الداشبورد المتكامل:
    echo • 5 بطاقات إحصائيات رئيسية
    echo • رسم بياني للمبيعات (7 أيام)
    echo • جدول الفواتير الأخيرة (12 فاتورة)
    echo • أعلى العملاء رصيداً (7 عملاء)
    echo • إحصائيات سريعة (5 إحصائيات)
    echo • تلوين تلقائي حسب الحالة
    echo • تحديث فوري للبيانات
    echo.
    echo 🎯 الأقسام المتوفرة:
    echo • 🏠 الداشبورد الرئيسي - إحصائيات شاملة وتفاعلية
    echo • 📊 إدارة الفواتير - عرض وإدارة كاملة للفواتير
    echo • ➕ إضافة فاتورة جديدة - نموذج متطور لإنشاء الفواتير
    echo • 💰 السندات والمدفوعات - إدارة المدفوعات والتحصيلات
    echo • 👥 إدارة العملاء - قاعدة بيانات العملاء الشاملة
    echo • 📦 إدارة المنتجات - كتالوج المنتجات والمخزون
    echo • 📈 التقارير والإحصائيات - تحليلات مفصلة ومتقدمة
    echo • 🏢 معلومات الشركة - بيانات الشركة والإعدادات
    echo • 👤 إدارة المستخدمين - إدارة المستخدمين والصلاحيات
    echo • ⚙️ الإعدادات العامة - تخصيص النظام والتفضيلات
    echo • 💾 حفظ البيانات - نسخ احتياطية وحفظ
    echo • 🚪 تسجيل خروج - خروج آمن من النظام
    echo.
    echo 📊 البيانات التجريبية الشاملة:
    echo • 8 عملاء بمعلومات كاملة وأرصدة متنوعة
    echo • 10 منتجات بفئات وأسعار مختلفة
    echo • 10 فواتير بحالات وأصناف متعددة
    echo • 7 سندات قبض مربوطة بالفواتير
    echo • 2 مستخدم بصلاحيات مختلفة
    echo • معلومات شركة كاملة
    echo • بيانات واقعية ومنطقية
    echo.
    echo 👤 نظام المستخدمين والصلاحيات:
    echo • مستخدم مدير بجميع الصلاحيات
    echo • مستخدم موظف بصلاحيات محدودة
    echo • التحقق من الصلاحيات قبل الوصول
    echo • تسجيل آخر دخول للمستخدمين
    echo • إمكانية إضافة مستخدمين جدد
    echo • إدارة الأدوار والصلاحيات
    echo.
    echo 🎨 التصميم والواجهة:
    echo ✓ ألوان متناسقة ومريحة للعين
    echo ✓ خطوط Segoe UI الأنيقة
    echo ✓ حواف مدورة في جميع العناصر
    echo ✓ ظلال وتأثيرات ثلاثية الأبعاد
    echo ✓ تدرجات لونية جميلة
    echo ✓ أيقونات تعبيرية واضحة
    echo ✓ تخطيط متوازن ومنظم
    echo ✓ تأثيرات تفاعلية عند التمرير
    echo ✓ تصميم متجاوب مع أحجام الشاشات
    echo.
    echo 🔧 الوظائف المتقدمة:
    echo • حساب تلقائي للضرائب والخصومات
    echo • تتبع حالات الفواتير المختلفة
    echo • ربط المدفوعات بالفواتير
    echo • حساب الأرباح والخسائر
    echo • فلترة وبحث متقدم
    echo • تلوين تلقائي حسب الحالة
    echo • التحقق من صحة البيانات
    echo • رسائل تأكيد ونجاح العمليات
    echo • حفظ واستعادة البيانات
    echo • نسخ احتياطية تلقائية
    echo.
    echo 📱 سهولة الاستخدام:
    echo • واجهة بديهية وسهلة التنقل
    echo • أزرار واضحة ومفهومة
    echo • رسائل مساعدة وتوجيهية
    echo • اختصارات سريعة للعمليات
    echo • تصميم متجاوب ومرن
    echo • دعم اللغة العربية بالكامل
    echo • تحديث تلقائي للوقت والبيانات
    echo.
    echo 🎉 هذا أشمل وأفضل نظام فواتير متكامل!

) else (
    echo.
    echo ✗ فشل في بناء النظام المتكامل
    echo.
    echo الأخطاء المحتملة:
    echo 1. أخطاء في الكود المصدري
    echo 2. مراجع مفقودة
    echo 3. مشاكل في مترجم C#
    echo.
    echo للتشخيص:
    echo 1. افتح IntegratedInvoiceSystem.cs وتحقق من الأخطاء
    echo 2. تأكد من وجود جميع المراجع المطلوبة
    echo 3. استخدم Visual Studio للحصول على تفاصيل الأخطاء
    echo.
    echo بدائل أخرى:
    echo • استخدم Visual Studio لبناء المشروع
    echo • راجع الكود للتأكد من عدم وجود أخطاء إملائية
    echo • جرب الأنظمة الأخرى المتوفرة
)

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
