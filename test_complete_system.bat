@echo off
chcp 65001 > nul
echo ========================================
echo اختبار النظام الشامل المحدث
echo ========================================
echo.

echo هذا الاختبار سيتحقق من:
echo 1. صحة جميع النماذج والواجهات
echo 2. نظام سندات القبض والصرف
echo 3. كشف حساب العميل التفصيلي
echo 4. نظام السداد والتقارير
echo 5. إمكانية البناء بدون أخطاء
echo.

set errors=0

echo بدء الاختبار...
echo.

echo ========================================
echo 1. فحص نماذج السندات
echo ========================================

if exist "Models\Receipt.cs" (
    echo ✓ ملف Receipt.cs موجود
    
    findstr /C:"CustomerId" "Models\Receipt.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على خاصية CustomerId
    ) else (
        echo ✗ خاصية CustomerId مفقودة
        set /a errors+=1
    )
    
    findstr /C:"ReferenceNumber" "Models\Receipt.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على خاصية ReferenceNumber
    ) else (
        echo ✗ خاصية ReferenceNumber مفقودة
        set /a errors+=1
    )
) else (
    echo ✗ ملف Receipt.cs مفقود
    set /a errors+=1
)

if exist "Models\Payment.cs" (
    echo ✓ ملف Payment.cs موجود
    
    findstr /C:"PaymentType" "Models\Payment.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على خاصية PaymentType
    ) else (
        echo ✗ خاصية PaymentType مفقودة
        set /a errors+=1
    )
    
    findstr /C:"Beneficiary" "Models\Payment.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على خاصية Beneficiary
    ) else (
        echo ✗ خاصية Beneficiary مفقودة
        set /a errors+=1
    )
) else (
    echo ✗ ملف Payment.cs مفقود
    set /a errors+=1
)

if exist "Models\Invoice.cs" (
    echo ✓ ملف Invoice.cs موجود
    
    findstr /C:"PaidAmount" "Models\Invoice.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على خاصية PaidAmount
    ) else (
        echo ✗ خاصية PaidAmount مفقودة
        set /a errors+=1
    )
) else (
    echo ✗ ملف Invoice.cs مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo 2. فحص واجهات السندات
echo ========================================

if exist "Forms\ReceiptsForm.cs" (
    echo ✓ ملف ReceiptsForm.cs موجود
    
    findstr /C:"class ReceiptsForm" "Forms\ReceiptsForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على فئة ReceiptsForm
    ) else (
        echo ✗ فئة ReceiptsForm مفقودة
        set /a errors+=1
    )
) else (
    echo ✗ ملف ReceiptsForm.cs مفقود
    set /a errors+=1
)

if exist "Forms\AddEditReceiptForm.cs" (
    echo ✓ ملف AddEditReceiptForm.cs موجود
) else (
    echo ✗ ملف AddEditReceiptForm.cs مفقود
    set /a errors+=1
)

if exist "Forms\PaymentsForm.cs" (
    echo ✓ ملف PaymentsForm.cs موجود
    
    findstr /C:"class PaymentsForm" "Forms\PaymentsForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على فئة PaymentsForm
    ) else (
        echo ✗ فئة PaymentsForm مفقودة
        set /a errors+=1
    )
) else (
    echo ✗ ملف PaymentsForm.cs مفقود
    set /a errors+=1
)

if exist "Forms\AddEditPaymentForm.cs" (
    echo ✓ ملف AddEditPaymentForm.cs موجود
) else (
    echo ✗ ملف AddEditPaymentForm.cs مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo 3. فحص واجهات كشف الحساب
echo ========================================

if exist "Forms\CustomerStatementForm.cs" (
    echo ✓ ملف CustomerStatementForm.cs موجود
    
    findstr /C:"class CustomerStatementForm" "Forms\CustomerStatementForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على فئة CustomerStatementForm
    ) else (
        echo ✗ فئة CustomerStatementForm مفقودة
        set /a errors+=1
    )
    
    findstr /C:"CustomerPaymentForm" "Forms\CustomerStatementForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يستخدم CustomerPaymentForm
    ) else (
        echo ✗ لا يستخدم CustomerPaymentForm
        set /a errors+=1
    )
) else (
    echo ✗ ملف CustomerStatementForm.cs مفقود
    set /a errors+=1
)

if exist "Forms\CustomerPaymentForm.cs" (
    echo ✓ ملف CustomerPaymentForm.cs موجود
    
    findstr /C:"class CustomerPaymentForm" "Forms\CustomerPaymentForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على فئة CustomerPaymentForm
    ) else (
        echo ✗ فئة CustomerPaymentForm مفقودة
        set /a errors+=1
    )
) else (
    echo ✗ ملف CustomerPaymentForm.cs مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo 4. فحص واجهات التقارير
echo ========================================

if exist "Forms\ReceiptsReportForm.cs" (
    echo ✓ ملف ReceiptsReportForm.cs موجود
    
    findstr /C:"class ReceiptsReportForm" "Forms\ReceiptsReportForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على فئة ReceiptsReportForm
    ) else (
        echo ✗ فئة ReceiptsReportForm مفقودة
        set /a errors+=1
    )
) else (
    echo ✗ ملف ReceiptsReportForm.cs مفقود
    set /a errors+=1
)

if exist "Forms\PaymentsReportForm.cs" (
    echo ✓ ملف PaymentsReportForm.cs موجود
    
    findstr /C:"class PaymentsReportForm" "Forms\PaymentsReportForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على فئة PaymentsReportForm
    ) else (
        echo ✗ فئة PaymentsReportForm مفقودة
        set /a errors+=1
    )
) else (
    echo ✗ ملف PaymentsReportForm.cs مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo 5. فحص تحديثات النموذج الرئيسي
echo ========================================

if exist "Forms\MainForm.cs" (
    echo ✓ ملف MainForm.cs موجود
    
    findstr /C:"CustomerStatement_Click" "Forms\MainForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على دالة CustomerStatement_Click
    ) else (
        echo ✗ دالة CustomerStatement_Click مفقودة
        set /a errors+=1
    )
    
    findstr /C:"ReceiptsReport_Click" "Forms\MainForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على دالة ReceiptsReport_Click
    ) else (
        echo ✗ دالة ReceiptsReport_Click مفقودة
        set /a errors+=1
    )
    
    findstr /C:"PaymentsReport_Click" "Forms\MainForm.cs" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ يحتوي على دالة PaymentsReport_Click
    ) else (
        echo ✗ دالة PaymentsReport_Click مفقودة
        set /a errors+=1
    )
) else (
    echo ✗ ملف MainForm.cs مفقود
    set /a errors+=1
)

echo.
echo ========================================
echo 6. اختبار بناء سريع
echo ========================================

where csc >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ مترجم C# متوفر
    
    echo محاولة بناء النماذج الجديدة...
    
    csc /target:library ^
        /reference:System.dll ^
        /reference:System.Core.dll ^
        /reference:System.Drawing.dll ^
        /reference:System.Windows.Forms.dll ^
        /out:TestCompleteSystem.dll ^
        Models\Receipt.cs ^
        Models\Payment.cs ^
        Models\Invoice.cs >nul 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo ✓ نجح بناء النماذج الجديدة
        if exist "TestCompleteSystem.dll" del "TestCompleteSystem.dll" >nul 2>&1
    ) else (
        echo ✗ فشل بناء النماذج الجديدة
        set /a errors+=1
    )
) else (
    echo ⚠ مترجم C# غير متوفر (استخدم Developer Command Prompt)
)

echo.
echo ========================================
echo النتائج النهائية
echo ========================================

echo إجمالي الأخطاء: %errors%

if %errors% EQU 0 (
    echo.
    echo 🎉 ممتاز! النظام الشامل جاهز للتشغيل
    echo.
    echo الميزات المتوفرة:
    echo ✓ إدارة سندات القبض مع التقارير
    echo ✓ إدارة سندات الصرف مع التقارير  
    echo ✓ كشف حساب العميل التفصيلي
    echo ✓ نظام تسجيل دفعات العملاء
    echo ✓ تقارير شاملة قابلة للطباعة والتصدير
    echo ✓ واجهات سهلة الاستخدام باللغة العربية
    echo ✓ فلترة وبحث متقدم
    echo ✓ تلوين البيانات حسب الحالة
    echo.
    echo خيارات التشغيل:
    echo 1. build_simple.bat - للاختبار السريع
    echo 2. build_complete.bat - للنسخة الكاملة
    echo 3. build_without_sqlite.bat - لتجنب مشاكل SQLite
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo الواجهات الجديدة:
    echo - سندات القبض: قائمة السندات ^> سندات القبض
    echo - سندات الصرف: قائمة السندات ^> سندات الصرف
    echo - كشف حساب العميل: قائمة التقارير ^> كشف حساب العميل
    echo - تقارير السندات: قائمة التقارير ^> تقرير سندات القبض/الصرف
    
) else (
    echo.
    echo ⚠ يوجد %errors% خطأ يجب إصلاحه
    echo.
    echo الإجراءات المقترحة:
    echo 1. تأكد من وجود جميع ملفات النماذج والواجهات
    echo 2. راجع خصائص النماذج في ملفات Models
    echo 3. تأكد من تطابق أسماء الفئات والدوال
    echo 4. تحقق من صحة المراجع والاستيرادات
    echo.
    echo يمكنك مراجعة الملفات التالية:
    echo - Models\Receipt.cs, Payment.cs, Invoice.cs
    echo - Forms\ReceiptsForm.cs, PaymentsForm.cs
    echo - Forms\CustomerStatementForm.cs, CustomerPaymentForm.cs
    echo - Forms\ReceiptsReportForm.cs, PaymentsReportForm.cs
    echo - Forms\MainForm.cs
)

echo.
echo تاريخ الاختبار: %date% %time%

echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
