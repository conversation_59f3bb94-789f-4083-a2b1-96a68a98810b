using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using AccountingSystem.Models;

namespace AccountingSystem.Utils
{
    /// <summary>
    /// مولد التقارير والطباعة
    /// </summary>
    public class ReportGenerator
    {
        private PrintDocument printDocument;
        private List<string> reportLines;
        private int currentLine;
        private Font titleFont;
        private Font headerFont;
        private Font normalFont;
        private Font boldFont;
        
        public ReportGenerator()
        {
            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;
            
            // تعريف الخطوط
            titleFont = new Font("Tahoma", 16, FontStyle.Bold);
            headerFont = new Font("Tahoma", 12, FontStyle.Bold);
            normalFont = new Font("Tahoma", 10);
            boldFont = new Font("Tahoma", 10, FontStyle.Bold);
        }
        
        /// <summary>
        /// طباعة تقرير شجرة الحسابات
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        public void PrintAccountsTree(List<Account> accounts)
        {
            try
            {
                reportLines = new List<string>();
                currentLine = 0;
                
                // عنوان التقرير
                reportLines.Add("شجرة الحسابات");
                reportLines.Add("================");
                reportLines.Add($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd}");
                reportLines.Add("");
                
                // ترتيب الحسابات حسب الكود
                var sortedAccounts = accounts.OrderBy(a => a.AccountCode).ToList();
                
                foreach (var account in sortedAccounts)
                {
                    string indent = new string(' ', (account.Level - 1) * 4);
                    string line = $"{indent}{account.AccountCode} - {account.AccountName}";
                    
                    if (account.IsParent)
                    {
                        line += " (حساب رئيسي)";
                    }
                    else
                    {
                        line += $" - الرصيد: {account.Balance:N2}";
                    }
                    
                    reportLines.Add(line);
                }
                
                // معاينة الطباعة
                PrintPreviewDialog previewDialog = new PrintPreviewDialog();
                previewDialog.Document = printDocument;
                previewDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// طباعة تقرير القيود اليومية
        /// </summary>
        /// <param name="journalEntries">قائمة القيود اليومية</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        public void PrintJournalEntries(List<JournalEntry> journalEntries, DateTime fromDate, DateTime toDate)
        {
            try
            {
                reportLines = new List<string>();
                currentLine = 0;
                
                // عنوان التقرير
                reportLines.Add("تقرير القيود اليومية");
                reportLines.Add("==================");
                reportLines.Add($"من تاريخ: {fromDate:yyyy/MM/dd} إلى تاريخ: {toDate:yyyy/MM/dd}");
                reportLines.Add($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd}");
                reportLines.Add("");
                
                decimal totalDebit = 0;
                decimal totalCredit = 0;
                
                foreach (var entry in journalEntries.OrderBy(e => e.EntryDate))
                {
                    reportLines.Add($"رقم القيد: {entry.EntryNumber}");
                    reportLines.Add($"التاريخ: {entry.EntryDate:yyyy/MM/dd}");
                    reportLines.Add($"الوصف: {entry.Description}");
                    reportLines.Add($"المرجع: {entry.Reference}");
                    reportLines.Add("");
                    
                    // تفاصيل القيد
                    if (entry.Details != null && entry.Details.Any())
                    {
                        reportLines.Add("الحساب                    مدين        دائن");
                        reportLines.Add("----------------------------------------");
                        
                        foreach (var detail in entry.Details.OrderBy(d => d.LineNumber))
                        {
                            string accountName = detail.Account?.AccountName ?? "غير محدد";
                            string debit = detail.DebitAmount > 0 ? detail.DebitAmount.ToString("N2") : "";
                            string credit = detail.CreditAmount > 0 ? detail.CreditAmount.ToString("N2") : "";
                            
                            reportLines.Add($"{accountName.PadRight(20)} {debit.PadLeft(10)} {credit.PadLeft(10)}");
                        }
                        
                        reportLines.Add("----------------------------------------");
                        reportLines.Add($"{"الإجمالي".PadRight(20)} {entry.TotalDebit.ToString("N2").PadLeft(10)} {entry.TotalCredit.ToString("N2").PadLeft(10)}");
                    }
                    
                    totalDebit += entry.TotalDebit;
                    totalCredit += entry.TotalCredit;
                    
                    reportLines.Add("");
                    reportLines.Add("========================================");
                    reportLines.Add("");
                }
                
                // الإجمالي العام
                reportLines.Add("الإجمالي العام:");
                reportLines.Add($"إجمالي المدين: {totalDebit:N2}");
                reportLines.Add($"إجمالي الدائن: {totalCredit:N2}");
                
                // معاينة الطباعة
                PrintPreviewDialog previewDialog = new PrintPreviewDialog();
                previewDialog.Document = printDocument;
                previewDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// طباعة فاتورة
        /// </summary>
        /// <param name="invoice">الفاتورة</param>
        public void PrintInvoice(Invoice invoice)
        {
            try
            {
                reportLines = new List<string>();
                currentLine = 0;
                
                // عنوان الفاتورة
                reportLines.Add("فاتورة مبيعات");
                reportLines.Add("=============");
                reportLines.Add("");
                
                // معلومات الفاتورة
                reportLines.Add($"رقم الفاتورة: {invoice.InvoiceNumber}");
                reportLines.Add($"تاريخ الفاتورة: {invoice.InvoiceDate:yyyy/MM/dd}");
                reportLines.Add($"تاريخ الاستحقاق: {invoice.DueDate:yyyy/MM/dd}");
                reportLines.Add("");
                
                // معلومات العميل
                if (invoice.Customer != null)
                {
                    reportLines.Add("بيانات العميل:");
                    reportLines.Add($"الاسم: {invoice.Customer.CustomerName}");
                    reportLines.Add($"الهاتف: {invoice.Customer.Phone}");
                    reportLines.Add($"العنوان: {invoice.Customer.Address}");
                }
                reportLines.Add("");
                
                // تفاصيل الفاتورة
                reportLines.Add("تفاصيل الفاتورة:");
                reportLines.Add("المنتج                الكمية    السعر      الإجمالي");
                reportLines.Add("------------------------------------------------");
                
                if (invoice.Details != null && invoice.Details.Any())
                {
                    foreach (var detail in invoice.Details.OrderBy(d => d.LineNumber))
                    {
                        string productName = detail.Product?.ProductName ?? "غير محدد";
                        reportLines.Add($"{productName.PadRight(20)} {detail.Quantity.ToString("N2").PadLeft(8)} {detail.UnitPrice.ToString("N2").PadLeft(8)} {detail.LineTotal.ToString("N2").PadLeft(10)}");
                    }
                }
                
                reportLines.Add("------------------------------------------------");
                
                // الإجماليات
                reportLines.Add($"المجموع الفرعي: {invoice.SubTotal:N2}");
                if (invoice.DiscountAmount > 0)
                {
                    reportLines.Add($"الخصم: {invoice.DiscountAmount:N2}");
                }
                if (invoice.TaxAmount > 0)
                {
                    reportLines.Add($"الضريبة: {invoice.TaxAmount:N2}");
                }
                reportLines.Add($"الإجمالي: {invoice.TotalAmount:N2}");
                reportLines.Add($"المدفوع: {invoice.PaidAmount:N2}");
                reportLines.Add($"المتبقي: {invoice.RemainingAmount:N2}");
                
                // معاينة الطباعة
                PrintPreviewDialog previewDialog = new PrintPreviewDialog();
                previewDialog.Document = printDocument;
                previewDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// طباعة سند قبض أو صرف
        /// </summary>
        /// <param name="receipt">سند القبض</param>
        public void PrintReceipt(Receipt receipt)
        {
            try
            {
                reportLines = new List<string>();
                currentLine = 0;
                
                // عنوان السند
                reportLines.Add("سند قبض");
                reportLines.Add("========");
                reportLines.Add("");
                
                // معلومات السند
                reportLines.Add($"رقم السند: {receipt.ReceiptNumber}");
                reportLines.Add($"التاريخ: {receipt.ReceiptDate:yyyy/MM/dd}");
                reportLines.Add($"المبلغ: {receipt.Amount:N2} {receipt.Currency}");
                reportLines.Add($"طريقة الدفع: {receipt.PaymentMethod}");
                
                if (!string.IsNullOrEmpty(receipt.CheckNumber))
                {
                    reportLines.Add($"رقم الشيك: {receipt.CheckNumber}");
                    reportLines.Add($"تاريخ الشيك: {receipt.CheckDate:yyyy/MM/dd}");
                    reportLines.Add($"البنك: {receipt.BankName}");
                }
                
                reportLines.Add("");
                reportLines.Add($"الوصف: {receipt.Description}");
                reportLines.Add($"المرجع: {receipt.Reference}");
                
                // معاينة الطباعة
                PrintPreviewDialog previewDialog = new PrintPreviewDialog();
                previewDialog.Document = printDocument;
                previewDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة السند: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// حفظ التقرير كملف نصي
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        public void SaveReportToFile(string filePath)
        {
            try
            {
                if (reportLines != null && reportLines.Any())
                {
                    File.WriteAllLines(filePath, reportLines, Encoding.UTF8);
                    MessageBox.Show($"تم حفظ التقرير في: {filePath}", "تم الحفظ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            try
            {
                float yPosition = e.MarginBounds.Top;
                float leftMargin = e.MarginBounds.Left;
                float lineHeight = normalFont.GetHeight(e.Graphics);
                
                // طباعة السطور
                while (currentLine < reportLines.Count && yPosition < e.MarginBounds.Bottom - lineHeight)
                {
                    string line = reportLines[currentLine];
                    Font fontToUse = normalFont;
                    
                    // تحديد نوع الخط حسب محتوى السطر
                    if (currentLine == 0) // العنوان
                    {
                        fontToUse = titleFont;
                    }
                    else if (line.Contains("=") || line.Contains("-"))
                    {
                        fontToUse = normalFont;
                    }
                    else if (line.Contains(":"))
                    {
                        fontToUse = boldFont;
                    }
                    
                    e.Graphics.DrawString(line, fontToUse, Brushes.Black, leftMargin, yPosition);
                    yPosition += lineHeight;
                    currentLine++;
                }
                
                // التحقق من وجود صفحات إضافية
                if (currentLine < reportLines.Count)
                {
                    e.HasMorePages = true;
                }
                else
                {
                    e.HasMorePages = false;
                    currentLine = 0; // إعادة تعيين للطباعة التالية
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الصفحة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// تحرير الموارد
        /// </summary>
        public void Dispose()
        {
            printDocument?.Dispose();
            titleFont?.Dispose();
            headerFont?.Dispose();
            normalFont?.Dispose();
            boldFont?.Dispose();
        }
    }
}
