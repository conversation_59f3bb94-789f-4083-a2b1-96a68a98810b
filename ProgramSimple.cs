using System;
using System.Windows.Forms;
using AccountingSystem.Forms;
using AccountingSystem.Services;

namespace AccountingSystem
{
    /// <summary>
    /// نقطة دخول التطبيق المبسط
    /// </summary>
    static class ProgramSimple
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // تهيئة الخدمات
                InitializeServices();
                
                // عرض نموذج تسجيل الدخول
                var loginForm = new LoginForm();
                Application.Run(loginForm);
                
                // إذا تم تسجيل الدخول بنجاح، عرض النموذج الرئيسي
                if (AuthenticationService.CurrentUser != null)
                {
                    Application.Run(new MainFormSimple());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private static void InitializeServices()
        {
            try
            {
                // تهيئة خدمة المصادقة
                AuthenticationService.Initialize();
                
                // إنشاء مجلد البيانات إذا لم يكن موجوداً
                string dataFolder = "SimpleData";
                if (!System.IO.Directory.Exists(dataFolder))
                {
                    System.IO.Directory.CreateDirectory(dataFolder);
                }
                
                // إنشاء ملف الإعداد إذا لم يكن موجوداً
                string configFile = "system_config.json";
                if (!System.IO.File.Exists(configFile))
                {
                    string defaultConfig = @"{
  ""DatabasePath"": ""SimpleData"",
  ""CompanyName"": ""شركة المحاسبة الذكية"",
  ""CompanyAddress"": ""المملكة العربية السعودية"",
  ""Currency"": ""ريال سعودي"",
  ""DateFormat"": ""yyyy/MM/dd"",
  ""Language"": ""ar-SA"",
  ""BackupEnabled"": true,
  ""AutoBackupDays"": 7
}";
                    System.IO.File.WriteAllText(configFile, defaultConfig, System.Text.Encoding.UTF8);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الخدمات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }
}
