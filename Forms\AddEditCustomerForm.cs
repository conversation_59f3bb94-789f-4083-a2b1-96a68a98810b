using System;
using System.Drawing;
using System.Windows.Forms;
using AccountingSystem.Models;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج إضافة/تعديل العميل
    /// </summary>
    public partial class AddEditCustomerForm : Form
    {
        private TextBox txtCustomerName;
        private TextBox txtPhone;
        private TextBox txtEmail;
        private TextBox txtAddress;
        private TextBox txtBalance;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;

        private Label lblCustomerName;
        private Label lblPhone;
        private Label lblEmail;
        private Label lblAddress;
        private Label lblBalance;

        public Customer Customer { get; private set; }
        private bool isEditMode;

        public AddEditCustomerForm(Customer customer = null)
        {
            InitializeComponent();

            if (customer != null)
            {
                isEditMode = true;
                Customer = customer;
                LoadCustomerData();
                this.Text = "تعديل العميل";
            }
            else
            {
                isEditMode = false;
                Customer = new Customer();
                this.Text = "إضافة عميل جديد";
            }
        }

        private void InitializeComponent()
        {
            this.txtCustomerName = new TextBox();
            this.txtPhone = new TextBox();
            this.txtEmail = new TextBox();
            this.txtAddress = new TextBox();
            this.txtBalance = new TextBox();
            this.chkIsActive = new CheckBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();

            this.lblCustomerName = new Label();
            this.lblPhone = new Label();
            this.lblEmail = new Label();
            this.lblAddress = new Label();
            this.lblBalance = new Label();

            this.SuspendLayout();

            // Form
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Customer Name Label
            this.lblCustomerName.Text = "اسم العميل:";
            this.lblCustomerName.Location = new Point(400, 30);
            this.lblCustomerName.Size = new Size(80, 23);
            this.lblCustomerName.TextAlign = ContentAlignment.MiddleRight;

            // Customer Name TextBox
            this.txtCustomerName.Location = new Point(50, 30);
            this.txtCustomerName.Size = new Size(330, 23);
            this.txtCustomerName.Font = new Font("Tahoma", 10F);

            // Phone Label
            this.lblPhone.Text = "رقم الهاتف:";
            this.lblPhone.Location = new Point(400, 70);
            this.lblPhone.Size = new Size(80, 23);
            this.lblPhone.TextAlign = ContentAlignment.MiddleRight;

            // Phone TextBox
            this.txtPhone.Location = new Point(50, 70);
            this.txtPhone.Size = new Size(330, 23);
            this.txtPhone.Font = new Font("Tahoma", 10F);

            // Email Label
            this.lblEmail.Text = "البريد الإلكتروني:";
            this.lblEmail.Location = new Point(400, 110);
            this.lblEmail.Size = new Size(80, 23);
            this.lblEmail.TextAlign = ContentAlignment.MiddleRight;

            // Email TextBox
            this.txtEmail.Location = new Point(50, 110);
            this.txtEmail.Size = new Size(330, 23);
            this.txtEmail.Font = new Font("Tahoma", 10F);

            // Address Label
            this.lblAddress.Text = "العنوان:";
            this.lblAddress.Location = new Point(400, 150);
            this.lblAddress.Size = new Size(80, 23);
            this.lblAddress.TextAlign = ContentAlignment.MiddleRight;

            // Address TextBox
            this.txtAddress.Location = new Point(50, 150);
            this.txtAddress.Size = new Size(330, 60);
            this.txtAddress.Font = new Font("Tahoma", 10F);
            this.txtAddress.Multiline = true;
            this.txtAddress.ScrollBars = ScrollBars.Vertical;

            // Balance Label
            this.lblBalance.Text = "الرصيد الحالي:";
            this.lblBalance.Location = new Point(400, 230);
            this.lblBalance.Size = new Size(80, 23);
            this.lblBalance.TextAlign = ContentAlignment.MiddleRight;

            // Balance TextBox
            this.txtBalance.Location = new Point(50, 230);
            this.txtBalance.Size = new Size(150, 23);
            this.txtBalance.Font = new Font("Tahoma", 10F);
            this.txtBalance.TextAlign = HorizontalAlignment.Right;
            this.txtBalance.Text = "0.00";

            // IsActive CheckBox
            this.chkIsActive.Text = "نشط";
            this.chkIsActive.Location = new Point(300, 230);
            this.chkIsActive.Size = new Size(80, 23);
            this.chkIsActive.Checked = true;
            this.chkIsActive.Font = new Font("Tahoma", 10F);

            // Save Button
            this.btnSave.Text = "حفظ";
            this.btnSave.Location = new Point(280, 300);
            this.btnSave.Size = new Size(100, 35);
            this.btnSave.BackColor = Color.FromArgb(76, 175, 80);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            this.btnSave.Click += BtnSave_Click;

            // Cancel Button
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.Location = new Point(150, 300);
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.BackColor = Color.FromArgb(244, 67, 54);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Tahoma", 10F);
            this.btnCancel.Click += BtnCancel_Click;

            // Add controls to form
            this.Controls.Add(this.lblCustomerName);
            this.Controls.Add(this.txtCustomerName);
            this.Controls.Add(this.lblPhone);
            this.Controls.Add(this.txtPhone);
            this.Controls.Add(this.lblEmail);
            this.Controls.Add(this.txtEmail);
            this.Controls.Add(this.lblAddress);
            this.Controls.Add(this.txtAddress);
            this.Controls.Add(this.lblBalance);
            this.Controls.Add(this.txtBalance);
            this.Controls.Add(this.chkIsActive);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnCancel);

            this.ResumeLayout(false);

            // Set default button
            this.AcceptButton = this.btnSave;
            this.CancelButton = this.btnCancel;
        }

        private void LoadCustomerData()
        {
            if (Customer != null)
            {
                txtCustomerName.Text = Customer.CustomerName;
                txtPhone.Text = Customer.Phone;
                txtEmail.Text = Customer.Email;
                txtAddress.Text = Customer.Address;
                txtBalance.Text = Customer.CurrentBalance.ToString("F2");
                chkIsActive.Checked = Customer.IsActive;
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtCustomerName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCustomerName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPhone.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPhone.Focus();
                return false;
            }

            if (!decimal.TryParse(txtBalance.Text, out decimal balance))
            {
                MessageBox.Show("يرجى إدخال رصيد صحيح", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtBalance.Focus();
                return false;
            }

            // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(txtEmail.Text);
                    if (addr.Address != txtEmail.Text)
                    {
                        MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtEmail.Focus();
                        return false;
                    }
                }
                catch
                {
                    MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEmail.Focus();
                    return false;
                }
            }

            return true;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                Customer.CustomerName = txtCustomerName.Text.Trim();
                Customer.Phone = txtPhone.Text.Trim();
                Customer.Email = txtEmail.Text.Trim();
                Customer.Address = txtAddress.Text.Trim();
                Customer.CurrentBalance = decimal.Parse(txtBalance.Text);
                Customer.IsActive = chkIsActive.Checked;

                if (!isEditMode)
                {
                    Customer.CreatedDate = DateTime.Now;
                    Customer.CreatedBy = Services.AuthenticationService.CurrentUser?.Username ?? "System";
                }
                else
                {
                    Customer.ModifiedDate = DateTime.Now;
                    Customer.ModifiedBy = Services.AuthenticationService.CurrentUser?.Username ?? "System";
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
