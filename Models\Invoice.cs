using System;
using System.Collections.Generic;

namespace AccountingSystem.Models
{
    /// <summary>
    /// نموذج الفاتورة
    /// </summary>
    public class Invoice
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public int CustomerId { get; set; }
        public string InvoiceType { get; set; } // مبيعات، مشتريات، مردود مبيعات، مردود مشتريات
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public string Status { get; set; } // مدفوع، جزئي، غير مدفوع، مسودة، مؤكدة، ملغية
        public string PaymentMethod { get; set; } // نقدي، آجل، مختلط
        public string Currency { get; set; }
        public string Notes { get; set; }
        public string Reference { get; set; }
        public bool IsPosted { get; set; }
        public int? JournalEntryId { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }

        // تفاصيل الفاتورة
        public List<InvoiceDetail> Details { get; set; } = new List<InvoiceDetail>();

        // خصائص إضافية للعرض
        public Customer Customer { get; set; }
        public JournalEntry JournalEntry { get; set; }

        // خصائص محسوبة
        public decimal RemainingAmount => TotalAmount - PaidAmount;
        public bool IsOverdue => DueDate < DateTime.Now && Status != "مدفوع";
        public int DaysOverdue => IsOverdue ? (DateTime.Now - DueDate).Days : 0;
    }

    /// <summary>
    /// تفاصيل الفاتورة
    /// </summary>
    public class InvoiceDetail
    {
        public int Id { get; set; }
        public int InvoiceId { get; set; }
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal LineTotal { get; set; }
        public string Description { get; set; }
        public int LineNumber { get; set; }

        // خصائص إضافية للعرض
        public Product Product { get; set; }
        public Invoice Invoice { get; set; }
    }
}
