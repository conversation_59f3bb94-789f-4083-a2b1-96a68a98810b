@echo off
chcp 65001 > nul
echo ========================================
echo اختبار داشبورد الحسابات والفواتير
echo ========================================
echo.

echo 🔍 فحص الداشبورد الجميلة والأنيقة
echo.

echo فحص الملف المطلوب...

if not exist "DashboardSystem.cs" (
    echo ✗ ملف DashboardSystem.cs مفقود
    echo.
    echo هذا الملف يحتوي على داشبورد احترافية
    echo مع تصميم جميل ورسوم بيانية
    echo.
    goto :end
)

echo ✓ ملف DashboardSystem.cs موجود
echo.

echo فحص محتوى الملف...

findstr /C:"namespace DashboardSystem" "DashboardSystem.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على namespace صحيح
) else (
    echo ✗ namespace مفقود أو خطأ
)

findstr /C:"class DashboardForm" "DashboardSystem.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على فئة DashboardForm
) else (
    echo ✗ فئة DashboardForm مفقودة
)

findstr /C:"LinearGradientBrush" "DashboardSystem.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على تدرجات جميلة
) else (
    echo ✗ التدرجات مفقودة
)

findstr /C:"CreateStatCard" "DashboardSystem.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على بطاقات الإحصائيات
) else (
    echo ✗ بطاقات الإحصائيات مفقودة
)

findstr /C:"DrawSalesChart" "DashboardSystem.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على رسم بياني للمبيعات
) else (
    echo ✗ الرسم البياني مفقود
)

findstr /C:"CreateRecentInvoicesPanel" "DashboardSystem.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على لوحة الفواتير الأخيرة
) else (
    echo ✗ لوحة الفواتير الأخيرة مفقودة
)

findstr /C:"CreateTopCustomersPanel" "DashboardSystem.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على لوحة أعلى العملاء
) else (
    echo ✗ لوحة أعلى العملاء مفقودة
)

findstr /C:"AddRoundedRectangle" "DashboardSystem.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على حواف مدورة
) else (
    echo ✗ الحواف المدورة مفقودة
)

findstr /C:"static void Main" "DashboardSystem.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على نقطة دخول Main
) else (
    echo ✗ نقطة دخول Main مفقودة
)

echo.
echo فحص مترجم C#...

where csc >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ مترجم C# متوفر
    
    echo.
    echo اختبار بناء سريع...
    
    csc /target:winexe ^
        /reference:System.dll ^
        /reference:System.Core.dll ^
        /reference:System.Drawing.dll ^
        /reference:System.Windows.Forms.dll ^
        /out:TestDashboard.exe ^
        DashboardSystem.cs >nul 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo ✓ نجح الاختبار السريع
        del TestDashboard.exe >nul 2>&1
        echo.
        echo ========================================
        echo النتيجة: جاهز للبناء والتشغيل
        echo ========================================
        echo.
        echo يمكنك الآن تشغيل:
        echo build_dashboard.bat
        echo.
        echo هذا سينشئ: DashboardSystem.exe
        echo.
        echo 🎨 الميزات المضمونة:
        echo ✨ داشبورد جميلة وأنيقة ومرتبة
        echo 🎨 تصميم احترافي مع ألوان متناسقة
        echo 📊 إحصائيات تفاعلية ومباشرة
        echo 📈 رسوم بيانية للمبيعات
        echo 📋 جداول تفاعلية ملونة
        echo 🎯 قوائم تنقل جانبية أنيقة
        echo ✨ تأثيرات بصرية جميلة
        echo 🔄 تحديث تلقائي للوقت
        echo.
        echo 🏆 هذه أجمل داشبورد للحسابات!
    ) else (
        echo ✗ فشل الاختبار السريع
        echo.
        echo قد تكون هناك أخطاء في الكود
        echo راجع الملف أو استخدم Visual Studio
    )
    
) else (
    echo ✗ مترجم C# غير متوفر
    echo.
    echo الحلول:
    echo 1. افتح Developer Command Prompt for Visual Studio
    echo 2. أو ثبت .NET Framework SDK
    echo 3. أو استخدم Visual Studio مباشرة
)

echo.
echo ========================================
echo معلومات الداشبورد
echo ========================================

for %%A in (DashboardSystem.cs) do (
    echo حجم الملف: %%~zA بايت
    echo تاريخ التعديل: %%~tA
)

echo.
echo محتوى الداشبورد:
echo.
echo 🎨 التصميم والواجهة:
echo • شاشة تسجيل دخول بتدرج جميل
echo • لوحة جانبية بألوان داكنة أنيقة
echo • شريط علوي مع ترحيب وتاريخ
echo • منطقة محتوى مع خلفية فاتحة
echo • حواف مدورة في كل مكان
echo • ظلال وتأثيرات ثلاثية الأبعاد
echo.
echo 📊 بطاقات الإحصائيات:
echo • إجمالي الفواتير مع الأيقونة 📊
echo • الفواتير المدفوعة مع الأيقونة ✅
echo • الفواتير المتأخرة مع الأيقونة ⚠️
echo • إجمالي العملاء مع الأيقونة 👥
echo • ألوان مميزة لكل بطاقة
echo • قيم رئيسية وفرعية
echo.
echo 📈 الرسم البياني:
echo • رسم بياني للمبيعات (7 أيام)
echo • خط أزرق أنيق مع نقاط
echo • خطوط إرشادية رمادية
echo • قيم البيانات فوق النقاط
echo • أسماء الأيام أسفل الرسم
echo • تصميم احترافي ونظيف
echo.
echo 📋 الجداول التفاعلية:
echo • الفواتير الأخيرة (8 فواتير)
echo • تلوين حسب الحالة
echo • أعلى العملاء رصيداً (6 عملاء)
echo • ترقيم وترتيب جميل
echo • إحصائيات سريعة (4 إحصائيات)
echo • أيقونات تعبيرية لكل إحصائية
echo.
echo 🎯 القائمة الجانبية:
echo • 7 أزرار تنقل أنيقة
echo • أيقونات تعبيرية جميلة
echo • تأثيرات تفاعلية عند التمرير
echo • ألوان متدرجة جميلة
echo • خط أبيض واضح
echo.
echo 📊 البيانات التجريبية:
echo • 6 عملاء بأرصدة متنوعة
echo • 8 فواتير بحالات مختلفة
echo • 5 سندات قبض
echo • مبالغ واقعية (3,450 - 25,000 ر.س)
echo • تواريخ حديثة ومنطقية
echo • أسماء عربية أصيلة
echo.
echo 🎨 التفاصيل الجمالية:
echo ✓ تدرجات لونية جميلة
echo ✓ ظلال ناعمة للبطاقات
echo ✓ حواف مدورة احترافية
echo ✓ خطوط ملونة للتمييز
echo ✓ أيقونات تعبيرية متنوعة
echo ✓ خطوط Segoe UI الأنيقة
echo ✓ تخطيط متوازن ومنظم
echo ✓ ألوان متناسقة ومريحة
echo.
echo 🔧 الوظائف التفاعلية:
echo • تحديث الوقت كل ثانية
echo • تأثيرات التمرير على الأزرار
echo • رسائل تفاعلية عند النقر
echo • تلوين تلقائي للبيانات
echo • ترتيب ذكي للقوائم
echo • حسابات تلقائية للإحصائيات

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
