using System;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Services;
using IntegratedInvoiceSystem.Forms;

namespace IntegratedInvoiceSystem
{
    /// <summary>
    /// نقطة دخول التطبيق المتكامل
    /// </summary>
    static class IntegratedProgram
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق المتكامل
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // تهيئة التطبيق
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // تحميل البيانات عند بدء التطبيق
                DataService.LoadData();
                
                // تشغيل نموذج تسجيل الدخول
                using (var loginForm = new LoginForm())
                {
                    Application.Run(loginForm);
                }
            }
            catch (Exception ex)
            {
                // معالجة الأخطاء العامة
                MessageBox.Show($"حدث خطأ في التطبيق:\n{ex.Message}", 
                    "خطأ في النظام", 
                    MessageBoxButtons.OK, 
                    MessageBoxIcon.Error);
            }
            finally
            {
                // حفظ البيانات عند إغلاق التطبيق
                try
                {
                    DataService.SaveData();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ البيانات:\n{ex.Message}", 
                        "خطأ في الحفظ", 
                        MessageBoxButtons.OK, 
                        MessageBoxIcon.Warning);
                }
            }
        }
    }
}
