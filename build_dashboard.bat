@echo off
chcp 65001 > nul
echo ========================================
echo بناء داشبورد الحسابات والفواتير
echo ========================================
echo.

echo 🎨 داشبورد جميلة وأنيقة ومرتبة
echo ✨ تصميم احترافي مع رسوم بيانية
echo 📊 إحصائيات تفاعلية ومباشرة
echo.

rem التحقق من وجود مترجم C#
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ مترجم C# غير متوفر
    echo.
    echo الحلول:
    echo 1. افتح Developer Command Prompt for Visual Studio
    echo 2. أو ثبت .NET Framework SDK
    echo 3. أو استخدم Visual Studio لبناء المشروع
    echo.
    goto :end
)

echo ✓ مترجم C# متوفر
echo.

echo فحص الملف المطلوب...
if not exist "DashboardSystem.cs" (
    echo ✗ ملف DashboardSystem.cs مفقود
    echo يرجى التأكد من وجود الملف
    goto :end
)

echo ✓ ملف DashboardSystem.cs موجود
echo.

echo بناء الداشبورد...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /out:DashboardSystem.exe ^
    DashboardSystem.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء الداشبورد بنجاح!
    echo.
    echo تم إنشاء: DashboardSystem.exe
    echo.
    
    echo إنشاء ملف الإعداد...
    echo ^<?xml version="1.0" encoding="utf-8"?^> > DashboardSystem.exe.config
    echo ^<configuration^> >> DashboardSystem.exe.config
    echo   ^<startup^> >> DashboardSystem.exe.config
    echo     ^<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/^> >> DashboardSystem.exe.config
    echo   ^</startup^> >> DashboardSystem.exe.config
    echo ^</configuration^> >> DashboardSystem.exe.config
    
    echo ✓ تم إنشاء ملف الإعداد
    echo.
    
    set /p run="هل تريد تشغيل الداشبورد الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل الداشبورد...
        start "" "DashboardSystem.exe"
        echo.
        echo ✓ تم تشغيل الداشبورد بنجاح!
    )
    
    echo.
    echo ========================================
    echo داشبورد الحسابات والفواتير
    echo ========================================
    echo.
    echo ملف التشغيل: DashboardSystem.exe
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo 🎨 مميزات التصميم:
    echo ✨ واجهة حديثة وأنيقة
    echo 🎨 ألوان متناسقة واحترافية
    echo 🖼️ تدرجات جميلة وظلال
    echo 📱 تصميم متجاوب ومرن
    echo 🔄 تحديث تلقائي للوقت
    echo 🎯 أيقونات تعبيرية جميلة
    echo.
    echo 📊 الإحصائيات المتوفرة:
    echo • إجمالي الفواتير والمبالغ
    echo • الفواتير المدفوعة والمتأخرة
    echo • إجمالي العملاء والأرصدة
    echo • متوسط قيمة الفاتورة
    echo • نسبة التحصيل
    echo • المدفوعات اليومية
    echo.
    echo 📈 الرسوم البيانية:
    echo • رسم بياني للمبيعات (7 أيام)
    echo • نقاط بيانية ملونة
    echo • خطوط إرشادية
    echo • قيم تفاعلية
    echo • تصميم احترافي
    echo.
    echo 📋 الجداول التفاعلية:
    echo • الفواتير الأخيرة مع التلوين
    echo • أعلى العملاء رصيداً
    echo • إحصائيات سريعة
    echo • ترتيب ذكي للبيانات
    echo.
    echo 🎯 القوائم الجانبية:
    echo • 🏠 الرئيسية - الداشبورد الكامل
    echo • 📊 الفواتير - إدارة شاملة
    echo • 💰 السندات - تتبع المدفوعات
    echo • 👥 العملاء - إدارة قاعدة البيانات
    echo • 📈 التقارير - تحليلات مفصلة
    echo • ⚙️ الإعدادات - تخصيص النظام
    echo • 🚪 خروج - تسجيل خروج آمن
    echo.
    echo 📊 البيانات التجريبية الغنية:
    echo • 6 عملاء بأرصدة متنوعة
    echo • 8 فواتير بحالات مختلفة
    echo • 5 سندات قبض
    echo • مبالغ واقعية ومتنوعة
    echo • تواريخ حديثة ومنطقية
    echo.
    echo 🎨 التفاصيل الجمالية:
    echo ✓ شاشة تسجيل دخول بتدرج جميل
    echo ✓ لوحة جانبية بألوان داكنة أنيقة
    echo ✓ بطاقات إحصائيات بظلال
    echo ✓ حواف مدورة في كل مكان
    echo ✓ خطوط ملونة للتمييز
    echo ✓ تأثيرات تفاعلية عند التمرير
    echo ✓ أيقونات تعبيرية جميلة
    echo ✓ خطوط Segoe UI الأنيقة
    echo.
    echo 🔧 كيفية الاستخدام:
    echo 1. شغل البرنامج
    echo 2. ستظهر شاشة تسجيل دخول جميلة
    echo 3. سجل دخول بـ admin/admin123
    echo 4. ستظهر الداشبورد الرئيسية
    echo 5. استكشف الإحصائيات والرسوم البيانية
    echo 6. استخدم القائمة الجانبية للتنقل
    echo 7. اضغط على أي قسم لاستكشاف الميزات
    echo.
    echo 🎉 مميزات خاصة:
    echo • تحديث الوقت كل ثانية
    echo • رسالة ترحيب شخصية
    echo • تلوين تلقائي حسب الحالة
    echo • ترتيب ذكي للبيانات
    echo • تصميم متجاوب مع الشاشة
    echo • ظلال وتأثيرات ثلاثية الأبعاد
    echo • انتقالات سلسة بين الأقسام
    echo.
    echo 🏆 هذه أجمل داشبورد للحسابات والفواتير!
    
) else (
    echo.
    echo ✗ فشل في بناء الداشبورد
    echo.
    echo الأخطاء المحتملة:
    echo 1. أخطاء في الكود المصدري
    echo 2. مراجع مفقودة
    echo 3. مشاكل في مترجم C#
    echo.
    echo للتشخيص:
    echo 1. افتح DashboardSystem.cs وتحقق من الأخطاء
    echo 2. تأكد من وجود جميع المراجع المطلوبة
    echo 3. استخدم Visual Studio للحصول على تفاصيل الأخطاء
    echo.
    echo بدائل أخرى:
    echo • استخدم Visual Studio لبناء المشروع
    echo • راجع الكود للتأكد من عدم وجود أخطاء إملائية
)

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
