using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using AccountingSystem.Data;
using AccountingSystem.Models;

namespace AccountingSystem.Services
{
    /// <summary>
    /// خدمة إدارة القيود اليومية
    /// </summary>
    public class JournalService
    {
        /// <summary>
        /// الحصول على جميع القيود اليومية
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="status">الحالة</param>
        /// <returns>قائمة القيود اليومية</returns>
        public static List<JournalEntry> GetJournalEntries(DateTime? fromDate = null, DateTime? toDate = null, string status = null)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    string whereClause = "WHERE 1=1";
                    var parameters = new DynamicParameters();
                    
                    if (fromDate.HasValue)
                    {
                        whereClause += " AND EntryDate >= @FromDate";
                        parameters.Add("FromDate", fromDate.Value.ToString("yyyy-MM-dd"));
                    }
                    
                    if (toDate.HasValue)
                    {
                        whereClause += " AND EntryDate <= @ToDate";
                        parameters.Add("ToDate", toDate.Value.ToString("yyyy-MM-dd"));
                    }
                    
                    if (!string.IsNullOrEmpty(status) && status != "الكل")
                    {
                        whereClause += " AND Status = @Status";
                        parameters.Add("Status", status);
                    }
                    
                    return connection.Query<JournalEntry>($@"
                        SELECT * FROM JournalEntries 
                        {whereClause}
                        ORDER BY EntryDate DESC, Id DESC", parameters).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على القيود اليومية: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// الحصول على قيد يومي بالمعرف
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <returns>القيد اليومي مع التفاصيل</returns>
        public static JournalEntry GetJournalEntryById(int journalEntryId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // الحصول على القيد الرئيسي
                    var journalEntry = connection.QueryFirstOrDefault<JournalEntry>(@"
                        SELECT * FROM JournalEntries WHERE Id = @Id", new { Id = journalEntryId });
                    
                    if (journalEntry != null)
                    {
                        // الحصول على تفاصيل القيد
                        journalEntry.Details = connection.Query<JournalEntryDetail>(@"
                            SELECT jed.*, a.AccountName, a.AccountCode
                            FROM JournalEntryDetails jed
                            INNER JOIN Accounts a ON jed.AccountId = a.Id
                            WHERE jed.JournalEntryId = @JournalEntryId
                            ORDER BY jed.LineNumber", 
                            new { JournalEntryId = journalEntryId }).ToList();
                    }
                    
                    return journalEntry;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على القيد اليومي: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// إنشاء قيد يومي جديد
        /// </summary>
        /// <param name="journalEntry">القيد اليومي</param>
        /// <returns>معرف القيد الجديد</returns>
        public static int CreateJournalEntry(JournalEntry journalEntry)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // التحقق من توازن القيد
                            if (Math.Abs(journalEntry.TotalDebit - journalEntry.TotalCredit) > 0.01m)
                            {
                                throw new Exception("القيد غير متوازن");
                            }
                            
                            // إدراج القيد الرئيسي
                            var journalEntryId = connection.QuerySingle<int>(@"
                                INSERT INTO JournalEntries (
                                    EntryNumber, EntryDate, Description, Reference,
                                    TotalDebit, TotalCredit, Status, IsPosted,
                                    CreatedDate, CreatedBy
                                ) VALUES (
                                    @EntryNumber, @EntryDate, @Description, @Reference,
                                    @TotalDebit, @TotalCredit, @Status, @IsPosted,
                                    @CreatedDate, @CreatedBy
                                );
                                SELECT last_insert_rowid();",
                                new
                                {
                                    EntryNumber = journalEntry.EntryNumber,
                                    EntryDate = journalEntry.EntryDate.ToString("yyyy-MM-dd"),
                                    Description = journalEntry.Description,
                                    Reference = journalEntry.Reference,
                                    TotalDebit = journalEntry.TotalDebit,
                                    TotalCredit = journalEntry.TotalCredit,
                                    Status = journalEntry.Status ?? "مسودة",
                                    IsPosted = journalEntry.IsPosted ? 1 : 0,
                                    CreatedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                    CreatedBy = AuthenticationService.CurrentUser?.Username
                                }, transaction);
                            
                            // إدراج تفاصيل القيد
                            if (journalEntry.Details != null && journalEntry.Details.Any())
                            {
                                int lineNumber = 1;
                                foreach (var detail in journalEntry.Details)
                                {
                                    connection.Execute(@"
                                        INSERT INTO JournalEntryDetails (
                                            JournalEntryId, AccountId, DebitAmount, CreditAmount,
                                            Description, Reference, LineNumber
                                        ) VALUES (
                                            @JournalEntryId, @AccountId, @DebitAmount, @CreditAmount,
                                            @Description, @Reference, @LineNumber
                                        )",
                                        new
                                        {
                                            JournalEntryId = journalEntryId,
                                            AccountId = detail.AccountId,
                                            DebitAmount = detail.DebitAmount,
                                            CreditAmount = detail.CreditAmount,
                                            Description = detail.Description,
                                            Reference = detail.Reference,
                                            LineNumber = lineNumber++
                                        }, transaction);
                                }
                            }
                            
                            // إذا كان القيد مرحل، تحديث أرصدة الحسابات
                            if (journalEntry.IsPosted)
                            {
                                PostJournalEntryToAccounts(journalEntryId, connection, transaction);
                            }
                            
                            transaction.Commit();
                            return journalEntryId;
                        }
                        catch (Exception)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء القيد اليومي: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// تحديث قيد يومي
        /// </summary>
        /// <param name="journalEntry">القيد اليومي</param>
        public static void UpdateJournalEntry(JournalEntry journalEntry)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // التحقق من أن القيد غير مرحل
                            var existingEntry = connection.QueryFirstOrDefault<JournalEntry>(@"
                                SELECT * FROM JournalEntries WHERE Id = @Id", 
                                new { Id = journalEntry.Id }, transaction);
                            
                            if (existingEntry == null)
                            {
                                throw new Exception("القيد غير موجود");
                            }
                            
                            if (existingEntry.IsPosted)
                            {
                                throw new Exception("لا يمكن تعديل قيد مرحل");
                            }
                            
                            // التحقق من توازن القيد
                            if (Math.Abs(journalEntry.TotalDebit - journalEntry.TotalCredit) > 0.01m)
                            {
                                throw new Exception("القيد غير متوازن");
                            }
                            
                            // تحديث القيد الرئيسي
                            connection.Execute(@"
                                UPDATE JournalEntries SET 
                                    EntryDate = @EntryDate,
                                    Description = @Description,
                                    Reference = @Reference,
                                    TotalDebit = @TotalDebit,
                                    TotalCredit = @TotalCredit,
                                    Status = @Status,
                                    ModifiedDate = @ModifiedDate,
                                    ModifiedBy = @ModifiedBy
                                WHERE Id = @Id",
                                new
                                {
                                    Id = journalEntry.Id,
                                    EntryDate = journalEntry.EntryDate.ToString("yyyy-MM-dd"),
                                    Description = journalEntry.Description,
                                    Reference = journalEntry.Reference,
                                    TotalDebit = journalEntry.TotalDebit,
                                    TotalCredit = journalEntry.TotalCredit,
                                    Status = journalEntry.Status,
                                    ModifiedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                    ModifiedBy = AuthenticationService.CurrentUser?.Username
                                }, transaction);
                            
                            // حذف التفاصيل القديمة
                            connection.Execute("DELETE FROM JournalEntryDetails WHERE JournalEntryId = @JournalEntryId", 
                                new { JournalEntryId = journalEntry.Id }, transaction);
                            
                            // إدراج التفاصيل الجديدة
                            if (journalEntry.Details != null && journalEntry.Details.Any())
                            {
                                int lineNumber = 1;
                                foreach (var detail in journalEntry.Details)
                                {
                                    connection.Execute(@"
                                        INSERT INTO JournalEntryDetails (
                                            JournalEntryId, AccountId, DebitAmount, CreditAmount,
                                            Description, Reference, LineNumber
                                        ) VALUES (
                                            @JournalEntryId, @AccountId, @DebitAmount, @CreditAmount,
                                            @Description, @Reference, @LineNumber
                                        )",
                                        new
                                        {
                                            JournalEntryId = journalEntry.Id,
                                            AccountId = detail.AccountId,
                                            DebitAmount = detail.DebitAmount,
                                            CreditAmount = detail.CreditAmount,
                                            Description = detail.Description,
                                            Reference = detail.Reference,
                                            LineNumber = lineNumber++
                                        }, transaction);
                                }
                            }
                            
                            transaction.Commit();
                        }
                        catch (Exception)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث القيد اليومي: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// حذف قيد يومي
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        public static void DeleteJournalEntry(int journalEntryId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // التحقق من أن القيد غير مرحل
                            var journalEntry = connection.QueryFirstOrDefault<JournalEntry>(@"
                                SELECT * FROM JournalEntries WHERE Id = @Id", 
                                new { Id = journalEntryId }, transaction);
                            
                            if (journalEntry == null)
                            {
                                throw new Exception("القيد غير موجود");
                            }
                            
                            if (journalEntry.IsPosted)
                            {
                                throw new Exception("لا يمكن حذف قيد مرحل");
                            }
                            
                            // حذف تفاصيل القيد
                            connection.Execute("DELETE FROM JournalEntryDetails WHERE JournalEntryId = @JournalEntryId", 
                                new { JournalEntryId = journalEntryId }, transaction);
                            
                            // حذف القيد الرئيسي
                            connection.Execute("DELETE FROM JournalEntries WHERE Id = @Id", 
                                new { Id = journalEntryId }, transaction);
                            
                            transaction.Commit();
                        }
                        catch (Exception)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف القيد اليومي: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// ترحيل قيد يومي
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        public static void PostJournalEntry(int journalEntryId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // التحقق من أن القيد غير مرحل
                            var journalEntry = connection.QueryFirstOrDefault<JournalEntry>(@"
                                SELECT * FROM JournalEntries WHERE Id = @Id", 
                                new { Id = journalEntryId }, transaction);
                            
                            if (journalEntry == null)
                            {
                                throw new Exception("القيد غير موجود");
                            }
                            
                            if (journalEntry.IsPosted)
                            {
                                throw new Exception("القيد مرحل مسبقاً");
                            }
                            
                            // التحقق من توازن القيد
                            if (Math.Abs(journalEntry.TotalDebit - journalEntry.TotalCredit) > 0.01m)
                            {
                                throw new Exception("القيد غير متوازن");
                            }
                            
                            // تحديث حالة القيد
                            connection.Execute(@"
                                UPDATE JournalEntries SET 
                                    Status = 'مؤكد',
                                    IsPosted = 1,
                                    PostedDate = @PostedDate,
                                    PostedBy = @PostedBy
                                WHERE Id = @Id",
                                new
                                {
                                    Id = journalEntryId,
                                    PostedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                    PostedBy = AuthenticationService.CurrentUser?.Username
                                }, transaction);
                            
                            // ترحيل القيد إلى أرصدة الحسابات
                            PostJournalEntryToAccounts(journalEntryId, connection, transaction);
                            
                            transaction.Commit();
                        }
                        catch (Exception)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في ترحيل القيد اليومي: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// ترحيل القيد إلى أرصدة الحسابات
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <param name="connection">اتصال قاعدة البيانات</param>
        /// <param name="transaction">المعاملة</param>
        private static void PostJournalEntryToAccounts(int journalEntryId, System.Data.IDbConnection connection, System.Data.IDbTransaction transaction)
        {
            // الحصول على تفاصيل القيد
            var details = connection.Query<JournalEntryDetail>(@"
                SELECT * FROM JournalEntryDetails WHERE JournalEntryId = @JournalEntryId", 
                new { JournalEntryId = journalEntryId }, transaction).ToList();
            
            foreach (var detail in details)
            {
                if (detail.DebitAmount > 0)
                {
                    AccountService.UpdateAccountBalance(detail.AccountId, detail.DebitAmount, true);
                }
                
                if (detail.CreditAmount > 0)
                {
                    AccountService.UpdateAccountBalance(detail.AccountId, detail.CreditAmount, false);
                }
            }
        }
        
        /// <summary>
        /// توليد رقم قيد جديد
        /// </summary>
        /// <returns>رقم القيد الجديد</returns>
        public static string GenerateEntryNumber()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    var lastEntry = connection.QueryFirstOrDefault<string>(@"
                        SELECT EntryNumber 
                        FROM JournalEntries 
                        ORDER BY Id DESC 
                        LIMIT 1");
                    
                    int nextNumber = 1;
                    if (!string.IsNullOrEmpty(lastEntry))
                    {
                        // استخراج الرقم من رقم القيد
                        var parts = lastEntry.Split('-');
                        if (parts.Length > 1 && int.TryParse(parts[1], out int number))
                        {
                            nextNumber = number + 1;
                        }
                    }
                    
                    return $"JE-{nextNumber:D6}";
                }
            }
            catch (Exception)
            {
                return $"JE-{DateTime.Now:yyyyMMdd}-001";
            }
        }
        
        /// <summary>
        /// البحث في القيود اليومية
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة القيود المطابقة</returns>
        public static List<JournalEntry> SearchJournalEntries(string searchTerm)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    return connection.Query<JournalEntry>(@"
                        SELECT * FROM JournalEntries 
                        WHERE EntryNumber LIKE @SearchTerm 
                            OR Description LIKE @SearchTerm 
                            OR Reference LIKE @SearchTerm
                        ORDER BY EntryDate DESC", 
                        new { SearchTerm = $"%{searchTerm}%" }).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث في القيود اليومية: {ex.Message}", ex);
            }
        }
    }
}
