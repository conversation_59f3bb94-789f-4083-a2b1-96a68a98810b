using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AccountingSystem.Models;
using AccountingSystem.Services;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج إدارة العملاء
    /// </summary>
    public partial class CustomersForm : Form
    {
        private DataGridView dgvCustomers;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnClose;
        private TextBox txtSearch;
        private Label lblSearch;
        private List<Customer> customers;

        public CustomersForm()
        {
            InitializeComponent();
            LoadCustomers();
        }

        private void InitializeComponent()
        {
            this.dgvCustomers = new DataGridView();
            this.btnAdd = new Button();
            this.btnEdit = new Button();
            this.btnDelete = new Button();
            this.btnClose = new Button();
            this.txtSearch = new TextBox();
            this.lblSearch = new Label();

            this.SuspendLayout();

            // Form
            this.Text = "إدارة العملاء";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Search Label
            this.lblSearch.Text = "البحث:";
            this.lblSearch.Location = new Point(800, 20);
            this.lblSearch.Size = new Size(60, 23);
            this.lblSearch.TextAlign = ContentAlignment.MiddleRight;

            // Search TextBox
            this.txtSearch.Location = new Point(600, 20);
            this.txtSearch.Size = new Size(180, 23);
            this.txtSearch.TextChanged += TxtSearch_TextChanged;

            // Add Button
            this.btnAdd.Text = "إضافة عميل";
            this.btnAdd.Location = new Point(480, 18);
            this.btnAdd.Size = new Size(100, 27);
            this.btnAdd.BackColor = Color.FromArgb(76, 175, 80);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.FlatStyle = FlatStyle.Flat;
            this.btnAdd.Click += BtnAdd_Click;

            // Edit Button
            this.btnEdit.Text = "تعديل";
            this.btnEdit.Location = new Point(360, 18);
            this.btnEdit.Size = new Size(100, 27);
            this.btnEdit.BackColor = Color.FromArgb(255, 152, 0);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.Click += BtnEdit_Click;

            // Delete Button
            this.btnDelete.Text = "حذف";
            this.btnDelete.Location = new Point(240, 18);
            this.btnDelete.Size = new Size(100, 27);
            this.btnDelete.BackColor = Color.FromArgb(244, 67, 54);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.Click += BtnDelete_Click;

            // Close Button
            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(20, 18);
            this.btnClose.Size = new Size(100, 27);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Click += BtnClose_Click;

            // DataGridView
            this.dgvCustomers.Location = new Point(20, 60);
            this.dgvCustomers.Size = new Size(860, 480);
            this.dgvCustomers.AllowUserToAddRows = false;
            this.dgvCustomers.AllowUserToDeleteRows = false;
            this.dgvCustomers.ReadOnly = true;
            this.dgvCustomers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvCustomers.RightToLeft = RightToLeft.Yes;
            this.dgvCustomers.Font = new Font("Tahoma", 10F);
            this.dgvCustomers.DoubleClick += DgvCustomers_DoubleClick;

            SetupDataGridView();

            // Add controls to form
            this.Controls.Add(this.lblSearch);
            this.Controls.Add(this.txtSearch);
            this.Controls.Add(this.btnAdd);
            this.Controls.Add(this.btnEdit);
            this.Controls.Add(this.btnDelete);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.dgvCustomers);

            this.ResumeLayout(false);
        }

        private void SetupDataGridView()
        {
            dgvCustomers.Columns.Clear();

            dgvCustomers.Columns.Add("Id", "المعرف");
            dgvCustomers.Columns["Id"].Width = 60;
            dgvCustomers.Columns["Id"].Visible = false;

            dgvCustomers.Columns.Add("CustomerCode", "كود العميل");
            dgvCustomers.Columns["CustomerCode"].Width = 100;

            dgvCustomers.Columns.Add("CustomerName", "اسم العميل");
            dgvCustomers.Columns["CustomerName"].Width = 200;

            dgvCustomers.Columns.Add("Phone", "الهاتف");
            dgvCustomers.Columns["Phone"].Width = 120;

            dgvCustomers.Columns.Add("Email", "البريد الإلكتروني");
            dgvCustomers.Columns["Email"].Width = 150;

            dgvCustomers.Columns.Add("Address", "العنوان");
            dgvCustomers.Columns["Address"].Width = 200;

            dgvCustomers.Columns.Add("Balance", "الرصيد");
            dgvCustomers.Columns["Balance"].Width = 100;
            dgvCustomers.Columns["Balance"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvCustomers.Columns["Balance"].DefaultCellStyle.Format = "N2";
        }

        private void LoadCustomers()
        {
            try
            {
                // إنشاء بيانات تجريبية للعملاء
                customers = new List<Customer>
                {
                    new Customer { Id = 1, CustomerCode = "C001", CustomerName = "أحمد محمد علي", Phone = "0501234567", Email = "<EMAIL>", Address = "الرياض، المملكة العربية السعودية", CurrentBalance = 5000.00m, IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                    new Customer { Id = 2, CustomerCode = "C002", CustomerName = "فاطمة عبدالله", Phone = "0507654321", Email = "<EMAIL>", Address = "جدة، المملكة العربية السعودية", CurrentBalance = -2500.00m, IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                    new Customer { Id = 3, CustomerCode = "C003", CustomerName = "محمد سعد الدين", Phone = "0551122334", Email = "<EMAIL>", Address = "الدمام، المملكة العربية السعودية", CurrentBalance = 0.00m, IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                    new Customer { Id = 4, CustomerCode = "C004", CustomerName = "نورا أحمد", Phone = "0554433221", Email = "<EMAIL>", Address = "مكة المكرمة، المملكة العربية السعودية", CurrentBalance = 12000.00m, IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                    new Customer { Id = 5, CustomerCode = "C005", CustomerName = "خالد العتيبي", Phone = "0556677889", Email = "<EMAIL>", Address = "المدينة المنورة، المملكة العربية السعودية", CurrentBalance = -800.00m, IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" }
                };

                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            dgvCustomers.Rows.Clear();

            var filteredCustomers = customers;

            // تطبيق فلتر البحث
            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                string searchText = txtSearch.Text.ToLower();
                filteredCustomers = customers.Where(c =>
                    c.CustomerName.ToLower().Contains(searchText) ||
                    c.CustomerCode.ToLower().Contains(searchText) ||
                    c.Phone.Contains(searchText) ||
                    (c.Email != null && c.Email.ToLower().Contains(searchText))
                ).ToList();
            }

            foreach (var customer in filteredCustomers.Where(c => c.IsActive))
            {
                var rowIndex = dgvCustomers.Rows.Add();
                var row = dgvCustomers.Rows[rowIndex];

                row.Cells["Id"].Value = customer.Id;
                row.Cells["CustomerCode"].Value = customer.CustomerCode;
                row.Cells["CustomerName"].Value = customer.CustomerName;
                row.Cells["Phone"].Value = customer.Phone;
                row.Cells["Email"].Value = customer.Email;
                row.Cells["Address"].Value = customer.Address;
                row.Cells["Balance"].Value = customer.CurrentBalance;

                // تلوين الصفوف حسب الرصيد
                if (customer.CurrentBalance > 0)
                {
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else if (customer.CurrentBalance < 0)
                {
                    row.DefaultCellStyle.BackColor = Color.LightCoral;
                }
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            RefreshDataGridView();
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new AddEditCustomerForm();
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    var newCustomer = addForm.Customer;
                    newCustomer.Id = customers.Count > 0 ? customers.Max(c => c.Id) + 1 : 1;
                    newCustomer.CustomerCode = $"C{newCustomer.Id:000}";
                    customers.Add(newCustomer);
                    RefreshDataGridView();

                    MessageBox.Show("تم إضافة العميل بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            EditSelectedCustomer();
        }

        private void DgvCustomers_DoubleClick(object sender, EventArgs e)
        {
            EditSelectedCustomer();
        }

        private void EditSelectedCustomer()
        {
            try
            {
                if (dgvCustomers.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار عميل للتعديل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int customerId = (int)dgvCustomers.SelectedRows[0].Cells["Id"].Value;
                var customer = customers.FirstOrDefault(c => c.Id == customerId);

                if (customer != null)
                {
                    var editForm = new AddEditCustomerForm(customer);
                    if (editForm.ShowDialog() == DialogResult.OK)
                    {
                        var updatedCustomer = editForm.Customer;
                        var index = customers.FindIndex(c => c.Id == customerId);
                        if (index >= 0)
                        {
                            customers[index] = updatedCustomer;
                            RefreshDataGridView();

                            MessageBox.Show("تم تعديل العميل بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvCustomers.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار عميل للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int customerId = (int)dgvCustomers.SelectedRows[0].Cells["Id"].Value;
                var customer = customers.FirstOrDefault(c => c.Id == customerId);

                if (customer != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف العميل '{customer.CustomerName}'؟",
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        customer.IsActive = false; // حذف منطقي
                        RefreshDataGridView();

                        MessageBox.Show("تم حذف العميل بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
