using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using AccountingSystem.Models;

namespace AccountingSystem.Data
{
    /// <summary>
    /// مساعد قاعدة البيانات المبسط (بدون SQLite)
    /// يستخدم ملفات نصية لحفظ البيانات
    /// </summary>
    public static class SimpleDatabaseHelper
    {
        private static readonly string DataFolder = "SimpleData";
        private static readonly string UsersFile = Path.Combine(DataFolder, "users.txt");
        private static readonly string AccountsFile = Path.Combine(DataFolder, "accounts.txt");
        private static readonly string JournalEntriesFile = Path.Combine(DataFolder, "journal_entries.txt");
        private static readonly string CustomersFile = Path.Combine(DataFolder, "customers.txt");
        private static readonly string ProductsFile = Path.Combine(DataFolder, "products.txt");

        static SimpleDatabaseHelper()
        {
            InitializeDatabase();
        }

        /// <summary>
        /// تهيئة قاعدة البيانات المبسطة
        /// </summary>
        public static void InitializeDatabase()
        {
            try
            {
                // إنشاء مجلد البيانات إذا لم يكن موجوداً
                if (!Directory.Exists(DataFolder))
                {
                    Directory.CreateDirectory(DataFolder);
                }

                // إنشاء ملفات البيانات إذا لم تكن موجودة
                CreateInitialData();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء البيانات الأولية
        /// </summary>
        private static void CreateInitialData()
        {
            // إنشاء مستخدم افتراضي
            if (!File.Exists(UsersFile))
            {
                var defaultUser = new User
                {
                    Id = 1,
                    Username = "admin",
                    PasswordHash = Utils.SecurityHelper.HashPassword("admin123"),
                    FullName = "مدير النظام",
                    Email = "<EMAIL>",
                    Role = "Admin",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = "System"
                };

                SaveUser(defaultUser);
            }

            // إنشاء حسابات افتراضية
            if (!File.Exists(AccountsFile))
            {
                CreateDefaultAccounts();
            }
        }

        /// <summary>
        /// إنشاء الحسابات الافتراضية
        /// </summary>
        private static void CreateDefaultAccounts()
        {
            var accounts = new List<Account>
            {
                new Account { Id = 1, AccountCode = "1", AccountName = "الأصول", AccountType = "أصول", Level = 1, IsParent = true, DebitCredit = "مدين", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                new Account { Id = 2, AccountCode = "2", AccountName = "الخصوم", AccountType = "خصوم", Level = 1, IsParent = true, DebitCredit = "دائن", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                new Account { Id = 3, AccountCode = "3", AccountName = "حقوق الملكية", AccountType = "حقوق ملكية", Level = 1, IsParent = true, DebitCredit = "دائن", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                new Account { Id = 4, AccountCode = "4", AccountName = "الإيرادات", AccountType = "إيرادات", Level = 1, IsParent = true, DebitCredit = "دائن", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                new Account { Id = 5, AccountCode = "5", AccountName = "المصروفات", AccountType = "مصروفات", Level = 1, IsParent = true, DebitCredit = "مدين", IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },

                // حسابات فرعية
                new Account { Id = 11, AccountCode = "101", AccountName = "النقدية", AccountType = "أصول", Level = 2, IsParent = false, DebitCredit = "مدين", ParentAccountId = 1, Balance = 10000, IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                new Account { Id = 12, AccountCode = "102", AccountName = "البنك", AccountType = "أصول", Level = 2, IsParent = false, DebitCredit = "مدين", ParentAccountId = 1, Balance = 50000, IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                new Account { Id = 21, AccountCode = "201", AccountName = "الدائنون", AccountType = "خصوم", Level = 2, IsParent = false, DebitCredit = "دائن", ParentAccountId = 2, Balance = 5000, IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                new Account { Id = 31, AccountCode = "301", AccountName = "رأس المال", AccountType = "حقوق ملكية", Level = 2, IsParent = false, DebitCredit = "دائن", ParentAccountId = 3, Balance = 100000, IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                new Account { Id = 41, AccountCode = "401", AccountName = "مبيعات", AccountType = "إيرادات", Level = 2, IsParent = false, DebitCredit = "دائن", ParentAccountId = 4, Balance = 25000, IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" },
                new Account { Id = 51, AccountCode = "501", AccountName = "مصروفات عمومية", AccountType = "مصروفات", Level = 2, IsParent = false, DebitCredit = "مدين", ParentAccountId = 5, Balance = 8000, IsActive = true, CreatedDate = DateTime.Now, CreatedBy = "System" }
            };

            SaveAccounts(accounts);
        }

        /// <summary>
        /// حفظ مستخدم
        /// </summary>
        public static void SaveUser(User user)
        {
            try
            {
                var users = GetAllUsers();

                // تحديث أو إضافة
                var existingIndex = users.FindIndex(u => u.Id == user.Id);
                if (existingIndex >= 0)
                {
                    users[existingIndex] = user;
                }
                else
                {
                    if (user.Id == 0)
                    {
                        user.Id = users.Count > 0 ? users.Max(u => u.Id) + 1 : 1;
                    }
                    users.Add(user);
                }

                SaveUsers(users);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ المستخدم: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        public static List<User> GetAllUsers()
        {
            try
            {
                if (!File.Exists(UsersFile))
                    return new List<User>();

                var users = new List<User>();
                var lines = File.ReadAllLines(UsersFile, Encoding.UTF8);

                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var parts = line.Split('|');
                    if (parts.Length >= 8)
                    {
                        users.Add(new User
                        {
                            Id = int.Parse(parts[0]),
                            Username = parts[1],
                            PasswordHash = parts[2],
                            FullName = parts[3],
                            Email = parts[4],
                            Role = parts[5],
                            IsActive = bool.Parse(parts[6]),
                            CreatedDate = DateTime.Parse(parts[7]),
                            CreatedBy = parts.Length > 8 ? parts[8] : ""
                        });
                    }
                }

                return users;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة المستخدمين: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ قائمة المستخدمين
        /// </summary>
        private static void SaveUsers(List<User> users)
        {
            try
            {
                var lines = new List<string>();
                foreach (var user in users)
                {
                    lines.Add($"{user.Id}|{user.Username}|{user.PasswordHash}|{user.FullName}|{user.Email}|{user.Role}|{user.IsActive}|{user.CreatedDate:yyyy-MM-dd HH:mm:ss}|{user.CreatedBy}");
                }

                File.WriteAllLines(UsersFile, lines, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ المستخدمين: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على جميع الحسابات
        /// </summary>
        public static List<Account> GetAllAccounts()
        {
            try
            {
                if (!File.Exists(AccountsFile))
                    return new List<Account>();

                var accounts = new List<Account>();
                var lines = File.ReadAllLines(AccountsFile, Encoding.UTF8);

                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var parts = line.Split('|');
                    if (parts.Length >= 12)
                    {
                        accounts.Add(new Account
                        {
                            Id = int.Parse(parts[0]),
                            AccountCode = parts[1],
                            AccountName = parts[2],
                            AccountType = parts[3],
                            Level = int.Parse(parts[4]),
                            IsParent = bool.Parse(parts[5]),
                            DebitCredit = parts[6],
                            ParentAccountId = string.IsNullOrEmpty(parts[7]) ? (int?)null : int.Parse(parts[7]),
                            Balance = decimal.Parse(parts[8]),
                            IsActive = bool.Parse(parts[9]),
                            CreatedDate = DateTime.Parse(parts[10]),
                            CreatedBy = parts[11],
                            Description = parts.Length > 12 ? parts[12] : ""
                        });
                    }
                }

                return accounts;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة الحسابات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ قائمة الحسابات
        /// </summary>
        private static void SaveAccounts(List<Account> accounts)
        {
            try
            {
                var lines = new List<string>();
                foreach (var account in accounts)
                {
                    lines.Add($"{account.Id}|{account.AccountCode}|{account.AccountName}|{account.AccountType}|{account.Level}|{account.IsParent}|{account.DebitCredit}|{account.ParentAccountId}|{account.Balance}|{account.IsActive}|{account.CreatedDate:yyyy-MM-dd HH:mm:ss}|{account.CreatedBy}|{account.Description}");
                }

                File.WriteAllLines(AccountsFile, lines, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الحسابات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// البحث عن مستخدم بالاسم
        /// </summary>
        public static User GetUserByUsername(string username)
        {
            try
            {
                var users = GetAllUsers();
                return users.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن المستخدم: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث تاريخ آخر تسجيل دخول
        /// </summary>
        public static void UpdateLastLoginDate(int userId)
        {
            try
            {
                var users = GetAllUsers();
                var user = users.FirstOrDefault(u => u.Id == userId);
                if (user != null)
                {
                    user.LastLoginDate = DateTime.Now;
                    SaveUsers(users);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث تاريخ آخر تسجيل دخول: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        public static void CreateBackup(string backupPath)
        {
            try
            {
                if (!Directory.Exists(Path.GetDirectoryName(backupPath)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(backupPath));
                }

                // نسخ مجلد البيانات بالكامل
                CopyDirectory(DataFolder, backupPath);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// نسخ مجلد
        /// </summary>
        private static void CopyDirectory(string sourceDir, string destDir)
        {
            if (!Directory.Exists(destDir))
            {
                Directory.CreateDirectory(destDir);
            }

            foreach (var file in Directory.GetFiles(sourceDir))
            {
                var destFile = Path.Combine(destDir, Path.GetFileName(file));
                File.Copy(file, destFile, true);
            }

            foreach (var dir in Directory.GetDirectories(sourceDir))
            {
                var destSubDir = Path.Combine(destDir, Path.GetFileName(dir));
                CopyDirectory(dir, destSubDir);
            }
        }

        /// <summary>
        /// الحصول على جميع العملاء
        /// </summary>
        public static List<Customer> GetAllCustomers()
        {
            try
            {
                if (!File.Exists(CustomersFile))
                    return new List<Customer>();

                var customers = new List<Customer>();
                var lines = File.ReadAllLines(CustomersFile, Encoding.UTF8);

                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var parts = line.Split('|');
                    if (parts.Length >= 10)
                    {
                        customers.Add(new Customer
                        {
                            Id = int.Parse(parts[0]),
                            CustomerCode = parts[1],
                            CustomerName = parts[2],
                            Phone = parts[3],
                            Email = parts[4],
                            Address = parts[5],
                            CurrentBalance = decimal.Parse(parts[6]),
                            IsActive = bool.Parse(parts[7]),
                            CreatedDate = DateTime.Parse(parts[8]),
                            CreatedBy = parts[9]
                        });
                    }
                }

                return customers;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة العملاء: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ عميل
        /// </summary>
        public static void SaveCustomer(Customer customer)
        {
            try
            {
                var customers = GetAllCustomers();

                // تحديث أو إضافة
                var existingIndex = customers.FindIndex(c => c.Id == customer.Id);
                if (existingIndex >= 0)
                {
                    customers[existingIndex] = customer;
                }
                else
                {
                    if (customer.Id == 0)
                    {
                        customer.Id = customers.Count > 0 ? customers.Max(c => c.Id) + 1 : 1;
                    }
                    customers.Add(customer);
                }

                SaveCustomers(customers);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ العميل: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ قائمة العملاء
        /// </summary>
        private static void SaveCustomers(List<Customer> customers)
        {
            try
            {
                var lines = new List<string>();
                foreach (var customer in customers)
                {
                    lines.Add($"{customer.Id}|{customer.CustomerCode}|{customer.CustomerName}|{customer.Phone}|{customer.Email}|{customer.Address}|{customer.CurrentBalance}|{customer.IsActive}|{customer.CreatedDate:yyyy-MM-dd HH:mm:ss}|{customer.CreatedBy}");
                }

                File.WriteAllLines(CustomersFile, lines, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ العملاء: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على جميع المنتجات
        /// </summary>
        public static List<Product> GetAllProducts()
        {
            try
            {
                if (!File.Exists(ProductsFile))
                    return new List<Product>();

                var products = new List<Product>();
                var lines = File.ReadAllLines(ProductsFile, Encoding.UTF8);

                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var parts = line.Split('|');
                    if (parts.Length >= 12)
                    {
                        products.Add(new Product
                        {
                            Id = int.Parse(parts[0]),
                            ProductCode = parts[1],
                            ProductName = parts[2],
                            Category = parts[3],
                            SalePrice = decimal.Parse(parts[4]),
                            PurchasePrice = decimal.Parse(parts[5]),
                            CurrentStock = decimal.Parse(parts[6]),
                            Unit = parts[7],
                            Description = parts[8],
                            IsActive = bool.Parse(parts[9]),
                            CreatedDate = DateTime.Parse(parts[10]),
                            CreatedBy = parts[11]
                        });
                    }
                }

                return products;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة المنتجات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ منتج
        /// </summary>
        public static void SaveProduct(Product product)
        {
            try
            {
                var products = GetAllProducts();

                // تحديث أو إضافة
                var existingIndex = products.FindIndex(p => p.Id == product.Id);
                if (existingIndex >= 0)
                {
                    products[existingIndex] = product;
                }
                else
                {
                    if (product.Id == 0)
                    {
                        product.Id = products.Count > 0 ? products.Max(p => p.Id) + 1 : 1;
                    }
                    products.Add(product);
                }

                SaveProducts(products);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ المنتج: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ قائمة المنتجات
        /// </summary>
        private static void SaveProducts(List<Product> products)
        {
            try
            {
                var lines = new List<string>();
                foreach (var product in products)
                {
                    lines.Add($"{product.Id}|{product.ProductCode}|{product.ProductName}|{product.Category}|{product.SalePrice}|{product.PurchasePrice}|{product.CurrentStock}|{product.Unit}|{product.Description}|{product.IsActive}|{product.CreatedDate:yyyy-MM-dd HH:mm:ss}|{product.CreatedBy}");
                }

                File.WriteAllLines(ProductsFile, lines, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ المنتجات: {ex.Message}", ex);
            }
        }
    }
}
