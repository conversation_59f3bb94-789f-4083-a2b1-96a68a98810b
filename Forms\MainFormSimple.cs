using System;
using System.Drawing;
using System.Windows.Forms;
using AccountingSystem.Services;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// النموذج الرئيسي المبسط لنظام السندات
    /// </summary>
    public partial class MainFormSimple : Form
    {
        private MenuStrip menuStrip;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel userLabel;
        private ToolStripStatusLabel dateLabel;
        
        public MainFormSimple()
        {
            InitializeComponent();
            SetupMenus();
            SetupForm();
        }
        
        private void InitializeComponent()
        {
            this.menuStrip = new MenuStrip();
            this.statusStrip = new StatusStrip();
            this.userLabel = new ToolStripStatusLabel();
            this.dateLabel = new ToolStripStatusLabel();
            
            this.SuspendLayout();
            
            // Form
            this.Text = "نظام إدارة السندات والمدفوعات";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Icon = SystemIcons.Application;
            
            // Menu Strip
            this.menuStrip.Location = new Point(0, 0);
            this.menuStrip.Size = new Size(1200, 24);
            this.menuStrip.TabIndex = 0;
            this.menuStrip.RightToLeft = RightToLeft.Yes;
            
            // Status Strip
            this.statusStrip.Location = new Point(0, 776);
            this.statusStrip.Size = new Size(1200, 22);
            this.statusStrip.TabIndex = 1;
            this.statusStrip.RightToLeft = RightToLeft.Yes;
            
            // User Label
            this.userLabel.Name = "userLabel";
            this.userLabel.Size = new Size(200, 17);
            this.userLabel.Text = "المستخدم: غير محدد";
            
            // Date Label
            this.dateLabel.Name = "dateLabel";
            this.dateLabel.Size = new Size(200, 17);
            this.dateLabel.Text = $"التاريخ: {DateTime.Now:yyyy/MM/dd HH:mm:ss}";
            
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.userLabel,
                this.dateLabel
            });
            
            // Add controls to form
            this.MainMenuStrip = this.menuStrip;
            this.Controls.Add(this.menuStrip);
            this.Controls.Add(this.statusStrip);
            
            this.ResumeLayout(false);
            this.PerformLayout();
        }
        
        private void SetupMenus()
        {
            // قائمة الملف
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("تسجيل خروج", null, Logout_Click));
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("خروج", null, Exit_Click));
            
            // قائمة السندات
            var receiptsMenu = new ToolStripMenuItem("السندات");
            receiptsMenu.DropDownItems.Add(new ToolStripMenuItem("سندات القبض", null, Receipts_Click));
            receiptsMenu.DropDownItems.Add(new ToolStripMenuItem("سندات الصرف", null, Payments_Click));
            
            // قائمة التقارير
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("كشف حساب العميل", null, CustomerStatement_Click));
            reportsMenu.DropDownItems.Add(new ToolStripSeparator());
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("تقرير سندات القبض", null, ReceiptsReport_Click));
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("تقرير سندات الصرف", null, PaymentsReport_Click));
            
            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("حول البرنامج", null, About_Click));
            
            // إضافة القوائم إلى شريط القوائم
            this.menuStrip.Items.AddRange(new ToolStripItem[] {
                fileMenu,
                receiptsMenu,
                reportsMenu,
                helpMenu
            });
        }
        
        private void SetupForm()
        {
            // إعداد النموذج
            this.FormClosing += MainFormSimple_FormClosing;
            
            // إعداد مؤقت لتحديث شريط الحالة
            var timer = new Timer();
            timer.Interval = 1000; // كل ثانية
            timer.Tick += Timer_Tick;
            timer.Start();
            
            UpdateStatusBar();
        }
        
        private void UpdateStatusBar()
        {
            if (AuthenticationService.CurrentUser != null)
            {
                userLabel.Text = $"المستخدم: {AuthenticationService.CurrentUser.FullName}";
            }
            dateLabel.Text = $"التاريخ: {DateTime.Now:yyyy/MM/dd HH:mm:ss}";
        }
        
        private void Timer_Tick(object sender, EventArgs e)
        {
            dateLabel.Text = $"التاريخ: {DateTime.Now:yyyy/MM/dd HH:mm:ss}";
        }
        
        private void MainFormSimple_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد الإغلاق",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
            else
            {
                AuthenticationService.Logout();
            }
        }
        
        // Event Handlers للقوائم
        private void Logout_Click(object sender, EventArgs e)
        {
            AuthenticationService.Logout();
            this.Hide();
            var loginForm = new LoginForm();
            loginForm.Show();
        }
        
        private void Exit_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        
        private void Receipts_Click(object sender, EventArgs e)
        {
            try
            {
                var receiptsForm = new ReceiptsForm();
                receiptsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح سندات القبض: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void Payments_Click(object sender, EventArgs e)
        {
            try
            {
                var paymentsForm = new PaymentsForm();
                paymentsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح سندات الصرف: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void CustomerStatement_Click(object sender, EventArgs e)
        {
            try
            {
                var statementForm = new CustomerStatementForm();
                statementForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح كشف حساب العميل: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void ReceiptsReport_Click(object sender, EventArgs e)
        {
            try
            {
                var receiptsForm = new ReceiptsForm();
                receiptsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير سندات القبض: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void PaymentsReport_Click(object sender, EventArgs e)
        {
            try
            {
                var paymentsForm = new PaymentsForm();
                paymentsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير سندات الصرف: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void About_Click(object sender, EventArgs e)
        {
            MessageBox.Show("نظام إدارة السندات والمدفوعات\nالإصدار 1.0\n\nنظام شامل لإدارة سندات القبض والصرف\nمع كشف حساب العميل التفصيلي",
                "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
