using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Dapper;
using AccountingSystem.Data;
using AccountingSystem.Models;
using AccountingSystem.Services;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل الحسابات
    /// </summary>
    public partial class AddEditAccountForm : Form
    {
        private Label lblAccountCode;
        private Label lblAccountName;
        private Label lblAccountNameEnglish;
        private Label lblParentAccount;
        private Label lblAccountType;
        private Label lblDebitCredit;
        private Label lblDescription;
        private TextBox txtAccountCode;
        private TextBox txtAccountName;
        private TextBox txtAccountNameEnglish;
        private ComboBox cmbParentAccount;
        private ComboBox cmbAccountType;
        private ComboBox cmbDebitCredit;
        private TextBox txtDescription;
        private CheckBox chkIsParent;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;
        
        private Account _account;
        private bool _isEditMode;
        
        public Account ParentAccount { get; set; }
        
        public AddEditAccountForm(Account account = null)
        {
            _account = account;
            _isEditMode = account != null;
            
            InitializeComponent();
            LoadParentAccounts();
            
            if (_isEditMode)
            {
                LoadAccountData();
                this.Text = "تعديل حساب";
            }
            else
            {
                this.Text = "إضافة حساب جديد";
                GenerateAccountCode();
            }
        }
        
        private void InitializeComponent()
        {
            this.lblAccountCode = new Label();
            this.lblAccountName = new Label();
            this.lblAccountNameEnglish = new Label();
            this.lblParentAccount = new Label();
            this.lblAccountType = new Label();
            this.lblDebitCredit = new Label();
            this.lblDescription = new Label();
            this.txtAccountCode = new TextBox();
            this.txtAccountName = new TextBox();
            this.txtAccountNameEnglish = new TextBox();
            this.cmbParentAccount = new ComboBox();
            this.cmbAccountType = new ComboBox();
            this.cmbDebitCredit = new ComboBox();
            this.txtDescription = new TextBox();
            this.chkIsParent = new CheckBox();
            this.chkIsActive = new CheckBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            
            this.SuspendLayout();
            
            // Form
            this.Text = "إضافة/تعديل حساب";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            int yPos = 20;
            int spacing = 50;
            int labelWidth = 120;
            int controlWidth = 300;
            
            // Account Code
            this.lblAccountCode.Text = "كود الحساب:";
            this.lblAccountCode.Location = new Point(350, yPos);
            this.lblAccountCode.Size = new Size(labelWidth, 23);
            this.lblAccountCode.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtAccountCode.Location = new Point(30, yPos);
            this.txtAccountCode.Size = new Size(controlWidth, 23);
            this.txtAccountCode.Font = new Font("Tahoma", 10F);
            
            yPos += spacing;
            
            // Account Name
            this.lblAccountName.Text = "اسم الحساب:";
            this.lblAccountName.Location = new Point(350, yPos);
            this.lblAccountName.Size = new Size(labelWidth, 23);
            this.lblAccountName.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtAccountName.Location = new Point(30, yPos);
            this.txtAccountName.Size = new Size(controlWidth, 23);
            this.txtAccountName.Font = new Font("Tahoma", 10F);
            
            yPos += spacing;
            
            // Account Name English
            this.lblAccountNameEnglish.Text = "الاسم بالإنجليزية:";
            this.lblAccountNameEnglish.Location = new Point(350, yPos);
            this.lblAccountNameEnglish.Size = new Size(labelWidth, 23);
            this.lblAccountNameEnglish.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtAccountNameEnglish.Location = new Point(30, yPos);
            this.txtAccountNameEnglish.Size = new Size(controlWidth, 23);
            this.txtAccountNameEnglish.Font = new Font("Tahoma", 10F);
            
            yPos += spacing;
            
            // Parent Account
            this.lblParentAccount.Text = "الحساب الأب:";
            this.lblParentAccount.Location = new Point(350, yPos);
            this.lblParentAccount.Size = new Size(labelWidth, 23);
            this.lblParentAccount.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbParentAccount.Location = new Point(30, yPos);
            this.cmbParentAccount.Size = new Size(controlWidth, 23);
            this.cmbParentAccount.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbParentAccount.Font = new Font("Tahoma", 10F);
            
            yPos += spacing;
            
            // Account Type
            this.lblAccountType.Text = "نوع الحساب:";
            this.lblAccountType.Location = new Point(350, yPos);
            this.lblAccountType.Size = new Size(labelWidth, 23);
            this.lblAccountType.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbAccountType.Location = new Point(30, yPos);
            this.cmbAccountType.Size = new Size(controlWidth, 23);
            this.cmbAccountType.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbAccountType.Items.AddRange(new string[] { "أصول", "خصوم", "حقوق ملكية", "إيرادات", "مصروفات" });
            this.cmbAccountType.Font = new Font("Tahoma", 10F);
            this.cmbAccountType.SelectedIndexChanged += CmbAccountType_SelectedIndexChanged;
            
            yPos += spacing;
            
            // Debit/Credit
            this.lblDebitCredit.Text = "طبيعة الحساب:";
            this.lblDebitCredit.Location = new Point(350, yPos);
            this.lblDebitCredit.Size = new Size(labelWidth, 23);
            this.lblDebitCredit.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbDebitCredit.Location = new Point(30, yPos);
            this.cmbDebitCredit.Size = new Size(controlWidth, 23);
            this.cmbDebitCredit.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbDebitCredit.Items.AddRange(new string[] { "مدين", "دائن" });
            this.cmbDebitCredit.Font = new Font("Tahoma", 10F);
            
            yPos += spacing;
            
            // Description
            this.lblDescription.Text = "الوصف:";
            this.lblDescription.Location = new Point(350, yPos);
            this.lblDescription.Size = new Size(labelWidth, 23);
            this.lblDescription.TextAlign = ContentAlignment.MiddleRight;
            
            this.txtDescription.Location = new Point(30, yPos);
            this.txtDescription.Size = new Size(controlWidth, 60);
            this.txtDescription.Multiline = true;
            this.txtDescription.Font = new Font("Tahoma", 10F);
            
            yPos += 80;
            
            // Checkboxes
            this.chkIsParent.Text = "حساب رئيسي";
            this.chkIsParent.Location = new Point(250, yPos);
            this.chkIsParent.Size = new Size(120, 23);
            this.chkIsParent.Font = new Font("Tahoma", 10F);
            
            this.chkIsActive.Text = "نشط";
            this.chkIsActive.Location = new Point(150, yPos);
            this.chkIsActive.Size = new Size(80, 23);
            this.chkIsActive.Checked = true;
            this.chkIsActive.Font = new Font("Tahoma", 10F);
            
            yPos += 50;
            
            // Buttons
            this.btnSave.Text = "حفظ";
            this.btnSave.Location = new Point(250, yPos);
            this.btnSave.Size = new Size(100, 35);
            this.btnSave.BackColor = Color.FromArgb(25, 118, 210);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            this.btnSave.Click += BtnSave_Click;
            
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.Location = new Point(130, yPos);
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.BackColor = Color.FromArgb(244, 67, 54);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Tahoma", 10F);
            this.btnCancel.Click += BtnCancel_Click;
            
            // Add controls to form
            this.Controls.AddRange(new Control[] {
                this.lblAccountCode, this.txtAccountCode,
                this.lblAccountName, this.txtAccountName,
                this.lblAccountNameEnglish, this.txtAccountNameEnglish,
                this.lblParentAccount, this.cmbParentAccount,
                this.lblAccountType, this.cmbAccountType,
                this.lblDebitCredit, this.cmbDebitCredit,
                this.lblDescription, this.txtDescription,
                this.chkIsParent, this.chkIsActive,
                this.btnSave, this.btnCancel
            });
            
            this.ResumeLayout(false);
        }
        
        private void LoadParentAccounts()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    var parentAccounts = connection.Query<Account>(@"
                        SELECT Id, AccountCode, AccountName 
                        FROM Accounts 
                        WHERE IsParent = 1 AND IsActive = 1 
                        ORDER BY AccountCode").ToList();
                    
                    cmbParentAccount.Items.Clear();
                    cmbParentAccount.Items.Add(new { Id = 0, Text = "-- بدون حساب أب --" });
                    
                    foreach (var account in parentAccounts)
                    {
                        cmbParentAccount.Items.Add(new { Id = account.Id, Text = $"{account.AccountCode} - {account.AccountName}" });
                    }
                    
                    cmbParentAccount.DisplayMember = "Text";
                    cmbParentAccount.ValueMember = "Id";
                    cmbParentAccount.SelectedIndex = 0;
                }
                
                // إذا كان هناك حساب أب محدد مسبقاً
                if (ParentAccount != null)
                {
                    for (int i = 0; i < cmbParentAccount.Items.Count; i++)
                    {
                        dynamic item = cmbParentAccount.Items[i];
                        if (item.Id == ParentAccount.Id)
                        {
                            cmbParentAccount.SelectedIndex = i;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الحسابات الأب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void GenerateAccountCode()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // الحصول على آخر كود حساب
                    var lastCode = connection.QueryFirstOrDefault<string>(@"
                        SELECT AccountCode 
                        FROM Accounts 
                        ORDER BY CAST(AccountCode AS INTEGER) DESC 
                        LIMIT 1");
                    
                    int nextCode = 1;
                    if (!string.IsNullOrEmpty(lastCode) && int.TryParse(lastCode, out int code))
                    {
                        nextCode = code + 1;
                    }
                    
                    txtAccountCode.Text = nextCode.ToString();
                }
            }
            catch (Exception)
            {
                txtAccountCode.Text = "1";
            }
        }
        
        private void LoadAccountData()
        {
            if (_account != null)
            {
                txtAccountCode.Text = _account.AccountCode;
                txtAccountName.Text = _account.AccountName;
                txtAccountNameEnglish.Text = _account.AccountNameEnglish;
                cmbAccountType.Text = _account.AccountType;
                cmbDebitCredit.Text = _account.DebitCredit;
                txtDescription.Text = _account.Description;
                chkIsParent.Checked = _account.IsParent;
                chkIsActive.Checked = _account.IsActive;
                
                // تحديد الحساب الأب
                if (_account.ParentAccountId.HasValue)
                {
                    for (int i = 0; i < cmbParentAccount.Items.Count; i++)
                    {
                        dynamic item = cmbParentAccount.Items[i];
                        if (item.Id == _account.ParentAccountId.Value)
                        {
                            cmbParentAccount.SelectedIndex = i;
                            break;
                        }
                    }
                }
            }
        }
        
        private void CmbAccountType_SelectedIndexChanged(object sender, EventArgs e)
        {
            // تحديد طبيعة الحساب تلقائياً حسب النوع
            switch (cmbAccountType.Text)
            {
                case "أصول":
                case "مصروفات":
                    cmbDebitCredit.Text = "مدين";
                    break;
                case "خصوم":
                case "حقوق ملكية":
                case "إيرادات":
                    cmbDebitCredit.Text = "دائن";
                    break;
            }
        }
        
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                try
                {
                    using (var connection = DatabaseHelper.GetConnection())
                    {
                        connection.Open();
                        
                        dynamic parentAccount = cmbParentAccount.SelectedItem;
                        int? parentAccountId = parentAccount.Id == 0 ? null : (int?)parentAccount.Id;
                        
                        if (_isEditMode)
                        {
                            // تحديث الحساب
                            connection.Execute(@"
                                UPDATE Accounts SET 
                                    AccountCode = @AccountCode,
                                    AccountName = @AccountName,
                                    AccountNameEnglish = @AccountNameEnglish,
                                    ParentAccountId = @ParentAccountId,
                                    AccountType = @AccountType,
                                    DebitCredit = @DebitCredit,
                                    Description = @Description,
                                    IsParent = @IsParent,
                                    IsActive = @IsActive,
                                    ModifiedDate = @ModifiedDate,
                                    ModifiedBy = @ModifiedBy
                                WHERE Id = @Id",
                                new
                                {
                                    Id = _account.Id,
                                    AccountCode = txtAccountCode.Text.Trim(),
                                    AccountName = txtAccountName.Text.Trim(),
                                    AccountNameEnglish = txtAccountNameEnglish.Text.Trim(),
                                    ParentAccountId = parentAccountId,
                                    AccountType = cmbAccountType.Text,
                                    DebitCredit = cmbDebitCredit.Text,
                                    Description = txtDescription.Text.Trim(),
                                    IsParent = chkIsParent.Checked ? 1 : 0,
                                    IsActive = chkIsActive.Checked ? 1 : 0,
                                    ModifiedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                    ModifiedBy = AuthenticationService.CurrentUser?.Username
                                });
                        }
                        else
                        {
                            // إضافة حساب جديد
                            connection.Execute(@"
                                INSERT INTO Accounts (
                                    AccountCode, AccountName, AccountNameEnglish, ParentAccountId,
                                    AccountType, Level, IsParent, IsActive, Balance, DebitCredit,
                                    Description, CreatedDate, CreatedBy
                                ) VALUES (
                                    @AccountCode, @AccountName, @AccountNameEnglish, @ParentAccountId,
                                    @AccountType, @Level, @IsParent, @IsActive, 0, @DebitCredit,
                                    @Description, @CreatedDate, @CreatedBy
                                )",
                                new
                                {
                                    AccountCode = txtAccountCode.Text.Trim(),
                                    AccountName = txtAccountName.Text.Trim(),
                                    AccountNameEnglish = txtAccountNameEnglish.Text.Trim(),
                                    ParentAccountId = parentAccountId,
                                    AccountType = cmbAccountType.Text,
                                    Level = parentAccountId.HasValue ? 2 : 1,
                                    IsParent = chkIsParent.Checked ? 1 : 0,
                                    IsActive = chkIsActive.Checked ? 1 : 0,
                                    DebitCredit = cmbDebitCredit.Text,
                                    Description = txtDescription.Text.Trim(),
                                    CreatedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                    CreatedBy = AuthenticationService.CurrentUser?.Username
                                });
                        }
                    }
                    
                    MessageBox.Show("تم حفظ الحساب بنجاح", "نجح الحفظ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ الحساب: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtAccountCode.Text))
            {
                MessageBox.Show("يرجى إدخال كود الحساب", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAccountCode.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(txtAccountName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الحساب", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAccountName.Focus();
                return false;
            }
            
            if (cmbAccountType.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار نوع الحساب", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbAccountType.Focus();
                return false;
            }
            
            if (cmbDebitCredit.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار طبيعة الحساب", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbDebitCredit.Focus();
                return false;
            }
            
            // التحقق من عدم تكرار كود الحساب
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    string query = "SELECT COUNT(*) FROM Accounts WHERE AccountCode = @AccountCode";
                    if (_isEditMode)
                    {
                        query += " AND Id != @Id";
                    }
                    
                    var count = connection.QuerySingle<int>(query, new 
                    { 
                        AccountCode = txtAccountCode.Text.Trim(),
                        Id = _account?.Id ?? 0
                    });
                    
                    if (count > 0)
                    {
                        MessageBox.Show("كود الحساب موجود مسبقاً", "تنبيه", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtAccountCode.Focus();
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            
            return true;
        }
        
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
