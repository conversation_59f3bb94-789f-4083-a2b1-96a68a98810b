@echo off
chcp 65001 > nul
echo ========================================
echo اختبار بناء نظام السندات
echo ========================================
echo.

echo فحص الملفات المطلوبة...
echo.

set missing=0

rem فحص النماذج
if not exist "Models\Receipt.cs" (
    echo ✗ Models\Receipt.cs مفقود
    set /a missing+=1
) else (
    echo ✓ Models\Receipt.cs موجود
)

if not exist "Models\Payment.cs" (
    echo ✗ Models\Payment.cs مفقود
    set /a missing+=1
) else (
    echo ✓ Models\Payment.cs موجود
)

if not exist "Models\Invoice.cs" (
    echo ✗ Models\Invoice.cs مفقود
    set /a missing+=1
) else (
    echo ✓ Models\Invoice.cs موجود
)

if not exist "Models\Customer.cs" (
    echo ✗ Models\Customer.cs مفقود
    set /a missing+=1
) else (
    echo ✓ Models\Customer.cs موجود
)

rem فحص النماذج
if not exist "Forms\ReceiptsForm.cs" (
    echo ✗ Forms\ReceiptsForm.cs مفقود
    set /a missing+=1
) else (
    echo ✓ Forms\ReceiptsForm.cs موجود
)

if not exist "Forms\AddEditReceiptForm.cs" (
    echo ✗ Forms\AddEditReceiptForm.cs مفقود
    set /a missing+=1
) else (
    echo ✓ Forms\AddEditReceiptForm.cs موجود
)

if not exist "Forms\PaymentsForm.cs" (
    echo ✗ Forms\PaymentsForm.cs مفقود
    set /a missing+=1
) else (
    echo ✓ Forms\PaymentsForm.cs موجود
)

if not exist "Forms\CustomerStatementForm.cs" (
    echo ✗ Forms\CustomerStatementForm.cs مفقود
    set /a missing+=1
) else (
    echo ✓ Forms\CustomerStatementForm.cs موجود
)

if not exist "Forms\MainFormSimple.cs" (
    echo ✗ Forms\MainFormSimple.cs مفقود
    set /a missing+=1
) else (
    echo ✓ Forms\MainFormSimple.cs موجود
)

if not exist "ProgramSimple.cs" (
    echo ✗ ProgramSimple.cs مفقود
    set /a missing+=1
) else (
    echo ✓ ProgramSimple.cs موجود
)

echo.
if %missing% GTR 0 (
    echo ⚠ يوجد %missing% ملف مفقود
    echo يرجى التأكد من وجود جميع الملفات قبل البناء
    echo.
    goto :end
)

echo ✓ جميع الملفات المطلوبة موجودة
echo.

echo ========================================
echo اختبار بناء مبسط
echo ========================================

echo محاولة بناء النماذج فقط...
csc /target:library ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /out:TestModels.dll ^
    Models\Customer.cs ^
    Models\Receipt.cs ^
    Models\Payment.cs ^
    Models\Invoice.cs

if %ERRORLEVEL% EQU 0 (
    echo ✓ نجح بناء النماذج
    del TestModels.dll >nul 2>&1
) else (
    echo ✗ فشل بناء النماذج
    echo.
    echo تحقق من أخطاء الكود في النماذج
    goto :end
)

echo.
echo محاولة بناء النماذج مع الخدمات...
csc /target:library ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /out:TestForms.dll ^
    Models\Customer.cs ^
    Models\Receipt.cs ^
    Models\Payment.cs ^
    Models\Invoice.cs ^
    Models\User.cs ^
    Services\SimpleAuthenticationService.cs ^
    Forms\AddEditReceiptForm.cs

if %ERRORLEVEL% EQU 0 (
    echo ✓ نجح بناء النماذج مع الخدمات
    del TestForms.dll >nul 2>&1
) else (
    echo ✗ فشل بناء النماذج مع الخدمات
    echo.
    echo المشكلة قد تكون في:
    echo 1. مراجع مفقودة
    echo 2. أخطاء في الكود
    echo 3. تضارب في أسماء الفئات
    goto :end
)

echo.
echo ========================================
echo النتيجة
echo ========================================

echo ✓ الاختبار نجح! يمكنك الآن تشغيل:
echo.
echo build_receipts_only.bat
echo.
echo أو للبناء التدريجي:
echo build_step_by_step.bat

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
