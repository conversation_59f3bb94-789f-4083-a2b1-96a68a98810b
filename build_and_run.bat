@echo off
echo ========================================
echo بناء وتشغيل نظام المحاسبة الذكي
echo ========================================
echo.

echo تحقق من وجود ملفات المكتبات المطلوبة...

if not exist "System.Data.SQLite.dll" (
    echo تحميل System.Data.SQLite.dll...
    echo يرجى تحميل System.Data.SQLite من: https://system.data.sqlite.org/downloads/
    echo ووضع الملف System.Data.SQLite.dll في مجلد المشروع
    pause
    exit /b 1
)

if not exist "Dapper.dll" (
    echo تحميل Dapper.dll...
    echo يرجى تحميل Dapper من NuGet أو من: https://www.nuget.org/packages/Dapper/
    pause
    exit /b 1
)

if not exist "Newtonsoft.Json.dll" (
    echo تحميل Newtonsoft.Json.dll...
    echo يرجى تحميل Newtonsoft.Json من NuGet أو من: https://www.nuget.org/packages/Newtonsoft.Json/
    pause
    exit /b 1
)

echo جميع المكتبات موجودة!
echo.

echo بناء المشروع باستخدام csc...
csc /target:winexe /reference:System.dll /reference:System.Core.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:System.Configuration.dll /reference:System.Data.SQLite.dll /reference:Dapper.dll /reference:Newtonsoft.Json.dll /out:AccountingSystem.exe *.cs Models\*.cs Data\*.cs Services\*.cs Utils\*.cs Forms\*.cs Properties\*.cs Tests\*.cs

if %ERRORLEVEL% NEQ 0 (
    echo فشل في بناء المشروع
    pause
    exit /b 1
)

echo تم بناء المشروع بنجاح!
echo.

echo تشغيل التطبيق...
start AccountingSystem.exe

echo.
echo تم تشغيل التطبيق بنجاح!
echo يمكنك تسجيل الدخول باستخدام:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
pause
