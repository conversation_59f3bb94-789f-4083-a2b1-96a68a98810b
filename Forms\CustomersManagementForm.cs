using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using IntegratedInvoiceSystem.Models;
using IntegratedInvoiceSystem.Services;

namespace IntegratedInvoiceSystem.Forms
{
    /// <summary>
    /// نموذج إدارة العملاء المتكامل
    /// </summary>
    public partial class CustomersManagementForm : Form
    {
        #region المتغيرات
        private List<Customer> customers;
        private List<Customer> filteredCustomers;
        private Customer selectedCustomer;
        #endregion

        #region البناء والتهيئة
        public CustomersManagementForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // تهيئة الإعدادات الأساسية
            this.WindowState = FormWindowState.Maximized;
            this.KeyPreview = true;

            // تهيئة القوائم المنسدلة
            cmbCustomerType.SelectedIndex = 0;
            cmbStatus.SelectedIndex = 0;

            // تهيئة DataGridView
            SetupDataGridView();
        }

        private void SetupDataGridView()
        {
            dataGridView.AutoGenerateColumns = false;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.ReadOnly = true;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;

            // تنسيق الألوان
            dataGridView.BackgroundColor = Color.White;
            dataGridView.GridColor = Color.FromArgb(189, 195, 199);
            dataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(46, 204, 113);
            dataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dataGridView.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dataGridView.RowHeadersVisible = false;
            dataGridView.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            AddDataGridViewColumns();
        }

        private void AddDataGridViewColumns()
        {
            dataGridView.Columns.Clear();

            // كود العميل
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerCode",
                HeaderText = "كود العميل",
                DataPropertyName = "CustomerCode",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // اسم العميل
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerName",
                HeaderText = "اسم العميل",
                DataPropertyName = "CustomerName",
                Width = 200
            });

            // الهاتف
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                DataPropertyName = "Phone",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // البريد الإلكتروني
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 180
            });

            // العنوان
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Address",
                HeaderText = "العنوان",
                DataPropertyName = "Address",
                Width = 200
            });

            // الرصيد الحالي
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentBalance",
                HeaderText = "الرصيد الحالي",
                DataPropertyName = "CurrentBalance",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "C2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // نوع العميل
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerType",
                HeaderText = "نوع العميل",
                DataPropertyName = "CustomerType",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // الحالة
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IsActive",
                HeaderText = "الحالة",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // تاريخ الإنشاء
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedDate",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "dd/MM/yyyy",
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            });
        }
        #endregion

        #region تحميل البيانات
        private void CustomersManagementForm_Load(object sender, EventArgs e)
        {
            LoadCustomers();
        }

        private void LoadCustomers()
        {
            try
            {
                lblStatusText.Text = "جاري تحميل العملاء...";

                // تحميل العملاء من الخدمة
                customers = DataService.GetCustomers();

                // تطبيق الفلترة
                ApplyFilters();

                lblStatusText.Text = "تم تحميل البيانات بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatusText.Text = "خطأ في تحميل البيانات";
            }
        }

        private void ApplyFilters()
        {
            try
            {
                filteredCustomers = customers.Where(customer =>
                {
                    // فلتر النص
                    bool textMatch = string.IsNullOrEmpty(txtSearch.Text) ||
                        customer.CustomerName.Contains(txtSearch.Text) ||
                        customer.CustomerCode.Contains(txtSearch.Text) ||
                        customer.Phone.Contains(txtSearch.Text) ||
                        customer.Email.Contains(txtSearch.Text);

                    // فلتر نوع العميل
                    bool typeMatch = cmbCustomerType.SelectedIndex == 0 ||
                        customer.CustomerType == cmbCustomerType.Text;

                    // فلتر الحالة
                    bool statusMatch = cmbStatus.SelectedIndex == 0 ||
                        (cmbStatus.Text == "نشط" && customer.IsActive) ||
                        (cmbStatus.Text == "غير نشط" && !customer.IsActive);

                    return textMatch && typeMatch && statusMatch;
                }).ToList();

                // ربط البيانات
                BindData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BindData()
        {
            try
            {
                var bindingList = filteredCustomers.Select(customer => new
                {
                    Id = customer.Id,
                    CustomerCode = customer.CustomerCode,
                    CustomerName = customer.CustomerName,
                    Phone = customer.Phone,
                    Email = customer.Email,
                    Address = customer.Address,
                    CurrentBalance = customer.CurrentBalance,
                    CustomerType = customer.CustomerType ?? "عميل",
                    IsActive = customer.IsActive ? "نشط" : "غير نشط",
                    CreatedDate = customer.CreatedDate
                }).ToList();

                dataGridView.DataSource = bindingList;

                // تحديث عداد السجلات
                lblRecordCount.Text = $"عدد السجلات: {filteredCustomers.Count}";

                // تلوين الصفوف حسب الحالة
                ColorizeRows();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ربط البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ColorizeRows()
        {
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                if (row.Cells["IsActive"].Value != null)
                {
                    string status = row.Cells["IsActive"].Value.ToString();
                    if (status == "غير نشط")
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(100, 100, 100);
                    }
                    else
                    {
                        // تلوين حسب الرصيد
                        if (row.Cells["CurrentBalance"].Value != null)
                        {
                            decimal balance = Convert.ToDecimal(row.Cells["CurrentBalance"].Value);
                            if (balance < 0)
                            {
                                row.DefaultCellStyle.BackColor = Color.FromArgb(255, 245, 245);
                            }
                            else if (balance > 0)
                            {
                                row.DefaultCellStyle.BackColor = Color.FromArgb(245, 255, 245);
                            }
                        }
                    }
                }
            }
        }
        #endregion

        #region معالجات الأحداث - الأزرار
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new AddEditCustomerForm();
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadCustomers();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج إضافة العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            EditSelectedCustomer();
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DeleteSelectedCustomer();
        }

        private void BtnView_Click(object sender, EventArgs e)
        {
            ViewCustomerStatement();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadCustomers();
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        #endregion

        #region معالجات الأحداث - البحث والفلترة
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CmbCustomerType_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CmbStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void BtnClearSearch_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbCustomerType.SelectedIndex = 0;
            cmbStatus.SelectedIndex = 0;
            ApplyFilters();
        }
        #endregion

        #region معالجات الأحداث - DataGridView
        private void DataGridView_SelectionChanged(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                var selectedRow = dataGridView.SelectedRows[0];
                if (selectedRow.Cells["Id"].Value != null)
                {
                    int customerId = Convert.ToInt32(selectedRow.Cells["Id"].Value);
                    selectedCustomer = customers.FirstOrDefault(c => c.Id == customerId);

                    // تحديث حالة الأزرار
                    UpdateButtonStates();
                }
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedCustomer();
            }
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = selectedCustomer != null;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
            btnView.Enabled = hasSelection;

            // تحديث القائمة المنسدلة
            menuEdit.Enabled = hasSelection;
            menuDelete.Enabled = hasSelection;
            menuView.Enabled = hasSelection;
            menuStatement.Enabled = hasSelection;
            menuActivate.Enabled = hasSelection && !selectedCustomer?.IsActive;
            menuDeactivate.Enabled = hasSelection && selectedCustomer?.IsActive == true;
        }
        #endregion

        #region معالجات الأحداث - القائمة المنسدلة
        private void MenuEdit_Click(object sender, EventArgs e)
        {
            EditSelectedCustomer();
        }

        private void MenuDelete_Click(object sender, EventArgs e)
        {
            DeleteSelectedCustomer();
        }

        private void MenuView_Click(object sender, EventArgs e)
        {
            ViewSelectedCustomer();
        }

        private void MenuStatement_Click(object sender, EventArgs e)
        {
            ViewCustomerStatement();
        }

        private void MenuActivate_Click(object sender, EventArgs e)
        {
            ToggleCustomerStatus(true);
        }

        private void MenuDeactivate_Click(object sender, EventArgs e)
        {
            ToggleCustomerStatus(false);
        }
        #endregion

        #region العمليات الأساسية
        private void EditSelectedCustomer()
        {
            if (selectedCustomer == null)
            {
                MessageBox.Show("يرجى اختيار عميل للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var editForm = new AddEditCustomerForm(selectedCustomer);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadCustomers();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج تعديل العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteSelectedCustomer()
        {
            if (selectedCustomer == null)
            {
                MessageBox.Show("يرجى اختيار عميل للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // التحقق من وجود فواتير مرتبطة
            var relatedInvoices = DataService.GetInvoices().Where(i => i.CustomerId == selectedCustomer.Id).ToList();
            if (relatedInvoices.Any())
            {
                MessageBox.Show("لا يمكن حذف العميل لوجود فواتير مرتبطة به", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show($"هل أنت متأكد من حذف العميل {selectedCustomer.CustomerName}؟",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    DataService.DeleteCustomer(selectedCustomer.Id);
                    LoadCustomers();
                    MessageBox.Show("تم حذف العميل بنجاح", "نجح الحذف",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void ViewSelectedCustomer()
        {
            if (selectedCustomer == null)
            {
                MessageBox.Show("يرجى اختيار عميل للعرض", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var viewForm = new CustomerDetailsForm(selectedCustomer);
                viewForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تفاصيل العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewCustomerStatement()
        {
            if (selectedCustomer == null)
            {
                MessageBox.Show("يرجى اختيار عميل لعرض كشف الحساب", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var statementForm = new CustomerStatementForm(selectedCustomer);
                statementForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض كشف حساب العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ToggleCustomerStatus(bool isActive)
        {
            if (selectedCustomer == null)
            {
                MessageBox.Show("يرجى اختيار عميل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string action = isActive ? "تفعيل" : "إلغاء تفعيل";
            var result = MessageBox.Show($"هل تريد {action} العميل {selectedCustomer.CustomerName}؟",
                $"تأكيد {action}", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    selectedCustomer.IsActive = isActive;
                    DataService.UpdateCustomer(selectedCustomer);
                    LoadCustomers();
                    MessageBox.Show($"تم {action} العميل بنجاح", $"نجح {action}",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في {action} العميل: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        #endregion

        #region معالجات إضافية
        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            switch (keyData)
            {
                case Keys.F1:
                    BtnAdd_Click(null, null);
                    return true;
                case Keys.F2:
                    BtnEdit_Click(null, null);
                    return true;
                case Keys.Delete:
                    BtnDelete_Click(null, null);
                    return true;
                case Keys.F3:
                    BtnView_Click(null, null);
                    return true;
                case Keys.F5:
                    BtnRefresh_Click(null, null);
                    return true;
                case Keys.Escape:
                    BtnClose_Click(null, null);
                    return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }
        #endregion
    }
}
