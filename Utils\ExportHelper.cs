using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace AccountingSystem.Utils
{
    /// <summary>
    /// مساعد تصدير البيانات
    /// </summary>
    public static class ExportHelper
    {
        /// <summary>
        /// تصدير DataGridView إلى Excel
        /// </summary>
        /// <param name="dgv">DataGridView</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="sheetName">اسم الورقة</param>
        public static void ExportDataGridViewToExcel(DataGridView dgv, string filePath, string sheetName = "Sheet1")
        {
            try
            {
                // Create CSV content (Excel can open CSV files)
                var csv = new StringBuilder();

                // Add headers
                var headers = new List<string>();
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    if (column.Visible)
                    {
                        headers.Add(EscapeCsvField(column.HeaderText));
                    }
                }
                csv.AppendLine(string.Join(",", headers));

                // Add data rows
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    if (row.IsNewRow) continue;

                    var values = new List<string>();
                    foreach (DataGridViewColumn column in dgv.Columns)
                    {
                        if (column.Visible)
                        {
                            var cellValue = row.Cells[column.Index].Value?.ToString() ?? "";
                            values.Add(EscapeCsvField(cellValue));
                        }
                    }
                    csv.AppendLine(string.Join(",", values));
                }

                // Save to file with UTF-8 encoding
                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير قائمة إلى CSV
        /// </summary>
        /// <typeparam name="T">نوع البيانات</typeparam>
        /// <param name="data">البيانات</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="headers">العناوين</param>
        /// <param name="valueSelector">محدد القيم</param>
        public static void ExportListToCsv<T>(List<T> data, string filePath, string[] headers, Func<T, string[]> valueSelector)
        {
            try
            {
                var csv = new StringBuilder();

                // Add headers
                csv.AppendLine(string.Join(",", headers.Select(EscapeCsvField)));

                // Add data
                foreach (var item in data)
                {
                    var values = valueSelector(item);
                    csv.AppendLine(string.Join(",", values.Select(EscapeCsvField)));
                }

                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير تقرير مخصص إلى CSV
        /// </summary>
        /// <param name="reportData">بيانات التقرير</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="title">عنوان التقرير</param>
        public static void ExportCustomReportToCsv(Dictionary<string, List<string[]>> reportData, string filePath, string title)
        {
            try
            {
                var csv = new StringBuilder();

                // Add title
                csv.AppendLine(EscapeCsvField(title));
                csv.AppendLine(EscapeCsvField($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm:ss}"));
                csv.AppendLine("");

                // Add sections
                foreach (var section in reportData)
                {
                    csv.AppendLine(EscapeCsvField(section.Key));
                    csv.AppendLine(new string('=', section.Key.Length));

                    foreach (var row in section.Value)
                    {
                        csv.AppendLine(string.Join(",", row.Select(EscapeCsvField)));
                    }

                    csv.AppendLine("");
                }

                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// تنسيق حقل CSV
        /// </summary>
        /// <param name="field">الحقل</param>
        /// <returns>الحقل المنسق</returns>
        private static string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return "";

            // If field contains comma, newline, or quote, wrap in quotes and escape quotes
            if (field.Contains(",") || field.Contains("\n") || field.Contains("\""))
            {
                return "\"" + field.Replace("\"", "\"\"") + "\"";
            }

            return field;
        }

        /// <summary>
        /// تصدير فاتورة إلى PDF (نص)
        /// </summary>
        /// <param name="invoice">الفاتورة</param>
        /// <param name="customer">العميل</param>
        /// <param name="filePath">مسار الملف</param>
        public static void ExportInvoiceToPdf(IntegratedInvoiceSystem.Models.Invoice invoice, IntegratedInvoiceSystem.Models.Customer customer, string filePath)
        {
            try
            {
                var content = new StringBuilder();

                content.AppendLine("فاتورة مبيعات");
                content.AppendLine("=============");
                content.AppendLine("");
                content.AppendLine($"رقم الفاتورة: {invoice.InvoiceNumber}");
                content.AppendLine($"تاريخ الفاتورة: {invoice.InvoiceDate:yyyy/MM/dd}");
                content.AppendLine($"تاريخ الاستحقاق: {invoice.DueDate:yyyy/MM/dd}");
                content.AppendLine("");
                content.AppendLine("بيانات العميل:");
                content.AppendLine($"الاسم: {customer?.CustomerName ?? "غير محدد"}");
                content.AppendLine($"الهاتف: {customer?.Phone ?? "غير محدد"}");
                content.AppendLine($"العنوان: {customer?.Address ?? "غير محدد"}");
                content.AppendLine("");
                content.AppendLine("تفاصيل الفاتورة:");
                content.AppendLine($"المبلغ الفرعي: {invoice.SubTotal:N2} ريال");
                content.AppendLine($"الضريبة: {invoice.TaxAmount:N2} ريال");
                content.AppendLine($"الخصم: {invoice.DiscountAmount:N2} ريال");
                content.AppendLine($"الإجمالي: {invoice.TotalAmount:N2} ريال");
                content.AppendLine($"المدفوع: {invoice.PaidAmount:N2} ريال");
                content.AppendLine($"المتبقي: {(invoice.TotalAmount - invoice.PaidAmount):N2} ريال");
                content.AppendLine("");
                content.AppendLine($"الحالة: {invoice.Status}");
                content.AppendLine($"ملاحظات: {invoice.Notes}");
                content.AppendLine("");
                content.AppendLine($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");

                File.WriteAllText(filePath, content.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير الفاتورة: {ex.Message}");
            }
        }

        /// <summary>
        /// فتح ملف بالبرنامج الافتراضي
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        public static void OpenFile(string filePath)
        {
            try
            {
                System.Diagnostics.Process.Start(filePath);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// الحصول على مسار سطح المكتب
        /// </summary>
        /// <returns>مسار سطح المكتب</returns>
        public static string GetDesktopPath()
        {
            return Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
        }

        /// <summary>
        /// الحصول على مسار المستندات
        /// </summary>
        /// <returns>مسار المستندات</returns>
        public static string GetDocumentsPath()
        {
            return Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
        }

        /// <summary>
        /// إنشاء اسم ملف فريد
        /// </summary>
        /// <param name="baseName">الاسم الأساسي</param>
        /// <param name="extension">الامتداد</param>
        /// <returns>اسم الملف الفريد</returns>
        public static string GenerateUniqueFileName(string baseName, string extension)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            return $"{baseName}_{timestamp}.{extension.TrimStart('.')}";
        }
    }
}
