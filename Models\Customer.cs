using System;

namespace AccountingSystem.Models
{
    /// <summary>
    /// نموذج العميل
    /// </summary>
    public class Customer
    {
        public int Id { get; set; }
        public string CustomerCode { get; set; }
        public string CustomerName { get; set; }
        public string CustomerNameEnglish { get; set; }
        public string ContactPerson { get; set; }
        public string Phone { get; set; }
        public string Mobile { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
        public string TaxNumber { get; set; }
        public string CommercialRegister { get; set; }
        public decimal CreditLimit { get; set; }
        public int PaymentTerms { get; set; } // مدة السداد بالأيام
        public bool IsActive { get; set; }
        public string CustomerType { get; set; } // عميل، مورد، كلاهما
        public int? AccountId { get; set; } // الحساب المرتبط في شجرة الحسابات
        public decimal CurrentBalance { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }
        
        // خصائص إضافية للعرض
        public Account Account { get; set; }
    }
}
