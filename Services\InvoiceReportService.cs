using System;
using System.Collections.Generic;
using System.Linq;
using IntegratedInvoiceSystem.Models;

namespace IntegratedInvoiceSystem.Services
{
    /// <summary>
    /// خدمة تقارير الفواتير
    /// </summary>
    public class InvoiceReportService
    {
        #region Properties
        public List<Invoice> Invoices { get; set; }
        public List<Customer> Customers { get; set; }
        public List<Receipt> Receipts { get; set; }
        #endregion

        #region Constructor
        public InvoiceReportService()
        {
            LoadData();
        }

        public InvoiceReportService(List<Invoice> invoices, List<Customer> customers, List<Receipt> receipts)
        {
            Invoices = invoices ?? new List<Invoice>();
            Customers = customers ?? new List<Customer>();
            Receipts = receipts ?? new List<Receipt>();
        }
        #endregion

        #region Data Loading
        private void LoadData()
        {
            try
            {
                Invoices = DataService.Invoices ?? new List<Invoice>();
                Customers = DataService.Customers ?? new List<Customer>();
                Receipts = DataService.Receipts ?? new List<Receipt>();
            }
            catch
            {
                Invoices = new List<Invoice>();
                Customers = new List<Customer>();
                Receipts = new List<Receipt>();
            }
        }
        #endregion

        #region Summary Reports
        /// <summary>
        /// الحصول على ملخص الفواتير حسب العميل
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="customerId">معرف العميل (اختياري)</param>
        /// <param name="status">الحالة (اختياري)</param>
        /// <returns>ملخص الفواتير</returns>
        public List<CustomerInvoiceSummary> GetCustomerInvoiceSummary(DateTime? fromDate = null, DateTime? toDate = null,
            int? customerId = null, string status = null)
        {
            var filteredInvoices = GetFilteredInvoices(fromDate, toDate, customerId, status);

            return filteredInvoices
                .GroupBy(i => i.CustomerId)
                .Select(g => new CustomerInvoiceSummary
                {
                    CustomerId = g.Key,
                    CustomerName = Customers.FirstOrDefault(c => c.Id == g.Key)?.CustomerName ?? "غير محدد",
                    InvoiceCount = g.Count(),
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    PaidAmount = g.Sum(i => i.PaidAmount),
                    OutstandingAmount = g.Sum(i => i.TotalAmount - i.PaidAmount),
                    CollectionRate = g.Sum(i => i.TotalAmount) > 0 ?
                        (g.Sum(i => i.PaidAmount) / g.Sum(i => i.TotalAmount)) * 100 : 0,
                    AverageInvoiceValue = g.Average(i => i.TotalAmount),
                    LastInvoiceDate = g.Max(i => i.InvoiceDate)
                })
                .OrderByDescending(s => s.TotalAmount)
                .ToList();
        }

        /// <summary>
        /// الحصول على تحليل أعمار الفواتير
        /// </summary>
        /// <param name="asOfDate">كما في تاريخ</param>
        /// <returns>تحليل الأعمار</returns>
        public List<InvoiceAgingAnalysis> GetInvoiceAgingAnalysis(DateTime? asOfDate = null)
        {
            var analysisDate = asOfDate ?? DateTime.Today;
            var outstandingInvoices = Invoices.Where(i => i.TotalAmount > i.PaidAmount).ToList();

            return outstandingInvoices
                .GroupBy(i => i.CustomerId)
                .Select(g => new InvoiceAgingAnalysis
                {
                    CustomerId = g.Key,
                    CustomerName = Customers.FirstOrDefault(c => c.Id == g.Key)?.CustomerName ?? "غير محدد",
                    Current = g.Where(i => (analysisDate - i.DueDate).Days <= 30)
                            .Sum(i => i.TotalAmount - i.PaidAmount),
                    Days31to60 = g.Where(i => (analysisDate - i.DueDate).Days > 30 &&
                                            (analysisDate - i.DueDate).Days <= 60)
                               .Sum(i => i.TotalAmount - i.PaidAmount),
                    Days61to90 = g.Where(i => (analysisDate - i.DueDate).Days > 60 &&
                                            (analysisDate - i.DueDate).Days <= 90)
                               .Sum(i => i.TotalAmount - i.PaidAmount),
                    Days91to120 = g.Where(i => (analysisDate - i.DueDate).Days > 90 &&
                                             (analysisDate - i.DueDate).Days <= 120)
                                .Sum(i => i.TotalAmount - i.PaidAmount),
                    Over120Days = g.Where(i => (analysisDate - i.DueDate).Days > 120)
                                .Sum(i => i.TotalAmount - i.PaidAmount),
                    TotalOutstanding = g.Sum(i => i.TotalAmount - i.PaidAmount)
                })
                .Where(a => a.TotalOutstanding > 0)
                .OrderByDescending(a => a.TotalOutstanding)
                .ToList();
        }

        /// <summary>
        /// الحصول على إحصائيات الفواتير
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>الإحصائيات</returns>
        public InvoiceStatistics GetInvoiceStatistics(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var filteredInvoices = GetFilteredInvoices(fromDate, toDate);

            return new InvoiceStatistics
            {
                TotalInvoices = filteredInvoices.Count,
                TotalAmount = filteredInvoices.Sum(i => i.TotalAmount),
                PaidAmount = filteredInvoices.Sum(i => i.PaidAmount),
                OutstandingAmount = filteredInvoices.Sum(i => i.TotalAmount - i.PaidAmount),
                CollectionRate = filteredInvoices.Sum(i => i.TotalAmount) > 0 ?
                    (filteredInvoices.Sum(i => i.PaidAmount) / filteredInvoices.Sum(i => i.TotalAmount)) * 100 : 0,
                AverageInvoiceValue = filteredInvoices.Any() ? filteredInvoices.Average(i => i.TotalAmount) : 0,
                AverageDaysToPayment = CalculateAverageDaysToPayment(filteredInvoices),
                OverdueInvoices = filteredInvoices.Count(i => i.DueDate < DateTime.Today && i.TotalAmount > i.PaidAmount),
                OverdueAmount = filteredInvoices.Where(i => i.DueDate < DateTime.Today && i.TotalAmount > i.PaidAmount)
                                              .Sum(i => i.TotalAmount - i.PaidAmount)
            };
        }
        #endregion

        #region Trend Analysis
        /// <summary>
        /// الحصول على اتجاه المبيعات الشهري
        /// </summary>
        /// <param name="months">عدد الأشهر</param>
        /// <returns>بيانات الاتجاه</returns>
        public List<MonthlyTrend> GetMonthlySalesTrend(int months = 12)
        {
            var startDate = DateTime.Today.AddMonths(-months);
            var filteredInvoices = Invoices.Where(i => i.InvoiceDate >= startDate).ToList();

            return filteredInvoices
                .GroupBy(i => new { Year = i.InvoiceDate.Year, Month = i.InvoiceDate.Month })
                .Select(g => new MonthlyTrend
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    Period = $"{g.Key.Year}/{g.Key.Month:00}",
                    InvoiceCount = g.Count(),
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    PaidAmount = g.Sum(i => i.PaidAmount),
                    CollectionRate = g.Sum(i => i.TotalAmount) > 0 ?
                        (g.Sum(i => i.PaidAmount) / g.Sum(i => i.TotalAmount)) * 100 : 0
                })
                .OrderBy(t => t.Year).ThenBy(t => t.Month)
                .ToList();
        }

        /// <summary>
        /// الحصول على توزيع الفواتير حسب الحالة
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>توزيع الحالات</returns>
        public List<StatusDistribution> GetStatusDistribution(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var filteredInvoices = GetFilteredInvoices(fromDate, toDate);

            return filteredInvoices
                .GroupBy(i => i.Status)
                .Select(g => new StatusDistribution
                {
                    Status = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    Percentage = (decimal)g.Count() / filteredInvoices.Count * 100
                })
                .OrderByDescending(s => s.TotalAmount)
                .ToList();
        }
        #endregion

        #region Performance Analysis
        /// <summary>
        /// الحصول على مؤشرات الأداء الرئيسية
        /// </summary>
        /// <param name="currentPeriodStart">بداية الفترة الحالية</param>
        /// <param name="currentPeriodEnd">نهاية الفترة الحالية</param>
        /// <param name="previousPeriodStart">بداية الفترة السابقة</param>
        /// <param name="previousPeriodEnd">نهاية الفترة السابقة</param>
        /// <returns>مؤشرات الأداء</returns>
        public List<PerformanceKPI> GetPerformanceKPIs(DateTime currentPeriodStart, DateTime currentPeriodEnd,
            DateTime previousPeriodStart, DateTime previousPeriodEnd)
        {
            var currentStats = GetInvoiceStatistics(currentPeriodStart, currentPeriodEnd);
            var previousStats = GetInvoiceStatistics(previousPeriodStart, previousPeriodEnd);

            var kpis = new List<PerformanceKPI>();

            // Total Revenue
            kpis.Add(new PerformanceKPI
            {
                Metric = "إجمالي الإيرادات",
                CurrentPeriod = currentStats.TotalAmount,
                PreviousPeriod = previousStats.TotalAmount,
                Change = currentStats.TotalAmount - previousStats.TotalAmount,
                ChangePercent = previousStats.TotalAmount > 0 ?
                    ((currentStats.TotalAmount - previousStats.TotalAmount) / previousStats.TotalAmount) * 100 : 0,
                Target = currentStats.TotalAmount * 1.1m, // 10% growth target
                Achievement = currentStats.TotalAmount > 0 && previousStats.TotalAmount > 0 ?
                    (currentStats.TotalAmount / (previousStats.TotalAmount * 1.1m)) * 100 : 0
            });

            // Invoice Count
            kpis.Add(new PerformanceKPI
            {
                Metric = "عدد الفواتير",
                CurrentPeriod = currentStats.TotalInvoices,
                PreviousPeriod = previousStats.TotalInvoices,
                Change = currentStats.TotalInvoices - previousStats.TotalInvoices,
                ChangePercent = previousStats.TotalInvoices > 0 ?
                    ((decimal)(currentStats.TotalInvoices - previousStats.TotalInvoices) / previousStats.TotalInvoices) * 100 : 0,
                Target = previousStats.TotalInvoices * 1.05m, // 5% growth target
                Achievement = previousStats.TotalInvoices > 0 ?
                    ((decimal)currentStats.TotalInvoices / (previousStats.TotalInvoices * 1.05m)) * 100 : 0
            });

            // Collection Rate
            kpis.Add(new PerformanceKPI
            {
                Metric = "معدل التحصيل",
                CurrentPeriod = currentStats.CollectionRate,
                PreviousPeriod = previousStats.CollectionRate,
                Change = currentStats.CollectionRate - previousStats.CollectionRate,
                ChangePercent = previousStats.CollectionRate > 0 ?
                    ((currentStats.CollectionRate - previousStats.CollectionRate) / previousStats.CollectionRate) * 100 : 0,
                Target = 85, // 85% collection rate target
                Achievement = currentStats.CollectionRate > 0 ? (currentStats.CollectionRate / 85) * 100 : 0
            });

            return kpis;
        }
        #endregion

        #region Helper Methods
        private List<Invoice> GetFilteredInvoices(DateTime? fromDate = null, DateTime? toDate = null,
            int? customerId = null, string status = null)
        {
            var filtered = Invoices.AsQueryable();

            if (fromDate.HasValue)
                filtered = filtered.Where(i => i.InvoiceDate >= fromDate.Value.Date);

            if (toDate.HasValue)
                filtered = filtered.Where(i => i.InvoiceDate <= toDate.Value.Date);

            if (customerId.HasValue)
                filtered = filtered.Where(i => i.CustomerId == customerId.Value);

            if (!string.IsNullOrEmpty(status))
                filtered = filtered.Where(i => i.Status == status);

            return filtered.ToList();
        }

        private double CalculateAverageDaysToPayment(List<Invoice> invoices)
        {
            var paidInvoices = invoices.Where(i => i.Status == "مدفوعة").ToList();
            if (!paidInvoices.Any()) return 0;

            var totalDays = 0.0;
            var count = 0;

            foreach (var invoice in paidInvoices)
            {
                // Find the last payment for this invoice
                var invoiceReceipts = Receipts.Where(r => r.InvoiceId == invoice.Id).OrderByDescending(r => r.ReceiptDate);
                if (invoiceReceipts.Any())
                {
                    var lastPaymentDate = invoiceReceipts.First().ReceiptDate;
                    totalDays += (lastPaymentDate - invoice.InvoiceDate).TotalDays;
                    count++;
                }
            }

            return count > 0 ? totalDays / count : 0;
        }
        #endregion
    }
}
