@echo off
chcp 65001 > nul
echo ========================================
echo بناء نظام الفواتير الشامل
echo ========================================
echo.

echo نظام فواتير متكامل مع جميع الميزات المطلوبة
echo تم إصلاح جميع مشاكل الواجهات والمراجع
echo.

rem التحقق من وجود مترجم C#
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ مترجم C# غير متوفر
    echo.
    echo الحلول:
    echo 1. افتح Developer Command Prompt for Visual Studio
    echo 2. أو ثبت .NET Framework SDK
    echo 3. أو استخدم Visual Studio لبناء المشروع
    echo.
    goto :end
)

echo ✓ مترجم C# متوفر
echo.

echo فحص الملف المطلوب...
if not exist "InvoicesSystemComplete.cs" (
    echo ✗ ملف InvoicesSystemComplete.cs مفقود
    echo يرجى التأكد من وجود الملف
    goto :end
)

echo ✓ ملف InvoicesSystemComplete.cs موجود
echo.

echo بناء النظام...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /out:InvoicesSystemComplete.exe ^
    InvoicesSystemComplete.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء النظام بنجاح!
    echo.
    echo تم إنشاء: InvoicesSystemComplete.exe
    echo.
    
    echo إنشاء ملف الإعداد...
    echo ^<?xml version="1.0" encoding="utf-8"?^> > InvoicesSystemComplete.exe.config
    echo ^<configuration^> >> InvoicesSystemComplete.exe.config
    echo   ^<startup^> >> InvoicesSystemComplete.exe.config
    echo     ^<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/^> >> InvoicesSystemComplete.exe.config
    echo   ^</startup^> >> InvoicesSystemComplete.exe.config
    echo ^</configuration^> >> InvoicesSystemComplete.exe.config
    
    echo ✓ تم إنشاء ملف الإعداد
    echo.
    
    set /p run="هل تريد تشغيل النظام الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام...
        start "" "InvoicesSystemComplete.exe"
        echo.
        echo ✓ تم تشغيل النظام بنجاح!
    )
    
    echo.
    echo ========================================
    echo نظام الفواتير الشامل
    echo ========================================
    echo.
    echo ملف التشغيل: InvoicesSystemComplete.exe
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo الميزات الرئيسية:
    echo ✓ إدارة الفواتير الكاملة
    echo ✓ إنشاء وتعديل الفواتير
    echo ✓ تتبع المدفوعات والمتأخرات
    echo ✓ تسجيل دفعات العملاء
    echo ✓ طباعة الفواتير
    echo ✓ تقارير شاملة ومفصلة
    echo ✓ واجهة عربية احترافية
    echo ✓ فلترة وبحث متقدم
    echo.
    echo القوائم المتوفرة:
    echo • ملف ^> تسجيل خروج / خروج
    echo • الفواتير ^> إدارة الفواتير
    echo • الفواتير ^> إنشاء فاتورة جديدة
    echo • الفواتير ^> الفواتير المتأخرة
    echo • الفواتير ^> الفواتير غير المدفوعة
    echo • السندات ^> سندات القبض
    echo • السندات ^> تسجيل دفعة
    echo • البيانات الأساسية ^> إدارة العملاء
    echo • البيانات الأساسية ^> إدارة المنتجات
    echo • التقارير ^> تقرير الفواتير
    echo • التقارير ^> كشف حساب العميل
    echo • التقارير ^> تقرير المبيعات
    echo • التقارير ^> تقرير الأرباح
    echo • مساعدة ^> حول البرنامج
    echo.
    echo البيانات التجريبية الغنية:
    echo • 5 عملاء بأرصدة وبيانات مختلفة
    echo • 7 فواتير بحالات متنوعة:
    echo   - فواتير مدفوعة بالكامل
    echo   - فواتير مدفوعة جزئياً
    echo   - فواتير متأخرة
    echo   - فواتير مؤكدة
    echo   - فواتير مسودة
    echo • مبالغ متنوعة من 3,450 إلى 23,000 ريال
    echo • تواريخ استحقاق مختلفة
    echo • ملاحظات وتفاصيل واقعية
    echo.
    echo الميزات المتقدمة:
    echo ✓ حساب الضريبة تلقائياً (15%%)
    echo ✓ إدارة الخصومات
    echo ✓ تتبع حالة كل فاتورة
    echo ✓ تلوين الفواتير حسب الحالة
    echo ✓ فلترة حسب التاريخ والحالة والعميل
    echo ✓ بحث في رقم الفاتورة والعميل والملاحظات
    echo ✓ عرض أيام التأخير للفواتير المتأخرة
    echo ✓ حماية من حذف الفواتير المدفوعة
    echo ✓ تحديث تلقائي للأرصدة
    echo ✓ طباعة فواتير مفصلة
    echo.
    echo كيفية الاستخدام:
    echo 1. شغل البرنامج
    echo 2. سجل دخول بـ admin/admin123
    echo 3. من قائمة "الفواتير" اختر "إدارة الفواتير"
    echo 4. استخدم الأزرار لإضافة أو تعديل أو حذف الفواتير
    echo 5. اضغط مرتين على فاتورة لتعديلها
    echo 6. استخدم "تسجيل دفعة" لتسجيل مدفوعات
    echo 7. استخدم الفلاتر للبحث والتصفية
    echo 8. اطبع الفواتير من زر "طباعة"
    
) else (
    echo.
    echo ✗ فشل في بناء النظام
    echo.
    echo الأخطاء المحتملة:
    echo 1. أخطاء في الكود المصدري
    echo 2. مراجع مفقودة
    echo 3. مشاكل في مترجم C#
    echo.
    echo للتشخيص:
    echo 1. افتح InvoicesSystemComplete.cs وتحقق من الأخطاء
    echo 2. تأكد من وجود جميع المراجع المطلوبة
    echo 3. استخدم Visual Studio للحصول على تفاصيل الأخطاء
    echo.
    echo بدائل أخرى:
    echo • استخدم Visual Studio لبناء المشروع
    echo • راجع الكود للتأكد من عدم وجود أخطاء إملائية
    echo • جرب build_simple_working.bat للنظام المبسط
)

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
