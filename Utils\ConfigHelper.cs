using System;
using System.IO;
using System.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace AccountingSystem.Utils
{
    /// <summary>
    /// مساعد إدارة الإعدادات
    /// </summary>
    public static class ConfigHelper
    {
        private static JObject _config;
        private static readonly string ConfigFilePath = "system_config.json";
        
        /// <summary>
        /// تحميل الإعدادات
        /// </summary>
        static ConfigHelper()
        {
            LoadConfig();
        }
        
        /// <summary>
        /// تحميل ملف الإعدادات
        /// </summary>
        private static void LoadConfig()
        {
            try
            {
                if (File.Exists(ConfigFilePath))
                {
                    string configContent = File.ReadAllText(ConfigFilePath);
                    _config = JObject.Parse(configContent);
                }
                else
                {
                    // إنشاء ملف إعدادات افتراضي
                    CreateDefaultConfig();
                }
            }
            catch (Exception)
            {
                // في حالة فشل تحميل الإعدادات، استخدم الإعدادات الافتراضية
                CreateDefaultConfig();
            }
        }
        
        /// <summary>
        /// إنشاء ملف إعدادات افتراضي
        /// </summary>
        private static void CreateDefaultConfig()
        {
            _config = new JObject
            {
                ["SystemSettings"] = new JObject
                {
                    ["ApplicationName"] = "نظام المحاسبة الذكي",
                    ["Version"] = "1.0.0",
                    ["CompanyName"] = "شركة التطوير"
                },
                ["SecuritySettings"] = new JObject
                {
                    ["PasswordMinLength"] = 6,
                    ["SessionTimeoutMinutes"] = 30
                },
                ["DatabaseSettings"] = new JObject
                {
                    ["AutoBackup"] = true,
                    ["BackupIntervalDays"] = 1
                }
            };
        }
        
        /// <summary>
        /// الحصول على قيمة إعداد
        /// </summary>
        /// <typeparam name="T">نوع القيمة</typeparam>
        /// <param name="path">مسار الإعداد</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>قيمة الإعداد</returns>
        public static T GetSetting<T>(string path, T defaultValue = default(T))
        {
            try
            {
                var token = _config.SelectToken(path);
                if (token != null)
                {
                    return token.ToObject<T>();
                }
            }
            catch (Exception)
            {
                // في حالة الخطأ، إرجاع القيمة الافتراضية
            }
            
            return defaultValue;
        }
        
        /// <summary>
        /// تعيين قيمة إعداد
        /// </summary>
        /// <param name="path">مسار الإعداد</param>
        /// <param name="value">القيمة الجديدة</param>
        public static void SetSetting(string path, object value)
        {
            try
            {
                var pathParts = path.Split('.');
                JObject current = _config;
                
                // التنقل إلى المسار المطلوب
                for (int i = 0; i < pathParts.Length - 1; i++)
                {
                    if (current[pathParts[i]] == null)
                    {
                        current[pathParts[i]] = new JObject();
                    }
                    current = (JObject)current[pathParts[i]];
                }
                
                // تعيين القيمة
                current[pathParts[pathParts.Length - 1]] = JToken.FromObject(value);
                
                // حفظ الإعدادات
                SaveConfig();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تعيين الإعداد {path}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// حفظ الإعدادات إلى الملف
        /// </summary>
        public static void SaveConfig()
        {
            try
            {
                string configContent = _config.ToString(Formatting.Indented);
                File.WriteAllText(ConfigFilePath, configContent);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }
        
        /// <summary>
        /// إعادة تحميل الإعدادات
        /// </summary>
        public static void ReloadConfig()
        {
            LoadConfig();
        }
        
        /// <summary>
        /// الحصول على إعدادات النظام
        /// </summary>
        public static class System
        {
            public static string ApplicationName => GetSetting("SystemSettings.ApplicationName", "نظام المحاسبة الذكي");
            public static string Version => GetSetting("SystemSettings.Version", "1.0.0");
            public static string CompanyName => GetSetting("SystemSettings.CompanyName", "شركة التطوير");
            public static string DatabasePath => GetSetting("SystemSettings.DatabasePath", "database.db");
            public static string BackupPath => GetSetting("SystemSettings.BackupPath", "Backups");
            public static string ReportsPath => GetSetting("SystemSettings.ReportsPath", "Reports");
        }
        
        /// <summary>
        /// إعدادات الأمان
        /// </summary>
        public static class Security
        {
            public static int PasswordMinLength => GetSetting("SecuritySettings.PasswordMinLength", 6);
            public static bool PasswordRequireNumbers => GetSetting("SecuritySettings.PasswordRequireNumbers", true);
            public static int SessionTimeoutMinutes => GetSetting("SecuritySettings.SessionTimeoutMinutes", 30);
            public static int MaxLoginAttempts => GetSetting("SecuritySettings.MaxLoginAttempts", 3);
        }
        
        /// <summary>
        /// إعدادات قاعدة البيانات
        /// </summary>
        public static class Database
        {
            public static bool AutoBackup => GetSetting("DatabaseSettings.AutoBackup", true);
            public static int BackupIntervalDays => GetSetting("DatabaseSettings.BackupIntervalDays", 1);
            public static int KeepBackupDays => GetSetting("DatabaseSettings.KeepBackupDays", 30);
            public static int ConnectionTimeout => GetSetting("DatabaseSettings.ConnectionTimeout", 30);
        }
        
        /// <summary>
        /// إعدادات الذكاء الاصطناعي
        /// </summary>
        public static class AI
        {
            public static bool EnableAI => GetSetting("AISettings.EnableAI", true);
            public static int AnalysisFrequencyDays => GetSetting("AISettings.AnalysisFrequencyDays", 7);
            public static double RecommendationThreshold => GetSetting("AISettings.RecommendationThreshold", 0.7);
            public static bool ErrorDetectionEnabled => GetSetting("AISettings.ErrorDetectionEnabled", true);
        }
        
        /// <summary>
        /// إعدادات التقارير
        /// </summary>
        public static class Reports
        {
            public static string DefaultCurrency => GetSetting("ReportSettings.DefaultCurrency", "ريال سعودي");
            public static string CurrencySymbol => GetSetting("ReportSettings.CurrencySymbol", "ر.س");
            public static string DateFormat => GetSetting("ReportSettings.DateFormat", "yyyy/MM/dd");
            public static string NumberFormat => GetSetting("ReportSettings.NumberFormat", "N2");
        }
        
        /// <summary>
        /// إعدادات واجهة المستخدم
        /// </summary>
        public static class UI
        {
            public static string Theme => GetSetting("UISettings.Theme", "Default");
            public static string FontFamily => GetSetting("UISettings.FontFamily", "Tahoma");
            public static int FontSize => GetSetting("UISettings.FontSize", 10);
            public static bool RightToLeft => GetSetting("UISettings.RightToLeft", true);
            public static bool AutoSave => GetSetting("UISettings.AutoSave", true);
        }
        
        /// <summary>
        /// إعدادات الضرائب
        /// </summary>
        public static class Tax
        {
            public static decimal DefaultTaxRate => GetSetting("TaxSettings.DefaultTaxRate", 15.0m);
            public static string TaxAccountCode => GetSetting("TaxSettings.TaxAccountCode", "2101");
            public static bool EnableTaxCalculation => GetSetting("TaxSettings.EnableTaxCalculation", true);
        }
        
        /// <summary>
        /// الحصول على سلسلة الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>سلسلة الاتصال</returns>
        public static string GetConnectionString()
        {
            try
            {
                // محاولة الحصول على سلسلة الاتصال من App.config أولاً
                var connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;
                
                if (!string.IsNullOrEmpty(connectionString))
                {
                    return connectionString;
                }
                
                // إذا لم توجد، استخدم المسار الافتراضي
                string databasePath = System.DatabasePath;
                return $"Data Source={databasePath};Version=3;";
            }
            catch (Exception)
            {
                // في حالة الخطأ، استخدم المسار الافتراضي
                return "Data Source=database.db;Version=3;";
            }
        }
    }
}
