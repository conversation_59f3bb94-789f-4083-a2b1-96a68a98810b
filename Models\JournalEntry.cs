using System;
using System.Collections.Generic;

namespace AccountingSystem.Models
{
    /// <summary>
    /// نموذج القيد اليومي
    /// </summary>
    public class JournalEntry
    {
        public int Id { get; set; }
        public string EntryNumber { get; set; }
        public DateTime EntryDate { get; set; }
        public string Description { get; set; }
        public string Reference { get; set; }
        public decimal TotalDebit { get; set; }
        public decimal TotalCredit { get; set; }
        public string Status { get; set; } // مسودة، مؤكد، ملغي
        public bool IsPosted { get; set; }
        public DateTime? PostedDate { get; set; }
        public string PostedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }
        
        // تفاصيل القيد
        public List<JournalEntryDetail> Details { get; set; } = new List<JournalEntryDetail>();
    }
    
    /// <summary>
    /// تفاصيل القيد اليومي
    /// </summary>
    public class JournalEntryDetail
    {
        public int Id { get; set; }
        public int JournalEntryId { get; set; }
        public int AccountId { get; set; }
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public string Description { get; set; }
        public string Reference { get; set; }
        public int LineNumber { get; set; }
        
        // خصائص إضافية للعرض
        public Account Account { get; set; }
        public JournalEntry JournalEntry { get; set; }
    }
}
