using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace AccountingSystem.Utils
{
    /// <summary>
    /// مساعد الطباعة
    /// </summary>
    public static class PrintHelper
    {
        private static List<string> printLines;
        private static int currentLine;
        private static Font printFont;
        private static string reportTitle;

        /// <summary>
        /// طباعة تقرير
        /// </summary>
        /// <param name="lines">أسطر التقرير</param>
        /// <param name="title">عنوان التقرير</param>
        public static void PrintReport(List<string> lines, string title)
        {
            try
            {
                printLines = lines;
                currentLine = 0;
                reportTitle = title;
                printFont = new Font("Courier New", 10);

                PrintDocument printDocument = new PrintDocument();
                printDocument.PrintPage += PrintDocument_PrintPage;

                PrintPreviewDialog previewDialog = new PrintPreviewDialog();
                previewDialog.Document = printDocument;
                previewDialog.WindowState = FormWindowState.Maximized;
                previewDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة فاتورة
        /// </summary>
        /// <param name="invoice">الفاتورة</param>
        /// <param name="customer">العميل</param>
        public static void PrintInvoice(IntegratedInvoiceSystem.Models.Invoice invoice, IntegratedInvoiceSystem.Models.Customer customer)
        {
            try
            {
                var lines = new List<string>();

                lines.Add("فاتورة مبيعات");
                lines.Add("=============");
                lines.Add("");
                lines.Add($"رقم الفاتورة: {invoice.InvoiceNumber}");
                lines.Add($"تاريخ الفاتورة: {invoice.InvoiceDate:yyyy/MM/dd}");
                lines.Add($"تاريخ الاستحقاق: {invoice.DueDate:yyyy/MM/dd}");
                lines.Add("");
                lines.Add("بيانات العميل:");
                lines.Add($"الاسم: {customer?.CustomerName ?? "غير محدد"}");
                lines.Add($"الهاتف: {customer?.Phone ?? "غير محدد"}");
                lines.Add($"العنوان: {customer?.Address ?? "غير محدد"}");
                lines.Add("");
                lines.Add("تفاصيل الفاتورة:");
                lines.Add($"المبلغ الفرعي: {invoice.SubTotal:N2} ريال");
                lines.Add($"الضريبة: {invoice.TaxAmount:N2} ريال");
                lines.Add($"الخصم: {invoice.DiscountAmount:N2} ريال");
                lines.Add($"الإجمالي: {invoice.TotalAmount:N2} ريال");
                lines.Add($"المدفوع: {invoice.PaidAmount:N2} ريال");
                lines.Add($"المتبقي: {(invoice.TotalAmount - invoice.PaidAmount):N2} ريال");
                lines.Add("");
                lines.Add($"الحالة: {invoice.Status}");
                lines.Add($"ملاحظات: {invoice.Notes}");
                lines.Add("");
                lines.Add($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");

                PrintReport(lines, "فاتورة مبيعات");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة سند قبض
        /// </summary>
        /// <param name="receipt">سند القبض</param>
        /// <param name="customer">العميل</param>
        public static void PrintReceipt(IntegratedInvoiceSystem.Models.Receipt receipt, IntegratedInvoiceSystem.Models.Customer customer)
        {
            try
            {
                var lines = new List<string>();

                lines.Add("سند قبض");
                lines.Add("========");
                lines.Add("");
                lines.Add($"رقم السند: {receipt.ReceiptNumber}");
                lines.Add($"تاريخ السند: {receipt.ReceiptDate:yyyy/MM/dd}");
                lines.Add("");
                lines.Add("بيانات العميل:");
                lines.Add($"الاسم: {customer?.CustomerName ?? "غير محدد"}");
                lines.Add($"الهاتف: {customer?.Phone ?? "غير محدد"}");
                lines.Add("");
                lines.Add("تفاصيل السند:");
                lines.Add($"المبلغ: {receipt.Amount:N2} ريال");
                lines.Add($"طريقة الدفع: {receipt.PaymentMethod}");
                lines.Add($"رقم المرجع: {receipt.ReferenceNumber}");
                lines.Add($"الحالة: {receipt.Status}");
                lines.Add($"ملاحظات: {receipt.Notes}");
                lines.Add("");
                lines.Add($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");

                PrintReport(lines, "سند قبض");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة سند القبض: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة كشف حساب عميل
        /// </summary>
        /// <param name="customer">العميل</param>
        /// <param name="invoices">الفواتير</param>
        /// <param name="receipts">سندات القبض</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        public static void PrintCustomerStatement(IntegratedInvoiceSystem.Models.Customer customer, List<IntegratedInvoiceSystem.Models.Invoice> invoices,
            List<IntegratedInvoiceSystem.Models.Receipt> receipts, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var lines = new List<string>();

                lines.Add("كشف حساب عميل");
                lines.Add("===============");
                lines.Add("");
                lines.Add($"اسم العميل: {customer.CustomerName}");
                lines.Add($"الهاتف: {customer.Phone}");
                lines.Add($"العنوان: {customer.Address}");
                lines.Add($"الفترة: من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}");
                lines.Add("");

                // الفواتير
                lines.Add("الفواتير:");
                lines.Add("رقم الفاتورة".PadRight(15) + "التاريخ".PadLeft(12) + "الاستحقاق".PadLeft(12) +
                         "الإجمالي".PadLeft(12) + "المدفوع".PadLeft(12) + "المتبقي".PadLeft(12) + "الحالة".PadRight(15));
                lines.Add(new string('=', 90));

                var customerInvoices = invoices.Where(i => i.CustomerId == customer.Id &&
                                                         i.InvoiceDate >= fromDate.Date &&
                                                         i.InvoiceDate <= toDate.Date).ToList();

                foreach (var invoice in customerInvoices.OrderByDescending(i => i.InvoiceDate))
                {
                    var remainingAmount = invoice.TotalAmount - invoice.PaidAmount;
                    string line = $"{invoice.InvoiceNumber.PadRight(15)} {invoice.InvoiceDate:yyyy/MM/dd} " +
                                 $"{invoice.DueDate:yyyy/MM/dd}    {invoice.TotalAmount.ToString("N2").PadLeft(12)} " +
                                 $"{invoice.PaidAmount.ToString("N2").PadLeft(12)} {remainingAmount.ToString("N2").PadLeft(12)} " +
                                 $"{invoice.Status}";
                    lines.Add(line);
                }

                lines.Add("");

                // سندات القبض
                lines.Add("سندات القبض:");
                lines.Add("رقم السند".PadRight(12) + "التاريخ".PadLeft(12) + "المبلغ".PadLeft(12) +
                         "طريقة الدفع".PadRight(15) + "رقم المرجع".PadRight(15) + "الحالة".PadRight(10));
                lines.Add(new string('=', 80));

                var customerReceipts = receipts.Where(r => r.CustomerId == customer.Id &&
                                                         r.ReceiptDate >= fromDate.Date &&
                                                         r.ReceiptDate <= toDate.Date).ToList();

                foreach (var receipt in customerReceipts.OrderByDescending(r => r.ReceiptDate))
                {
                    string line = $"{receipt.ReceiptNumber.PadRight(12)} {receipt.ReceiptDate:yyyy/MM/dd} " +
                                 $"{receipt.Amount.ToString("N2").PadLeft(12)} {receipt.PaymentMethod.PadRight(15)} " +
                                 $"{receipt.ReferenceNumber.PadRight(15)} {receipt.Status}";
                    lines.Add(line);
                }

                lines.Add("");

                // الملخص
                var totalInvoices = customerInvoices.Sum(i => i.TotalAmount);
                var totalPaid = customerInvoices.Sum(i => i.PaidAmount);
                var totalOutstanding = totalInvoices - totalPaid;

                lines.Add("الملخص:");
                lines.Add($"إجمالي الفواتير: {totalInvoices:N2} ريال");
                lines.Add($"إجمالي المدفوع: {totalPaid:N2} ريال");
                lines.Add($"إجمالي المستحق: {totalOutstanding:N2} ريال");
                lines.Add("");
                lines.Add($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");

                PrintReport(lines, "كشف حساب عميل");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة كشف الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حفظ التقرير كملف نصي
        /// </summary>
        /// <param name="lines">أسطر التقرير</param>
        /// <param name="title">عنوان التقرير</param>
        public static void SaveReportAsText(List<string> lines, string title)
        {
            try
            {
                var saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Text Files|*.txt";
                saveDialog.Title = "حفظ التقرير";
                saveDialog.FileName = $"{title}_{DateTime.Now:yyyyMMdd}.txt";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    File.WriteAllLines(saveDialog.FileName, lines, Encoding.UTF8);
                    MessageBox.Show("تم حفظ التقرير بنجاح!", "حفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            try
            {
                float yPos = 0;
                int count = 0;
                float leftMargin = e.MarginBounds.Left;
                float topMargin = e.MarginBounds.Top;
                string line = null;

                // Calculate the number of lines per page
                float linesPerPage = e.MarginBounds.Height / printFont.GetHeight(e.Graphics);

                // Print header
                Font headerFont = new Font("Tahoma", 14, FontStyle.Bold);
                e.Graphics.DrawString(reportTitle, headerFont, Brushes.Black, leftMargin, topMargin);
                e.Graphics.DrawString($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}",
                    printFont, Brushes.Black, leftMargin, topMargin + headerFont.GetHeight(e.Graphics));

                yPos = topMargin + headerFont.GetHeight(e.Graphics) + printFont.GetHeight(e.Graphics) + 10;
                count = 2;

                // Print each line of the document
                while (count < linesPerPage && currentLine < printLines.Count)
                {
                    line = printLines[currentLine];
                    e.Graphics.DrawString(line, printFont, Brushes.Black, leftMargin, yPos);
                    yPos += printFont.GetHeight(e.Graphics);
                    currentLine++;
                    count++;
                }

                // If more lines exist, print another page
                if (currentLine < printLines.Count)
                    e.HasMorePages = true;
                else
                    e.HasMorePages = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الصفحة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
