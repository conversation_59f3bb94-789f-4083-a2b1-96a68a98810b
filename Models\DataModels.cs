using System;
using System.Collections.Generic;
using System.Linq;

namespace IntegratedInvoiceSystem.Models
{
    // نموذج العميل
    public class Customer
    {
        public int Id { get; set; }
        public string CustomerCode { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public decimal CurrentBalance { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public string TaxNumber { get; set; }
        public string ContactPerson { get; set; }
        public string CustomerType { get; set; } = "عادي"; // نوع العميل
    }

    // نموذج المنتج
    public class Product
    {
        public int Id { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string Category { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal SalePrice => UnitPrice; // خاصية للتوافق
        public decimal PurchasePrice => CostPrice; // خاصية للتوافق
        public int StockQuantity { get; set; }
        public decimal CurrentStock => StockQuantity; // خاصية للتوافق
        public decimal MinimumStock { get; set; } = 10; // الحد الأدنى للمخزون
        public string Unit { get; set; }
        public bool IsActive { get; set; }
        public decimal CostPrice { get; set; }
        public string Description { get; set; }
        public string Barcode { get; set; }
    }

    // نموذج الفاتورة
    public class Invoice
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public int CustomerId { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public string PaymentTerms { get; set; }
        public string DeliveryAddress { get; set; }
        public List<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();

        // خصائص محسوبة
        public decimal RemainingAmount => TotalAmount - PaidAmount;
        public bool IsOverdue => DueDate < DateTime.Now && Status != "مدفوعة" && Status != "ملغية";
        public int DaysOverdue => IsOverdue ? (DateTime.Now - DueDate).Days : 0;
    }

    // نموذج صنف الفاتورة
    public class InvoiceItem
    {
        public int Id { get; set; }
        public int InvoiceId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountPercent { get; set; }
        public decimal TotalPrice => (Quantity * UnitPrice) * (1 - DiscountPercent / 100);
        public string Notes { get; set; }
    }

    // نموذج السند
    public class Receipt
    {
        public int Id { get; set; }
        public string ReceiptNumber { get; set; }
        public DateTime ReceiptDate { get; set; }
        public int CustomerId { get; set; }
        public int? InvoiceId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string ReferenceNumber { get; set; }
        public string Notes { get; set; }
        public string Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public string BankName { get; set; }
        public DateTime? ClearanceDate { get; set; }
    }

    // نموذج الشركة
    public class Company
    {
        public string Name { get; set; } = "شركة الفواتير المتكاملة";
        public string Address { get; set; } = "الرياض، المملكة العربية السعودية";
        public string Phone { get; set; } = "+966 11 123 4567";
        public string Email { get; set; } = "<EMAIL>";
        public string TaxNumber { get; set; } = "***************";
        public string CommercialRegister { get; set; } = "**********";
        public string Logo { get; set; } = "";
        public decimal TaxRate { get; set; } = 0.15m;
    }

    // نموذج المستخدم
    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public string Role { get; set; }
        public bool IsActive { get; set; }
        public DateTime LastLogin { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }
        public List<string> Permissions { get; set; } = new List<string>();
    }

    // نموذج التقرير
    public class ReportData
    {
        public string Title { get; set; }
        public DateTime GeneratedDate { get; set; }
        public string GeneratedBy { get; set; }
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
    }

    // نموذج الإعدادات
    public class SystemSettings
    {
        public decimal DefaultTaxRate { get; set; } = 0.15m;
        public string DefaultCurrency { get; set; } = "ر.س";
        public int InvoiceNumberLength { get; set; } = 3;
        public string InvoicePrefix { get; set; } = "INV";
        public string ReceiptPrefix { get; set; } = "R";
        public bool AutoBackup { get; set; } = true;
        public int BackupIntervalDays { get; set; } = 7;
        public string BackupPath { get; set; } = "Backup";
        public bool ShowNotifications { get; set; } = true;
        public int OverdueDaysWarning { get; set; } = 3;
        public int LowStockWarning { get; set; } = 10;
    }

    // نموذج الإحصائيات
    public class DashboardStats
    {
        public int TotalInvoices { get; set; }
        public decimal TotalSales { get; set; }
        public int PaidInvoices { get; set; }
        public decimal PaidAmount { get; set; }
        public int OverdueInvoices { get; set; }
        public decimal OverdueAmount { get; set; }
        public int TotalCustomers { get; set; }
        public decimal TotalBalance { get; set; }
        public decimal TotalProfit { get; set; }
        public int NewCustomersThisMonth { get; set; }
        public decimal AverageInvoiceValue { get; set; }
        public decimal CollectionRate { get; set; }
        public int LowStockProducts { get; set; }
        public decimal TodayPayments { get; set; }
        public int DueToday { get; set; }
    }

    // نموذج عنصر القائمة
    public class MenuItem
    {
        public string Text { get; set; }
        public string Key { get; set; }
        public string Permission { get; set; }
        public string Icon { get; set; }
        public Action Action { get; set; }
        public bool IsVisible { get; set; } = true;
        public bool IsEnabled { get; set; } = true;
    }

    // نموذج الإشعار
    public class Notification
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public string Type { get; set; } // Info, Warning, Error, Success
        public DateTime CreatedDate { get; set; }
        public bool IsRead { get; set; }
        public string UserId { get; set; }
        public string Icon { get; set; }
        public string ActionUrl { get; set; }
    }

    // نموذج الفلتر
    public class FilterCriteria
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string Status { get; set; }
        public int? CustomerId { get; set; }
        public string SearchText { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public bool? IsOverdue { get; set; }
    }

    // نموذج نتيجة البحث
    public class SearchResult<T>
    {
        public List<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasNextPage => PageNumber < TotalPages;
        public bool HasPreviousPage => PageNumber > 1;
    }

    // نموذج العملية
    public class Operation
    {
        public int Id { get; set; }
        public string Type { get; set; } // Create, Update, Delete, View
        public string EntityType { get; set; } // Invoice, Customer, Product, etc.
        public int EntityId { get; set; }
        public string Description { get; set; }
        public DateTime Timestamp { get; set; }
        public string UserId { get; set; }
        public string UserName { get; set; }
        public string Details { get; set; }
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; }
    }

    // نموذج النسخة الاحتياطية
    public class BackupInfo
    {
        public string FileName { get; set; }
        public DateTime CreatedDate { get; set; }
        public long FileSize { get; set; }
        public string CreatedBy { get; set; }
        public string Description { get; set; }
        public bool IsAutomatic { get; set; }
        public string FilePath { get; set; }
    }
}
