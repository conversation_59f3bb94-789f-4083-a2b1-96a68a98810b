using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace DashboardSystem
{
    // نماذج البيانات
    public class Customer
    {
        public int Id { get; set; }
        public string CustomerCode { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public decimal CurrentBalance { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class Invoice
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public int CustomerId { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }

        public decimal RemainingAmount => TotalAmount - PaidAmount;
        public bool IsOverdue => DueDate < DateTime.Now && Status != "مدفوعة" && Status != "ملغية";
        public int DaysOverdue => IsOverdue ? (DateTime.Now - DueDate).Days : 0;
    }

    public class Receipt
    {
        public int Id { get; set; }
        public string ReceiptNumber { get; set; }
        public DateTime ReceiptDate { get; set; }
        public int CustomerId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string Status { get; set; }
    }

    // خدمة المصادقة
    public static class AuthService
    {
        public static string CurrentUser { get; private set; } = "admin";

        public static bool Login(string username, string password)
        {
            if (username == "admin" && password == "admin123")
            {
                CurrentUser = username;
                return true;
            }
            return false;
        }

        public static void Logout()
        {
            CurrentUser = null;
        }
    }

    // نموذج تسجيل الدخول المحسن
    public class LoginForm : Form
    {
        private Panel mainPanel;
        private Panel loginPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Label lblTitle;

        public LoginForm()
        {
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "تسجيل الدخول - نظام الحسابات والفواتير";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.RightToLeft = RightToLeft.Yes;

            // الخلفية الرئيسية مع تدرج
            mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Paint += MainPanel_Paint;

            // لوحة تسجيل الدخول
            loginPanel = new Panel();
            loginPanel.Size = new Size(400, 350);
            loginPanel.Location = new Point(200, 125);
            loginPanel.BackColor = Color.White;
            loginPanel.Paint += LoginPanel_Paint;

            // العنوان
            lblTitle = new Label();
            lblTitle.Text = "نظام الحسابات والفواتير";
            lblTitle.Font = new Font("Segoe UI", 24F, FontStyle.Bold);
            lblTitle.ForeColor = Color.FromArgb(41, 128, 185);
            lblTitle.Location = new Point(50, 40);
            lblTitle.Size = new Size(300, 50);
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;

            var lblSubtitle = new Label();
            lblSubtitle.Text = "داشبورد احترافي وأنيق";
            lblSubtitle.Font = new Font("Segoe UI", 12F);
            lblSubtitle.ForeColor = Color.FromArgb(127, 140, 141);
            lblSubtitle.Location = new Point(50, 90);
            lblSubtitle.Size = new Size(300, 30);
            lblSubtitle.TextAlign = ContentAlignment.MiddleCenter;

            // اسم المستخدم
            var lblUsername = new Label();
            lblUsername.Text = "اسم المستخدم";
            lblUsername.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            lblUsername.ForeColor = Color.FromArgb(52, 73, 94);
            lblUsername.Location = new Point(50, 140);
            lblUsername.Size = new Size(100, 25);

            txtUsername = new TextBox();
            txtUsername.Location = new Point(50, 165);
            txtUsername.Size = new Size(300, 30);
            txtUsername.Font = new Font("Segoe UI", 11F);
            txtUsername.Text = "admin";
            txtUsername.BorderStyle = BorderStyle.FixedSingle;

            // كلمة المرور
            var lblPassword = new Label();
            lblPassword.Text = "كلمة المرور";
            lblPassword.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            lblPassword.ForeColor = Color.FromArgb(52, 73, 94);
            lblPassword.Location = new Point(50, 200);
            lblPassword.Size = new Size(100, 25);

            txtPassword = new TextBox();
            txtPassword.Location = new Point(50, 225);
            txtPassword.Size = new Size(300, 30);
            txtPassword.Font = new Font("Segoe UI", 11F);
            txtPassword.UseSystemPasswordChar = true;
            txtPassword.Text = "admin123";
            txtPassword.BorderStyle = BorderStyle.FixedSingle;

            // زر الدخول
            btnLogin = new Button();
            btnLogin.Text = "دخول";
            btnLogin.Location = new Point(50, 275);
            btnLogin.Size = new Size(300, 45);
            btnLogin.BackColor = Color.FromArgb(41, 128, 185);
            btnLogin.ForeColor = Color.White;
            btnLogin.FlatStyle = FlatStyle.Flat;
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnLogin.Cursor = Cursors.Hand;
            btnLogin.Click += BtnLogin_Click;

            // إضافة التحكمات
            loginPanel.Controls.Add(lblTitle);
            loginPanel.Controls.Add(lblSubtitle);
            loginPanel.Controls.Add(lblUsername);
            loginPanel.Controls.Add(txtUsername);
            loginPanel.Controls.Add(lblPassword);
            loginPanel.Controls.Add(txtPassword);
            loginPanel.Controls.Add(btnLogin);

            mainPanel.Controls.Add(loginPanel);
            this.Controls.Add(mainPanel);

            this.AcceptButton = btnLogin;
        }

        private void MainPanel_Paint(object sender, PaintEventArgs e)
        {
            // تدرج الخلفية
            using (LinearGradientBrush brush = new LinearGradientBrush(
                mainPanel.ClientRectangle,
                Color.FromArgb(74, 144, 226),
                Color.FromArgb(41, 128, 185),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, mainPanel.ClientRectangle);
            }
        }

        private void LoginPanel_Paint(object sender, PaintEventArgs e)
        {
            // ظل للوحة
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, loginPanel.Width, loginPanel.Height), 15);
                loginPanel.Region = new Region(path);
            }
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (AuthService.Login(txtUsername.Text, txtPassword.Text))
            {
                this.Hide();
                var dashboardForm = new DashboardForm();
                dashboardForm.ShowDialog();
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    // الداشبورد الرئيسي
    public class DashboardForm : Form
    {
        private Panel sidePanel;
        private Panel topPanel;
        private Panel contentPanel;
        private Panel statsPanel;
        private Label lblWelcome;
        private Label lblDateTime;
        private Timer timeTimer;

        // بيانات النظام
        private List<Customer> customers;
        private List<Invoice> invoices;
        private List<Receipt> receipts;

        public DashboardForm()
        {
            InitializeForm();
            LoadSampleData();
            SetupDashboard();
            StartTimer();
        }

        private void InitializeForm()
        {
            this.Text = "نظام الحسابات والفواتير - الداشبورد";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(236, 240, 241);

            // الشريط الجانبي
            sidePanel = new Panel();
            sidePanel.Width = 280;
            sidePanel.Dock = DockStyle.Right;
            sidePanel.BackColor = Color.FromArgb(44, 62, 80);
            sidePanel.Paint += SidePanel_Paint;

            // الشريط العلوي
            topPanel = new Panel();
            topPanel.Height = 80;
            topPanel.Dock = DockStyle.Top;
            topPanel.BackColor = Color.White;
            topPanel.Paint += TopPanel_Paint;

            // منطقة المحتوى
            contentPanel = new Panel();
            contentPanel.Dock = DockStyle.Fill;
            contentPanel.BackColor = Color.FromArgb(236, 240, 241);
            contentPanel.Padding = new Padding(20);

            this.Controls.Add(contentPanel);
            this.Controls.Add(topPanel);
            this.Controls.Add(sidePanel);

            SetupSidePanel();
            SetupTopPanel();
        }

        private void SetupSidePanel()
        {
            // شعار النظام
            var logoPanel = new Panel();
            logoPanel.Height = 100;
            logoPanel.Dock = DockStyle.Top;
            logoPanel.BackColor = Color.FromArgb(52, 73, 94);

            var lblLogo = new Label();
            lblLogo.Text = "💼\nنظام الحسابات";
            lblLogo.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            lblLogo.ForeColor = Color.White;
            lblLogo.TextAlign = ContentAlignment.MiddleCenter;
            lblLogo.Dock = DockStyle.Fill;

            logoPanel.Controls.Add(lblLogo);
            sidePanel.Controls.Add(logoPanel);

            // قائمة التنقل
            var menuItems = new[]
            {
                new { Text = "🏠 الرئيسية", Action = new Action(() => ShowDashboard()) },
                new { Text = "📊 الفواتير", Action = new Action(() => ShowInvoices()) },
                new { Text = "💰 السندات", Action = new Action(() => ShowReceipts()) },
                new { Text = "👥 العملاء", Action = new Action(() => ShowCustomers()) },
                new { Text = "📈 التقارير", Action = new Action(() => ShowReports()) },
                new { Text = "⚙️ الإعدادات", Action = new Action(() => ShowSettings()) },
                new { Text = "🚪 خروج", Action = new Action(() => Logout()) }
            };

            int yPos = 120;
            foreach (var item in menuItems)
            {
                var menuButton = CreateMenuButton(item.Text, item.Action);
                menuButton.Location = new Point(10, yPos);
                sidePanel.Controls.Add(menuButton);
                yPos += 60;
            }
        }

        private Button CreateMenuButton(string text, Action action)
        {
            var button = new Button();
            button.Text = text;
            button.Size = new Size(260, 50);
            button.BackColor = Color.Transparent;
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            button.TextAlign = ContentAlignment.MiddleLeft;
            button.Padding = new Padding(20, 0, 0, 0);
            button.Cursor = Cursors.Hand;
            button.Click += (s, e) => action();

            // تأثيرات التفاعل
            button.MouseEnter += (s, e) => button.BackColor = Color.FromArgb(52, 152, 219);
            button.MouseLeave += (s, e) => button.BackColor = Color.Transparent;

            return button;
        }

        private void SetupTopPanel()
        {
            // رسالة الترحيب
            lblWelcome = new Label();
            lblWelcome.Text = $"مرحباً، {AuthService.CurrentUser}";
            lblWelcome.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            lblWelcome.ForeColor = Color.FromArgb(44, 62, 80);
            lblWelcome.Location = new Point(30, 20);
            lblWelcome.Size = new Size(300, 40);

            // التاريخ والوقت
            lblDateTime = new Label();
            lblDateTime.Font = new Font("Segoe UI", 12F);
            lblDateTime.ForeColor = Color.FromArgb(127, 140, 141);
            lblDateTime.Location = new Point(30, 50);
            lblDateTime.Size = new Size(400, 25);

            topPanel.Controls.Add(lblWelcome);
            topPanel.Controls.Add(lblDateTime);
        }

        private void SidePanel_Paint(object sender, PaintEventArgs e)
        {
            // تدرج للشريط الجانبي
            using (LinearGradientBrush brush = new LinearGradientBrush(
                sidePanel.ClientRectangle,
                Color.FromArgb(44, 62, 80),
                Color.FromArgb(52, 73, 94),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, sidePanel.ClientRectangle);
            }
        }

        private void TopPanel_Paint(object sender, PaintEventArgs e)
        {
            // ظل للشريط العلوي
            using (Pen pen = new Pen(Color.FromArgb(189, 195, 199), 1))
            {
                e.Graphics.DrawLine(pen, 0, topPanel.Height - 1, topPanel.Width, topPanel.Height - 1);
            }
        }

        private void StartTimer()
        {
            timeTimer = new Timer();
            timeTimer.Interval = 1000;
            timeTimer.Tick += (s, e) =>
            {
                lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy - HH:mm:ss",
                    new System.Globalization.CultureInfo("ar-SA"));
            };
            timeTimer.Start();

            // تحديث فوري
            lblDateTime.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy - HH:mm:ss",
                new System.Globalization.CultureInfo("ar-SA"));
        }

        private void LoadSampleData()
        {
            // بيانات تجريبية للعملاء
            customers = new List<Customer>
            {
                new Customer { Id = 1, CustomerCode = "C001", CustomerName = "أحمد محمد علي", Phone = "0501234567", Email = "<EMAIL>", CurrentBalance = 15000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-30) },
                new Customer { Id = 2, CustomerCode = "C002", CustomerName = "فاطمة عبدالله", Phone = "0507654321", Email = "<EMAIL>", CurrentBalance = -2500, IsActive = true, CreatedDate = DateTime.Now.AddDays(-25) },
                new Customer { Id = 3, CustomerCode = "C003", CustomerName = "محمد سعد الدين", Phone = "0551122334", Email = "<EMAIL>", CurrentBalance = 8000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-20) },
                new Customer { Id = 4, CustomerCode = "C004", CustomerName = "نورا أحمد", Phone = "0554433221", Email = "<EMAIL>", CurrentBalance = 0, IsActive = true, CreatedDate = DateTime.Now.AddDays(-15) },
                new Customer { Id = 5, CustomerCode = "C005", CustomerName = "خالد العتيبي", Phone = "0556677889", Email = "<EMAIL>", CurrentBalance = 25000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-10) },
                new Customer { Id = 6, CustomerCode = "C006", CustomerName = "سارة الأحمد", Phone = "0559988776", Email = "<EMAIL>", CurrentBalance = 12000, IsActive = true, CreatedDate = DateTime.Now.AddDays(-5) }
            };

            // بيانات تجريبية للفواتير
            invoices = new List<Invoice>
            {
                new Invoice { Id = 1, InvoiceNumber = "INV-2024-001", InvoiceDate = DateTime.Now.AddDays(-45), DueDate = DateTime.Now.AddDays(-15), CustomerId = 1, TotalAmount = 11500, PaidAmount = 5000, Status = "مدفوعة جزئياً", Notes = "فاتورة مبيعات أجهزة كمبيوتر" },
                new Invoice { Id = 2, InvoiceNumber = "INV-2024-002", InvoiceDate = DateTime.Now.AddDays(-30), DueDate = DateTime.Now.AddDays(-10), CustomerId = 2, TotalAmount = 5750, PaidAmount = 5750, Status = "مدفوعة", Notes = "فاتورة خدمات استشارية" },
                new Invoice { Id = 3, InvoiceNumber = "INV-2024-003", InvoiceDate = DateTime.Now.AddDays(-25), DueDate = DateTime.Now.AddDays(-5), CustomerId = 3, TotalAmount = 9200, PaidAmount = 0, Status = "متأخرة", Notes = "فاتورة مواد خام" },
                new Invoice { Id = 4, InvoiceNumber = "INV-2024-004", InvoiceDate = DateTime.Now.AddDays(-20), DueDate = DateTime.Now.AddDays(10), CustomerId = 4, TotalAmount = 13800, PaidAmount = 0, Status = "مؤكدة", Notes = "فاتورة معدات مكتبية" },
                new Invoice { Id = 5, InvoiceNumber = "INV-2024-005", InvoiceDate = DateTime.Now.AddDays(-15), DueDate = DateTime.Now.AddDays(15), CustomerId = 5, TotalAmount = 23000, PaidAmount = 10000, Status = "مدفوعة جزئياً", Notes = "فاتورة مشروع تطوير" },
                new Invoice { Id = 6, InvoiceNumber = "INV-2024-006", InvoiceDate = DateTime.Now.AddDays(-10), DueDate = DateTime.Now.AddDays(20), CustomerId = 1, TotalAmount = 3450, PaidAmount = 0, Status = "مسودة", Notes = "فاتورة صيانة" },
                new Invoice { Id = 7, InvoiceNumber = "INV-2024-007", InvoiceDate = DateTime.Now.AddDays(-60), DueDate = DateTime.Now.AddDays(-30), CustomerId = 2, TotalAmount = 8625, PaidAmount = 0, Status = "متأخرة", Notes = "فاتورة تدريب" },
                new Invoice { Id = 8, InvoiceNumber = "INV-2024-008", InvoiceDate = DateTime.Now.AddDays(-5), DueDate = DateTime.Now.AddDays(25), CustomerId = 6, TotalAmount = 15600, PaidAmount = 15600, Status = "مدفوعة", Notes = "فاتورة مواد تسويقية" }
            };

            // بيانات تجريبية للسندات
            receipts = new List<Receipt>
            {
                new Receipt { Id = 1, ReceiptNumber = "R001", ReceiptDate = DateTime.Now.AddDays(-28), CustomerId = 1, Amount = 5000, PaymentMethod = "نقدي", Status = "مؤكد" },
                new Receipt { Id = 2, ReceiptNumber = "R002", ReceiptDate = DateTime.Now.AddDays(-18), CustomerId = 2, Amount = 5750, PaymentMethod = "تحويل بنكي", Status = "مؤكد" },
                new Receipt { Id = 3, ReceiptNumber = "R003", ReceiptDate = DateTime.Now.AddDays(-10), CustomerId = 5, Amount = 10000, PaymentMethod = "شيك", Status = "مؤكد" },
                new Receipt { Id = 4, ReceiptNumber = "R004", ReceiptDate = DateTime.Now.AddDays(-3), CustomerId = 6, Amount = 15600, PaymentMethod = "تحويل بنكي", Status = "مؤكد" },
                new Receipt { Id = 5, ReceiptNumber = "R005", ReceiptDate = DateTime.Now.AddDays(-1), CustomerId = 1, Amount = 2000, PaymentMethod = "نقدي", Status = "مسودة" }
            };
        }

        private void SetupDashboard()
        {
            ShowDashboard();
        }

        // عرض الداشبورد الرئيسي
        private void ShowDashboard()
        {
            contentPanel.Controls.Clear();

            // لوحة الإحصائيات
            var statsContainer = new Panel();
            statsContainer.Height = 150;
            statsContainer.Dock = DockStyle.Top;
            statsContainer.BackColor = Color.Transparent;
            statsContainer.Padding = new Padding(0, 0, 0, 20);

            // إحصائيات سريعة
            var stats = new[]
            {
                new { Title = "إجمالي الفواتير", Value = invoices.Count.ToString(), SubValue = $"{invoices.Sum(i => i.TotalAmount):N0} ر.س", Color = Color.FromArgb(52, 152, 219), Icon = "📊" },
                new { Title = "الفواتير المدفوعة", Value = invoices.Count(i => i.Status == "مدفوعة").ToString(), SubValue = $"{invoices.Where(i => i.Status == "مدفوعة").Sum(i => i.TotalAmount):N0} ر.س", Color = Color.FromArgb(46, 204, 113), Icon = "✅" },
                new { Title = "الفواتير المتأخرة", Value = invoices.Count(i => i.IsOverdue).ToString(), SubValue = $"{invoices.Where(i => i.IsOverdue).Sum(i => i.RemainingAmount):N0} ر.س", Color = Color.FromArgb(231, 76, 60), Icon = "⚠️" },
                new { Title = "إجمالي العملاء", Value = customers.Count(c => c.IsActive).ToString(), SubValue = $"{customers.Sum(c => c.CurrentBalance):N0} ر.س رصيد", Color = Color.FromArgb(155, 89, 182), Icon = "👥" }
            };

            int cardWidth = 280;
            int cardSpacing = 20;
            int startX = 20;

            for (int i = 0; i < stats.Length; i++)
            {
                var statCard = CreateStatCard(stats[i].Title, stats[i].Value, stats[i].SubValue, stats[i].Color, stats[i].Icon);
                statCard.Location = new Point(startX + (i * (cardWidth + cardSpacing)), 10);
                statsContainer.Controls.Add(statCard);
            }

            contentPanel.Controls.Add(statsContainer);

            // منطقة الرسوم البيانية والجداول
            var chartsContainer = new Panel();
            chartsContainer.Dock = DockStyle.Fill;
            chartsContainer.BackColor = Color.Transparent;

            // الرسم البياني للمبيعات
            var salesChart = CreateSalesChart();
            salesChart.Location = new Point(20, 20);
            chartsContainer.Controls.Add(salesChart);

            // جدول الفواتير الأخيرة
            var recentInvoicesPanel = CreateRecentInvoicesPanel();
            recentInvoicesPanel.Location = new Point(650, 20);
            chartsContainer.Controls.Add(recentInvoicesPanel);

            // جدول العملاء الأعلى رصيداً
            var topCustomersPanel = CreateTopCustomersPanel();
            topCustomersPanel.Location = new Point(20, 350);
            chartsContainer.Controls.Add(topCustomersPanel);

            // إحصائيات سريعة إضافية
            var quickStatsPanel = CreateQuickStatsPanel();
            quickStatsPanel.Location = new Point(650, 350);
            chartsContainer.Controls.Add(quickStatsPanel);

            contentPanel.Controls.Add(chartsContainer);
        }

        private Panel CreateStatCard(string title, string value, string subValue, Color color, string icon)
        {
            var card = new Panel();
            card.Size = new Size(280, 120);
            card.BackColor = Color.White;
            card.Paint += (s, e) => DrawCardShadow(e, card, color);

            // الأيقونة
            var iconLabel = new Label();
            iconLabel.Text = icon;
            iconLabel.Font = new Font("Segoe UI Emoji", 24F);
            iconLabel.Location = new Point(220, 20);
            iconLabel.Size = new Size(50, 50);
            iconLabel.TextAlign = ContentAlignment.MiddleCenter;

            // العنوان
            var titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(20, 20);
            titleLabel.Size = new Size(180, 25);

            // القيمة الرئيسية
            var valueLabel = new Label();
            valueLabel.Text = value;
            valueLabel.Font = new Font("Segoe UI", 20F, FontStyle.Bold);
            valueLabel.ForeColor = color;
            valueLabel.Location = new Point(20, 45);
            valueLabel.Size = new Size(100, 35);

            // القيمة الفرعية
            var subValueLabel = new Label();
            subValueLabel.Text = subValue;
            subValueLabel.Font = new Font("Segoe UI", 10F);
            subValueLabel.ForeColor = Color.FromArgb(127, 140, 141);
            subValueLabel.Location = new Point(20, 85);
            subValueLabel.Size = new Size(180, 20);

            card.Controls.Add(iconLabel);
            card.Controls.Add(titleLabel);
            card.Controls.Add(valueLabel);
            card.Controls.Add(subValueLabel);

            return card;
        }

        private void DrawCardShadow(PaintEventArgs e, Panel card, Color accentColor)
        {
            // خلفية بيضاء مع حواف مدورة
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, card.Width, card.Height), 10);
                card.Region = new Region(path);

                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }

            // خط ملون في الأعلى
            using (SolidBrush brush = new SolidBrush(accentColor))
            {
                e.Graphics.FillRectangle(brush, 0, 0, card.Width, 4);
            }
        }

        private Panel CreateSalesChart()
        {
            var chartPanel = new Panel();
            chartPanel.Size = new Size(600, 300);
            chartPanel.BackColor = Color.White;
            chartPanel.Paint += (s, e) => DrawSalesChart(e, chartPanel);

            var titleLabel = new Label();
            titleLabel.Text = "📈 مبيعات آخر 7 أيام";
            titleLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(20, 15);
            titleLabel.Size = new Size(200, 30);

            chartPanel.Controls.Add(titleLabel);
            return chartPanel;
        }

        private void DrawSalesChart(PaintEventArgs e, Panel panel)
        {
            // خلفية بيضاء مع حواف مدورة
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, panel.Width, panel.Height), 10);
                panel.Region = new Region(path);

                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }

            // رسم بياني بسيط للمبيعات
            var salesData = new[] { 15000, 22000, 18000, 25000, 30000, 28000, 35000 };
            var days = new[] { "السبت", "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة" };

            int chartX = 50;
            int chartY = 60;
            int chartWidth = 500;
            int chartHeight = 180;
            int maxValue = salesData.Max();

            // رسم الخطوط الإرشادية
            using (Pen gridPen = new Pen(Color.FromArgb(236, 240, 241), 1))
            {
                for (int i = 0; i <= 5; i++)
                {
                    int y = chartY + (chartHeight * i / 5);
                    e.Graphics.DrawLine(gridPen, chartX, y, chartX + chartWidth, y);
                }
            }

            // رسم البيانات
            using (Pen linePen = new Pen(Color.FromArgb(52, 152, 219), 3))
            using (SolidBrush pointBrush = new SolidBrush(Color.FromArgb(52, 152, 219)))
            {
                var points = new Point[salesData.Length];

                for (int i = 0; i < salesData.Length; i++)
                {
                    int x = chartX + (chartWidth * i / (salesData.Length - 1));
                    int y = chartY + chartHeight - (int)((double)salesData[i] / maxValue * chartHeight);
                    points[i] = new Point(x, y);

                    // رسم النقطة
                    e.Graphics.FillEllipse(pointBrush, x - 4, y - 4, 8, 8);

                    // رسم القيمة
                    using (Font font = new Font("Segoe UI", 9F))
                    using (SolidBrush textBrush = new SolidBrush(Color.FromArgb(52, 73, 94)))
                    {
                        string valueText = (salesData[i] / 1000).ToString() + "ك";
                        var textSize = e.Graphics.MeasureString(valueText, font);
                        e.Graphics.DrawString(valueText, font, textBrush, x - textSize.Width / 2, y - 25);
                    }

                    // رسم اسم اليوم
                    using (Font font = new Font("Segoe UI", 9F))
                    using (SolidBrush textBrush = new SolidBrush(Color.FromArgb(127, 140, 141)))
                    {
                        var textSize = e.Graphics.MeasureString(days[i], font);
                        e.Graphics.DrawString(days[i], font, textBrush, x - textSize.Width / 2, chartY + chartHeight + 10);
                    }
                }

                // رسم الخط
                if (points.Length > 1)
                {
                    e.Graphics.DrawLines(linePen, points);
                }
            }
        }

        private Panel CreateRecentInvoicesPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(600, 300);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "📋 الفواتير الأخيرة";
            titleLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(20, 15);
            titleLabel.Size = new Size(200, 30);

            var listView = new ListView();
            listView.Location = new Point(20, 50);
            listView.Size = new Size(560, 230);
            listView.View = View.Details;
            listView.FullRowSelect = true;
            listView.GridLines = true;
            listView.HeaderStyle = ColumnHeaderStyle.Nonclickable;
            listView.Font = new Font("Segoe UI", 10F);

            listView.Columns.Add("رقم الفاتورة", 120);
            listView.Columns.Add("العميل", 150);
            listView.Columns.Add("المبلغ", 100);
            listView.Columns.Add("الحالة", 100);
            listView.Columns.Add("التاريخ", 90);

            var recentInvoices = invoices.OrderByDescending(i => i.InvoiceDate).Take(8);
            foreach (var invoice in recentInvoices)
            {
                var customer = customers.FirstOrDefault(c => c.Id == invoice.CustomerId);
                var item = new ListViewItem(invoice.InvoiceNumber);
                item.SubItems.Add(customer?.CustomerName ?? "غير محدد");
                item.SubItems.Add(invoice.TotalAmount.ToString("N0"));
                item.SubItems.Add(invoice.Status);
                item.SubItems.Add(invoice.InvoiceDate.ToString("MM/dd"));

                // تلوين حسب الحالة
                if (invoice.Status == "مدفوعة")
                    item.BackColor = Color.FromArgb(212, 237, 218);
                else if (invoice.Status == "متأخرة")
                    item.BackColor = Color.FromArgb(248, 215, 218);
                else if (invoice.Status == "مدفوعة جزئياً")
                    item.BackColor = Color.FromArgb(255, 243, 205);

                listView.Items.Add(item);
            }

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(listView);
            return panel;
        }

        private Panel CreateTopCustomersPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(600, 250);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "🏆 أعلى العملاء رصيداً";
            titleLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(20, 15);
            titleLabel.Size = new Size(200, 30);

            var topCustomers = customers.OrderByDescending(c => c.CurrentBalance).Take(6);
            int yPos = 60;
            int rank = 1;

            foreach (var customer in topCustomers)
            {
                var customerPanel = new Panel();
                customerPanel.Location = new Point(20, yPos);
                customerPanel.Size = new Size(560, 30);
                customerPanel.BackColor = Color.FromArgb(248, 249, 250);

                var rankLabel = new Label();
                rankLabel.Text = rank.ToString();
                rankLabel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                rankLabel.ForeColor = Color.FromArgb(52, 152, 219);
                rankLabel.Location = new Point(10, 5);
                rankLabel.Size = new Size(30, 20);
                rankLabel.TextAlign = ContentAlignment.MiddleCenter;

                var nameLabel = new Label();
                nameLabel.Text = customer.CustomerName;
                nameLabel.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
                nameLabel.ForeColor = Color.FromArgb(52, 73, 94);
                nameLabel.Location = new Point(50, 5);
                nameLabel.Size = new Size(200, 20);

                var codeLabel = new Label();
                codeLabel.Text = customer.CustomerCode;
                codeLabel.Font = new Font("Segoe UI", 9F);
                codeLabel.ForeColor = Color.FromArgb(127, 140, 141);
                codeLabel.Location = new Point(260, 5);
                codeLabel.Size = new Size(80, 20);

                var balanceLabel = new Label();
                balanceLabel.Text = customer.CurrentBalance.ToString("N0") + " ر.س";
                balanceLabel.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
                balanceLabel.ForeColor = customer.CurrentBalance >= 0 ? Color.FromArgb(46, 204, 113) : Color.FromArgb(231, 76, 60);
                balanceLabel.Location = new Point(350, 5);
                balanceLabel.Size = new Size(150, 20);
                balanceLabel.TextAlign = ContentAlignment.MiddleRight;

                customerPanel.Controls.Add(rankLabel);
                customerPanel.Controls.Add(nameLabel);
                customerPanel.Controls.Add(codeLabel);
                customerPanel.Controls.Add(balanceLabel);

                panel.Controls.Add(customerPanel);
                yPos += 35;
                rank++;
            }

            panel.Controls.Add(titleLabel);
            return panel;
        }

        private Panel CreateQuickStatsPanel()
        {
            var panel = new Panel();
            panel.Size = new Size(600, 250);
            panel.BackColor = Color.White;
            panel.Paint += (s, e) => DrawPanelBackground(e, panel);

            var titleLabel = new Label();
            titleLabel.Text = "⚡ إحصائيات سريعة";
            titleLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(52, 73, 94);
            titleLabel.Location = new Point(20, 15);
            titleLabel.Size = new Size(200, 30);

            var quickStats = new[]
            {
                new { Label = "متوسط قيمة الفاتورة", Value = invoices.Average(i => i.TotalAmount).ToString("N0") + " ر.س", Icon = "💰" },
                new { Label = "إجمالي المدفوعات اليوم", Value = receipts.Where(r => r.ReceiptDate.Date == DateTime.Today).Sum(r => r.Amount).ToString("N0") + " ر.س", Icon = "📈" },
                new { Label = "عدد الفواتير المستحقة", Value = invoices.Count(i => i.DueDate <= DateTime.Now && i.Status != "مدفوعة").ToString(), Icon = "⏰" },
                new { Label = "نسبة التحصيل", Value = ((invoices.Sum(i => i.PaidAmount) / invoices.Sum(i => i.TotalAmount)) * 100).ToString("F1") + "%", Icon = "📊" }
            };

            int yPos = 60;
            foreach (var stat in quickStats)
            {
                var statPanel = new Panel();
                statPanel.Location = new Point(20, yPos);
                statPanel.Size = new Size(560, 40);
                statPanel.BackColor = Color.FromArgb(248, 249, 250);

                var iconLabel = new Label();
                iconLabel.Text = stat.Icon;
                iconLabel.Font = new Font("Segoe UI Emoji", 16F);
                iconLabel.Location = new Point(15, 8);
                iconLabel.Size = new Size(30, 25);

                var labelText = new Label();
                labelText.Text = stat.Label;
                labelText.Font = new Font("Segoe UI", 11F);
                labelText.ForeColor = Color.FromArgb(52, 73, 94);
                labelText.Location = new Point(55, 10);
                labelText.Size = new Size(300, 20);

                var valueText = new Label();
                valueText.Text = stat.Value;
                valueText.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                valueText.ForeColor = Color.FromArgb(52, 152, 219);
                valueText.Location = new Point(360, 8);
                valueText.Size = new Size(180, 25);
                valueText.TextAlign = ContentAlignment.MiddleRight;

                statPanel.Controls.Add(iconLabel);
                statPanel.Controls.Add(labelText);
                statPanel.Controls.Add(valueText);

                panel.Controls.Add(statPanel);
                yPos += 45;
            }

            panel.Controls.Add(titleLabel);
            return panel;
        }

        private void DrawPanelBackground(PaintEventArgs e, Panel panel)
        {
            using (GraphicsPath path = new GraphicsPath())
            {
                path.AddRoundedRectangle(new Rectangle(0, 0, panel.Width, panel.Height), 10);
                panel.Region = new Region(path);

                using (SolidBrush brush = new SolidBrush(Color.White))
                {
                    e.Graphics.FillPath(brush, path);
                }
            }
        }

        // دوال التنقل
        private void ShowInvoices()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة الفواتير\n\nالميزات المتوفرة:\n• عرض جميع الفواتير\n• إضافة فاتورة جديدة\n• تعديل الفواتير\n• طباعة الفواتير\n• فلترة وبحث متقدم",
                "إدارة الفواتير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReceipts()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة السندات\n\nالميزات المتوفرة:\n• عرض جميع السندات\n• إضافة سند جديد\n• تعديل السندات\n• ربط السندات بالفواتير\n• تقارير المدفوعات",
                "إدارة السندات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCustomers()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة إدارة العملاء\n\nالميزات المتوفرة:\n• عرض جميع العملاء\n• إضافة عميل جديد\n• تعديل بيانات العملاء\n• كشف حساب العميل\n• تقارير العملاء",
                "إدارة العملاء", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReports()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة التقارير\n\nالتقارير المتوفرة:\n• تقرير المبيعات\n• تقرير المدفوعات\n• تقرير العملاء\n• تقرير الأرباح\n• تقارير مخصصة",
                "التقارير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowSettings()
        {
            MessageBox.Show("🎉 سيتم فتح شاشة الإعدادات\n\nالإعدادات المتوفرة:\n• إعدادات الشركة\n• إعدادات الضرائب\n• إعدادات الطباعة\n• إعدادات النسخ الاحتياطي\n• إعدادات المستخدمين",
                "الإعدادات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Logout()
        {
            var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                AuthService.Logout();
                this.Close();
            }
        }
    }

    // إضافة دالة مساعدة للحواف المدورة
    public static class GraphicsExtensions
    {
        public static void AddRoundedRectangle(this GraphicsPath path, Rectangle rect, int radius)
        {
            int diameter = radius * 2;
            Size size = new Size(diameter, diameter);
            Rectangle arc = new Rectangle(rect.Location, size);

            // الزاوية اليسرى العلوية
            path.AddArc(arc, 180, 90);

            // الزاوية اليمنى العلوية
            arc.X = rect.Right - diameter;
            path.AddArc(arc, 270, 90);

            // الزاوية اليمنى السفلى
            arc.Y = rect.Bottom - diameter;
            path.AddArc(arc, 0, 90);

            // الزاوية اليسرى السفلى
            arc.X = rect.Left;
            path.AddArc(arc, 90, 90);

            path.CloseFigure();
        }
    }

    // نقطة دخول التطبيق
    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new LoginForm());
        }
    }
}