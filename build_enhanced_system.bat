@echo off
chcp 65001 > nul
echo ========================================
echo بناء نظام الفواتير المحسن والمتكامل
echo ========================================
echo.

echo 🎨 نظام محسن مع داشبورد متكامل
echo ✨ واجهة رئيسية جميلة ومدمجة
echo 📊 شاشات إضافة وعرض الفواتير محسنة
echo 🔧 إدارة شاملة ومتطورة
echo.

rem التحقق من وجود مترجم C#
where csc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ مترجم C# غير متوفر
    echo.
    echo الحلول:
    echo 1. افتح Developer Command Prompt for Visual Studio
    echo 2. أو ثبت .NET Framework SDK
    echo 3. أو استخدم Visual Studio لبناء المشروع
    echo.
    goto :end
)

echo ✓ مترجم C# متوفر
echo.

echo فحص الملف المطلوب...
if not exist "EnhancedInvoiceSystem.cs" (
    echo ✗ ملف EnhancedInvoiceSystem.cs مفقود
    echo يرجى التأكد من وجود الملف
    goto :end
)

echo ✓ ملف EnhancedInvoiceSystem.cs موجود
echo.

echo بناء النظام المحسن...

csc /target:winexe ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /out:EnhancedInvoiceSystem.exe ^
    EnhancedInvoiceSystem.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم بناء النظام المحسن بنجاح!
    echo.
    echo تم إنشاء: EnhancedInvoiceSystem.exe
    echo.
    
    echo إنشاء ملف الإعداد...
    echo ^<?xml version="1.0" encoding="utf-8"?^> > EnhancedInvoiceSystem.exe.config
    echo ^<configuration^> >> EnhancedInvoiceSystem.exe.config
    echo   ^<startup^> >> EnhancedInvoiceSystem.exe.config
    echo     ^<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/^> >> EnhancedInvoiceSystem.exe.config
    echo   ^</startup^> >> EnhancedInvoiceSystem.exe.config
    echo ^</configuration^> >> EnhancedInvoiceSystem.exe.config
    
    echo ✓ تم إنشاء ملف الإعداد
    echo.
    
    set /p run="هل تريد تشغيل النظام المحسن الآن؟ (Y/N): "
    if /i "%run%"=="Y" (
        echo.
        echo تشغيل النظام المحسن...
        start "" "EnhancedInvoiceSystem.exe"
        echo.
        echo ✓ تم تشغيل النظام المحسن بنجاح!
    )
    
    echo.
    echo ========================================
    echo نظام الفواتير المحسن والمتكامل
    echo ========================================
    echo.
    echo ملف التشغيل: EnhancedInvoiceSystem.exe
    echo.
    echo معلومات تسجيل الدخول:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo 🎨 التحسينات الجديدة:
    echo ✅ واجهة رئيسية محسنة ومدمجة مع الداشبورد
    echo ✅ شاشة إضافة فاتورة جديدة شاملة ومتطورة
    echo ✅ شاشة عرض الفواتير محسنة مع فلترة متقدمة
    echo ✅ تصميم أنيق ومتناسق في جميع الشاشات
    echo ✅ تنقل سلس بين الأقسام المختلفة
    echo ✅ بيانات تجريبية غنية ومتنوعة
    echo.
    echo 🏠 الواجهة الرئيسية المحسنة:
    echo • شريط جانبي أنيق مع 9 أقسام رئيسية
    echo • شريط علوي مع ترحيب وتاريخ وإشعارات
    echo • داشبورد متكامل مع إحصائيات تفاعلية
    echo • رسوم بيانية جميلة للمبيعات
    echo • جداول تفاعلية ملونة
    echo • تحديث تلقائي للوقت والبيانات
    echo.
    echo 📊 شاشة إدارة الفواتير:
    echo • عرض جميع الفواتير في جدول منسق
    echo • شريط بحث وفلترة متقدم
    echo • أزرار إجراءات سريعة (إضافة، تعديل، حذف، طباعة)
    echo • تلوين تلقائي حسب حالة الفاتورة
    echo • شريط ملخص بالإحصائيات
    echo • فلترة حسب التاريخ والحالة
    echo.
    echo ➕ شاشة إضافة فاتورة جديدة:
    echo • نموذج شامل ومنظم
    echo • اختيار العميل من قائمة منسدلة
    echo • تحديد تواريخ الفاتورة والاستحقاق
    echo • إضافة أصناف متعددة للفاتورة
    echo • حساب تلقائي للضرائب والخصومات
    echo • ملخص مالي تفاعلي
    echo • التحقق من صحة البيانات
    echo.
    echo 📦 نموذج إضافة الأصناف:
    echo • اختيار المنتج من قائمة شاملة
    echo • تحديد الكمية وسعر الوحدة
    echo • حساب نسبة الخصم
    echo • عرض الإجمالي تلقائياً
    echo • التحقق من صحة البيانات
    echo.
    echo 📊 البيانات التجريبية المحسنة:
    echo • 6 عملاء بمعلومات كاملة وأرصدة متنوعة
    echo • 6 منتجات بفئات وأسعار مختلفة
    echo • 8 فواتير بحالات وأصناف متعددة
    echo • 5 سندات قبض مربوطة بالفواتير
    echo • بيانات واقعية ومنطقية
    echo.
    echo 🎯 الأقسام المتوفرة:
    echo • 🏠 الداشبورد الرئيسي - إحصائيات شاملة
    echo • 📊 إدارة الفواتير - عرض وإدارة كاملة
    echo • ➕ إضافة فاتورة جديدة - نموذج متطور
    echo • 💰 السندات والمدفوعات - إدارة المدفوعات
    echo • 👥 إدارة العملاء - قاعدة بيانات العملاء
    echo • 📦 إدارة المنتجات - كتالوج المنتجات
    echo • 📈 التقارير والإحصائيات - تحليلات مفصلة
    echo • ⚙️ الإعدادات - تخصيص النظام
    echo • 🚪 تسجيل خروج - خروج آمن
    echo.
    echo 🎨 التصميم والواجهة:
    echo ✓ ألوان متناسقة ومريحة للعين
    echo ✓ خطوط Segoe UI الأنيقة
    echo ✓ حواف مدورة في جميع العناصر
    echo ✓ ظلال وتأثيرات ثلاثية الأبعاد
    echo ✓ تدرجات لونية جميلة
    echo ✓ أيقونات تعبيرية واضحة
    echo ✓ تخطيط متوازن ومنظم
    echo ✓ تأثيرات تفاعلية عند التمرير
    echo.
    echo 🔧 الوظائف المتقدمة:
    echo • حساب تلقائي للضرائب (15%%)
    echo • إدارة الخصومات على مستوى الصنف
    echo • تتبع حالات الفواتير المختلفة
    echo • ربط المدفوعات بالفواتير
    echo • فلترة وبحث متقدم
    echo • تلوين تلقائي حسب الحالة
    echo • التحقق من صحة البيانات
    echo • رسائل تأكيد ونجاح العمليات
    echo.
    echo 📱 سهولة الاستخدام:
    echo • واجهة بديهية وسهلة التنقل
    echo • أزرار واضحة ومفهومة
    echo • رسائل مساعدة وتوجيهية
    echo • اختصارات لوحة المفاتيح
    echo • تصميم متجاوب مع أحجام الشاشات
    echo.
    echo 🎉 هذا أفضل نظام فواتير محسن ومتكامل!
    
) else (
    echo.
    echo ✗ فشل في بناء النظام المحسن
    echo.
    echo الأخطاء المحتملة:
    echo 1. أخطاء في الكود المصدري
    echo 2. مراجع مفقودة
    echo 3. مشاكل في مترجم C#
    echo.
    echo للتشخيص:
    echo 1. افتح EnhancedInvoiceSystem.cs وتحقق من الأخطاء
    echo 2. تأكد من وجود جميع المراجع المطلوبة
    echo 3. استخدم Visual Studio للحصول على تفاصيل الأخطاء
    echo.
    echo بدائل أخرى:
    echo • استخدم Visual Studio لبناء المشروع
    echo • راجع الكود للتأكد من عدم وجود أخطاء إملائية
)

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
