using System;

namespace AccountingSystem.Models
{
    /// <summary>
    /// نموذج الحساب في شجرة الحسابات
    /// </summary>
    public class Account
    {
        public int Id { get; set; }
        public string AccountCode { get; set; }
        public string AccountName { get; set; }
        public string AccountNameEnglish { get; set; }
        public int? ParentAccountId { get; set; }
        public string AccountType { get; set; } // أصول، خصوم، حقوق ملكية، إيرادات، مصروفات
        public int Level { get; set; }
        public bool IsParent { get; set; }
        public bool IsActive { get; set; }
        public decimal Balance { get; set; }
        public string DebitCredit { get; set; } // مدين أو دائن
        public string Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }
        
        // خصائص إضافية للعرض
        public string FullAccountCode { get; set; }
        public string FullAccountName { get; set; }
        public Account ParentAccount { get; set; }
    }
}
