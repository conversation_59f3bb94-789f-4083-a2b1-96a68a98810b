@echo off
chcp 65001 > nul
echo ========================================
echo اختبار الملف الواحد
echo ========================================
echo.

echo فحص الملف المطلوب...

if not exist "ReceiptsSystemAll.cs" (
    echo ✗ ملف ReceiptsSystemAll.cs مفقود
    echo.
    echo هذا الملف يحتوي على:
    echo • جميع نماذج البيانات
    echo • جميع النماذج والواجهات
    echo • خدمة المصادقة
    echo • نقطة دخول التطبيق
    echo.
    goto :end
)

echo ✓ ملف ReceiptsSystemAll.cs موجود
echo.

echo فحص محتوى الملف...

findstr /C:"class Customer" "ReceiptsSystemAll.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على فئة Customer
) else (
    echo ✗ فئة Customer مفقودة
)

findstr /C:"class Receipt" "ReceiptsSystemAll.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على فئة Receipt
) else (
    echo ✗ فئة Receipt مفقودة
)

findstr /C:"class Payment" "ReceiptsSystemAll.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على فئة Payment
) else (
    echo ✗ فئة Payment مفقودة
)

findstr /C:"class LoginForm" "ReceiptsSystemAll.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على فئة LoginForm
) else (
    echo ✗ فئة LoginForm مفقودة
)

findstr /C:"class MainForm" "ReceiptsSystemAll.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على فئة MainForm
) else (
    echo ✗ فئة MainForm مفقودة
)

findstr /C:"class ReceiptsForm" "ReceiptsSystemAll.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على فئة ReceiptsForm
) else (
    echo ✗ فئة ReceiptsForm مفقودة
)

findstr /C:"class PaymentsForm" "ReceiptsSystemAll.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على فئة PaymentsForm
) else (
    echo ✗ فئة PaymentsForm مفقودة
)

findstr /C:"class CustomerStatementForm" "ReceiptsSystemAll.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على فئة CustomerStatementForm
) else (
    echo ✗ فئة CustomerStatementForm مفقودة
)

findstr /C:"static void Main" "ReceiptsSystemAll.cs" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ يحتوي على نقطة دخول Main
) else (
    echo ✗ نقطة دخول Main مفقودة
)

echo.
echo فحص مترجم C#...

where csc >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ مترجم C# متوفر
    
    echo.
    echo اختبار بناء سريع...
    
    csc /target:winexe ^
        /reference:System.dll ^
        /reference:System.Core.dll ^
        /reference:System.Drawing.dll ^
        /reference:System.Windows.Forms.dll ^
        /out:TestSingle.exe ^
        ReceiptsSystemAll.cs >nul 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo ✓ نجح الاختبار السريع
        del TestSingle.exe >nul 2>&1
        echo.
        echo ========================================
        echo النتيجة: جاهز للبناء
        echo ========================================
        echo.
        echo يمكنك الآن تشغيل:
        echo build_single_file.bat
        echo.
        echo هذا سينشئ: ReceiptsSystemSingle.exe
    ) else (
        echo ✗ فشل الاختبار السريع
        echo.
        echo قد تكون هناك أخطاء في الكود
        echo راجع الملف أو استخدم Visual Studio
    )
    
) else (
    echo ✗ مترجم C# غير متوفر
    echo.
    echo الحلول:
    echo 1. افتح Developer Command Prompt for Visual Studio
    echo 2. أو ثبت .NET Framework SDK
    echo 3. أو استخدم Visual Studio مباشرة
)

echo.
echo ========================================
echo معلومات الملف
echo ========================================

for %%A in (ReceiptsSystemAll.cs) do (
    echo حجم الملف: %%~zA بايت
    echo تاريخ التعديل: %%~tA
)

echo.
echo محتوى الملف:
echo • نماذج البيانات: Customer, Receipt, Payment, Invoice, User
echo • خدمة المصادقة: AuthService
echo • نماذج الواجهات: LoginForm, MainForm, ReceiptsForm, PaymentsForm, CustomerStatementForm
echo • نقطة دخول التطبيق: Program.Main
echo • بيانات تجريبية غنية لجميع النماذج

:end
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
