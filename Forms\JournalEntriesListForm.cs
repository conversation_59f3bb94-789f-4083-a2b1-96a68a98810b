using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Dapper;
using AccountingSystem.Data;
using AccountingSystem.Models;
using AccountingSystem.Services;

namespace AccountingSystem.Forms
{
    /// <summary>
    /// نموذج قائمة القيود اليومية
    /// </summary>
    public partial class JournalEntriesListForm : Form
    {
        private Panel panelFilter;
        private Label lblFromDate;
        private Label lblToDate;
        private Label lblStatus;
        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private ComboBox cmbStatus;
        private Button btnSearch;
        private Button btnClear;
        
        private DataGridView dgvJournalEntries;
        
        private Panel panelButtons;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnView;
        private Button btnPrint;
        private Button btnClose;
        
        private List<JournalEntry> journalEntries;
        
        public JournalEntriesListForm()
        {
            InitializeComponent();
            LoadJournalEntries();
        }
        
        private void InitializeComponent()
        {
            this.panelFilter = new Panel();
            this.lblFromDate = new Label();
            this.lblToDate = new Label();
            this.lblStatus = new Label();
            this.dtpFromDate = new DateTimePicker();
            this.dtpToDate = new DateTimePicker();
            this.cmbStatus = new ComboBox();
            this.btnSearch = new Button();
            this.btnClear = new Button();
            
            this.dgvJournalEntries = new DataGridView();
            
            this.panelButtons = new Panel();
            this.btnAdd = new Button();
            this.btnEdit = new Button();
            this.btnDelete = new Button();
            this.btnView = new Button();
            this.btnPrint = new Button();
            this.btnClose = new Button();
            
            this.SuspendLayout();
            
            // Form
            this.Text = "قائمة القيود اليومية";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Filter Panel
            this.panelFilter.Location = new Point(12, 12);
            this.panelFilter.Size = new Size(1176, 80);
            this.panelFilter.BorderStyle = BorderStyle.FixedSingle;
            
            // Filter Controls
            this.lblFromDate.Text = "من تاريخ:";
            this.lblFromDate.Location = new Point(1050, 20);
            this.lblFromDate.Size = new Size(80, 23);
            this.lblFromDate.TextAlign = ContentAlignment.MiddleRight;
            
            this.dtpFromDate.Location = new Point(850, 20);
            this.dtpFromDate.Size = new Size(180, 23);
            this.dtpFromDate.Format = DateTimePickerFormat.Short;
            this.dtpFromDate.Value = DateTime.Now.AddMonths(-1);
            
            this.lblToDate.Text = "إلى تاريخ:";
            this.lblToDate.Location = new Point(750, 20);
            this.lblToDate.Size = new Size(80, 23);
            this.lblToDate.TextAlign = ContentAlignment.MiddleRight;
            
            this.dtpToDate.Location = new Point(550, 20);
            this.dtpToDate.Size = new Size(180, 23);
            this.dtpToDate.Format = DateTimePickerFormat.Short;
            this.dtpToDate.Value = DateTime.Now;
            
            this.lblStatus.Text = "الحالة:";
            this.lblStatus.Location = new Point(450, 20);
            this.lblStatus.Size = new Size(80, 23);
            this.lblStatus.TextAlign = ContentAlignment.MiddleRight;
            
            this.cmbStatus.Location = new Point(250, 20);
            this.cmbStatus.Size = new Size(180, 23);
            this.cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbStatus.Items.AddRange(new string[] { "الكل", "مسودة", "مؤكد" });
            this.cmbStatus.SelectedIndex = 0;
            
            this.btnSearch.Text = "بحث";
            this.btnSearch.Location = new Point(150, 18);
            this.btnSearch.Size = new Size(80, 27);
            this.btnSearch.BackColor = Color.FromArgb(25, 118, 210);
            this.btnSearch.ForeColor = Color.White;
            this.btnSearch.FlatStyle = FlatStyle.Flat;
            this.btnSearch.Click += BtnSearch_Click;
            
            this.btnClear.Text = "مسح";
            this.btnClear.Location = new Point(50, 18);
            this.btnClear.Size = new Size(80, 27);
            this.btnClear.BackColor = Color.Gray;
            this.btnClear.ForeColor = Color.White;
            this.btnClear.FlatStyle = FlatStyle.Flat;
            this.btnClear.Click += BtnClear_Click;
            
            // Add controls to filter panel
            this.panelFilter.Controls.AddRange(new Control[] {
                this.lblFromDate, this.dtpFromDate,
                this.lblToDate, this.dtpToDate,
                this.lblStatus, this.cmbStatus,
                this.btnSearch, this.btnClear
            });
            
            // DataGridView
            this.dgvJournalEntries.Location = new Point(12, 110);
            this.dgvJournalEntries.Size = new Size(1176, 480);
            this.dgvJournalEntries.AllowUserToAddRows = false;
            this.dgvJournalEntries.AllowUserToDeleteRows = false;
            this.dgvJournalEntries.ReadOnly = true;
            this.dgvJournalEntries.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvJournalEntries.MultiSelect = false;
            this.dgvJournalEntries.RightToLeft = RightToLeft.Yes;
            this.dgvJournalEntries.Font = new Font("Tahoma", 10F);
            this.dgvJournalEntries.DoubleClick += DgvJournalEntries_DoubleClick;
            
            SetupDataGridView();
            
            // Buttons Panel
            this.panelButtons.Location = new Point(12, 610);
            this.panelButtons.Size = new Size(1176, 50);
            
            this.btnAdd.Text = "إضافة";
            this.btnAdd.Location = new Point(1050, 10);
            this.btnAdd.Size = new Size(100, 35);
            this.btnAdd.BackColor = Color.FromArgb(76, 175, 80);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.FlatStyle = FlatStyle.Flat;
            this.btnAdd.Click += BtnAdd_Click;
            
            this.btnEdit.Text = "تعديل";
            this.btnEdit.Location = new Point(930, 10);
            this.btnEdit.Size = new Size(100, 35);
            this.btnEdit.BackColor = Color.FromArgb(255, 152, 0);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.Click += BtnEdit_Click;
            
            this.btnDelete.Text = "حذف";
            this.btnDelete.Location = new Point(810, 10);
            this.btnDelete.Size = new Size(100, 35);
            this.btnDelete.BackColor = Color.FromArgb(244, 67, 54);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.Click += BtnDelete_Click;
            
            this.btnView.Text = "عرض";
            this.btnView.Location = new Point(690, 10);
            this.btnView.Size = new Size(100, 35);
            this.btnView.BackColor = Color.FromArgb(96, 125, 139);
            this.btnView.ForeColor = Color.White;
            this.btnView.FlatStyle = FlatStyle.Flat;
            this.btnView.Click += BtnView_Click;
            
            this.btnPrint.Text = "طباعة";
            this.btnPrint.Location = new Point(570, 10);
            this.btnPrint.Size = new Size(100, 35);
            this.btnPrint.BackColor = Color.FromArgb(103, 58, 183);
            this.btnPrint.ForeColor = Color.White;
            this.btnPrint.FlatStyle = FlatStyle.Flat;
            this.btnPrint.Click += BtnPrint_Click;
            
            this.btnClose.Text = "إغلاق";
            this.btnClose.Location = new Point(20, 10);
            this.btnClose.Size = new Size(100, 35);
            this.btnClose.BackColor = Color.Gray;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Click += BtnClose_Click;
            
            // Add controls to buttons panel
            this.panelButtons.Controls.AddRange(new Control[] {
                this.btnAdd, this.btnEdit, this.btnDelete, this.btnView, this.btnPrint, this.btnClose
            });
            
            // Add controls to form
            this.Controls.Add(this.panelFilter);
            this.Controls.Add(this.dgvJournalEntries);
            this.Controls.Add(this.panelButtons);
            
            this.ResumeLayout(false);
        }
        
        private void SetupDataGridView()
        {
            dgvJournalEntries.Columns.Clear();
            
            dgvJournalEntries.Columns.Add("EntryNumber", "رقم القيد");
            dgvJournalEntries.Columns["EntryNumber"].Width = 120;
            
            dgvJournalEntries.Columns.Add("EntryDate", "التاريخ");
            dgvJournalEntries.Columns["EntryDate"].Width = 100;
            
            dgvJournalEntries.Columns.Add("Description", "الوصف");
            dgvJournalEntries.Columns["Description"].Width = 300;
            
            dgvJournalEntries.Columns.Add("Reference", "المرجع");
            dgvJournalEntries.Columns["Reference"].Width = 150;
            
            dgvJournalEntries.Columns.Add("TotalDebit", "إجمالي المدين");
            dgvJournalEntries.Columns["TotalDebit"].Width = 120;
            dgvJournalEntries.Columns["TotalDebit"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvJournalEntries.Columns["TotalDebit"].DefaultCellStyle.Format = "N2";
            
            dgvJournalEntries.Columns.Add("TotalCredit", "إجمالي الدائن");
            dgvJournalEntries.Columns["TotalCredit"].Width = 120;
            dgvJournalEntries.Columns["TotalCredit"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvJournalEntries.Columns["TotalCredit"].DefaultCellStyle.Format = "N2";
            
            dgvJournalEntries.Columns.Add("Status", "الحالة");
            dgvJournalEntries.Columns["Status"].Width = 80;
            
            dgvJournalEntries.Columns.Add("CreatedBy", "المنشئ");
            dgvJournalEntries.Columns["CreatedBy"].Width = 100;
        }
        
        private void LoadJournalEntries()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    string whereClause = "WHERE 1=1";
                    var parameters = new DynamicParameters();
                    
                    // تطبيق فلاتر البحث
                    whereClause += " AND EntryDate >= @FromDate";
                    parameters.Add("FromDate", dtpFromDate.Value.ToString("yyyy-MM-dd"));
                    
                    whereClause += " AND EntryDate <= @ToDate";
                    parameters.Add("ToDate", dtpToDate.Value.ToString("yyyy-MM-dd"));
                    
                    if (cmbStatus.Text != "الكل")
                    {
                        whereClause += " AND Status = @Status";
                        parameters.Add("Status", cmbStatus.Text);
                    }
                    
                    journalEntries = connection.Query<JournalEntry>($@"
                        SELECT * FROM JournalEntries 
                        {whereClause}
                        ORDER BY EntryDate DESC, Id DESC", parameters).ToList();
                }
                
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل القيود اليومية: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                journalEntries = new List<JournalEntry>();
            }
        }
        
        private void RefreshDataGridView()
        {
            dgvJournalEntries.Rows.Clear();
            
            foreach (var entry in journalEntries)
            {
                var rowIndex = dgvJournalEntries.Rows.Add();
                var row = dgvJournalEntries.Rows[rowIndex];
                
                row.Cells["EntryNumber"].Value = entry.EntryNumber;
                row.Cells["EntryDate"].Value = entry.EntryDate.ToString("yyyy/MM/dd");
                row.Cells["Description"].Value = entry.Description;
                row.Cells["Reference"].Value = entry.Reference;
                row.Cells["TotalDebit"].Value = entry.TotalDebit;
                row.Cells["TotalCredit"].Value = entry.TotalCredit;
                row.Cells["Status"].Value = entry.Status;
                row.Cells["CreatedBy"].Value = entry.CreatedBy;
                
                row.Tag = entry;
                
                // تلوين الصفوف حسب الحالة
                if (entry.Status == "مؤكد")
                {
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else
                {
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                }
            }
        }
        
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            LoadJournalEntries();
        }
        
        private void BtnClear_Click(object sender, EventArgs e)
        {
            dtpFromDate.Value = DateTime.Now.AddMonths(-1);
            dtpToDate.Value = DateTime.Now;
            cmbStatus.SelectedIndex = 0;
            LoadJournalEntries();
        }
        
        private void DgvJournalEntries_DoubleClick(object sender, EventArgs e)
        {
            BtnView_Click(sender, e);
        }
        
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var journalEntryForm = new JournalEntryForm();
            if (journalEntryForm.ShowDialog() == DialogResult.OK)
            {
                LoadJournalEntries();
            }
        }
        
        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvJournalEntries.SelectedRows.Count > 0)
            {
                var selectedEntry = dgvJournalEntries.SelectedRows[0].Tag as JournalEntry;
                if (selectedEntry != null)
                {
                    if (selectedEntry.IsPosted)
                    {
                        MessageBox.Show("لا يمكن تعديل قيد مرحل", "تنبيه", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    
                    // تحميل تفاصيل القيد
                    LoadJournalEntryDetails(selectedEntry);
                    
                    var journalEntryForm = new JournalEntryForm(selectedEntry);
                    if (journalEntryForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadJournalEntries();
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار قيد للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        
        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvJournalEntries.SelectedRows.Count > 0)
            {
                var selectedEntry = dgvJournalEntries.SelectedRows[0].Tag as JournalEntry;
                if (selectedEntry != null)
                {
                    if (selectedEntry.IsPosted)
                    {
                        MessageBox.Show("لا يمكن حذف قيد مرحل", "تنبيه", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    
                    var result = MessageBox.Show($"هل أنت متأكد من حذف القيد '{selectedEntry.EntryNumber}'؟", 
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        try
                        {
                            using (var connection = DatabaseHelper.GetConnection())
                            {
                                connection.Open();
                                using (var transaction = connection.BeginTransaction())
                                {
                                    // حذف تفاصيل القيد
                                    connection.Execute("DELETE FROM JournalEntryDetails WHERE JournalEntryId = @Id", 
                                        new { Id = selectedEntry.Id }, transaction);
                                    
                                    // حذف القيد الرئيسي
                                    connection.Execute("DELETE FROM JournalEntries WHERE Id = @Id", 
                                        new { Id = selectedEntry.Id }, transaction);
                                    
                                    transaction.Commit();
                                }
                            }
                            
                            MessageBox.Show("تم حذف القيد بنجاح", "نجح الحذف", 
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            
                            LoadJournalEntries();
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف القيد: {ex.Message}", "خطأ", 
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار قيد للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        
        private void BtnView_Click(object sender, EventArgs e)
        {
            if (dgvJournalEntries.SelectedRows.Count > 0)
            {
                var selectedEntry = dgvJournalEntries.SelectedRows[0].Tag as JournalEntry;
                if (selectedEntry != null)
                {
                    // تحميل تفاصيل القيد
                    LoadJournalEntryDetails(selectedEntry);
                    
                    var journalEntryForm = new JournalEntryForm(selectedEntry);
                    journalEntryForm.ShowDialog();
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار قيد للعرض", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        
        private void LoadJournalEntryDetails(JournalEntry journalEntry)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    var details = connection.Query<JournalEntryDetail>(@"
                        SELECT jed.*, a.AccountName, a.AccountCode
                        FROM JournalEntryDetails jed
                        INNER JOIN Accounts a ON jed.AccountId = a.Id
                        WHERE jed.JournalEntryId = @JournalEntryId
                        ORDER BY jed.LineNumber", 
                        new { JournalEntryId = journalEntry.Id }).ToList();
                    
                    journalEntry.Details = details;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل القيد: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                journalEntry.Details = new List<JournalEntryDetail>();
            }
        }
        
        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                var reportGenerator = new Utils.ReportGenerator();
                reportGenerator.PrintJournalEntries(journalEntries, dtpFromDate.Value, dtpToDate.Value);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
